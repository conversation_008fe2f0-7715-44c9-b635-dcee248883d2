package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


/**
 * 卡包列表请求参数
 * <AUTHOR>
 * @date 2025/07/22
 */
@Data
public class CouponPayDetailPageRequestDTO extends BaseParam {

    @Schema(description = "spuId")
    @NotNull(message = "spuId不能为空")
    private  Long spuId;

    @Schema(description = "每页显示条数")
    @Min(value = 1,message = "每页最少1条数据")
    @Max(value = 20,message = "每页最多20条数据")
    private Integer pageSize = 10;

    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "页码为空错误")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum=1;

}
