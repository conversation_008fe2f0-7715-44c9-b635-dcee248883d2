package com.jsrxjt.mobile.api.distribution.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jsrxjt.common.core.vo.SignRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 扫码提货交易确认请求参数
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "扫码提货交易确认")
public class PickChannelOrderConfirmRequestDTO extends SignRequest {

    @Schema(description = "第三方商户ID，默认值：1-瑞祥商户")
    @JSONField(name = "third_id")
    private String third_id;

    @Schema(description = "类型描述，例如：place-分销订单 yike-逸刻 watsons-屈臣氏 ")
    @JSONField(name = "type")
    private String type;

    @Schema(description = "平台订单流水号")
    @JSONField(name = "order_no")
    private String order_no;

    @Schema(description = "平台交易流水号")
    @JSONField(name = "trade_no")
    private String trade_no;

    @Schema(description = "交易状态 00确认成功")
    @JSONField(name = "trade_status")
    private String trade_status;

}
