package com.jsrxjt.adapter.order.controller;

import cn.hutool.json.JSONUtil;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.locallife.dto.request.LocalLifeCreateOrderDTO;
import com.jsrxjt.mobile.api.locallife.dto.request.LocalLifePayQueryRequest;
import com.jsrxjt.mobile.api.locallife.dto.request.LocalLifeRefundOrderDTO;
import com.jsrxjt.mobile.api.locallife.dto.request.LocalLifeRefundQueryRequest;
import com.jsrxjt.mobile.api.locallife.dto.response.LocalLifeCreateOrderResponse;
import com.jsrxjt.mobile.api.locallife.dto.response.LocalLifeOrderPayQueryResponse;
import com.jsrxjt.mobile.api.locallife.dto.response.LocalLifeOrderRefundQueryResponse;
import com.jsrxjt.mobile.api.locallife.dto.response.LocalLifeOrderRefundResponse;
import com.jsrxjt.mobile.biz.distribution.service.LocalLifeCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/localLife-order")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "本地生活订单接口", description = "本地生活订单相关接口")
public class LocalLifeOrderController {

    private final LocalLifeCaseService localLifeCaseService;

    @PostMapping("/prepay")
    @Operation(summary = "本地生活下单")
    public ApiResponse<LocalLifeCreateOrderResponse> prepay(LocalLifeCreateOrderDTO requestDTO) {
        log.info("localLifeOrderCreateNotify(request={})", JSONUtil.toJsonStr(requestDTO));
        return localLifeCaseService.prePay(requestDTO);
    }

    @PostMapping("/refund")
    @Operation(summary = "本地生活订单退款")
    public ApiResponse<LocalLifeOrderRefundResponse> refund(LocalLifeRefundOrderDTO requestDTO) {
        log.info("localLifeOrderRefundNotify(request={})", JSONUtil.toJsonStr(requestDTO));
        return localLifeCaseService.refund(requestDTO);
    }

    /**
     * 订单支付状态查询
     *
     * @param request
     * @return
     */
    @PostMapping("/payQuery")
    public ApiResponse<LocalLifeOrderPayQueryResponse> payQuery(LocalLifePayQueryRequest request) {
        return localLifeCaseService.payQuery(request);
    }

    @PostMapping("/refundQuery")
    public ApiResponse<LocalLifeOrderRefundQueryResponse> payQuery(LocalLifeRefundQueryRequest request) {
        return localLifeCaseService.refundQuery(request);
    }


}
