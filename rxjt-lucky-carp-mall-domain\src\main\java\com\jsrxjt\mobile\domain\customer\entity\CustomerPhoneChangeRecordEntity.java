package com.jsrxjt.mobile.domain.customer.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class CustomerPhoneChangeRecordEntity {

    @Schema(description = "变更记录id")
    private Long id;

    @Schema(description = "用户id")
    private Long customerId;

    @Schema(description = "变更方式 1用户主动修改 2后台修改 3用户主动注销")
    private Integer changeMethod;

    @Schema(description = "原手机号")
    private String originalPhone;

    @Schema(description = "新手机号")
    private String newPhone;

    @Schema(description = "变更时间")
    private Date createTime;

    @Schema(description = "变更操作人id")
    private Long createId;
}
