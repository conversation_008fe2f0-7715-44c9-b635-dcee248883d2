package com.jsrxjt.mobile.api.contentcenter.types;

import lombok.Getter;

/**
 * @Description: 内容中心的模块枚举
 * @Author: ywt
 * @Date: 2025-05-09 10:29
 * @Version: 1.0
 */
@Getter
public enum ContentcenterTypeEnum {
    CONTENT_ADV(1,"广告"),
    CONTENT_INFO(2,"资讯"),
    CONTENT_START_PAGE(3,"启动页"),
    CONTENT_SEARCH_KEY(4,"搜索关键词");
//    CONTENT_SEARCH_FIND(5,"搜索发现");


    private final Integer code;
    private final String value;

    ContentcenterTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }
}
