package com.jsrxjt.mobile.job.jobhandler;

import com.jsrxjt.mobile.biz.app.AlipayAppService;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Slf4j
@RequiredArgsConstructor
public class AlipayVoucherJobHandler {

    private final OrderRepository orderRepository;

    private final AlipayAppService alipayAppService;

    /**
     * 支付宝定时任务 每五分钟执行一次  查询距离下单时间20min的充值中订单 实际状态  失败退款
     */
    @XxlJob("AlipayVoucherJobHandler")
    public void processDeliveringAlipayVoucherOrder() {
        log.info("开始处理充值中支付宝订单");
        try {
            List<OrderInfoEntity> list = orderRepository.findDeliveringAlipayVoucherOrder();
            alipayAppService.processDeliveringAlipayVoucherOrder(list);
            log.info("充值中支付宝订单处理结束");
        } catch (Exception e) {
            String errorMsg = "充值中支付宝订单处理失败";
            log.error(errorMsg, e);
            XxlJobHelper.handleFail(errorMsg);
            throw e;
        }
    }


}
