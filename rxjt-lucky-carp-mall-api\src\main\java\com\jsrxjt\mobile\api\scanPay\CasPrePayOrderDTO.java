package com.jsrxjt.mobile.api.scanPay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 预支付订单信息
 * @Author: ZY
 */
@Data
@Schema(description = "收银台预支付订单信息")
public class CasPrePayOrderDTO {

    // 支付码
    private String code;
    // 订单id
    private String orderId;
    // 订单号
    private String orderNo;
    //付款金额
    private String amount;
    // todo 预支付明细
}
