package com.jsrxjt.mobile.domain.riskcontrol.repository;

import com.jsrxjt.mobile.domain.riskcontrol.entity.SpuRiskFilterEntity;

import java.util.List;

/**
 * @Description: 产品风控策略sku领域接口
 * @Author: ywt
 * @Date: 2025-09-18 10:55
 * @Version: 1.0
 */
public interface ProductRiskControlSkuRepository {
    //获取风控禁用的spu
    List<SpuRiskFilterEntity> getRiskDisableProducts(String accountName, Long companyId);

    SpuRiskFilterEntity getRiskDisableProduct(String accountName, Long companyId, Long productSpuId, Integer productType);
}
