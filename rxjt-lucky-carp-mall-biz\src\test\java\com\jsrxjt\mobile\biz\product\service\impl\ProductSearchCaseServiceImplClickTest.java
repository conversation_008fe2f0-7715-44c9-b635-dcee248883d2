package com.jsrxjt.mobile.biz.product.service.impl;

import com.jsrxjt.mobile.domain.product.repository.SearchKeywordRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

/**
 * 搜索关键词点击量增加功能测试
 * 
 * <AUTHOR>
 * @since 2025/11/14
 */
@ExtendWith(MockitoExtension.class)
class ProductSearchCaseServiceImplClickTest {

    @Mock
    private SearchKeywordRepository searchKeywordRepository;

    @InjectMocks
    private ProductSearchCaseServiceImpl productSearchCaseService;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    void testIncrementSearchKeywordClickNum_Success() {
        // Given
        Long keywordId = 1L;

        // When
        productSearchCaseService.incrementSearchKeywordClickNum(keywordId);

        // Then
        verify(searchKeywordRepository, times(1)).incrementClickNum(keywordId);
    }

    @Test
    void testIncrementSearchKeywordClickNum_NullId() {
        // Given
        Long keywordId = null;

        // When
        productSearchCaseService.incrementSearchKeywordClickNum(keywordId);

        // Then
        verify(searchKeywordRepository, never()).incrementClickNum(any());
    }

    @Test
    void testIncrementSearchKeywordClickNum_RepositoryException() {
        // Given
        Long keywordId = 1L;
        doThrow(new RuntimeException("数据库连接失败")).when(searchKeywordRepository).incrementClickNum(keywordId);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            productSearchCaseService.incrementSearchKeywordClickNum(keywordId);
        });

        verify(searchKeywordRepository, times(1)).incrementClickNum(keywordId);
    }
}
