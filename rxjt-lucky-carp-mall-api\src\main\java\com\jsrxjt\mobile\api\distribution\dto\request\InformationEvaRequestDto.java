package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 点赞评价请求参数
 * @Author: ywt
 * @Date: 2025-06-11 11:16
 * @Version: 1.0
 */
@Data
public class InformationEvaRequestDto {
    /*@Schema(description = "用户id")
    private Long userId;
*/
    @Schema(description = "资讯评价id")
    @NotNull(message = "资讯评价id为空错误")
    private Long evaId;
}
