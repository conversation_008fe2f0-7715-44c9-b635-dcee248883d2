package com.jsrxjt.mobile.biz.ticket;

import com.jsrxjt.mobile.api.coupon.dto.response.CouponNotifyResponseDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponOrderCreatedNotifyDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketGiveOutNotifyRequest;
import com.jsrxjt.mobile.api.ticket.response.TicketNotifyResponseDTO;

/**
 * 下单赠券的券订单用例服务
 * 
 * <AUTHOR>
 * @since 2025/09/23
 */
public interface GiftTicketOrderCaseService {

    /**
     * 处理下单赠券的券订单回调
     * 
     * @param notifyDTO 下单赠券的券订单创建通知
     * @return 处理结果
     */
    CouponNotifyResponseDTO processCouponOrderCreatedNotify(CouponOrderCreatedNotifyDTO notifyDTO);

    /**
     * 处理卡券发放通知
     * 
     * @param request 卡券发放通知请求
     * @return 处理结果
     */
    TicketNotifyResponseDTO processTicketGiveOutNotify(TicketGiveOutNotifyRequest request);

}
