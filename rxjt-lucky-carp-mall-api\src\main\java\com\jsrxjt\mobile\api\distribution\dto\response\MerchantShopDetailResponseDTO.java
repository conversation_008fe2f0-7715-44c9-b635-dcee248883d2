package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 商户大全的门店响应
 * @Author: ywt
 * @Date: 2025-05-28 09:48
 * @Version: 1.0
 */
@Data
@Schema(description = "商户大全门店详情响应")
public class MerchantShopDetailResponseDTO {
    @Schema(description = "门店id")
    private String id;

    @Schema(description = "base信息")
    MerchantShopBaseResponseDTO base;

    @Schema(description = "config信息")
    MerchantShopConfigResponseDTO config;
}
