package com.jsrxjt.mobile.api.distribution.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-10-16
 */
@Data
@NoArgsConstructor
public class DistributionOrderRefundInfoResponseDTO {
    /**
     * 分销业务中心订单号
     */
    private String orderNo;

    /**
     * 分销业务中心交易号
     */
    private String tradeNo;

    /**
     * 福鲤圈侧退款单号
     */
    private String thirdRefundOrderNo;

    /**
     * 退款状态 11退款中 10退款成功 12退款失败
     */
    private String refundStatus;

    /**
     * 退款成功时间，10位UTC时间戳，非退款成功状态统一传0
     */
    private String refundTime;

    /**
     * 退款金额，若订单退款未成功，传0.00即可
     */
    private String refundAmount;
}
