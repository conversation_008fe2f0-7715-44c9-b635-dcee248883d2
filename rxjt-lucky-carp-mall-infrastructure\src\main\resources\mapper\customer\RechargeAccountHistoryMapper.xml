<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsrxjt.mobile.infra.customer.persistent.mapper.RechargeAccountHistoryMapper">
    
    <resultMap id="RechargeAccountHistoryResult" type="com.jsrxjt.mobile.infra.customer.persistent.po.RechargeAccountHistoryPO">
        <id column="id" property="id"/>
        <result column="customer_id" property="customerId"/>
        <result column="product_spu_id" property="productSpuId"/>
        <result column="product_type" property="productType"/>
        <result column="account_type" property="accountType"/>
        <result column="account" property="account"/>
        <result column="create_time" property="createTime"/>
        <result column="mod_time" property="modTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>
    
    <select id="selectByCustomerAndProduct" resultMap="RechargeAccountHistoryResult">
        SELECT * FROM recharge_account_history
        WHERE customer_id = #{customerId}
          AND product_spu_id = #{productSpuId}
          AND product_type = #{productType}
          AND account_type = #{accountType}
          AND del_flag = 0
        ORDER BY mod_time DESC
        LIMIT 1
    </select>
    
    <select id="selectByCustomerId" resultMap="RechargeAccountHistoryResult">
        SELECT * FROM recharge_account_history
        WHERE customer_id = #{customerId}
          AND del_flag = 0
        ORDER BY create_time DESC
    </select>
    
    <select id="selectByCriteria" resultMap="RechargeAccountHistoryResult">
        SELECT * FROM recharge_account_history
        <where>
            del_flag = 0
            <if test="query.customerId != null">
                AND customer_id = #{query.customerId}
            </if>
            <if test="query.productSpuId != null">
                AND product_spu_id = #{query.productSpuId}
            </if>
            <if test="query.productType != null">
                AND product_type = #{query.productType}
            </if>
            <if test="query.accountType != null">
                AND account_type = #{query.accountType}
            </if>
            <if test="query.account != null and query.account != ''">
                AND account = #{query.account}
            </if>
        </where>
        ORDER BY mod_time DESC
    </select>
</mapper>
