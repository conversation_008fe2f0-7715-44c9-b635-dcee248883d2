package com.jsrxjt.mobile.api.app.types;

import lombok.Getter;

@Getter
public enum AppCouponExplainTypeEnum {

    USE_SCOPE(1,"适用范围"),

    RECHARGE_USAGE_INSTRUCTIONS(2,"充值使用说明"),

    TIPS(3,"温馨提示（下单确认提示）"),

    EXCHANGE_NOTICE(4,"兑换须知"),

    MARQUEE(5,"跑马灯内容");

    /**
     * 类型值
     */
    private final Integer type;

    /**
     * 类型描述
     */
    private final String desc;

    AppCouponExplainTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
