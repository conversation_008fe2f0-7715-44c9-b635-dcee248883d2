package com.jsrxjt.mobile.infra.order.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.query.AfterSaleListQuery;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.infra.order.persistent.mapper.AfterSaleMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.AfterSalePO;
import com.jsrxjt.mobile.infra.order.persistent.po.AfterSaleWithItemPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后仓储实现类
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AfterSaleRepositoryImpl implements AfterSaleRepository {

    private final AfterSaleMapper afterSaleMapper;
    private final BusinessIdGenerator businessIdGenerator;

    @Override
    @Transactional
    public void save(AfterSaleEntity afterSale) {
        AfterSalePO afterSalePO = new AfterSalePO();
        BeanUtils.copyProperties(afterSale, afterSalePO);

        // 如果ID为空，则生成ID
        if (afterSalePO.getId() == null) {
            afterSalePO.setId(businessIdGenerator.generateId());
            afterSale.setId(afterSalePO.getId()); // 回填ID
        }

        int insert = afterSaleMapper.insert(afterSalePO);
        if (insert <= 0) {
            throw new BizException("售后单保存失败");
        }

        log.info("售后单保存成功，售后单号：{}", afterSale.getAfterSaleNo());
    }

    @Override
    public void update(AfterSaleEntity afterSale) {
        AfterSalePO afterSalePO = new AfterSalePO();
        BeanUtils.copyProperties(afterSale, afterSalePO);

        LambdaQueryWrapper<AfterSalePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSalePO::getId, afterSalePO.getId()).eq(AfterSalePO::getCustomerId,
                afterSale.getCustomerId());

        int update = afterSaleMapper.update(afterSalePO, queryWrapper);
        if (update <= 0) {
            throw new BizException("售后单更新失败");
        }

        log.info("售后单更新成功，售后单号：{}", afterSale.getAfterSaleNo());
    }

    @Override
    public List<AfterSaleEntity> findValidAfterSalesByOrderNo(String orderNo) {
        LambdaQueryWrapper<AfterSalePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSalePO::getOrderNo, orderNo)
                .and(wrapper -> wrapper
                        .eq(AfterSalePO::getAfterSaleStatus, AfterSaleStatusEnum.PENDING_AUDIT.getCode()) // 待审核
                        .or()
                        .eq(AfterSalePO::getAfterSaleStatus, AfterSaleStatusEnum.AUDIT_PASSED.getCode()) // 审核通过
                        .or()
                        .eq(AfterSalePO::getRefundStatus, RefundStatusEnum.REFUNDING.getCode()) // 退款中
                        .or()
                        .eq(AfterSalePO::getRefundStatus, RefundStatusEnum.REFUND_SUCCESS.getCode()) // 退款成功
                );

        List<AfterSalePO> afterSalePOs = afterSaleMapper.selectList(queryWrapper);

        if (afterSalePOs == null || afterSalePOs.isEmpty()) {
            return Collections.emptyList();
        }

        return afterSalePOs.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    /**
     * PO转换为Entity
     */
    private AfterSaleEntity convertToEntity(AfterSalePO afterSalePO) {
        AfterSaleEntity entity = new AfterSaleEntity();
        BeanUtils.copyProperties(afterSalePO, entity);
        return entity;
    }

    @Override
    public AfterSaleEntity findByAfterSaleNoAndCustomerId(String afterSaleNo, Long customerId) {
        LambdaQueryWrapper<AfterSalePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSalePO::getAfterSaleNo, afterSaleNo)
                .eq(AfterSalePO::getCustomerId, customerId);

        AfterSalePO afterSalePO = afterSaleMapper.selectOne(queryWrapper);

        if (afterSalePO == null) {
            return null;
        }

        return convertToEntity(afterSalePO);
    }

    @Override
    public AfterSaleEntity findByOrderNoAndExternalRefundNo(String orderNo, String externalRefundNo) {
        LambdaQueryWrapper<AfterSalePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSalePO::getOrderNo, orderNo)
                .eq(AfterSalePO::getExternalRefundNo, externalRefundNo);

        AfterSalePO afterSalePO = afterSaleMapper.selectOne(queryWrapper);

        if (afterSalePO == null) {
            return null;
        }

        return convertToEntity(afterSalePO);
    }

    @Override
    public AfterSaleEntity findByExternalRefundNo(String externalRefundNo) {
        LambdaQueryWrapper<AfterSalePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSalePO::getExternalRefundNo, externalRefundNo);

        AfterSalePO afterSalePO = afterSaleMapper.selectOne(queryWrapper);
        if (afterSalePO == null) {
            return null;
        }
        return convertToEntity(afterSalePO);
    }

    @Override
    public PageDTO<AfterSaleEntity> findAfterSaleListByPage(AfterSaleListQuery query) {
        // 创建分页对象
        Page<AfterSaleWithItemPO> page = new Page<>(query.getPageNum(), query.getPageSize());

        // 构建查询条件
        QueryWrapper<AfterSalePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("a.customer_id", query.getCustomerId());

        if (query.getAfterSaleStatus() != null) {
            queryWrapper.eq("a.after_sale_status", query.getAfterSaleStatus());
        }

        if (query.getAfterSaleType() != null) {
            queryWrapper.eq("a.after_sale_type", query.getAfterSaleType());
        }

        // 售后申请时间范围查询
        if (query.getCreateTimeStart() != null) {
            queryWrapper.ge("a.create_time", query.getCreateTimeStart());
        }
        if (query.getCreateTimeEnd() != null) {
            queryWrapper.le("a.create_time", query.getCreateTimeEnd());
        }

        // 订单号查询
        if (StringUtils.hasText(query.getOrderNo())) {
            queryWrapper.eq("oi.order_no", query.getOrderNo());
        }

        // 售后单号查询
        if (StringUtils.hasText(query.getAfterSaleNo())) {
            queryWrapper.eq("a.after_sale_no", query.getAfterSaleNo());
        }

        // 商品名称模糊查询
        if (StringUtils.hasText(query.getProductName())) {
            queryWrapper.like("oi.product_name", query.getProductName());
        }

        // 商品一级分类ID查询（需要通过订单项关联）
        if (query.getFirstCategoryId() != null) {
            queryWrapper.eq("oi.first_category_id", query.getFirstCategoryId());
        }

        if (query.getIsShow() != null ) {
            queryWrapper.eq("a.is_show", query.getIsShow());
        }

        queryWrapper.orderByDesc("a.id");

        // 执行分页查询
        IPage<AfterSaleWithItemPO> resultPage = afterSaleMapper.selectAfterSaleListWithItemsPage(page, queryWrapper);

        // 转换为实体列表
        List<AfterSaleEntity> afterSaleList = resultPage.getRecords().stream()
                .map(this::convertToAfterSaleEntity)
                .collect(Collectors.toList());

        // 构建分页结果
        return PageDTO.<AfterSaleEntity>builder()
                .records(afterSaleList)
                .total(resultPage.getTotal())
                .current(resultPage.getCurrent())
                .size(resultPage.getSize())
                .pages(resultPage.getPages())
                .build();
    }

    @Override
    public int countAfterSaleByStatus(Long customerId) {
        LambdaQueryWrapper<AfterSalePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSalePO::getCustomerId, customerId)
                .and(wrapper -> wrapper
                        .eq(AfterSalePO::getAfterSaleStatus, AfterSaleStatusEnum.PENDING_AUDIT.getCode())
                        .or()
                        .eq(AfterSalePO::getAfterSaleStatus, AfterSaleStatusEnum.AUDIT_PASSED.getCode()));
        return Math.toIntExact(afterSaleMapper.selectCount(queryWrapper));
    }

    @Override
    public List<AfterSaleEntity> findRefundedAfterSalesByOrderNo(String orderNo) {
        LambdaQueryWrapper<AfterSalePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSalePO::getOrderNo, orderNo)
                .eq(AfterSalePO::getRefundStatus, RefundStatusEnum.REFUND_SUCCESS.getCode());

        List<AfterSalePO> afterSalePOs = afterSaleMapper.selectList(queryWrapper);

        if (afterSalePOs == null || afterSalePOs.isEmpty()) {
            return Collections.emptyList();
        }

        return afterSalePOs.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    /**
     * 将AfterSaleWithItemPO转换为AfterSaleEntity
     */
    private AfterSaleEntity convertToAfterSaleEntity(AfterSaleWithItemPO po) {
        AfterSaleEntity entity = new AfterSaleEntity();
        entity.setId(po.getAfterSaleId());
        entity.setAfterSaleNo(po.getAfterSaleNo());
        entity.setOrderNo(po.getOrderNo());
        entity.setAfterSaleType(po.getAfterSaleType());
        entity.setAfterSaleStatus(po.getAfterSaleStatus());
        entity.setRefundStatus(po.getRefundStatus());
        entity.setRefundAmount(po.getRefundAmount());
        entity.setAfterSaleQuantity(po.getAfterSaleQuantity());
        entity.setCreateTime(po.getCreateTime());

        // 设置商品信息
        OrderItemEntity item = new OrderItemEntity();
        item.setProductName(po.getProductName());
        item.setBrandName(po.getBrandName());
        item.setProductLogo(po.getProductLogo());
        item.setSellPrice(po.getSellPrice());
        item.setFaceAmount(po.getFaceAmount());
        item.setQuantity(po.getQuantity());
        item.setFlatProductType(po.getFlatProductType());
        entity.setOrderItem(item);
        return entity;
    }
}
