package com.jsrxjt.mobile.domain.payment.gateway.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 会员卡充值接口响应参数
 * <AUTHOR>
 * @Date 2025/10/11
 */
@Data
public class CardRechargeResponse {

    /**
     * 系统业务订单号
     */
    @JSONField(name = "recharge_order_no")
    private String rechargeOrderNo;

    /**
     * 外部业务订单号
     */
    @JSONField(name = "recharge_out_order_no")
    private String rechargeOutOrderNo;

    /**
     * 微信支付配置
     */
    @JSONField(name = "wx_pay_config")
    private String wxPayConfig;

    /**
     * 充值状态
     */
    @JSONField(name = "recharge_status")
    private String rechargeStatus;

    /**
     * 充值金额
     */
    @JSONField(name = "recharge_amount")
    private String rechargeAmount;            ;

}
