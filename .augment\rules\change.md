---
type: "manual"
---

# 项目变更记录


## 2025-07-16 - 售后模块完善

### 新增功能
1. **售后领域服务优化**
   - 修改 `AfterSaleServiceImpl` 中的异常类型从 `RuntimeException` 改为 `BizException`
   - 完善售后申请的业务逻辑和异常处理

2. **售后日志领域服务**
   - 新增 `AfterSaleLogService` 接口和 `AfterSaleLogServiceImpl` 实现
   - 提供统一的售后操作日志创建功能
   - 支持操作类型、操作内容、操作人等信息记录

3. **售后仓储层完善**
   - 新增 `AfterSaleRepository` 接口，提供售后单的CRUD操作
   - 新增 `AfterSaleLogRepository` 接口，提供售后日志的CRUD操作
   - 实现 `AfterSaleRepositoryImpl` 和 `AfterSaleLogRepositoryImpl`
   - 新增 `AfterSaleMapper` 和 `AfterSaleLogMapper` 数据访问层

4. **充值失败自动售后**
   - 在 `CouponOrderCaseServiceImpl` 中实现充值失败时自动申请售后单功能
   - 自动创建全额退款售后申请
   - 记录系统自动申请的售后操作日志
   - 保证事务一致性，异常不影响主流程

### 技术改进
1. **分层架构优化**
   - Domain层专注业务逻辑，不直接操作数据持久化
   - BIZ层负责业务编排和数据持久化
   - 遵循DDD分层架构原则

2. **异常处理统一**
   - 统一使用 `BizException` 处理业务异常
   - 完善异常信息和错误提示

3. **事务管理**
   - 在仓储实现层添加 `@Transactional` 注解
   - 充值失败流程使用手动事务管理确保数据一致性

### 文件变更清单
- 修改：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/service/impl/AfterSaleServiceImpl.java`
- 新增：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/service/AfterSaleLogService.java`
- 新增：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/service/impl/AfterSaleLogServiceImpl.java`
- 新增：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/repository/AfterSaleRepository.java`
- 新增：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/repository/AfterSaleLogRepository.java`
- 新增：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/persistent/mapper/AfterSaleMapper.java`
- 新增：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/persistent/mapper/AfterSaleLogMapper.java`
- 新增：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/repository/AfterSaleRepositoryImpl.java`
- 新增：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/repository/AfterSaleLogRepositoryImpl.java`
- 修改：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/order/coupon/impl/CouponOrderCaseServiceImpl.java`

### 业务影响
1. **用户体验提升**：充值失败时自动创建售后申请，无需用户手动操作
2. **运营效率提升**：完整的售后操作日志，便于问题追踪和处理
3. **系统稳定性**：统一的异常处理和事务管理，提高系统可靠性

### 后续计划
1. 完善售后审核流程的BIZ层实现
2. 添加售后状态变更的消息通知
3. 完善售后相关的API接口层实现

## 2025-07-16 - 售后仓储层和充值失败自动售后实现

### 新增功能
1. **售后仓储层完整实现**
   - 实现 `AfterSaleRepository` 和 `AfterSaleLogRepository` 接口
   - 新增 `AfterSaleMapper` 和 `AfterSaleLogMapper` 数据访问层
   - 实现 `AfterSaleRepositoryImpl` 和 `AfterSaleLogRepositoryImpl` 仓储实现类
   - 支持售后单和售后日志的完整CRUD操作

2. **充值失败自动售后完整实现**
   - 在 `CouponOrderCaseServiceImpl` 中完善 `createAfterSaleForRechargeFailed` 方法
   - 实现充值失败时自动创建售后单的完整逻辑
   - 自动生成售后操作日志记录
   - 异常处理不影响主流程，确保系统稳定性

### 技术实现细节
1. **仓储模式实现**
   - 遵循DDD仓储模式，领域层定义接口，基础设施层实现
   - 使用 `BeanUtils.copyProperties` 进行实体转换
   - 集成 `BusinessIdGenerator` 自动生成业务ID
   - 添加完善的日志记录和异常处理

2. **事务管理优化**
   - 仓储实现层使用 `@Transactional` 注解
   - 充值失败流程中售后创建异常不影响主事务
   - 保证数据一致性和系统稳定性

3. **售后自动化流程**
   - 充值失败时自动创建全额退款售后申请
   - 系统自动填充售后原因和描述信息
   - 创建对应的售后操作日志记录
   - 操作人标识为"系统"，操作人ID为0

### 文件变更清单
- 新增：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/repository/AfterSaleRepository.java`
- 新增：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/repository/AfterSaleLogRepository.java`
- 新增：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/persistent/mapper/AfterSaleMapper.java`
- 新增：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/persistent/mapper/AfterSaleLogMapper.java`
- 新增：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/repository/AfterSaleRepositoryImpl.java`
- 新增：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/repository/AfterSaleLogRepositoryImpl.java`
- 修改：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/order/coupon/impl/CouponOrderCaseServiceImpl.java`

### 业务价值
1. **自动化程度提升**：充值失败场景下的售后处理完全自动化
2. **数据完整性保障**：完整的售后数据存储和日志记录
3. **系统健壮性增强**：异常处理机制确保主流程不受影响
4. **运营效率提升**：减少人工干预，提高问题处理效率

---

## 2025-06-25 - 订单仓储查询优化

### 变更概述

优化 `OrderRepositoryImpl.findByOrderNo` 方法，使其能够同时查询订单主体信息和订单项信息，为支付回调等业务场景提供完整的订单数据。

### 修改文件

**基础设施层：OrderRepositoryImpl 增强**

- 文件路径：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/repository/OrderRepositoryImpl.java`
- 新增导入：`OrderItemEntity`
- 修改方法：`findByOrderNo()` - 增加订单项查询逻辑
- 重构方法：`convertToEntity()` - 支持订单项数据转换

### 技术实现

#### 查询优化逻辑

1. **订单主体查询**：根据 `orderNo` 查询订单主表信息
2. **订单项查询**：根据 `orderNo` 查询所有相关订单项
3. **数据转换**：将 PO 对象转换为 Entity 对象，包含完整的订单项列表
4. **数据组装**：将订单项列表设置到订单主体的 `orderItems` 字段中

#### 核心实现代码

```java
private OrderInfoEntity convertToEntity(OrderPO orderPO, String orderNo) {
    OrderInfoEntity entity = new OrderInfoEntity();
    BeanUtils.copyProperties(orderPO, entity);

    // 查询订单项信息
    LambdaQueryWrapper<OrderItemPO> itemQueryWrapper = new LambdaQueryWrapper<>();
    itemQueryWrapper.eq(OrderItemPO::getOrderNo, orderNo);
    List<OrderItemPO> orderItemPOs = orderItemMapper.selectList(itemQueryWrapper);

    if (orderItemPOs != null && !orderItemPOs.isEmpty()) {
        List<OrderItemEntity> orderItems = orderItemPOs.stream().map(itemPO -> {
            OrderItemEntity itemEntity = new OrderItemEntity();
            BeanUtils.copyProperties(itemPO, itemEntity);
            return itemEntity;
        }).toList();
        entity.setOrderItems(orderItems);
    }

    return entity;
}
```

### 业务价值

1. **数据完整性**：一次查询获取订单的完整信息，包括订单项详情
2. **支付回调支持**：为支付回调处理提供准确的购买数量等订单项信息
3. **性能优化**：避免多次数据库查询，提高查询效率
4. **向前兼容**：保持接口不变，只增强数据内容

### 遵循的编程原则

1. **SOLID 原则**：职责分离，查询逻辑与转换逻辑分开
2. **DDD 架构**：基础设施层负责数据持久化，领域层使用实体对象
3. **数据一致性**：确保订单主体和订单项数据的关联正确
4. **异常安全**：处理订单项为空的情况，避免空指针异常

---

## 2025-06-25 - 支付回调月度购买数量统计功能实现

### 变更概述

为支付回调处理流程添加当月商品购买数量统计功能，在用户支付成功后自动更新当月该商品的购买数量，完善了月度限购统计体系。现在支持从订单项中获取准确的购买数量信息。

### 修改文件

**业务层：PaymentCallbackCaseServiceImpl 增强**

- 文件路径：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/payment/service/impl/PaymentCallbackCaseServiceImpl.java`
- 新增方法：`addProductMonthlyQuantity()` - 增加当月该商品的购买数量
- 新增方法：`calculateQuantityFromOrder()` - 从订单信息中计算购买数量
- 修复 `addProductMonthlyAmount()` 方法中的参数问题

### 技术实现

#### 业务流程优化

支付回调处理流程中第 7 步的完整实现：

1. **金额统计**（第 6 步）：使用 `addProductMonthlyAmount()` 更新当月购买金额
2. **数量统计**（第 7 步）：使用 `addProductMonthlyQuantity()` 更新当月购买数量
3. **数据一致性**：确保金额和数量统计使用相同的 `ProductItemId` 构建逻辑

#### 核心实现逻辑

```java
// 第7步：增加当月该商品的购买数量
private void addProductMonthlyQuantity(OrderInfoEntity orderInfo) {
    ProductItemId productItemId = ProductItemId.of(
            orderInfo.getProductSpuId(),
            orderInfo.getProductSkuId(),
            orderInfo.getFlatProductType());

    // 计算购买数量：从订单项中获取实际购买数量
    Integer quantity = calculateQuantityFromOrder(orderInfo);

    calculateAmountService.updateMonthlyPurchaseQuantity(
            orderInfo.getCustomerId(),
            productItemId,
            quantity,
            "ADD");
}

// 从订单信息中计算购买数量
private Integer calculateQuantityFromOrder(OrderInfoEntity orderInfo) {
    // 从订单项中获取购买数量
    if (orderInfo.getOrderItems() != null && !orderInfo.getOrderItems().isEmpty()) {
        // 累计所有订单项的数量
        return orderInfo.getOrderItems().stream()
                .mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
                .sum();
    }

    // 如果没有订单项信息，使用默认值1（符合当前系统的单商品订单设计）
    log.warn("订单项信息为空，使用默认购买数量1，订单号：{}", orderInfo.getOrderNo());
    return 1;
}
```

#### 数量获取策略

1. **优先策略**：从 `OrderInfoEntity.getOrderItems()` 中获取准确的购买数量
2. **累加计算**：如果存在多个订单项，则累加所有订单项的数量
3. **异常处理**：如果订单项为空，使用默认值 1 并记录警告日志
4. **空值安全**：对订单项的数量字段进行空值判断，确保数据安全

### 业务价值

1. **准确性提升**：基于实际订单项数据统计购买数量，避免计算误差
2. **数据完整性**：与订单仓储优化配合，确保能获取到完整的订单项信息
3. **限购支持**：为月度限购功能提供准确的数量统计基础
4. **向前兼容**：支持单商品和多商品订单场景，保持系统灵活性

### 遵循的编程原则

1. **SOLID 原则**：每个方法职责单一，数量计算与更新逻辑分离
2. **DDD 架构**：业务编排在 biz 层，核心逻辑委托给 domain 层
3. **数据一致性**：金额和数量统计使用相同的 `ProductItemId` 构建逻辑
4. **异常安全**：完善的空值判断和默认值处理，避免系统异常

### 与其他功能的协同

1. **订单仓储优化**：依赖于 `OrderRepositoryImpl.findByOrderNo` 查询订单项信息
2. **限购校验**：为下单和结算流程的限购校验提供准确的数量统计
3. **域服务集成**：使用 `CalculateAmountService.updateMonthlyPurchaseQuantity` 进行数据更新

---

## 2025-06-25 - 结算服务套餐子产品信息支持

### 变更概述

为 `CalculatorCaseService.calculateAmount` 方法的返回值添加套餐子产品信息支持，当产品类型为套餐时，返回套餐的子产品基本信息列表，包括 `amountName`、`packageCouponNum`、`amount` 字段。

### 修改文件

1. **API 层：SettleResponseDTO 增强**

   - 文件路径：`rxjt-lucky-carp-mall-api/src/main/java/com/jsrxjt/mobile/api/order/dto/response/SettleResponseDTO.java`
   - 新增字段：`List<PackageSubProductInfo> subProductList`
   - 新增内部类：`PackageSubProductInfo`（包含 `amountName`、`packageCouponNum`、`amount` 三个字段）
   - 为所有字段添加 `@Schema` 注解，完善 API 文档说明

2. **业务层：CalculatorCaseServiceImpl 增强**
   - 文件路径：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/order/impl/CalculatorCaseServiceImpl.java`
   - 新增导入：`JSON`、`PackageSubSkuEntity`、`StringUtils`、`List`、`Collectors`
   - 修改 `buildSettleResponse` 方法：为套餐产品添加子产品信息设置
   - 新增 `buildPackageSubProductList` 方法：从 ProductItem 的 extraInfo 中解析套餐子 SKU 信息

### 技术实现

#### 套餐子产品信息解析流程

1. **数据来源**：从 `ProductItem.getExtraInfo()` 中获取 JSON 格式的套餐子 SKU 信息
2. **解析过程**：使用 `JSON.parseArray()` 将 JSON 字符串解析为 `PackageSubSkuEntity` 列表
3. **数据转换**：将 `PackageSubSkuEntity` 转换为 `SettleResponseDTO.PackageSubProductInfo` 对象
4. **字段映射**：
   - `amountName`：面值名称
   - `packageCouponNum`：该套餐中的子 SKU 数量
   - `amount`：面值

#### 核心代码片段

```java
// SettleResponseDTO 新增字段
/**
 * 套餐子产品信息列表（仅套餐产品有值）
 */
private List<PackageSubProductInfo> subProductList;

@Data
public static class PackageSubProductInfo {
    private String amountName;        // 面值名称
    private Integer packageCouponNum; // 该套餐中的子sku数量
    private BigDecimal amount;        // 面值
}

// CalculatorCaseServiceImpl 核心方法
private List<SettleResponseDTO.PackageSubProductInfo> buildPackageSubProductList(ProductItem productItem) {
    String extraInfo = productItem.getExtraInfo();
    if (StringUtils.isBlank(extraInfo)) {
        return null;
    }

    List<PackageSubSkuEntity> subSkus = JSON.parseArray(extraInfo, PackageSubSkuEntity.class);
    return subSkus.stream()
            .map(subSku -> {
                SettleResponseDTO.PackageSubProductInfo subProductInfo = new SettleResponseDTO.PackageSubProductInfo();
                subProductInfo.setAmountName(subSku.getAmountName());
                subProductInfo.setPackageCouponNum(subSku.getPackageCouponNum());
                subProductInfo.setAmount(subSku.getAmount());
                return subProductInfo;
            })
            .collect(Collectors.toList());
}
```

### 业务逻辑

1. **判断产品类型**：只有当 `productType` 为 `ProductTypeEnum.PACKAGE.getType()` 时才设置子产品信息
2. **异常处理**：包含完善的日志记录，解析失败时返回 null 而不抛出异常
3. **数据校验**：检查 extraInfo 是否为空，子 SKU 列表是否为空
4. **性能优化**：使用 Stream API 进行数据转换，提高代码可读性

### 影响范围

- **API 层**：SettleResponseDTO 向前兼容，新增字段对原有接口无影响
- **业务层**：仅对套餐产品增加额外处理，对其他产品类型无影响
- **调用方**：客户端可根据产品类型判断是否使用 subProductList 字段

### 遵循的编程原则

1. **SOLID 原则**：每个方法职责单一，buildPackageSubProductList 专门处理套餐子产品信息
2. **DDD 架构**：在 biz 层进行业务编排，domain 层的 ProductItem 提供数据支持
3. **异常安全**：解析失败不影响主流程，只是子产品信息为空
4. **代码复用**：复用现有的 JSON 解析和 Stream 转换逻辑

---

# 产品搜索功能实现方案

## 需求概述

实现产品搜索功能，支持按关键词搜索产品，优先匹配 productName 或 brandName，并支持分页查询。新增产品联想词功能，支持在输入关键词后自动提示相关产品，并高亮匹配关键词。

## 技术选型

- **搜索引擎**：Easy-ES 2.1 版本
- **索引名称**：product_info
- **分词器**：IK 分词器（ik_max_word 用于索引，ik_smart 用于搜索）
- **高亮标签**：`<em>...</em>`

## 实现架构

采用 DDD 架构设计，通过以下层次结构实现：

1. **适配层**：`ProductSearchController`
2. **应用层**：`ProductSearchCaseService`（采用用例式服务）
3. **领域层**：`ProductBaseInfoRepository`
4. **基础设施层**：`ProductBaseInfoRepositoryImpl`、`ProductInfoEsMapper`

## 数据流向

### 产品搜索

1. Controller 接收请求参数`ProductSearchRequestDTO`（继承自`BaseParam`）
2. 调用应用层用例服务`ProductSearchCaseService`的`searchProducts`方法
3. 用例服务调用领域仓库`ProductBaseInfoRepository`的`pageProducts`方法
4. 基础设施层使用 Easy-ES 执行查询，将文档映射为领域实体
5. 直接返回领域分页对象`PageDTO<ProductSpuBaseInfo>`给前端，不做 DTO 转换

### 产品联想词

1. Controller 接收请求参数`ProductSuggestionRequestDTO`（继承自`BaseParam`）
2. 调用应用层用例服务`ProductSearchCaseService`的`getSuggestions`方法
3. 用例服务调用领域仓库`ProductBaseInfoRepository`的`getSuggestions`方法
4. 基础设施层使用 Easy-ES 执行查询，将结果转换为 `ProductSuggestionResponseDTO` 列表
5. 返回包含高亮关键词的联想词列表给前端

## 核心实现

### 1. ES 索引映射

```json
{
  "mappings": {
    "properties": {
      "id": { "type": "keyword" },
      "spuId": { "type": "keyword" },
      "productName": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": { "keyword": { "type": "keyword", "ignore_above": 256 } }
      },
      "subTitle": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "brandId": { "type": "keyword" },
      "brandName": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "productType": { "type": "integer" },
      "productCategoryId": { "type": "keyword" },
      "productCategoryName": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "productLogo": { "type": "keyword" },
      "spuIdWithType": { "type": "keyword" },
      "updateTimeStamp": { "type": "long" }
    }
  }
}
```

### 2. 搜索逻辑

- 使用 LambdaEsQueryWrapper 构建查询条件
- 对 productName 给予更高权重（2.0f），对 brandName 给予普通权重（1.0f）
- 搜索逻辑调整：从原先匹配产品名称+副标题改为匹配产品名称+品牌名称
- 使用 match 查询支持分词搜索
- 分页获取结果并转换为领域实体

### 3. 联想词逻辑

- 同样使用 LambdaEsQueryWrapper 构建查询条件
- 联想词匹配逻辑也从匹配产品名称+副标题改为匹配产品名称+品牌名称
- 限制返回字段和返回数量，提高查询性能
- 将匹配的关键词部分添加高亮标签 `<em>...</em>`
- 将结果转换为包含高亮信息的 `ProductSuggestionResponseDTO` 列表

### 4. 关键代码

```java
// Controller层 - 搜索接口
@PostMapping("/list")
@VerifySign(hasToken = true)
public BaseResponse<PageDTO<ProductSpuBaseInfo>> searchProducts(@RequestBody @Valid ProductSearchRequestDTO request) {
    log.info("接收到搜索产品请求，关键词：{}", request.getKeyword());
    return BaseResponse.succeed(productSearchService.searchProducts(request));
}

// Controller层 - 联想词接口
@PostMapping("/suggestions")
@VerifySign(hasToken = true)
public BaseResponse<List<ProductSuggestionResponseDTO>> getSuggestions(@RequestBody @Valid ProductSuggestionRequestDTO request) {
    log.info("接收到联想词请求，关键词：{}", request.getKeyword());
    return BaseResponse.succeed(productSearchService.getSuggestions(request));
}

// 应用层 - 联想词服务实现
@Override
public List<ProductSuggestionResponseDTO> getSuggestions(ProductSuggestionRequestDTO request) {
    String keyword = request.getKeyword();
    Integer maxCount = request.getMaxCount();

    log.info("获取产品联想词开始，关键词：{}, 最大返回数：{}", keyword, maxCount);

    // 调用领域层获取联想词并直接返回结果
    List<ProductSuggestionResponseDTO> result = productBaseInfoRepository.getSuggestions(keyword, maxCount);

    log.info("获取产品联想词完成，共获取到{}条记录", result.size());
    return result;
}

// 基础设施层 - 搜索实现
@Override
public PageDTO<ProductSpuBaseInfo> pageProducts(String keyword, Integer page, Integer size) {
    // ... 其他代码省略

    try {
        // 创建查询条件
        LambdaEsQueryWrapper<ProductInfoDoc> wrapper = new LambdaEsQueryWrapper<>();
        // 优先匹配产品名称，其次匹配品牌名称
        wrapper.match(ProductInfoDoc::getProductName, keyword, 2.0f)
                .or()
                .match(ProductInfoDoc::getBrandName, keyword, 1.0f);

        // 执行分页查询
        EsPageInfo<ProductInfoDoc> pageInfo = productInfoEsMapper.pageQuery(wrapper, page, size);

        // ... 其他代码省略
    }
}

// 基础设施层 - 联想词查询实现
@Override
public List<ProductSuggestionResponseDTO> getSuggestions(String keyword, Integer maxCount) {
    // ... 其他代码省略

    try {
        // 创建查询条件
        LambdaEsQueryWrapper<ProductInfoDoc> wrapper = new LambdaEsQueryWrapper<>();
        // 优先匹配产品名称，其次匹配品牌名称
        wrapper.match(ProductInfoDoc::getProductName, keyword, 2.0f)
                .or()
                .match(ProductInfoDoc::getBrandName, keyword, 1.0f)
                .limit(maxCount);

        // ... 其他代码省略
    }
}
```

## 2025-06-17 - 产品 SKU 当月购买数量统计功能实现

### 变更内容

#### 接口层面新增

1. **CalculateAmountService 接口扩展**：
   - 新增 `getMonthlyPurchaseQuantity()` 方法：获取用户当月产品已购买数量
   - 新增 `updateMonthlyPurchaseQuantity()` 方法：更新用户当月产品购买数量

#### 实现层面增强

2. **CalculateAmountServiceImpl 实现类扩展**：

   - 实现 `getMonthlyPurchaseQuantity()` 方法：

     - 使用 Redis Hash 结构存储数据
     - Key 格式：`order:purchased:quantity:yyyyMM:用户ID`
     - Field 格式：`productType_spuId_skuId`
     - 支持异常处理，默认返回 0

   - 实现 `updateMonthlyPurchaseQuantity()` 方法：

     - 支持 ADD/SUBTRACT 操作类型
     - 使用 Redis 的 hIncrBy 原子操作
     - 自动设置过期时间（当月最后一秒+2 秒）
     - 完善的日志记录和异常处理

   - 新增 `buildProductSkuField()` 私有方法：
     - 构建产品 SKU 字段标识
     - 格式：`productType_spuId_skuId`

#### 常量定义扩展

3. **RedisKeyConstants 常量类增强**：
   - 新增 `ORDER_PURCHASED_MONTHLY_QUANTITY` 常量
   - 定义为：`"order:purchased:quantity:%s:%d"`
   - 完善了注释说明 Hash 结构和字段格式

### 技术特点

- **数据存储策略**：类比现有的金额统计，使用 Redis Hash 结构存储数量数据
- **字段粒度**：SKU 级别的统计，比 SPU 级别更精确
- **过期策略**：复用现有的月度过期逻辑，确保数据及时清理
- **原子操作**：使用 Redis 的 hIncrBy 确保并发安全
- **异常处理**：完善的异常处理机制，不影响主业务流程

### 设计原则

- 遵循现有代码的设计模式和命名规范
- 保持与金额统计功能的一致性
- 确保代码的可维护性和扩展性
- 支持高并发场景下的数据准确性

---

## 优化考虑

1. **架构优化**：

   - 采用用例式服务（CaseService）模式，对外提供清晰的业务用例
   - 直接返回领域实体，避免不必要的 DTO 转换，减少代码复杂度
   - 联想词功能与搜索共享基础架构，但通过不同的 DTO 返回高亮等特定信息

2. **性能优化**：

   - 设置默认分页大小为 10，避免大量数据返回
   - 联想词查询限制最大返回条数，并指定只返回必要字段，减少网络传输量
   - 使用 builder 模式创建 PageDTO 和 ResponseDTO，提高代码可读性
   - ES 索引使用了高亮标记，便于前端展示匹配结果

3. **搜索体验优化**：

   - 从原来的匹配产品名称+副标题改为匹配产品名称+品牌名称，扩大搜索范围，提升搜索命中率
   - 品牌名称通常是用户记忆的重要特征，可以帮助用户更快地找到想要的产品
   - 保持产品名称的搜索权重(2.0f)高于品牌名称(1.0f)，确保搜索结果的相关性

4. **边界处理**：
   - 空关键词处理，直接返回空结果
   - 统一异常处理，确保 API 返回友好错误信息
   - 联想词结果为空时返回空列表而非 null，避免前端异常

## 领域模型调整

为了支持按品牌搜索产品，对领域模型进行了以下调整：

---

## 2025-06-17: 实现支付成功消息发送功能

### 变更内容

参考 `sendAuditMessage` 的实现方式，完成了 `sendPaymentSuccessMessage` 方法的实现。

### 新增文件

1. **PaymentSuccessMessage.java** - 支付成功消息值对象

   - 位置：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/payment/types/PaymentSuccessMessage.java`
   - 功能：定义支付成功消息的数据结构，包含订单号、客户 ID、支付金额、支付时间、交易号、支付渠道等信息
   - 参考：`AuditMessage` 类的实现模式

2. **PaymentSuccessMessageProducer.java** - 支付成功消息生产者接口

   - 位置：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/payment/messaging/PaymentSuccessMessageProducer.java`
   - 功能：定义发送支付成功消息的接口规范
   - 参考：`AuditMessageProducer` 接口的实现模式

3. **PaymentSuccessMessageProducerImpl.java** - 支付成功消息生产者实现类
   - 位置：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/payment/messaging/impl/PaymentSuccessMessageProducerImpl.java`
   - 功能：具体实现支付成功消息的发送逻辑，使用 `TOPIC_PAYMENT_SUCCESS` 主题
   - 参考：`AuditMessageProducerImpl` 类的实现模式

### 修改文件

1. **PaymentCallbackCaseServiceImpl.java** - 支付回调业务服务实现类
   - 位置：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/payment/service/impl/PaymentCallbackCaseServiceImpl.java`
   - 修改内容：
     - 添加 `PaymentSuccessMessageProducer` 依赖注入
     - 将 `sendPaymentSuccessMessage` 方法从预留接口改为实际实现
     - 构建 `PaymentSuccessMessage` 对象并发送到消息队列

### 技术实现

- **架构模式**：遵循 DDD 领域驱动设计，将消息相关逻辑放在 domain 层
- **消息队列**：使用统一的 `MqSendGateWay` 网关发送消息
- **序列化**：使用 `fastjson2` 进行 JSON 序列化
- **依赖注入**：使用 Spring 的 `@Service` 和 `@RequiredArgsConstructor` 注解

### 业务价值

- 支付成功后可以触发后续业务流程：库存扣减、发货通知、积分增加等
- 实现了支付回调与业务处理的解耦，提高系统的可扩展性
- 为后续的订单状态流转提供了消息驱动的基础

### 遵循的编程规矩

1. ✅ 代码遵循 SOLID 原则 - 单一职责，每个类职责明确
2. ✅ 每个函数只干一件事 - `send` 方法只负责发送消息
3. ✅ 参考了示例代码的实现模式 - 完全参考 `AuditMessage` 的实现方式
4. ✅ 代码具有良好的可复用性 - 消息发送逻辑可被其他业务复用
5. ✅ 考虑了边界情况 - 消息发送异常会被 MQ 网关处理

6. **在`ProductSpuBaseInfo`实体中增加品牌相关字段**：

   - 增加`brandId`字段，用于存储品牌 ID
   - 增加`brandName`字段，用于存储品牌名称

7. **在`ProductInfoDoc`文档中增加对应字段**：
   - 增加`brandId`字段，类型为`KEYWORD`，用于精确匹配
   - 增加`brandName`字段，类型为`TEXT`，使用 IK 分词器，支持分词搜索
   - 对`brandName`字段添加`@HighLight`注解，支持高亮显示

## 总结

本功能采用了 DDD 架构设计，并使用用例服务模式（CaseService）将业务用例与领域逻辑分离。通过 Easy-ES 实现了高性能的全文搜索，优先匹配产品名称，同时兼顾品牌名称，为用户提供了精准的搜索体验。搜索逻辑的调整（从副标题匹配改为品牌名称匹配）更符合用户习惯，提升了搜索的命中率和准确性。新增的联想词功能帮助用户在输入过程中快速获取相关产品信息，并通过高亮显示提升用户体验。

整个实现保持了架构清晰、代码简洁的特点，通过权重设置保证了搜索结果的相关性，并做好了各类边界情况的处理。产品联想词的实现极大地提升了搜索体验，使用户能够更快地定位到想要的产品。

## 产品分类功能实现

### 需求概述

实现产品分类查询功能，支持获取一级分类列表。

### 技术选型

- 基于 MyBatis Plus 实现数据访问
- 使用 DDD 分层架构实现业务逻辑

### 实现架构

- **控制层**：`CategoryController` - 处理 HTTP 请求，转换 DTO（位于 coupon 模块）
- **用例服务层**：`ProductCategoryCaseService` - 业务编排（位于 product 模块）
- **领域层**：`ProductCategory`(领域实体), `ProductCategoryRepository`(仓库接口)
- **基础设施层**：`ProductCategoryRepositoryImpl`(仓库实现), `ProductCategoryMapper`(MyBatis 接口), `ProductCategoryPO`(持久化对象)

### 数据流

1. 控制器（位于 coupon 模块）接收 HTTP 请求
2. 调用产品模块的用例服务获取领域对象列表
3. 将领域对象转换为`ProductCategoryDTO`返回给前端

### 结构调整

1. 将 Controller 实现放在了 coupon 模块的`CategoryController`中，实现模块间的功能整合
2. 领域服务和仓库等核心实现保留在 product 模块中
3. 使用`ProductCategoryDTO`作为响应 DTO，与领域模型保持分离

### 核心实现

1. 查询条件：一级分类(level=1)、启用状态(status=1)、按排序降序
2. 数据库表结构：分类 ID、分类名称、分类等级、排序值、状态、父级 ID、父级名称
3. 跨模块调用：coupon 模块调用 product 模块的服务，实现业务协作

### 优化考虑

1. 使用专用的`ProductCategoryDTO`作为 API 响应对象，确保接口稳定性
2. 通过模块间调用实现功能组合，符合 DDD 的上下文映射模式
3. 保持 Controller 轻量化，仅负责请求处理和响应转换

### 总结

产品分类功能虽然分布在不同模块中，但仍严格遵循 DDD 架构原则。通过模块间的协作，保持了业务逻辑的内聚性，同时实现了接口的统一管理。这种设计能够更好地支持业务变化，并且保持代码结构清晰，易于维护。

## 二级分类及关联数据查询功能

### 需求概述

实现根据一级分类 ID 查询对应的二级分类名称、广告位和产品列表功能。

### 技术选型

- 使用 MyBatis Plus 查询二级分类基本信息
- 使用 Easy-ES 检索产品信息
- 基于 DDD 架构设计

### 实现架构

- **控制层**：`CategoryController` - 处理 HTTP 请求，接收一级分类 ID
- **用例服务层**：`ProductCategoryCaseService` - 业务编排，组合多个领域服务
- **领域层**：
  - `ProductCategory`(分类实体)、`ProductCategoryRepository`(分类仓库接口)
  - `AdvertisementEntity`(广告实体)、`AdvertisementRepository`(广告仓库接口)
  - `ProductSpuBaseInfo`(产品实体)、`ProductBaseInfoRepository`(产品仓库接口)
- **基础设施层**：
  - `ProductCategoryRepositoryImpl`(分类仓库实现)
  - `ProductBaseInfoRepositoryImpl`(产品仓库实现)

### 数据流

1. 控制器接收一级分类 ID 的请求参数(`CategoryDetailRequestDTO`)
2. 调用用例服务获取二级分类及关联数据
3. 用例服务协调三个仓库的调用：
   - 调用`ProductCategoryRepository`查询二级分类列表
   - 调用`AdvertisementRepository`查询广告位列表
   - 调用`ProductBaseInfoRepository`查询每个二级分类下的产品列表
4. 组装数据并转换为`CategorySearchResponseDTO`返回

### 核心实现

1. 广告位查询：传入`AdvertisementTypeEnum.ADV_CATEGORY.getCode()`（对应值为 4）和一级分类 ID 查询相关广告
2. 产品查询：根据分类 ID 使用 ES 检索，且只返回分类页显示标记为 true 的产品
3. 数据结构设计：
   - `CategorySearchResponseDTO`作为顶层响应对象，包含广告列表和分类详情列表
   - `CategoryProductDTO`作为二级分类的详情对象，包含分类信息和产品列表
   - `ProductBaseInfoDTO`和`AdvertisementInfoDTO`作为具体数据项
4. 领域模型与 DTO 转换：使用 BeanUtil 工具类高效转换

### 优化考虑

1. 限制每个二级分类下产品数量为 20 条，避免返回过多数据
2. 使用枚举类型`AdvertisementTypeEnum`定义广告类型，提高代码可读性
3. 使用请求 DTO 和响应 DTO 分离，便于后续接口扩展
4. 使用统一的异常处理机制，确保接口稳定性

### 总结

二级分类查询功能通过聚合设计，在一次请求中获取了完整分类页面所需的全部数据：广告信息和多个二级分类及其产品信息。通过使用合理的 DTO 设计，使前端能够更灵活地展示数据，提升了用户体验。整个实现遵循 DDD 分层架构，保持了业务逻辑的内聚性，同时通过合理的查询优化确保了接口性能。

## 默认搜索词功能实现

### 需求概述

基于区域 ID 和类型实现默认搜索词与搜索发现获取功能，支持全国通用和区域特定的默认搜索词配置。

### 技术选型

- 基于 MyBatis Plus 实现数据访问
- 使用 DDD 分层架构实现业务逻辑
- 采用联表查询提升查询效率

### 实现架构

- **控制层**：新增`ProductSearchController.getDefaultSearchKeywords`方法 - 处理请求并返回结果
- **应用层**：扩展`ProductSearchCaseService.getDefaultSearchKeywords`方法 - 协调业务流程
- **领域层**：新增`SearchKeyword`实体和`SearchKeywordRepository`接口 - 领域模型定义
- **基础设施层**：
  - `SearchKeywordRepositoryImpl` - 实现查询逻辑
  - `SearchDetailMapper` - MyBatis 映射接口
  - `SearchDetailPO`和`ContentRegionRelationPO` - 持久化对象

### 数据流

1. 控制器接收区域 ID 和类型参数（`DefaultSearchKeywordRequestDTO`）
   - 类型参数：1-搜索框搜索词，2-搜索发现
2. 调用应用层服务获取默认搜索词
3. 应用层调用领域层仓库进行查询：
   - 先查询全国通用的搜索词(is_nationwide=1)
   - 对于非全国通用的搜索词，通过区域关联关系查询
4. 将查询结果去重、按排序值降序排序后转换为 DTO 返回

### 核心实现

1. **数据查询逻辑**：

   ```java
   // 1. 全国通用搜索词查询
   LambdaQueryWrapper<SearchDetailPO> wrapper = new LambdaQueryWrapper<>();
   wrapper.eq(SearchDetailPO::getType, type)
          .eq(SearchDetailPO::getStatus, 1)
          .eq(SearchDetailPO::getIsNationwide, 1)
          .orderByDesc(SearchDetailPO::getSort);

   // 2. 区域特定搜索词查询（通过Mapper自定义方法）
   List<SearchDetailPO> regionSearchDetails = searchDetailMapper.selectSpecificKeywords(districtId, type);

   // 3. 结果去重与排序
   return poList.stream()
          .distinct()
          .sorted((a, b) -> b.getSort().compareTo(a.getSort()))
          .map(this::convertToDomain)
          .collect(Collectors.toList());
   ```

2. **领域模型设计**：

   ```java
   public class SearchKeyword {
       private Long id;           // 关键词ID
       private String keyword;    // 关键词内容
       private String imlUrl;     // 图标广告图URL地址
       private Integer jumpType;  // 跳转类型（1-内部链接，2-外部链接）
       private String jumpUrl;    // 跳转URL
       private Integer isNationwide; // 是否全国通用（0-否，1-是）
       private Integer status;    // 状态（0-禁用，1-启用）
       private Integer sort;      // 排序值
       private Integer type;      // 关键词类型（1-搜索词，2-搜索发现）
   }
   ```

3. **接口设计**：
   ```java
   @PostMapping("/default-keywords")
   @Operation(summary = "获取默认搜索词或搜索发现列表", description = "根据区域ID和类型获取默认搜索词列表，包含图片和跳转链接")
   @VerifySign
   public BaseResponse<List<SearchKeywordResponseDTO>> getDefaultSearchKeywords(
           @Parameter(description = "默认搜索词请求参数") @RequestBody @Valid DefaultSearchKeywordRequestDTO request)
   ```

### 优化考虑

1. **多类型支持**：同一套接口同时支持搜索框默认搜索词和搜索发现两种类型
2. **结果去重与排序**：确保返回结果无重复并按照重要性排序
3. **参数校验**：使用`@NotNull`等注解确保必要参数必须传入
4. **富媒体支持**：返回包含图片 URL 和跳转链接的完整数据，支持前端富媒体展示

### 总结

本功能通过类型参数的支持，实现了搜索框默认搜索词和搜索发现两种场景的统一处理。每种类型都同时支持全国通用和区域特定配置，提供了极大的灵活性。实现时严格遵循 DDD 分层架构，使用领域模型表达业务概念，并优化了数据查询和处理逻辑，确保了良好的性能和扩展性。

# 项目修改记录

## 2025-07-09 - CouponOrderCaseServiceImpl实现

### 修改内容
1. **实现了 `CouponOrderCaseServiceImpl.processCouponOrderCreatedNotify` 方法**
   - 完整的卡券订单创建通知处理逻辑
   - 支持分布式锁防止并发处理
   - 参数验证和业务状态检查
   - 集成卡券平台策略模式获取卡券信息
   - 完善的异常处理和日志记录

2. **修复编译错误 - 添加 `CouponNotifyResponseDTO.fail()` 方法**
   - 在 `CouponNotifyResponseDTO` 类中添加了缺失的 `fail(String msg)` 静态方法
   - 保持与 `success()` 方法的一致性

### 技术实现
- **分布式锁**: 使用订单号作为锁key，防止重复处理同一订单
- **策略模式**: 通过 `CouponPlatformFactory` 获取对应的卡券平台策略
- **状态管理**: 验证发货状态，只处理待发货和发货中的订单
- **异常处理**: try-catch-finally完整的异常处理机制
- **日志分类**: 参考现有代码风格，添加了入口参数、验签检查、数据读取、业务逻辑、第三方服务调用、数据保存等分类日志

### 业务流程
1. 获取分布式锁
2. 验签检查（暂时跳过）
3. 根据外部订单号查找订单
4. 验证订单号和外部订单号一致性
5. 检查发货状态
6. 根据扁平化产品类型识别卡券类型
7. 调用卡券平台策略获取卡券信息
8. 更新订单状态为已发货
9. 记录卡券信息（TODO）

### 编译验证
- 所有模块编译通过
- 代码符合SOLID原则
- 每个方法职责单一

### 待完善
- 验签功能暂时跳过，需要后续补充

## 2025-07-09 - t_order_delivery表卡券发货信息保存功能实现

### 修改内容
1. **创建了完整的领域层和基础设施层架构**
   - `OrderDeliveryEntity` - 订单发货实体类
   - `OrderDeliveryRepository` - 订单发货仓储接口  
   - `OrderItemRepository` - 订单项仓储接口
   - `OrderDeliveryPO` - 订单发货持久化对象
   - `OrderDeliveryMapper` - 订单发货数据访问层
   - `OrderDeliveryRepositoryImpl` - 订单发货仓储实现类
   - `OrderItemRepositoryImpl` - 订单项仓储实现类

2. **实现了卡券发货信息保存功能**
   - 在`CouponOrderCaseServiceImpl`中实现了`saveCouponDeliveryInfo`方法
   - 支持批量保存多张卡券的发货记录
   - 完整映射卡券信息到发货表字段
   - 集成订单项信息获取SKU ID

### 技术实现亮点
- **领域驱动设计**: 严格按照DDD分层架构，实体、仓储、基础设施分离
- **批量处理**: 支持一个订单多张卡券的批量保存，提高性能
- **字段完整映射**: 
  - 卡券类型 → `coupon_verification_type`
  - 卡号 → `coupon_code` 
  - 卡密 → `coupon_pin`
  - CRC校验码 → `coupon_crc`
  - 短链接 → `coupon_url`
  - 短链接密码 → `coupon_url_pass`
  - 批次号 → `coupon_batch_no`
- **事务支持**: 使用`@Transactional`确保数据一致性
- **异常处理**: 完善的异常捕获和日志记录

### 数据库表设计对应
- 表名: `t_order_delivery`
- 支持卡券发放、实物发货、红包充值等多种发货类型
- 包含完整的卡券核销信息字段
- 支持逻辑删除和审计字段

### 业务流程完善
1. 获取卡券信息后立即保存到发货表
2. 为每张卡券创建独立的发货记录
3. 记录详细的发货时间和状态
4. 复制订单收货信息到发货记录

### 复用代码提醒 🔄
- 复用了MyBatis-Plus的`CommonBaseMapper`基础映射器
- 复用了现有的异常处理机制`BizException`
- 复用了Spring的`BeanUtils`对象转换工具
- 复用了现有的事务注解和日志框架

### 编译验证
- 修复了`CouponCardEntity`属性名映射错误
- 所有新增代码符合现有编码规范
- 依赖注入配置正确

## 2025-06-25 - CouponOrderCaseServiceImpl 手动事务管理优化

### 变更概述

为 `CouponOrderCaseServiceImpl` 的 `updateOrderAndDeliveryStatus` 方法添加手动事务管理，确保订单状态更新和发货信息保存的数据一致性，避免部分更新导致的数据不一致问题。

### 修改文件

**业务层：CouponOrderCaseServiceImpl 事务管理优化**

- 文件路径：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/order/coupon/impl/CouponOrderCaseServiceImpl.java`
- 新增导入：`PlatformTransactionManager`、`TransactionDefinition`、`TransactionStatus`、`DefaultTransactionDefinition`
- 新增依赖：`PlatformTransactionManager transactionManager`
- 修改方法：`updateOrderAndDeliveryStatus()` - 添加手动事务管理逻辑

### 技术实现

#### 事务管理策略

1. **事务隔离级别**：`ISOLATION_READ_COMMITTED` - 避免脏读，适合高并发场景
2. **事务传播行为**：`PROPAGATION_REQUIRED` - 如果当前存在事务则加入，否则创建新事务
3. **事务边界**：包含订单状态更新和发货信息保存两个操作
4. **异常处理**：任一操作失败都会触发事务回滚，确保数据一致性

#### 核心实现代码

```java
private void updateOrderAndDeliveryStatus(OrderInfoEntity orderInfo, List<CouponCardEntity> couponCards, String outOrderSn) {
    // 定义事务
    DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
    definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
    definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
    
    TransactionStatus status = transactionManager.getTransaction(definition);
    
    try {
        // 更新订单状态
        updateOrderToDelivered(orderInfo);
        
        // 保存发货信息
        saveCouponDeliveryInfo(orderInfo, couponCards);
        
        // 手动提交事务
        transactionManager.commit(status);
        log.info("卡券信息已保存到发货表，订单号：{}，卡券数量：{}", outOrderSn, couponCards.size());
        
    } catch (Exception e) {
        // 回滚事务
        transactionManager.rollback(status);
        log.error("更新订单和发货状态失败，事务已回滚，订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
        throw new BizException("更新订单和发货状态失败：" + e.getMessage());
    }
}
```

#### 事务管理优势

1. **数据一致性**：确保订单状态和发货信息的同步更新，避免数据不一致
2. **精确控制**：手动控制事务边界，比声明式事务更精确
3. **异常安全**：统一的异常处理和回滚机制，确保系统稳定性
4. **业务语义**：事务边界与业务操作边界完全一致

### 业务价值

1. **数据完整性**：防止订单状态已更新但发货信息保存失败的情况
2. **系统稳定性**：通过事务回滚机制，避免半成功状态导致的业务异常
3. **调试友好**：明确的事务边界和日志记录，便于问题排查和监控
4. **维护性提升**：集中的事务管理逻辑，便于后续维护和优化

### 遵循的编程原则

1. **ACID 原则**：保证事务的原子性、一致性、隔离性和持久性
2. **单一职责**：`updateOrderAndDeliveryStatus` 专注于事务管理和业务编排
3. **异常处理**：统一的异常捕获和转换，符合业务层异常处理规范
4. **日志规范**：详细的日志记录，包含成功和失败场景的关键信息

### 与 DDD 架构的配合

1. **业务编排**：在 biz 层进行事务管理，符合业务编排层的职责
2. **基础设施隔离**：事务管理不侵入 domain 层和 infrastructure 层
3. **依赖注入**：通过构造函数注入 `PlatformTransactionManager`，符合 DI 原则
4. **领域完整性**：确保卡券发放业务的领域完整性和一致性

---

## 2025/6/17 - 支持品诺和视听会员充值流程

### 变更概述
修改了卡券订单通知处理系统，以支持品诺和视听会员的充值流程。

### 主要变更

#### 1. CouponOrderNotifyController 改进
- **优化参数接收方式**: 将手动读取HttpServletRequest改为使用`@RequestBody Map<String, Object>`接收JSON参数
- **统一处理逻辑**: `/streaming-recharge`和`/pinnuo-recharge`接口都调用同一个处理方法
- **增加公共方法**: 新增`buildRechargeOrderNotifyProperties`方法构建充值通知DTO
- **字段映射**: 支持不同字段名映射（如`third_order_sn`映射到`outOrderSn`，`account`映射到`rechargeAccount`等）

#### 2. CouponOrderCaseServiceImpl 核心逻辑改进
- **流程分离**: 将原有的单一处理方法拆分为充值流程和卡券流程两个分支
- **充值状态处理**: 根据rechargeStatus状态码实现不同的处理逻辑：
  - 状态 2,3,4,7: 暂不处理，直接返回成功
  - 状态 5 (充值成功): 更新订单状态为交易完成，发货状态为已充值，写入充值信息到发货表
  - 状态 6 (充值失败): 更新发货状态为充值失败
- **新增方法**:
  - `processRechargeNotify()`: 处理充值通知的主逻辑
  - `processCouponOrderCreated()`: 处理原有卡券订单创建流程
  - `updateOrderForRechargeSuccess()`: 更新订单充值成功状态
  - `updateOrderToRechargeSuccess()`: 更新订单状态为充值成功
  - `saveRechargeDeliveryInfo()`: 保存充值发货信息到t_order_delivery表
  - `updateOrderForRechargeFailed()`: 更新订单充值失败状态
  - `updateOrderToRechargeFailed()`: 更新订单状态为充值失败

#### 3. 数据模型支持
- **发货类型扩展**: 使用发货类型3（红包或充值发放）来标识充值发货记录
- **充值信息记录**: 在OrderDeliveryEntity中记录充值账号、发货时间等信息
- **事务管理**: 使用手动事务管理确保订单状态更新和发货记录保存的原子性

### 技术细节

#### 状态码定义
```
0-待付款 1-交易关闭 2-付款成功 3-待充值 4-充值中 5-充值成功 6-充值失败 7-退款成功 8-充值系统异常
```

#### 处理流程
1. **Controller层**: 接收充值回调，构造统一的DTO对象
2. **Service层**: 根据是否有rechargeStatus判断处理分支
3. **充值成功处理**: 更新订单状态 + 创建发货记录（事务保护）
4. **充值失败处理**: 更新发货状态为充值失败

### 代码质量改进
- **遵循SOLID原则**: 单一职责，每个方法只处理一种业务逻辑
- **异常处理**: 完善的异常捕获和事务回滚机制
- **日志记录**: 详细的业务流程日志，便于问题追踪
- **充值失败处理**: 直接更新发货状态为充值失败，确保订单状态的正确性

### 影响范围
- **兼容性**: 原有卡券订单创建流程完全保持不变
- **新功能**: 新增品诺和视听会员充值状态处理
- **数据库**: 复用现有t_order_delivery表结构，无需额外表结构变更

### 测试要点
1. 验证不同充值状态的处理逻辑
2. 确认事务回滚机制有效性
3. 测试原有卡券流程不受影响
4. 验证发货记录正确保存

---

## 2025/6/17 - 充值状态枚举化重构

### 变更概述
为了消除魔数，新增了`CouponPlatformRechargeStatus`枚举类，并重构了充值状态判断逻辑。

### 主要变更

#### 1. 新增CouponPlatformRechargeStatus枚举类
- **文件位置**: `rxjt-lucky-carp-mall-api/src/main/java/com/jsrxjt/mobile/api/coupon/types/CouponPlatformRechargeStatus.java`
- **枚举值定义**:
  - `PENDING_PAYMENT(0, "待付款")`
  - `TRADE_CLOSED(1, "交易关闭")`
  - `PAYMENT_SUCCESS(2, "付款成功")`
  - `PENDING_RECHARGE(3, "待充值")`
  - `RECHARGING(4, "充值中")`
  - `RECHARGE_SUCCESS(5, "充值成功")`
  - `RECHARGE_FAILED(6, "充值失败")`
  - `REFUND_SUCCESS(7, "退款成功")`
  - `RECHARGE_SYSTEM_ERROR(8, "充值系统异常")`

#### 2. 新增工具方法
- **getByCode(int code)**: 根据状态码获取枚举实例
- **isWaitingStatus(int code)**: 判断是否为需要等待处理的状态（2,3,4,7）
- **isRechargeSuccess(int code)**: 判断是否为充值成功状态（5）
- **isRechargeFailed(int code)**: 判断是否为充值失败状态（6）

#### 3. 重构CouponOrderCaseServiceImpl
- **消除魔数**: 使用枚举常量替代硬编码的数字
- **优化判断逻辑**: 使用枚举的工具方法简化状态判断
- **增强日志**: 日志中同时显示状态码和状态描述
- **代码可读性**: 状态判断逻辑更加清晰和语义化

#### 4. 代码改进点
- **从**: `case 2, 3, 4, 7 ->` 
- **到**: `if (CouponPlatformRechargeStatus.isWaitingStatus(rechargeStatus))`
- **从**: `case 5 ->`
- **到**: `else if (CouponPlatformRechargeStatus.isRechargeSuccess(rechargeStatus))`
- **从**: `case 6 ->`
- **到**: `else if (CouponPlatformRechargeStatus.isRechargeFailed(rechargeStatus))`

### 技术优势

#### 1. 消除魔数
- 避免了硬编码的数字，提高代码可读性
- 状态值集中管理，便于维护和修改

#### 2. 类型安全
- 编译时检查，避免状态值错误
- IDE智能提示，减少开发错误

#### 3. 可扩展性
- 新增状态只需在枚举中添加，无需修改业务逻辑
- 工具方法支持状态分组判断

#### 4. 日志增强
- 日志同时显示状态码和描述，便于问题排查
- 状态描述统一管理，保持一致性

### 业务价值
1. **代码质量提升**: 消除魔数，提高代码可维护性
2. **错误减少**: 类型安全和编译检查减少运行时错误
3. **开发效率**: 清晰的状态定义和工具方法提高开发效率
4. **问题排查**: 增强的日志信息便于快速定位问题

### 影响范围
- **新增文件**: `CouponPlatformRechargeStatus.java`
- **修改文件**: `CouponOrderCaseServiceImpl.java`
- **向后兼容**: 完全兼容，不影响现有功能

## 2025-07-17 - 订单列表分页查询功能实现

### 变更概述

实现了完整的订单列表分页查询功能，支持按订单状态过滤、分页展示和用户权限控制，为前端提供丰富的订单信息展示能力。

### 新增功能

1. **API层 - 请求响应DTO**
   - 新增 `OrderListRequestDTO`：继承 `BaseParam` 支持分页，包含可选的订单状态过滤参数
   - 新增 `OrderListResponseDTO`：包含订单详细信息的完整字段，涵盖订单号、品牌信息、商品信息、各种价格、状态描述、时间信息等

2. **控制器层 - 订单列表接口**
   - 在 `OrderController` 中新增 `/list` 接口
   - 支持通过 token 验证用户身份
   - 自动获取当前登录用户ID，确保用户只能查询自己的订单

3. **业务层 - 查询逻辑**
   - 在 `OrderCaseService` 接口中新增 `getOrderList` 方法
   - 在 `OrderCaseServiceImpl` 中实现完整的查询和转换逻辑
   - 支持订单状态过滤（不传则查询全部）
   - 实现订单实体到响应DTO的转换，包含状态描述映射

4. **领域层 - 查询条件**
   - 新增 `OrderListQuery` 查询条件类，使用 Builder 模式
   - 在 `OrderRepository` 接口中新增 `findOrderListByPage` 方法定义

5. **基础设施层 - 数据访问**
   - 在 `OrderRepositoryImpl` 中实现分页查询逻辑
   - 使用 MyBatis-Plus 的分页功能
   - 支持按订单ID倒序排列
   - 完整的查询条件构建和结果转换

### 技术实现

#### 查询特性
- **分页支持**：集成 `BaseParam` 的分页参数，支持页码和每页条数设置
- **状态过滤**：支持按订单状态过滤，不传参数则查询用户全部订单
- **排序规则**：按订单ID倒序排列，确保最新订单在前
- **权限控制**：通过客户ID限制，用户只能查询自己的订单

#### 数据完整性
响应数据包含前端所需的完整订单信息：
- 基础信息：订单号、下单时间、支付超时时间
- 商品信息：品牌ID/名称、商品名称、SPU/SKU ID、面值、数量
- 价格信息：原价、售价、订单总金额、支付金额
- 状态信息：订单状态、发货状态、售后状态（含描述）
- 业务信息：充值账户、订单详情URL

#### 架构设计
- **DDD分层**：严格按照领域驱动设计分层，职责清晰
- **查询对象**：使用专门的 `OrderListQuery` 封装查询条件
- **DTO转换**：业务层负责实体到DTO的转换，包含状态枚举描述
- **异常安全**：完善的空值判断和默认值处理

### 修改文件

- 新增：`rxjt-lucky-carp-mall-api/src/main/java/com/jsrxjt/mobile/api/order/dto/request/OrderListRequestDTO.java`
- 新增：`rxjt-lucky-carp-mall-api/src/main/java/com/jsrxjt/mobile/api/order/dto/response/OrderListResponseDTO.java`
- 新增：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/query/OrderListQuery.java`
- 修改：`rxjt-lucky-carp-mall-order/src/main/java/com/jsrxjt/adapter/order/controller/OrderController.java`
- 修改：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/order/OrderCaseService.java`
- 修改：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/order/impl/OrderCaseServiceImpl.java`
- 修改：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/repository/OrderRepository.java`
- 修改：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/repository/OrderRepositoryImpl.java`

### 业务价值

1. **用户体验提升**：提供完整的订单列表查询功能，支持状态筛选和分页浏览
2. **数据展示丰富**：包含订单的完整信息，满足前端各种展示需求
3. **性能优化**：分页查询避免大量数据加载，提高响应速度
4. **安全性保障**：用户权限控制确保数据安全

### 遵循的编程原则

1. **SOLID原则**：每个类和方法职责单一，依赖抽象而非具体实现
2. **DDD架构**：严格按照分层架构，领域逻辑与基础设施分离
3. **代码复用**：复用现有的分页、状态枚举等基础组件
4. **异常安全**：完善的边界条件处理，确保系统稳定性

### 后续计划

1. 可考虑添加更多筛选条件（如时间范围、商品类型等）
2. 优化查询性能，考虑添加索引
3. 支持订单搜索功能（按订单号、商品名称搜索）

## 2025-07-21 - 订单列表分页查询JOIN优化和测试完善

### 变更概述

优化了订单列表分页查询功能，从原来的N+1查询改为单次JOIN查询，提升查询性能，并完善了相关测试用例。

### 技术改进

1. **JOIN查询优化**
   - 将原来通过`@Many`注解的N+1查询改为单次LEFT JOIN查询
   - 使用MyBatis XML配置实现复杂的一对多结果映射
   - 避免了多次数据库查询，显著提升性能

2. **SQL歧义问题解决**
   - 修复了JOIN查询中`customer_id`字段歧义的问题
   - 在QueryWrapper中使用表别名明确指定字段来源
   - 确保生成的SQL语句正确无歧义

3. **结果映射优化**
   - 创建了完整的XML ResultMap配置
   - 支持订单主体信息和订单项信息的自动映射
   - 实现了真正的一对多关系映射

### 核心实现

#### XML配置优化
- 新增 `OrderItemResultMap` 订单项结果映射
- 新增 `OrderWithItemsResultMap` 订单及订单项结果映射
- 使用 `<collection>` 标签实现一对多关联映射

#### 查询条件优化
```java
// 使用表别名避免字段歧义
QueryWrapper<OrderPO> queryWrapper = new QueryWrapper<>();
queryWrapper.eq("o.customer_id", query.getCustomerId());
queryWrapper.eq("o.order_status", query.getOrderStatus());
queryWrapper.orderByDesc("o.id");
```

#### JOIN查询SQL
```sql
SELECT 
    o.id as order_id, o.order_no, o.customer_id, o.order_status,
    o.delivery_status, o.after_sale_status, o.payment_amount,
    oi.id as item_id, oi.product_name, oi.brand_name, oi.sell_price
FROM t_order o
LEFT JOIN t_order_item oi ON o.order_no = oi.order_no
WHERE (o.customer_id = ?) ORDER BY o.id DESC
```

### 测试完善

1. **新增测试类 `OrderTests`**
   - `testPageOrderList()` - 基础分页查询测试
   - `testPageOrderListWithStatus()` - 按状态筛选测试  
   - `testPageOrderListPagination()` - 分页功能测试

2. **测试覆盖场景**
   - 全状态订单查询
   - 特定状态订单筛选
   - 多页数据分页查询
   - 边界条件和异常处理

### 修改文件

- 修改：`rxjt-lucky-carp-mall-infrastructure/src/main/resources/mapper/order/OrderMapper.xml`
- 修改：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/persistent/mapper/OrderMapper.java`
- 修改：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/repository/OrderRepositoryImpl.java`
- 新增：`rxjt-lucky-carp-mall-microservicesdemo/src/test/java/com/jsrxjt/adapter/demo/OrderTests.java`

### 性能提升

1. **查询效率**：从N+1查询优化为单次JOIN查询，减少数据库交互次数
2. **数据完整性**：一次查询获取订单及其所有订单项信息
3. **内存优化**：避免多次查询结果的内存占用
4. **网络开销**：减少应用与数据库之间的网络交互

### 业务价值

1. **用户体验提升**：查询响应速度更快，页面加载更流畅
2. **系统性能优化**：减少数据库压力，提高并发处理能力
3. **代码质量提升**：使用标准的MyBatis映射方式，代码更规范
4. **测试覆盖完善**：全面的测试用例确保功能稳定性

### 遵循的编程原则

1. **性能优先**：选择最优的查询方式，避免N+1问题
2. **标准实践**：使用MyBatis标准的XML配置方式
3. **测试驱动**：完善的测试用例保证代码质量
4. **可维护性**：清晰的映射配置，便于后续维护和扩展

### 后续优化建议

1. 可考虑添加查询缓存，进一步提升性能
2. 支持更多的查询条件和排序方式
3. 考虑分库分表场景下的查询优化

## 2025-07-21 - 售后详情查询功能实现

### 变更概述

实现了完整的售后详情查询功能，支持根据售后单号和客户ID查询售后详情，为前端提供丰富的售后信息展示能力，包括进度跟踪、操作日志和退款信息。

### 新增功能

1. **API层 - 请求响应DTO**
   - 新增 `AfterSaleDetailRequestDTO`：继承 `BaseParam`，包含售后单号参数
   - 新增 `AfterSaleDetailResponseDTO`：包含售后详细信息的完整字段，涵盖状态描述、退款金额、进度提示、操作日志、退款信息等

2. **Controller层 - 售后控制器**
   - 新增 `AfterSaleController`：提供售后相关接口
   - 新增 `/v1/after-sale/detail` 接口：支持根据售后单号查询详情
   - 集成token验证和权限控制

3. **业务层 - 售后业务服务**
   - 新增 `AfterSaleCaseService` 接口：定义售后业务方法
   - 新增 `AfterSaleCaseServiceImpl` 实现：完整的售后详情查询业务逻辑
   - 支持售后状态描述转换、进度列表构建、日志详情处理

4. **仓储层 - 数据查询支持**
   - 在 `AfterSaleRepository` 中新增 `findByAfterSaleNoAndCustomerId` 方法
   - 在 `AfterSaleRepositoryImpl` 中实现售后单查询逻辑
   - 在 `AfterSaleLogRepository` 中新增 `findByAfterSaleNo` 方法
   - 在 `AfterSaleLogRepositoryImpl` 中实现售后日志查询逻辑

### 技术实现

#### 查询特性
- **权限控制**：通过售后单号和客户ID双重验证，确保用户只能查询自己的售后单
- **数据完整性**：一次查询获取售后信息、关联订单信息和操作日志
- **状态转换**：自动将状态码转换为可读的描述信息
- **进度跟踪**：根据当前售后状态动态构建进度条显示

#### 响应数据结构
响应数据完全按照原型图设计，包含：
- **基础信息**：售后状态描述、退款总金额、提货凭证信息
- **进度信息**：四步进度条（提交申请→客服审核→处理中→已完成）
- **操作日志**：详细的售后操作记录，包含操作描述、时间和详情
- **退款信息**：商品信息、退款金额、申请数量、申请时间、售后原因等

#### 架构设计
- **DDD分层**：严格按照领域驱动设计分层，职责清晰分离
- **数据转换**：业务层负责实体到DTO的转换，包含状态枚举描述
- **异常安全**：完善的空值判断和权限验证，确保数据安全
- **日志记录**：完整的业务操作日志，便于问题追踪

### 修改文件

- 新增：`rxjt-lucky-carp-mall-api/src/main/java/com/jsrxjt/mobile/api/order/dto/request/AfterSaleDetailRequestDTO.java`
- 新增：`rxjt-lucky-carp-mall-api/src/main/java/com/jsrxjt/mobile/api/order/dto/response/AfterSaleDetailResponseDTO.java`
- 新增：`rxjt-lucky-carp-mall-order/src/main/java/com/jsrxjt/adapter/order/controller/AfterSaleController.java`
- 新增：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/order/AfterSaleCaseService.java`
- 新增：`rxjt-lucky-carp-mall-biz/src/main/java/com/jsrxjt/mobile/biz/order/impl/AfterSaleCaseServiceImpl.java`
- 修改：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/repository/AfterSaleRepository.java`
- 修改：`rxjt-lucky-carp-mall-domain/src/main/java/com/jsrxjt/mobile/domain/order/repository/AfterSaleLogRepository.java`
- 修改：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/repository/AfterSaleRepositoryImpl.java`
- 修改：`rxjt-lucky-carp-mall-infrastructure/src/main/java/com/jsrxjt/mobile/infra/order/repository/AfterSaleLogRepositoryImpl.java`
- 修改：`rxjt-lucky-carp-mall-microservicesdemo/src/main/java/com/jsrxjt/adapter/demo/controller/TestController.java`

### 业务价值

1. **用户体验提升**：提供完整的售后详情查询功能，用户可以清晰了解售后进度
2. **数据展示丰富**：包含售后的完整信息，满足前端各种展示需求
3. **进度可视化**：四步进度条让用户直观了解售后处理状态
4. **安全性保障**：严格的权限控制确保用户只能查看自己的售后信息
5. **运营支持**：详细的操作日志便于客服人员跟踪和处理问题

### 遵循的编程原则

1. **SOLID原则**：每个类和方法职责单一，依赖抽象而非具体实现
2. **DDD架构**：严格按照分层架构，领域逻辑与基础设施分离
3. **代码复用**：复用现有的仓储模式、异常处理等基础组件
4. **异常安全**：完善的边界条件处理和权限验证，确保系统稳定性
5. **数据一致性**：通过事务管理确保数据查询的一致性

### 复用代码提醒 🔄

- 复用了现有的 `BaseParam` 基础参数类
- 复用了 `@VerifySign` 验签注解和token验证机制
- 复用了 `BaseResponse` 统一响应格式
- 复用了 `BizException` 业务异常处理
- 复用了 `BeanUtils.copyProperties` 对象转换工具
- 复用了MyBatis-Plus的查询构造器和映射器

### 测试支持

- 在 `TestController` 中新增测试接口，便于开发调试
- 支持完整的售后详情查询测试
- 提供详细的日志输出，便于问题排查

### 后续计划

1. 可考虑添加售后列表查询功能
2. 支持售后状态变更通知
3. 完善售后图片上传和展示功能
4. 优化查询性能，考虑添加缓存机制

---




