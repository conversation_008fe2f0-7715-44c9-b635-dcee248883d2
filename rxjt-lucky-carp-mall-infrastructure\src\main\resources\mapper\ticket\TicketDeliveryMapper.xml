<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketDeliveryMapper">

    <!-- 批量插入优惠券发放记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ticket_delivery (
        ticket_order_no,
        order_id,
        order_no,
        external_order_no,
        ticket_type,
        customer_id,
        status,
        center_coupon_id,
        ticket_code,
        ticket_pin,
        url,
        url_pass,
        crc,
        batch_num,
        h5_url,
        check_type,
        ticket_id,
        ticket_name,
        ticket_valid_date,
        offset_page_type,
        brand_name,
        offset_logo,
        is_birthday_ticket,
        center_ticket_coupon_number,
        coupon_number_id,
        ticket_cat_code,
        discount_amount,
        threshold_amount,
        create_time,
        create_id,
        mod_time,
        mod_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.ticketOrderNo},
            #{item.orderId},
            #{item.orderNo},
            #{item.externalOrderNo},
            #{item.ticketType},
            #{item.customerId},
            #{item.status},
            #{item.centerCouponId},
            #{item.ticketCode},
            #{item.ticketPin},
            #{item.url},
            #{item.urlPass},
            #{item.crc},
            #{item.batchNum},
            #{item.h5Url},
            #{item.checkType},
            #{item.ticketId},
            #{item.ticketName},
            #{item.ticketValidDate},
            #{item.offsetPageType},
            #{item.brandName},
            #{item.offsetLogo},
            #{item.isBirthdayTicket},
            #{item.centerTicketCouponNumber},
            #{item.couponNumberId},
            #{item.ticketCatCode},
            #{item.discountAmount},
            #{item.thresholdAmount},
            #{item.createTime},
            #{item.createId},
            #{item.modTime},
            #{item.modId}
            )
        </foreach>
    </insert>

</mapper>
