package com.jsrxjt.mobile.biz.ticket.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryDetailRequestDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryRequestDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketVerifyRequestDTO;
import com.jsrxjt.mobile.api.ticket.response.*;
import com.jsrxjt.mobile.api.ticket.types.TicketStatusEnum;
import com.jsrxjt.mobile.api.ticket.types.TicketTypeEnum;
import com.jsrxjt.mobile.biz.ticket.TicketDeliveryService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.gateway.FuliquanShopTicketGateway;
import com.jsrxjt.mobile.domain.customer.gateway.request.ShopTicketListRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.ShopTicketListResponseDTO;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.global.gateway.GlobalGateWay;
import com.jsrxjt.mobile.domain.global.request.GlobalTicketListRequest;
import com.jsrxjt.mobile.domain.global.response.GlobalTicketResponse;
import com.jsrxjt.mobile.domain.shop.entity.ShopAgentEntity;
import com.jsrxjt.mobile.domain.shop.repository.ShopAgentRepository;
import com.jsrxjt.mobile.domain.ticket.entity.TicketDeliveryEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketOffsetRecordEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketDeliveryRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketOffsetRecordRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketStoreRelationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TicketDeliveryServiceImpl implements TicketDeliveryService {

    /**
     * 全球购券类型 1现金券 2满减券
     */
    public static final int GLOBAL_TICKET_TYPE_CASH = 1;
    public static final int GLOBAL_TICKET_TYPE_DISCOUNT = 2;

    private final TicketDeliveryRepository ticketDeliveryRepository;
    private final TicketRepository ticketRepository;
    private final FuliquanShopTicketGateway fuliquanShopTicketGateway;
    private final CustomerRepository customerRepository;

    private final GlobalGateWay globalGateWay;

    private final TicketStoreRelationRepository storeRelationRepository;

    private final ShopAgentRepository shopAgentRepository;

    private final TicketOffsetRecordRepository ticketOffsetRecordRepository;

    /**
     * @param request
     * @return
     */
    @Override
    public PageDTO<TicketDeliveryResponseDTO> getTicketDelivery(TicketDeliveryRequestDTO request) {
        long coustomerId = StpUtil.getLoginIdAsLong();
        PageDTO<TicketDeliveryEntity> ticketDelivery = ticketDeliveryRepository.getMerchantTicketByUserId(coustomerId, request.getPage(), request.getSize());
        if (ticketDelivery.getTotal() == 0) {
            return PageDTO.emptyBuild(request.getPage(), request.getSize());
        }
        List<TicketDeliveryResponseDTO> responses = new ArrayList<>();
        for (TicketDeliveryEntity ticketDeliveryEntity : ticketDelivery.getRecords()) {
            TicketDeliveryResponseDTO bean = BeanUtil.toBean(ticketDeliveryEntity, TicketDeliveryResponseDTO.class);
            responses.add(bean);
        }
        return PageDTO.build(responses, ticketDelivery.getTotal(), ticketDelivery.getSize(), ticketDelivery.getCurrent());
    }

    /**
     * @param request
     * @return
     */
    @Override
    public TicketDeliveryDetailResponseDTO getTicketDeliveryById(TicketDeliveryDetailRequestDTO request) {
        long coustomerId = StpUtil.getLoginIdAsLong();
        TicketDeliveryEntity ticketDeliveryEntity = ticketDeliveryRepository.getTicketDeliveryById(coustomerId, request.getId());
        if (ticketDeliveryEntity != null) {
            TicketDeliveryDetailResponseDTO bean = BeanUtil.toBean(ticketDeliveryEntity, TicketDeliveryDetailResponseDTO.class);
            TicketEntity ticket = ticketRepository.getTicketIncludeDelById(ticketDeliveryEntity.getTicketId());
            if (Objects.nonNull(ticket)) {
                bean.setUseManual(ticket.getUseManual());
            }
            return bean;
        }
        return null;
    }

    /**
     * @param request
     */
    @Override
    public void delTicketDelivery(TicketDeliveryDetailRequestDTO request) {
        ticketDeliveryRepository.delTicketDelivery(StpUtil.getLoginIdAsLong(), request.getId());
    }

    /**
     * @param request
     * @return
     */
    @Override
    public PageDTO<TicketShopListResponseDTO> getShopTicketList(TicketDeliveryRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        //判断是否已经领取
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if (customerEntity == null || customerEntity.getDelFlag() == 1 || customerEntity.getStatus() != 1) {
            throw new BizException("该会员账号不存在或注销");
        }
        /*ShopTicketListRequestDTO requestDTO = new ShopTicketListRequestDTO();
        requestDTO.setNonce(IdUtil.fastSimpleUUID());
        requestDTO.setTimestamp(Instant.now().getEpochSecond());
        requestDTO.setVipid(customerEntity.getVipId());
        requestDTO.setPage(request.getPage());
        requestDTO.setSize(request.getSize());*/

        PageDTO<TicketDeliveryEntity> ticketList = ticketDeliveryRepository.getTicketDeliveryByUserId(customerEntity.getId(), TicketTypeEnum.RX_SHOP_COUPON.getCode().intValue(), request.getPage(), request.getSize());

        /*PageDTO<ShopTicketListResponseDTO> shopTicketList = fuliquanShopTicketGateway.getShopTicketList(requestDTO);
        if (shopTicketList.getTotal() == 0) {
            return PageDTO.emptyBuild(request.getPage(), request.getSize());
        }*/
        List<TicketShopListResponseDTO> responses = new ArrayList<>();
        ticketList.getRecords().forEach(shopTicketListResponseDTO -> {
            TicketShopListResponseDTO bean = new TicketShopListResponseDTO();
            bean.setId(shopTicketListResponseDTO.getId());
            bean.setTicketName(shopTicketListResponseDTO.getTicketName());
            bean.setAmount(shopTicketListResponseDTO.getDiscountAmount());
            bean.setTicketCode(shopTicketListResponseDTO.getCenterTicketCouponNumber());
            bean.setStartTime(shopTicketListResponseDTO.getCreateTime());
            bean.setEndTime(shopTicketListResponseDTO.getTicketValidDate());
            bean.setSummary(shopTicketListResponseDTO.getBrandName());//产品要求使用品牌名当副标题
            bean.setTicketStatus(shopTicketListResponseDTO.getStatus().intValue());
//            bean.setRule(shopTicketListResponseDTO.getRule());
            bean.setLogo(shopTicketListResponseDTO.getOffsetLogo());
            responses.add(bean);
        });
        return PageDTO.build(responses, ticketList.getTotal(), ticketList.getSize(), ticketList.getCurrent());
    }

    @Override
    public PageDTO<GlobalTicketResponseDTO> getGlobalTicketList(TicketDeliveryRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if (customerEntity == null || customerEntity.getDelFlag() == 1 || customerEntity.getStatus() != 1) {
            throw new BizException("该会员账号不存在或注销");
        }
        GlobalTicketListRequest globalTicketListRequest = new GlobalTicketListRequest();
        globalTicketListRequest.setUnionId(customerEntity.getUnionid());
        globalTicketListRequest.setToPage(String.valueOf(request.getPage()));
        globalTicketListRequest.setPageRows(String.valueOf(request.getSize()));
        PageDTO<GlobalTicketResponse> globalTicketList = globalGateWay.getTicketList(globalTicketListRequest);
        if (globalTicketList == null || globalTicketList.getTotal() == 0) {
            return PageDTO.emptyBuild(request.getPage(), request.getSize());
        }
        List<GlobalTicketResponseDTO> responses = globalTicketList.getRecords().stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
        return PageDTO.build(responses, globalTicketList.getTotal(), globalTicketList.getSize(), globalTicketList.getCurrent());
    }

    @Override
    public TicketVerifyResponseDTO verifyTicket(TicketVerifyRequestDTO request) {
        log.info("开始核销券码: {}", request.getNo());
        TicketDeliveryEntity ticketDeliveryEntity = ticketDeliveryRepository.getByTicketCode(request.getNo());
        if (ticketDeliveryEntity == null) {
            return TicketVerifyResponseDTO.fail("该券码不存在");
        }
        if (!Objects.equals(ticketDeliveryEntity.getStatus(), TicketStatusEnum.UNUSED.getStatus())) {
            return TicketVerifyResponseDTO.fail("该券码已核销");
        }
        if (ticketDeliveryEntity.getTicketType() != 2) {
            return TicketVerifyResponseDTO.fail("该券码不支持在当前商家核销");
        }
        if (!storeRelationRepository.checkTicketStoreRelation(ticketDeliveryEntity.getTicketId(), request.getMdId())) {
            return TicketVerifyResponseDTO.fail("该券码不支持在当前商家核销");
        }
        ShopAgentEntity shopAgentEntity = shopAgentRepository.findByShopNo(request.getMdId());
        if (shopAgentEntity == null) {
            return TicketVerifyResponseDTO.fail("商家信息未维护，暂不支持核销");
        }
        boolean updateSuccess = updateTicketStatusToUsed(ticketDeliveryEntity.getId());
        if (!updateSuccess) {
            return TicketVerifyResponseDTO.fail("该券码已被核销，请刷新后重试");
        }
        createOffsetRecord(ticketDeliveryEntity, shopAgentEntity);
        TicketVerifyDetailResponseDTO responseDTO = new TicketVerifyDetailResponseDTO();
        responseDTO.setName(shopAgentEntity.getName());
        responseDTO.setNo(shopAgentEntity.getNo());
        responseDTO.setShopId(shopAgentEntity.getId());
        responseDTO.setStatus(0);//0成功 1失败
        return TicketVerifyResponseDTO.success(responseDTO);
    }

    private boolean updateTicketStatusToUsed(Long ticketDeliveryId) {
        try {
            return ticketDeliveryRepository.updateTicketStatusToUsed(ticketDeliveryId);
        } catch (Exception e) {
            log.error("更新券状态失败，ticketDeliveryId: {}", ticketDeliveryId, e);
            return false;
        }
    }

    private void createOffsetRecord(TicketDeliveryEntity ticketDelivery, ShopAgentEntity shopAgent) {
        Date now = new Date();
        TicketOffsetRecordEntity record = new TicketOffsetRecordEntity();
        record.setTicketId(ticketDelivery.getTicketId());
        record.setTicketDeliveryId(ticketDelivery.getId());
        record.setTicketCode(ticketDelivery.getTicketCode());
        record.setValidDate(ticketDelivery.getTicketValidDate());
        record.setTicketName(ticketDelivery.getTicketName());
        record.setCustomerId(ticketDelivery.getCustomerId());
        record.setOrderNo(ticketDelivery.getOrderNo());
        record.setStoreName(shopAgent.getName());
        record.setStoreId(shopAgent.getId());
        record.setPickTime(ticketDelivery.getCreateTime());
        record.setOffsetTime(now);
        record.setCreateTime(now);
        record.setDelFlag(NumberUtils.INTEGER_ZERO);
        ticketOffsetRecordRepository.save(record);
    }

    /**
     * 转换GlobalTicketResponse为DTO对象
     */
    private GlobalTicketResponseDTO convertToResponseDTO(GlobalTicketResponse globalTicketResponse) {
        GlobalTicketResponseDTO dto = new GlobalTicketResponseDTO();
        dto.setId(globalTicketResponse.getTicketId());
        dto.setTicketName(globalTicketResponse.getLhqName());
        dto.setAmount(globalTicketResponse.getReduceValue());
        dto.setTicketCode(globalTicketResponse.getTicketCode());
        dto.setStartTime(globalTicketResponse.getStartTime());
        dto.setEndTime(globalTicketResponse.getEndTime());
        dto.setTicketStatus(globalTicketResponse.getTicketStatus());
        dto.setUsingRange(globalTicketResponse.getUsingRange());
        if (globalTicketResponse.getLhqType() != null) {
            if (globalTicketResponse.getLhqType() == GLOBAL_TICKET_TYPE_CASH) {
                dto.setSummary(globalTicketResponse.getReduceValue() + "元无门槛券");
                dto.setTicketType("无门槛券");
            } else if (globalTicketResponse.getLhqType() == GLOBAL_TICKET_TYPE_DISCOUNT) {
                dto.setSummary("满" + globalTicketResponse.getCondition1() + "减" + globalTicketResponse.getReduceValue() + "元");
                dto.setTicketType("满减券");
            }
        }
        return dto;
    }
}
