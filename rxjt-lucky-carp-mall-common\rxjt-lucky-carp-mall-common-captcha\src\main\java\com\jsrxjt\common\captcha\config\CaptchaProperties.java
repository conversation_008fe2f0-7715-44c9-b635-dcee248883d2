package com.jsrxjt.common.captcha.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 验证码模块配置属性
 * <p>
 * 注意：AJ-Captcha的具体配置请使用 aj.captcha.* 前缀
 * 本配置只用于控制模块级别的功能开关
 *
 * <AUTHOR>
 * @since 2025-12-04
 */
@Data
@ConfigurationProperties(prefix = "jsrxjt.captcha")
public class CaptchaProperties {

    /**
     * 是否启用验证码模块
     * <p>
     * 当设置为false时，验证码相关的Bean不会被创建
     */
    private Boolean enabled = false;

}
