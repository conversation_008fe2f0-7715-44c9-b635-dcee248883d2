package com.jsrxjt.mobile.domain.captcha.gateway;

import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaRequest;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaResponse;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaVerifyRequest;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaVerifyResponse;

/**
 * 验证码网关接口
 * 定义验证码生成和验证的领域接口
 * 
 * <AUTHOR>
 * @since 2025-12-04
 */
public interface CaptchaGateway {

    /**
     * 获取验证码
     */
    CaptchaResponse generateCaptcha(CaptchaRequest request);

    /**
     * 验证验证码
     */
    CaptchaVerifyResponse verifyCaptcha(CaptchaVerifyRequest request);

}
