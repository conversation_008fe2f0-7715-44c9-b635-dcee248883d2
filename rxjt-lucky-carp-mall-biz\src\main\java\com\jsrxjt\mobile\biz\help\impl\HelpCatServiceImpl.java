package com.jsrxjt.mobile.biz.help.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.api.help.Respone.HelpCatResponseDTO;
import com.jsrxjt.mobile.biz.help.HelpCatService;
import com.jsrxjt.mobile.domain.help.entity.HelpCatEntity;
import com.jsrxjt.mobile.domain.help.repository.HelpCatRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class HelpCatServiceImpl implements HelpCatService {
    private final HelpCatRepository helpCatRepository;
    /**
     * 获取启用的帮助分类列表
     *
     * @return
     */
    @Override
    public List<HelpCatResponseDTO> helpCatList() {
        List<HelpCatEntity> list = helpCatRepository.findAllByStatus();
        return BeanUtil.copyToList(list, HelpCatResponseDTO.class);
    }
}
