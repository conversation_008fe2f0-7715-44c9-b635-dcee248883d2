package com.jsrxjt.mobile.domain.order.service.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 通用分销应用三方推单样例订单信息构建器
 *
 * <AUTHOR>
 * @Date 2025/7/24 17:58
 */
@Component
@Slf4j
public class CommonDistributionOrderInfoBuilder extends DefaultOrderInfoBuilder {

    public CommonDistributionOrderInfoBuilder(BusinessIdGenerator businessIdGenerator, ConfigRepository configRepository) {
        super(businessIdGenerator, configRepository);
    }

    @Override
    public void supplementOrderInfo(OrderInfoEntity orderInfo, CreateOrderDTO request) {
        log.info("补充应用推单订单信息，订单号：{}", orderInfo.getOrderNo());
        // 补充第三方特有信息
        if (StrUtil.isNotBlank(request.getExternalOrderNo())) {
            orderInfo.setExternalOrderNo(request.getExternalOrderNo());
        }

        if (StrUtil.isNotBlank(request.getThirdId())) {
            orderInfo.setThirdId(request.getThirdId());
        }

        if (StrUtil.isNotBlank(request.getExternalShopId())) {
            orderInfo.setExternalShopId(request.getExternalShopId());
        }

        if (StrUtil.isNotBlank(request.getExternalShopUserId())) {
            orderInfo.setExternalShopUserId(request.getExternalShopUserId());
        }

        if (StrUtil.isNotBlank(request.getTradeNo())) {
            orderInfo.setTradeNo(request.getTradeNo());
        }

        if(request.getExternalOrderExpireTime() != null) {
            orderInfo.setPayExpireTimestamp(Long.valueOf(request.getExternalOrderExpireTime()));
        }
        if(request.getExternalPayResultUrl() != null) {
            orderInfo.setExternalPayResultUrl(request.getExternalPayResultUrl());
        }
        if(request.getOrderDetailUrl() != null) {
            orderInfo.setOrderDetailUrl(request.getOrderDetailUrl());
        }
        if (StrUtil.isNotBlank(request.getDistTradeNo())) {
            orderInfo.setDistTradeNo(request.getDistTradeNo());
        }
        // 设置特定的订单渠道
        orderInfo.setOrderChannel(OrderChannelEnum.DISTRIBUTION_CENTER.getCode());
        if (Objects.equals(orderInfo.getAppFlag(),"RXQQG")) {
            // 瑞祥全球购应用独立对接，单独区分渠道
            orderInfo.setOrderChannel(OrderChannelEnum.RX_GLOBAL_MALL.getCode());
            String url = request.getOrderDetailUrl();
            // 去掉url统一拼接的多余参数
            if (StringUtils.isNotBlank(url)) {
                int firstQ = url.indexOf("?");
                if (firstQ != -1) {
                    int secondQ = url.indexOf("?", firstQ + 1);
                    if (secondQ != -1) {
                        url = url.substring(0, secondQ);  // 只保留到第二个 ? 之前
                    }
                }
                orderInfo.setOrderDetailUrl(url);
            }

        }
        log.info("第三方推单订单信息补充完成，外部订单号：{}", orderInfo.getExternalOrderNo());
    }
} 