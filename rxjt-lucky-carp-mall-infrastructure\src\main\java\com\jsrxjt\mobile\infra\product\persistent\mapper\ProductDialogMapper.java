package com.jsrxjt.mobile.infra.product.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductDialogPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 核销页弹框mapper
 * <AUTHOR>
 * @date 2025/09/12
 */
@Mapper
public interface ProductDialogMapper extends CommonBaseMapper<ProductDialogPO> {
    @Select("SELECT * FROM product_dialog WHERE product_spu_id = #{productSpuId} and product_type = #{productType} and dialog_type = #{dialogType}")
    List<ProductDialogPO> selectDialogWithDel(@Param("productSpuId") Long productSpuId, @Param("productType") Integer productType, @Param("dialogType") Integer dialogType);
}
