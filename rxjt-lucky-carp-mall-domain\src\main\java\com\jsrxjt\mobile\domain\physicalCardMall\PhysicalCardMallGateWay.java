package com.jsrxjt.mobile.domain.physicalCardMall;

import java.util.Map;

/**
 * @Description: 实体卡商城网关接口
 * @Author: ywt
 * @Date: 2025-10-23 10:30
 * @Version: 1.0
 */
public interface PhysicalCardMallGateWay {
    boolean checkSign(Map<String, Object> map);

    /**
     * 实体卡商城加密userId
     * @param userId
     * @return
     */
    String encodeUserId(Long userId);
    /**
     * 增加实体卡商城基本参数
     * @param object
     * @return
     */
    Map<String, Object> addCommonParams(Object object);

    /**
     * 通知实体卡商城支付成功
     * @param requestData
     * @return
     */
    boolean notifyPaySuccess(Map<String, Object> requestData);

    /**
     * 通知实体卡商城退款成功
     * @param requestData
     * @return
     */
    boolean notifyRefundSuccess(Map<String, Object> requestData);
}
