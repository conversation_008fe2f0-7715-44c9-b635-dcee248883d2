package com.jsrxjt.mobile.api.xcy.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by jeffery.yang on 2023/12/21 17:02
 *
 * @description: 退款入参
 * @author: jeffery.yang
 * @date: 2023/12/21 17:02
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XcyMallRefundRequestDTO extends XcyMallBaseRequest {

	/**
	 * 这里传商城主单号
	 * <p>required</p>
	 */
	private String tradeNo;

	/**
	 * 商城退款单号
	 * <p>required</p>
	 */
	private String refundNo;

	/**
	 * 退款金额。单位元，保留2位小数。
	 * <p>required</p>
	 */
	private String refundAmount;

	/**
	 * 退款原因，固定不变：食行生鲜退款?
	 * <p>required</p>
	 */
	private String refundDesc;

	/**
	 * 退款金额成本价
	 */
	private String refundCostAmount;
}
