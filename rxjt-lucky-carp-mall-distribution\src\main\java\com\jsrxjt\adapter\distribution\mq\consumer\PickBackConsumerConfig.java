package com.jsrxjt.adapter.distribution.mq.consumer;

import com.jsrxjt.common.core.util.SimpleUniqueMacIdGenerator;
import lombok.SneakyThrows;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;


/**
 * 提货券回调配置
 * <AUTHOR>
 * @date 2025/11/13
 */
@Configuration
@RefreshScope
public class PickBackConsumerConfig {


  @SneakyThrows
  @Bean(destroyMethod = "close")
  public PushConsumer pickBackConsumer(ClientConfiguration clientConfiguration,
                                         PickBackMessageListener pickBackMessageListener) {
    FilterExpression filterExpression = new FilterExpression("*", FilterExpressionType.TAG);
    String uniqueConsumerGroup = "pick_scan_back_group";
    String topic = "pick_scan_back_topic";
    return ClientServiceProvider.loadService().newPushConsumerBuilder()
        .setClientConfiguration(clientConfiguration)
        .setConsumerGroup(uniqueConsumerGroup)
        .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
        .setMessageListener(pickBackMessageListener)
        .build();
  }
}