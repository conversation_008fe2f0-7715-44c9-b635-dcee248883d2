package com.jsrxjt.mobile.biz.birth.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.birth.request.BirthCouponRequestDTO;
import com.jsrxjt.mobile.api.ticket.types.TicketTypeEnum;
import com.jsrxjt.mobile.biz.birth.CustomerBirthCouponService;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.gateway.FuliquanShopTicketGateway;
import com.jsrxjt.mobile.domain.customer.gateway.request.SendTicketRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.SendTicketResponseDTO;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.ticket.entity.TicketDeliveryEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketDeliveryRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketRepository;
import com.jsrxjt.mobile.domain.ticket.service.TicketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerBirthCouponServiceImpl implements CustomerBirthCouponService {

    private final CustomerRepository customerRepository;
    private final FuliquanShopTicketGateway fuliquanShopTicketGateway;
    private final TicketRepository ticketRepository;
    private final TicketService ticketService;
    private final TicketDeliveryRepository ticketDeliveryRepository;
    private final DistributedLock distributedLock;
    private final String BIRTHDAY_TICKET_LOCK_PREFIX = "birthday_tickets_send_lock:";

    /**
     * 领取会员生日券（300-100）
     *
     * @return
     */
    @Override
    public void receiveBirthCoupon(BirthCouponRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        String lockKey = BIRTHDAY_TICKET_LOCK_PREFIX + customerId;
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey);
            if (!lockAcquired) {
                throw new BizException("获取发放生日优惠券的锁失败，customerId：" + customerId);
            }
            //判断是否已经领取
            CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
            if (customerEntity == null || customerEntity.getDelFlag() == 1 || customerEntity.getStatus() != 1) {
                throw new BizException("该会员账号不存在或注销");
            }
            String birthday = customerEntity.getBirthday();
            if (StringUtils.isEmpty(birthday)) {
                throw new BizException("请您先设置生日");
            }
            Date jdkDate = DateUtil.parse(birthday).toJdkDate();
            if (DateTime.of(jdkDate).month() != DateTime.of(new Date()).month()) {
                //前端用于特殊展示
                throw BizException.BIRTH_COUPON_NOT_THIS_MONTH;
            }

            /*Long vipId = customerEntity.getVipId();
            SendTicketRequestDTO requestDTO = new SendTicketRequestDTO();
            requestDTO.setVipId(vipId);
            requestDTO.setTimestamp(Instant.now().getEpochSecond());
            requestDTO.setNonce(IdUtil.fastSimpleUUID());
            SendTicketResponseDTO sendTicketResponseDTO = fuliquanShopTicketGateway.sendCoupon(requestDTO);
            if (sendTicketResponseDTO == null || !sendTicketResponseDTO.getSendCoupon().equals("1")) {
                throw new BizException("领取生日券失败");
            }*/
            TicketDeliveryEntity ticketDeliveryEntity = ticketDeliveryRepository.getBirthdayTicket(customerId);
            if (Objects.nonNull(ticketDeliveryEntity)) {
                throw new BizException("您已领取过，不可重复领取");
            }
            TicketEntity ticket = ticketRepository.getTicketById(request.getTicketId());
            if (Objects.isNull(ticket)) {
                throw new BizException("您来迟了，生日券已发放完");
            }
            if (ticket.getTicketType() != TicketTypeEnum.RX_SHOP_COUPON.getCode().intValue()) {
                throw new BizException("领取失败，请联系客服");
            }
            List<String> ticketList = ticketService.receiveBirthCoupon(ticket.getCenterTicketId(), request.getNumber(), request.getTimeStamp(), request.getNonce());
            if (CollectionUtil.isEmpty(ticketList)) {
                throw new BizException("生日券已发放完，下次来早点哦");
            }
            List<TicketDeliveryEntity> ticketDeliveries = new ArrayList<>();
            ticketList.forEach(item -> {
                TicketDeliveryEntity entity = new TicketDeliveryEntity();
                entity.setCustomerId(customerId);
                entity.setTicketType(TicketTypeEnum.RX_SHOP_COUPON.getCode());
                entity.setStatus((byte) 1);
                entity.setCenterCouponId(ticket.getCenterCouponId());
                entity.setCenterTicketCouponNumber(item);
                entity.setTicketId(ticket.getTicketId());
                entity.setTicketName(ticket.getTicketName());
                entity.setBrandName(ticket.getBrandName());
                entity.setOffsetLogo(ticket.getOffsetLogo());
                entity.setIsBirthdayTicket(1);
                entity.setCreateTime(new Date());
                entity.setThresholdAmount(ticket.getThresholdAmount());
                entity.setDiscountAmount(ticket.getDiscountAmount());
                entity.setTicketCatCode(ticket.getTicketCatCode());

                if (Objects.nonNull(ticket.getTicketValidDate())) {
                    // 1. 获取当前时间（Date类型）
                    Date now = new Date();
                    // 2. 使用Calendar处理日期增减
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(now); // 将Calendar设置为当前时间
                    calendar.add(Calendar.DAY_OF_YEAR, ticket.getTicketValidDate()); // 增加N天
                    // 3. 转换回Date类型
                    Date afterNdays = calendar.getTime();
                    entity.setTicketValidDate(afterNdays);
                }
                ticketDeliveries.add(entity);
            });
            ticketDeliveryRepository.batchSave(ticketDeliveries);
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }
}
