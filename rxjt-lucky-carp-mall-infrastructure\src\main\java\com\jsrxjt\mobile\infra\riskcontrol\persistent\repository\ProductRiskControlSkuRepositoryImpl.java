package com.jsrxjt.mobile.infra.riskcontrol.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.domain.riskcontrol.entity.SpuRiskFilterEntity;
import com.jsrxjt.mobile.domain.riskcontrol.repository.ProductRiskControlSkuRepository;
import com.jsrxjt.mobile.infra.riskcontrol.persistent.mapper.ProductRiskControlSkuMapper;
import com.jsrxjt.mobile.infra.riskcontrol.persistent.po.ProductRiskControlSkuPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 产品风控策略sku基础层
 * @Author: ywt
 * @Date: 2025-09-18 10:56
 * @Version: 1.0
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ProductRiskControlSkuRepositoryImpl implements ProductRiskControlSkuRepository {
    private final ProductRiskControlSkuMapper productRiskControlSkuMapper;

    //获取风控禁用的spu
    @Override
    public List<SpuRiskFilterEntity> getRiskDisableProducts(String accountName, Long companyId) {
        List<ProductRiskControlSkuPO> list = productRiskControlSkuMapper.getRiskDisableProducts(accountName, companyId,null,null);
        List<ProductRiskControlSkuPO> newList = list.stream().filter(item -> Objects.nonNull(item.getProductSpuId())).collect(Collectors.toList());
        return BeanUtil.copyToList(newList, SpuRiskFilterEntity.class);
    }

    @Override
    public SpuRiskFilterEntity getRiskDisableProduct(String accountName, Long companyId, Long productSpuId, Integer productType) {
        List<ProductRiskControlSkuPO> list = productRiskControlSkuMapper.getRiskDisableProducts(accountName, companyId, productSpuId, productType);
        if (!list.isEmpty()) {
            return BeanUtil.copyProperties(list.get(0), SpuRiskFilterEntity.class);
        }
        return null;
    }
}
