package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class CouponPackageListResponseDTO {

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "品牌卡券数量")
    private Integer couponNum;

    @Schema(description = "品牌logo链接")
    private String brandLogo;

    @Schema(description = "卡券列表")
    private List<CouponPackageResponseDTO> couponList;

}
