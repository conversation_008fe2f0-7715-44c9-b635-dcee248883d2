package com.jsrxjt.mobile.biz.version.impl;


import com.jsrxjt.mobile.biz.version.AppVersionService;
import com.jsrxjt.mobile.domain.version.entity.AppVersionEntity;
import com.jsrxjt.mobile.domain.version.repository.AppVersionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppVersionServiceImpl implements AppVersionService {
    private final AppVersionRepository appVersionRepository;

    /**
     * 获取最新版本
     *
     * @param appType 1android 2ios
     * @return
     */
    @Override
    public AppVersionEntity getLatestVersion(Byte appType) {
        AppVersionEntity appVersionEntity = appVersionRepository.getLatestVersion(appType);
        return appVersionEntity;
    }
}
