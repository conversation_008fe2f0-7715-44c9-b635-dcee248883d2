package com.jsrxjt.mobile.biz.homeScanPay.service.impl;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.api.scanPay.types.PosV2OrderStatusTypeEnum;
import com.jsrxjt.mobile.biz.homeScanPay.service.HomeScanOrderService;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.TradeOrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.repository.TradeOrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:首页扫码付订单接口实现类
 * <AUTHOR>
 * @date 2025/11/07
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HomeScanOrderServiceImpl implements HomeScanOrderService {

    private final OrderRepository orderRepository;

    private final TradeOrderRepository tradeOrderRepository;

    @Override
    public OrderInfoEntity scanOrderStatusUpdate(String tradeNo, PosV2OrderStatusTypeEnum status) {
        if (status == null || tradeNo == null){
            log.error("扫码付订单状态为空");
            return null;
        }
        if (status == PosV2OrderStatusTypeEnum.PAYING || status == PosV2OrderStatusTypeEnum.WAIT){
            return null;
        }
        OrderInfoEntity existOrderEntity = orderRepository.findByTradeNoAndAppFlag(tradeNo, DistChannelType.SCANPAY.name());
        if (existOrderEntity == null){
            log.error("未找到订单信息");
            return null;
        }
        OrderInfoEntity updateParam = new OrderInfoEntity();
        // 设置查询条件
        updateParam.setId(existOrderEntity.getId());
        updateParam.setCustomerId(existOrderEntity.getCustomerId());
        switch (status){
            case FAIL:
            case CLOSED:
                // 更新订单状态为关闭
                updateParam.setOrderStatus(OrderStatusEnum.TRADE_CLOSED.getCode());
                updateParam.setPaymentStatus(PaymentStatusEnum.UNPAID.getCode());
                updateParam.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
                updateParam.setModTime(LocalDateTime.now());
                updateParam.setRemark("扫码付订单交易失败");
            case FULL:
                // 更新订单状态为待发货
                updateParam.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
                updateParam.setPaymentStatus(PaymentStatusEnum.PAID.getCode());
                updateParam.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
                updateParam.setPaymentTime(LocalDateTime.now());
                updateParam.setModTime(LocalDateTime.now());
                // 更新支付相关信息
                List<TradeOrderInfoEntity> tradeOrderInfoEntities =
                        tradeOrderRepository.listTradeOrderInfoByTradeNoAndOrderNo(
                                existOrderEntity.getTradeNo(), existOrderEntity.getOrderNo()
                        );
                String actualPayType = Optional.ofNullable(tradeOrderInfoEntities)
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(TradeOrderInfoEntity::getCardTradeType)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.joining(";"));
                updateParam.setActualPayType(actualPayType);
         }
        OrderStatusEnum orderStatus = OrderStatusEnum.getByCode(existOrderEntity.getOrderStatus());
        orderRepository.updateOrderCheckCurrentStatus(updateParam, PaymentStatusEnum.getByCode(existOrderEntity.getPaymentStatus()), orderStatus);
        log.info("订单支付信息更新成功，订单号：{}，状态：{}，交易号：{}",
                existOrderEntity.getOrderNo(), updateParam.getOrderStatus(), tradeNo);
        return updateParam;
    }
}
