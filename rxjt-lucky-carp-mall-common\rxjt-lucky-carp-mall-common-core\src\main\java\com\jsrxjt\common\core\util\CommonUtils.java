package com.jsrxjt.common.core.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtils {

    /**
     * 校验密码格式
     */
    public static boolean checkPassword(String password) {
        if (password.length() != 6) {
            return false;
        } else {
            if (isRepeatNum(password) || isConsecutiveNum(password) || isReverseConsecutiveNum(password)){
                return false;
            }
            return true;
        }
    }

    /**
     * 重复6位数字
     * @param str
     * @return
     */
    public static boolean isRepeatNum(String str){
        Pattern pattern=Pattern.compile("(\\d)\\1{5}");
        Matcher match=pattern.matcher(str);
        return match.matches();
    }

    /**
     * 连续6位数字-顺序
     * @param str
     * @return
     */
    public static boolean isConsecutiveNum(String str){
        Pattern pattern=Pattern.compile("(0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)){5}\\d");
        Matcher match=pattern.matcher(str);
        return match.matches();
    }

    /**
     * 连续6位数字-倒序
     * @param str
     * @return
     */
    public static boolean isReverseConsecutiveNum(String str) {
        Pattern pattern = Pattern.compile("(9(?=8)|8(?=7)|7(?=6)|6(?=5)|5(?=4)|4(?=3)|3(?=2)|2(?=1)|1(?=0)){5}\\d");
        Matcher match = pattern.matcher(str);
        return match.matches();
    }

    /**
     * 生成指定格式的文件名
     * 格式: IMG + 年月日 + 10位随机数
     * 示例: IMG202508118529637412
     */
    public static String generateImageName() {
        // 获取当前日期并格式化为年月日
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = dateFormat.format(new Date());

        // 生成10位随机数
        Random random = new Random();
        long randomNum = (long)(random.nextDouble() * 1_000_000_000L);
        String randomStr = String.format("%010d", randomNum);

        return "IMG" + dateStr + randomStr;
    }

    /**
     * 验证是否为允许的图片文件扩展名
     * @param extension 文件扩展名
     * @return 是否为有效的图片扩展名
     */
    public static boolean isValidImageExtension(String extension) {
        if (extension == null || extension.isEmpty()) {
            return false;
        }
        // 转换为小写进行比较
        String lowerExtension = extension.toLowerCase();
        // 允许的图片扩展名白名单
        return lowerExtension.equals(".jpg") ||
                lowerExtension.equals(".jpeg") ||
                lowerExtension.equals(".png") ||
                lowerExtension.equals(".gif");
    }
}
