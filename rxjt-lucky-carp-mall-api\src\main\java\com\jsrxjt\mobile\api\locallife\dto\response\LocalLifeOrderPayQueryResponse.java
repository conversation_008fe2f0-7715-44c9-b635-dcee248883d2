package com.jsrxjt.mobile.api.locallife.dto.response;

import com.jsrxjt.mobile.api.locallife.dto.LocalLifeBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LocalLifeOrderPayQueryResponse extends LocalLifeBaseDTO {

    private String orderNo;

    private String flqOrderNo;

    private String tradeStatus;

    private String tradeTime;
}
