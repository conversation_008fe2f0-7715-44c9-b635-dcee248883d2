<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.ticket.persistent.mapper.GiftTicketOrderMapper">

    <!-- 批量插入赠券订单 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO gift_ticket_order (
            ticket_order_no,
            order_id,
            order_no,
            external_order_no,
            ticket_type,
            customer_id,
            push_status,
            center_coupon_id,
            center_ticket_coupon_number,
            ticket_id,
            ticket_name,
            spec_pic_url,
            ticket_brand_id,
            should_num,
            actual_num,
            push_error_msg,
            create_time,
            create_id,
            mod_time,
            mod_id,
            del_flag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.ticketOrderNo},
                #{item.orderId},
                #{item.orderNo},
                #{item.externalOrderNo},
                #{item.ticketType},
                #{item.customerId},
                #{item.pushStatus},
                #{item.centerCouponId},
                #{item.centerTicketCouponNumber},
                #{item.ticketId},
                #{item.ticketName},
                #{item.specPicUrl},
                #{item.ticketBrandId},
                #{item.shouldNum},
                #{item.actualNum},
                #{item.pushErrorMsg},
                #{item.createTime},
                #{item.createId},
                #{item.modTime},
                #{item.modId},
                #{item.delFlag}
            )
        </foreach>
    </insert>

</mapper>
