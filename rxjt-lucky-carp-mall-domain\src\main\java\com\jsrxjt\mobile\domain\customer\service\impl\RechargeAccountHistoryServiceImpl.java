package com.jsrxjt.mobile.domain.customer.service.impl;

import com.jsrxjt.mobile.domain.customer.dp.RechargeAccount;
import com.jsrxjt.mobile.domain.customer.entity.RechargeAccountHistoryEntity;
import com.jsrxjt.mobile.domain.customer.repository.RechargeAccountHistoryRepository;
import com.jsrxjt.mobile.domain.customer.service.RechargeAccountHistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RechargeAccountHistoryServiceImpl implements RechargeAccountHistoryService {

    private final RechargeAccountHistoryRepository repository;

    @Override
    public RechargeAccountHistoryEntity save(RechargeAccount account) {
        RechargeAccountHistoryEntity entity = new RechargeAccountHistoryEntity();
        BeanUtils.copyProperties(account, entity);
        List<RechargeAccountHistoryEntity> list = repository.findByCriteria(entity);
        if (list != null && !list.isEmpty()) {
            Long id = list.get(0).getId();
            entity.setId(id);
        }
        return repository.save(entity);
    }
}
