package com.jsrxjt.mobile.infra.gateway.distribution.adapter.eleme;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.util.SpringContextHolder;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

 /**
 * 饿了么分销渠道适配器
 * <AUTHOR>
 * @since 2025/12/9
 */
@Component
@Slf4j
public class ELeMeDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

    public ELeMeDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                           DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getEleme();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.ELEME;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            if (StringUtils.isBlank(request.getUserId())) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID不能为空")
                        .build();
            }
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("userCode", request.getUserId());

            // 检查是否是特定环境
            if (isProdProfile()){
                params.put("mobile", request.getMobile());
            } else {
                // 使用指定手机号码才能进入饿了么测试环境
                params.put("mobile", "11405196411");
            }

            // 添加公共参数和签名
            otherAddCommonParams(params);

            String baseUrl = config.getBaseUrl() + config.getApiPath() + config.getAccessPath();
            String url = baseUrl + "?" + httpClientGateway.buildUrlParams(params);

            return DistAccessResponse.builder()
                    .success(true)
                    .redirectUrl(url)
                    .build();
        } catch (Exception e) {
            log.error("饿了么免登接入异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("饿了么免登接入异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        try {
            // 校验必填参数
            if (StringUtils.isBlank(request.getDistOrderNo()) || StringUtils.isBlank(request.getOrderNo())
                    || request.getTradeAmount() == null) {
                return DistPaidNotifyResponse.builder()
                        .success(false)
                        .status(400)
                        .message("必填参数不能为空")
                        .build();
            }

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("transactionId", request.getDistOrderNo());
            params.put("outTradeNo", request.getOrderNo());
            params.put("payAmount", request.getTradeAmount().multiply(new BigDecimal("100")).intValue());
            params.put("payStatus", "SUCCESS");

            // 添加公共参数和签名
            otherAddCommonParams(params);

            // 发送请求
            String url = config.getBaseUrl() + config.getApiPath() + config.getCashierCallbackPath();
            String response = httpClientGateway.doPostJson(url, JSONUtil.toJsonStr(params), config.getConnectTimeout(),
                    config.getReadTimeout());
            JSONObject result = JSON.parseObject(response);

            // 解析响应
            if (Objects.equals(result.getInteger(CODE_KEY), 200)) {
                return DistPaidNotifyResponse.builder()
                        .success(true)
                        .status(SUCCESS_CODE)
                        .message(result.getString(MESSAGE_KEY))
                        .build();
            } else {
                return DistPaidNotifyResponse.builder()
                        .success(false)
                        .status(result.getInteger(CODE_KEY))
                        .message(result.getString(MESSAGE_KEY))
                        .build();
            }
        } catch (Exception e) {
            log.error("{}支付回调通知异常: {}", request.getChannelType().name(), e.getMessage(), e);
            return DistPaidNotifyResponse.builder()
                    .success(false)
                    .status(500)
                    .message(request.getChannelType().name() + "支付回调通知异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        return DistRefundResultNotifyResponse.builder()
                .success(true)
                .status(SUCCESS_CODE)
                .build();
    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        return null;
    }

     private boolean isProdProfile() {
         Environment environment = SpringContextHolder.getBean(Environment.class);
         String[] activeProfiles = environment.getActiveProfiles();
         log.debug("Active profiles: {}", Arrays.toString(activeProfiles));
         for (String activeProfile : activeProfiles) {
             if ("prod".equals(activeProfile)) {
                 return true;
             }
         }
         return false;
     }
}
