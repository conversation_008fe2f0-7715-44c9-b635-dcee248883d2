package com.jsrxjt.mobile.infra.physicalCardMall.gatewayimpl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.util.aes.AESUtils;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.physicalCardMall.PhysicalCardMallGateWay;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import com.jsrxjt.mobile.infra.gateway.distribution.util.SignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 实体卡商城网关
 * @Author: ywt
 * @Date: 2025-10-23 10:35
 * @Version: 1.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class PhysicalCardMallGateWayImpl implements PhysicalCardMallGateWay {
    protected final DistributionConfig distributionConfig;
    private final AESUtils aesUtils;
    private final HttpClientGateway httpClientGateway;

    @Override
    public boolean checkSign(Map<String, Object> map) {
        String signature = String.valueOf(map.get("signature"));
        map.remove("signature");
        String checkSign = SignUtil.sign(map, distributionConfig.getPhysicalcardmall().getAppSecret());
        if (!signature.equals(checkSign)) {
            log.error("实体卡商城请求sign：{}，福鲤圈sign:{}", signature, checkSign);
            return false;
        }
        return true;
    }

    @Override
    public String encodeUserId(Long userId) {
        String userCode = null;
        try {
            userCode = aesUtils.encryptToBase64(String.valueOf(userId),
                    distributionConfig.getPhysicalcardmall().getAesSecretKey(),
                    distributionConfig.getPhysicalcardmall().getAesIv(),
                    16);
            userCode = URLEncoder.encode(userCode, StandardCharsets.UTF_8);
        } catch (Exception e) {
            return null;
        }
        return userCode;
    }

    @Override
    public Map<String, Object> addCommonParams(Object object) {
        Map<String, Object> params = JSONObject.parseObject(JSONUtil.toJsonStr(object), Map.class);
        params.put("nounce", getNounce());
        params.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        params.put("signature", SignUtil.sign(params, distributionConfig.getPhysicalcardmall().getAppSecret()));
        return params;
    }

    @Override
    public boolean notifyPaySuccess(Map<String, Object> requestData) {
        requestData.put("nounce", getNounce());
        requestData.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        requestData.put("signature", SignUtil.sign(requestData, distributionConfig.getPhysicalcardmall().getAppSecret()));
        log.info("通知实体卡商城支付成功请求参数：{}", requestData);
        try {
            String resp = httpClientGateway.doPost(distributionConfig.getPhysicalcardmall().getBaseUrl() +
                            distributionConfig.getPhysicalcardmall().getCashierCallbackPath(),
                    requestData,
                    distributionConfig.getPhysicalcardmall().getConnectTimeout(),
                    distributionConfig.getPhysicalcardmall().getReadTimeout());
            if (StringUtils.isEmpty(resp)) {
                return false;
            }
            JSONObject json = JSONObject.parseObject(resp);
            return Objects.equals(200, json.get("status"));
        } catch (Exception e) {
            log.error("通知实体卡商城支付成功异常", e);
            return false;
        }
    }

    @Override
    public boolean notifyRefundSuccess(Map<String, Object> requestData) {
        requestData.put("nounce", getNounce());
        requestData.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        requestData.put("signature", SignUtil.sign(requestData, distributionConfig.getPhysicalcardmall().getAppSecret()));
        log.info("通知实体卡商城退款成功请求参数：{}", requestData);
        try {
            String resp = httpClientGateway.doPost(distributionConfig.getPhysicalcardmall().getBaseUrl() +
                            distributionConfig.getPhysicalcardmall().getRefundCallbackPath(),
                    requestData,
                    distributionConfig.getPhysicalcardmall().getConnectTimeout(),
                    distributionConfig.getPhysicalcardmall().getReadTimeout());
            if (StringUtils.isEmpty(resp)) {
                return false;
            }
            JSONObject json = JSONObject.parseObject(resp);
            return Objects.equals(200, json.get("status"));
        } catch (Exception e) {
            log.error("通知实体卡商城退款成功异常", e);
            e.printStackTrace();
            return false;
        }
    }

    private String getNounce() {
        SecureRandom secureRandom = new SecureRandom();
        byte[] randomBytes = new byte[8];
        secureRandom.nextBytes(randomBytes);
        return bytesToHex(randomBytes);
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}
