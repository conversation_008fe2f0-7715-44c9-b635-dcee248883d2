package com.jsrxjt.mobile.api.coupon.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: SKU的其他信息，如活动信息等
 * @Author: ywt
 * @Date: 2025-07-15 10:51
 * @Version: 1.0
 */
@Data
public class CouponSkuExtraInfoRequestDTO {
    @Schema(description = "卡券的spuid")
    @NotNull(message = "卡券spuid为空错误")
    private Long couponSpuId;
    @Schema(description = "卡券的skuid")
    @NotNull(message = "卡券skuid为空错误")
    private Long couponSkuId;
    @Schema(description = "三级地址id")
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
}
