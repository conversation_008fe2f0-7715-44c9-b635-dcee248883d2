package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 售后详情响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@Setter
@Schema(description = "售后详情响应DTO")
public class AfterSaleDetailResponseDTO {

    @Schema(description = "售后状态: 1-待审核 20-审核通过 30-审核驳回 32-退款驳回 33-售后撤销 34-售后完成")
    private Integer afterSaleStatus;

    @Schema(description = "退款状态: 0-待退款 1-退款中 2-退款成功 3-退款失败 4-拒绝退款")
    private Integer refundStatus;

    @Schema(description = "售后类型: 1-部分退款 2-全额退款")
    private Integer afterSaleType;
    
    @Schema(description = "退款总金额")
    private BigDecimal refundTotalAmount;
    
    @Schema(description = "退款账户信息")
    private List<RefundAccountDTO> refundAccountList;
    
    @Schema(description = "售后进度提示")
    private List<AfterSaleProgressDTO> progressList;
    
    @Schema(description = "进度详情")
    private List<ProgressDetailDTO> progressDetails;
    
    @Schema(description = "退款信息")
    private RefundInfoDTO refundInfo;

    @Schema(description = "是否显示删除按钮 true 可显示，false 不显示")
    private Boolean canDelete;
    
    /**
     * 售后进度DTO
     */
    @Getter
    @Setter
    @Schema(description = "售后进度DTO")
    public static class AfterSaleProgressDTO {
        
        @Schema(description = "步骤名称")
        private String stepName;
        
        @Schema(description = "是否完成")
        private Boolean completed;
        
        @Schema(description = "是否当前步骤")
        private Boolean current;
    }
    
    /**
     * 售后进度详细信息
     */
    @Getter
    @Setter
    @Schema(description = "售后进度详细信息")
    @AllArgsConstructor
    public static class ProgressDetailDTO  {
        
        @Schema(description = "操作类型描述")
        private String operationDesc;
        
        @Schema(description = "操作详情")
        private String operationDetail;

        @Schema(description = "操作时间")
        private LocalDateTime operationTime;
    }
    
    /**
     * 退款信息DTO
     */
    @Getter
    @Setter
    @Schema(description = "退款信息DTO")
    public static class RefundInfoDTO {
        
        @Schema(description = "商品信息")
        private ProductInfoDTO productInfo;
        
        @Schema(description = "退款金额")
        private BigDecimal refundAmount;
        
        @Schema(description = "申请件数")
        private Integer applyQuantity;
        
        @Schema(description = "申请时间")
        private LocalDateTime applyTime;
        
        @Schema(description = "售后单号")
        private String afterSaleNo;
        
        @Schema(description = "订单编号")
        private String orderNo;
        
        @Schema(description = "退回原因")
        private String refundReason;
        
        /**
         * 商品信息DTO
         */
        @Getter
        @Setter
        @Schema(description = "商品信息DTO")
        public static class ProductInfoDTO {
            
            @Schema(description = "商品名称")
            private String productName;
            
            @Schema(description = "品牌名称")
            private String brandName;
            
            @Schema(description = "商品图标")
            private String productLogo;
            
            @Schema(description = "面额")
            private BigDecimal faceAmount;
            
            @Schema(description = "销售价格")
            private BigDecimal sellPrice;
            
            @Schema(description = "数量")
            private Integer quantity;

            @Schema(description = "扁平化商品类型")
            private Integer flatProductType;
        }
    }
}