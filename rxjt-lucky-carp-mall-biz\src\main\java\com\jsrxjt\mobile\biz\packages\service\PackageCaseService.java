package com.jsrxjt.mobile.biz.packages.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.packages.dto.request.PackageInfoRequestDTO;
import com.jsrxjt.mobile.api.packages.dto.request.PackageSkuExtraInfoRequestDTO;
import com.jsrxjt.mobile.api.packages.dto.response.PackageInfoResponseDTO;
import com.jsrxjt.mobile.api.packages.dto.response.PackageSkuExtralInfoResponseDTO;
import com.jsrxjt.mobile.api.packages.dto.response.PackageSkuInfoResponseDTO;

import java.util.List;

/**
 * @Description: 套餐服务接口
 * @Author: ywt
 * @Date: 2025-05-09 17:39
 * @Version: 1.0
 */
public interface PackageCaseService {
    BaseResponse<PackageInfoResponseDTO> packageInfo(PackageInfoRequestDTO requestDTO);
    List<PackageSkuInfoResponseDTO> skusInfo(PackageInfoRequestDTO requestDTO);
    PackageSkuExtralInfoResponseDTO skuExtraInfo(PackageSkuExtraInfoRequestDTO requestDTO);
}
