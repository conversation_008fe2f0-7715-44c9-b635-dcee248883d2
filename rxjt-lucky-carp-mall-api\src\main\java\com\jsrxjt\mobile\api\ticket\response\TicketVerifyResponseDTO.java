package com.jsrxjt.mobile.api.ticket.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 券核销返回结果
 * <AUTHOR>
 * @Date 2025/9/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketVerifyResponseDTO {

    /**
     * 响应码 success成功
     */
    @Schema(description = "响应码 0成功 1失败")
    private int code;

    private String msg;

    private Object data;

    public static TicketVerifyResponseDTO success(Object data) {
        return new TicketVerifyResponseDTO(0,"成功",data);
    }

    public static TicketVerifyResponseDTO fail(String msg) {
        return new TicketVerifyResponseDTO(1,msg,null);
    }
}
