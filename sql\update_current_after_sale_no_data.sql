-- 刷新t_order表中的current_after_sale_no字段
-- 执行时间：2025-11-03
-- 作者：系统
-- 描述：根据现有售后单数据，更新订单表中的当前售后单号字段

-- 第一步：检查需要更新的数据量
SELECT 
    COUNT(DISTINCT o.id) as total_orders_with_after_sale,
    COUNT(DISTINCT a.order_id) as orders_have_after_sale,
    COUNT(*) as total_after_sale_records
FROM t_order o
LEFT JOIN t_after_sale a ON o.id = a.order_id AND a.del_flag = 0
WHERE o.del_flag = 0 
  AND o.after_sale_status IS NOT NULL;

-- 第二步：查看当前current_after_sale_no字段的数据情况
SELECT 
    COUNT(*) as total_orders,
    COUNT(current_after_sale_no) as orders_with_current_after_sale_no,
    COUNT(*) - COUNT(current_after_sale_no) as orders_need_update
FROM t_order 
WHERE del_flag = 0;

-- 第三步：预览将要更新的数据（前10条）
SELECT 
    o.id as order_id,
    o.order_no,
    o.after_sale_status,
    o.current_after_sale_no as old_current_after_sale_no,
    latest_after_sale.after_sale_no as new_current_after_sale_no,
    latest_after_sale.after_sale_status,
    latest_after_sale.create_time as latest_after_sale_time
FROM t_order o
INNER JOIN (
    SELECT 
        a1.order_id,
        a1.after_sale_no,
        a1.after_sale_status,
        a1.create_time,
        ROW_NUMBER() OVER (PARTITION BY a1.order_id ORDER BY a1.create_time DESC, a1.id DESC) as rn
    FROM t_after_sale a1
    WHERE a1.del_flag = 0
) latest_after_sale ON o.id = latest_after_sale.order_id AND latest_after_sale.rn = 1
WHERE o.del_flag = 0
  AND o.after_sale_status IS NOT NULL
  AND (o.current_after_sale_no IS NULL OR o.current_after_sale_no != latest_after_sale.after_sale_no)
ORDER BY o.id
LIMIT 10;

-- 第四步：执行数据更新
-- 注意：建议先在测试环境执行，确认无误后再在生产环境执行

UPDATE t_order o
INNER JOIN (
    SELECT 
        a1.order_id,
        a1.after_sale_no,
        ROW_NUMBER() OVER (PARTITION BY a1.order_id ORDER BY a1.create_time DESC, a1.id DESC) as rn
    FROM t_after_sale a1
    WHERE a1.del_flag = 0
) latest_after_sale ON o.id = latest_after_sale.order_id AND latest_after_sale.rn = 1
SET 
    o.current_after_sale_no = latest_after_sale.after_sale_no,
    o.mod_time = NOW()
WHERE o.del_flag = 0
  AND o.after_sale_status IS NOT NULL
  AND (o.current_after_sale_no IS NULL OR o.current_after_sale_no != latest_after_sale.after_sale_no);

-- 第五步：验证更新结果
SELECT 
    COUNT(*) as total_orders,
    COUNT(current_after_sale_no) as orders_with_current_after_sale_no,
    COUNT(*) - COUNT(current_after_sale_no) as orders_still_null
FROM t_order 
WHERE del_flag = 0 
  AND after_sale_status IS NOT NULL;

-- 第六步：检查更新后的数据样例（前10条）
SELECT 
    o.id as order_id,
    o.order_no,
    o.after_sale_status,
    o.current_after_sale_no,
    a.after_sale_status as current_after_sale_status,
    a.create_time as after_sale_create_time
FROM t_order o
LEFT JOIN t_after_sale a ON o.current_after_sale_no = a.after_sale_no AND a.del_flag = 0
WHERE o.del_flag = 0 
  AND o.current_after_sale_no IS NOT NULL
ORDER BY o.mod_time DESC
LIMIT 10;

-- 第七步：统计各种售后状态的订单数量
SELECT 
    o.after_sale_status,
    CASE o.after_sale_status
        WHEN 1 THEN '待审核'
        WHEN 20 THEN '审核通过'
        WHEN 30 THEN '审核驳回'
        WHEN 32 THEN '退款驳回'
        WHEN 33 THEN '售后撤销'
        WHEN 34 THEN '售后完成'
        ELSE '未知状态'
    END as after_sale_status_desc,
    COUNT(*) as order_count,
    COUNT(o.current_after_sale_no) as orders_with_after_sale_no
FROM t_order o
WHERE o.del_flag = 0 
  AND o.after_sale_status IS NOT NULL
GROUP BY o.after_sale_status
ORDER BY o.after_sale_status;

-- 第八步：检查是否有异常数据（售后单号不存在的情况）
SELECT 
    o.id as order_id,
    o.order_no,
    o.current_after_sale_no,
    'after_sale_not_found' as issue
FROM t_order o
LEFT JOIN t_after_sale a ON o.current_after_sale_no = a.after_sale_no AND a.del_flag = 0
WHERE o.del_flag = 0 
  AND o.current_after_sale_no IS NOT NULL
  AND a.after_sale_no IS NULL;
