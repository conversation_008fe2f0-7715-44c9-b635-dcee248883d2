package com.jsrxjt.mobile.biz.account.impl;

import com.jsrxjt.mobile.api.account.dto.request.AccountTransferDTO;
import com.jsrxjt.mobile.biz.account.TransferCaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR> Fengping
 * 旧的案例，为了演进的错误示范
 * @since 2024/4/19
 **/
@Service
@RequiredArgsConstructor
public class TransferAOImpl implements TransferCaseService {
    private static final String TOPIC_AUDIT_LOG = "TOPIC_AUDIT_LOG";
    private final AccountMapper accountDAO;
    private final RocketMQTemplate<String, String> rocketMQTemplate;
    private final YahooForexService yahooForex;
    @Override
    public Boolean transfer(AccountTransferDTO account, String targetCurrency) {
        Long sourceUserId = account.getSourceUserId();
        //入参校验
        if (sourceUserId == null) {
            throw new IllegalArgumentException("sourceUserId is null");
        }
        String targetAccountNumber = account.getTargetAccountNumber();
        if (targetAccountNumber == null) {
            throw new IllegalArgumentException("targetAccountNumber is null");
        }
        BigDecimal targetAmount = account.getTargetAmount();
        if (targetAmount == null) {
            throw new IllegalArgumentException("targetAmount is null");
        }

         // 1. 从数据库读取数据，忽略所有校验逻辑如账号是否存在等
         AccountPO sourceAccountDO = accountDAO.selectByUserId(sourceUserId);
         AccountPO targetAccountDO = accountDAO.selectByAccountNumber(targetAccountNumber);


        // 2. 业务参数校验
        if (!targetAccountDO.getCurrency().equals(targetCurrency)) {
            throw new InvalidCurrencyException();
        }

        // 3. 获取外部数据，并且包含一定的业务逻辑
        // exchange rate = 1 source currency = X target currency
        BigDecimal exchangeRate = BigDecimal.ONE;
        if (sourceAccountDO.getCurrency().equals(targetCurrency)) {
            exchangeRate = yahooForex.getExchangeRate(sourceAccountDO.getCurrency(), targetCurrency);
        }
        BigDecimal sourceAmount = targetAmount.divide(exchangeRate, RoundingMode.DOWN);

        // 4. 业务参数校验
        if (sourceAccountDO.getAvailable().compareTo(sourceAmount) < 0) {
            throw new InsufficientFundsException();
        }

        if (sourceAccountDO.getDailyLimit().compareTo(sourceAmount) < 0) {
            throw new DailyLimitExceededException();
        }

        // 5. 计算新值，并且更新字段
        BigDecimal newSource = sourceAccountDO.getAvailable().subtract(sourceAmount);
        BigDecimal newTarget = targetAccountDO.getAvailable().add(targetAmount);
        sourceAccountDO.setAvailable(newSource);
        targetAccountDO.setAvailable(newTarget);

        // 6. 更新到数据库
        accountDAO.updateById(sourceAccountDO);
        accountDAO.updateById(targetAccountDO);

        // 7. 发送审计消息
        String message = sourceUserId + "," + targetAccountNumber + "," + targetAmount + "," + targetCurrency;
        rocketMQTemplate.send(TOPIC_AUDIT_LOG, message);

        return true;
    }
}
