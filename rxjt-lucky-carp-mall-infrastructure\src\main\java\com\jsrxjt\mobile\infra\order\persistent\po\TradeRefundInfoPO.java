package com.jsrxjt.mobile.infra.order.persistent.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 交易退款信息表PO
 * <AUTHOR>
 * @since 2025/9/26
 */
@Data
@TableName("trade_refund_info")
public class TradeRefundInfoPO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 爷-业务订单号
     */
    private String orderNo;

    /**
     * 爷-外部业务订单号
     */
    private String outOrderNo;

    /**
     * 爷-交易订单ID
     */
    private Long tradeId;

    /**
     * 父-交易单号
     */
    private String tradeNo;

    /**
     * 父-退款单ID
     */
    private Long refundId;

    /**
     * 父-退款单
     */
    private String refundNo;

    /**
     * 父-外部退款单
     */
    private String outRefundNo;

    /**
     * 父-交易明细单ID
     */
    private Long tradeInfoId;

    /**
     * 父-交易明细单号
     */
    private String tradeInfoNo;

    /**
     * 父-外部交易明细单号
     */
    private String outTradeInfoNo;

    /**
     * 子-退款单号
     */
    private String refundInfoNo;

    /**
     * 支付通道 CARD 卡系统 WECHAT_PAY 微信支付
     */
    private String refundChannel;

    /**
     * 微信交易ID（退款需要使用该参数）
     */
    private String wxTransactionId;

    /**
     * 微信退款单号
     */
    private String wxRefundId;

    /**
     * 微信支付金额
     */
    private Long wxPayAmount;

    /**
     * 渠道号
     */
    private String chanId;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 门店号
     */
    private String storeId;

    /**
     * 终端号
     */
    private String termId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 微信退款状态 SUCCESS 成功 FAIL 失败 REFUNDING 退款中
     */
    private String wxRefundStatus;

    /**
     * 退款状态 SUCCESS 成功 FAIL 失败 REFUNDING 退款中
     */
    private String refundStatus;

    /**
     * 退款金额
     */
    private Long refundAmount;

    /**
     * 交易卡类型 RX_RED_CARD 商联商户 RX_BLACK_CARD 黑金商户 RX_WHITE_CARD 白金商户 RX_PICK_CARD 凭证商户
     */
    private String cardTradeType;

    /**
     * 业务卡类型 RXHK 瑞祥红卡 SL 商联卡 HJ 黑金卡 BJ 白金卡
     */
    private String cardBusinessType;

    /**
     * 售后时间
     */
    private LocalDateTime refundAt;

    /**
     * 微信退款时间
     */
    private LocalDateTime wxRefundAt;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 删除时间（逻辑删除）
     */
    @TableField("deleted_at")
    private LocalDateTime deletedAt;
}
