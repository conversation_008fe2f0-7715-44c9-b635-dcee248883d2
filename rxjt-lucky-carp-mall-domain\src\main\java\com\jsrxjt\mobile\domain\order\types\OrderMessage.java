package com.jsrxjt.mobile.domain.order.types;

import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import lombok.AllArgsConstructor;
import lombok.Value;

/**
 * 订单消息体
 * <AUTHOR>
 * @since 2025-08-27
 */
@Value
@AllArgsConstructor
public class OrderMessage {

    /**
     * 订单ID
     */
    Long orderId;
    /**
     * 订单编号
     */
    String orderNo;

    /**
     * 客户ID
     */
    Long customerId;

    public OrderMessage(OrderInfoEntity orderInfo) {
        this.orderId = orderInfo.getId();
        this.orderNo = orderInfo.getOrderNo();
        this.customerId = orderInfo.getCustomerId();
    }
}
