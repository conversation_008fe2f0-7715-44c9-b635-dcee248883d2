package com.jsrxjt.mobile.api.customer.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CardBalanceQueryRequest{

    @Schema(description = "卡号")
    @NotBlank(message = "卡号不能为空")
    private String cardNo;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;
}
