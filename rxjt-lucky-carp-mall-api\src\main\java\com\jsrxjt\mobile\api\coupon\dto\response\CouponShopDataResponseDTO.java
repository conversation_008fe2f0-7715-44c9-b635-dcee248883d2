package com.jsrxjt.mobile.api.coupon.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 自发券门店数据响应
 * @Author: ywt
 * @Date: 2025-05-08 10:19
 * @Version: 1.0
 */
@Data
public class CouponShopDataResponseDTO {
    @Schema(description = "门店总数量")
    private Integer count;
    @Schema(description = "门店列表")
    private List<CouponShopResponseDTO> shopList;
}
