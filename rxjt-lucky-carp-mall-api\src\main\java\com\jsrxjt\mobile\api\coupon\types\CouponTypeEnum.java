package com.jsrxjt.mobile.api.coupon.types;

import java.util.HashMap;
import java.util.Map;

/**
 * 卡管券类型枚举
 * <AUTHOR>
 * @date 2025/03/18
 */
public enum CouponTypeEnum {

    REGULAR(1, "普通卡券"),

    VIDEO(2, "视频直冲"),

    PINUC(3, "品诺卡券");

   // PINUC_RECHARGE(4, "品诺直冲");

    CouponTypeEnum(int type, String text) {
        this.type = type;
        this.text = text;
    }
    
    private int type;

    private String text;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    private static final Map<Integer, CouponTypeEnum> CODE_MAP = new HashMap<>();

    static {
        for (CouponTypeEnum type : CouponTypeEnum.values()) {
            CODE_MAP.put(type.getType(), type);
        }
    }

    public static CouponTypeEnum getByType(int type) {
        return CODE_MAP.get(type);
    }

}
