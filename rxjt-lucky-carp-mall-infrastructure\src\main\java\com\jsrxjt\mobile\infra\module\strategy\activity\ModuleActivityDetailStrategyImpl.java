package com.jsrxjt.mobile.infra.module.strategy.activity;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.api.module.annotation.ModuleDetailTypeHandler;
import com.jsrxjt.mobile.api.module.types.ModuleDetailType;
import com.jsrxjt.mobile.domain.module.entity.ModuleDetailEntity;
import com.jsrxjt.mobile.domain.module.service.ModuleDetailStrategy;
import com.jsrxjt.mobile.infra.module.persistent.mapper.PageInfoMapper;
import com.jsrxjt.mobile.infra.module.persistent.po.PageInfoPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 *  活动页
 * <AUTHOR>
 * @date 2025/10/29
 */
@ModuleDetailTypeHandler({ModuleDetailType.ACTIVITY_PAGE})
@Component
@RequiredArgsConstructor
@Slf4j
public class ModuleActivityDetailStrategyImpl extends ModuleDetailStrategy {

    private final PageInfoMapper pageInfoMapper;

    @Override
    public ModuleDetailEntity updateModuleDetailEntityInfo(ModuleDetailEntity moduleDetailEntity) {
        if (StringUtils.isEmpty(moduleDetailEntity.getProductId())){
            log.error("未获取到活动页id");
            return moduleDetailEntity;
        }
        Long productId = Long.valueOf(moduleDetailEntity.getProductId());

        PageInfoPO pageInfoPO = pageInfoMapper.selectOne(new LambdaQueryWrapper<PageInfoPO>()
                .eq(PageInfoPO::getPageId, productId)
                .eq(PageInfoPO::getDelFlag, 0)
                .last("LIMIT 1"));
        if (pageInfoPO == null){
            moduleDetailEntity.setOnSale(false);
            updateModuleDetailEntityStatusOrDel(moduleDetailEntity.getDetailId(), null, 1);
            log.info("活动页已删除 pageId={}", productId);
            return moduleDetailEntity;
        }
        return moduleDetailEntity;
    }

}
