<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.product.persistent.mapper.ProductBrandMapper">

    <resultMap id="ProductBrandResultMap" type="com.jsrxjt.mobile.infra.product.persistent.po.ProductBrandPO">
        <!-- 主键字段 -->
        <id column="id" property="id" jdbcType="BIGINT" javaType="java.lang.Long"/>

        <!-- 基本信息字段 -->
        <result column="brand_name" property="brandName" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="status" property="status" jdbcType="TINYINT" javaType="java.lang.Integer"/>
        <result column="brand_logo" property="brandLogo" jdbcType="VARCHAR" javaType="java.lang.String"/>

        <!-- 时间字段 -->
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="mod_time" property="modTime" jdbcType="TIMESTAMP"/>
        <result column="del_time" property="delTime" jdbcType="TIMESTAMP"/>

        <!-- 操作人字段 -->
        <result column="create_id" property="createId" jdbcType="BIGINT" javaType="java.lang.Long"/>
        <result column="mod_id" property="modId" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="del_id" property="delId" jdbcType="INTEGER" javaType="java.lang.Integer"/>

        <!-- 状态字段 -->
        <result column="del_flag" property="delFlag" jdbcType="TINYINT" javaType="java.lang.Integer"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        id, brand_name, status, brand_logo, create_time, create_id, mod_id, mod_time, del_flag, del_id, del_time
    </sql>

    <select id="findBySpuId" resultType="com.jsrxjt.mobile.infra.product.persistent.po.ProductOffsetPagePO">
        select
            id,
            product_spu_id,
            product_type,
            scenarios,
            background_color,
            validity_date,
            remark,
            create_time,
            create_id,
            del_flag,
            mod_id,
            mod_time
        from product_offset_page
        where product_spu_id = #{spuId}
          and product_type = #{productType}
          and del_flag = 0
    </select>

    <select id="getBrandInfoIncludeDelById" resultMap="ProductBrandResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM product_brand
        WHERE id = #{brandId}
    </select>
</mapper>