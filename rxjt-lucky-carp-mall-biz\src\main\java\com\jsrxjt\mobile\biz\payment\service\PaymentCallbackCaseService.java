package com.jsrxjt.mobile.biz.payment.service;

import com.jsrxjt.mobile.api.payment.dto.request.PaymentCallbackRequestDTO;

/**
 * 支付回调业务服务接口
 * 
 * <AUTHOR>
 * @since 2025/6/17
 */
public interface PaymentCallbackCaseService {

    /**
     * 处理支付回调
     * 
     * @param request 支付回调请求
     * @throws com.jsrxjt.common.core.exception.BizException 业务异常时抛出
     */
    void handlePaymentCallback(PaymentCallbackRequestDTO request);

    /**
     * 处理退款回调
     *
     * @param request 退款回调请求
     * @throws com.jsrxjt.common.core.exception.BizException 业务异常时抛出
     */
    void handleRefundCallback(PaymentCallbackRequestDTO request);
}