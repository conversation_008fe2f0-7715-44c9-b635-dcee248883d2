package com.jsrxjt.mobile.domain.payment.gateway.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 收银台的退款接口响应
 * <AUTHOR>
 * @since 2025/9/26
 */
@Data
public class RefundResponse {
    /** 售后单ID */
    private Long id;

    /** 业务订单号 */
    private String orderNo;

    /** 交易号 */
    private String tradeNo;

    /** 本次退款交易号 */
    private String refundNo;

    /** 本次退款金额 */
    private Long refundAmount;

    /** 本次退款时间 */
    private LocalDateTime refundTime;

    /** 交易总金额 */
    private Long totalAmount;

    /** 支付订单号 */
    private String payOrderNo;

    /** 退款详情列表 */
    private List<TradeRefundInfo> tradeRefundInfo;
}
