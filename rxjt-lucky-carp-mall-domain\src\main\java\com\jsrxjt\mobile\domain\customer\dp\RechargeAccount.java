package com.jsrxjt.mobile.domain.customer.dp;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class RechargeAccount {
    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 产品SPU ID
     */
    private Long productSpuId;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 充值类型 0-其他 1-手机号 2-QQ号
     */
    private Integer accountType;

    /**
     * 账户
     */
    private String account;
}
