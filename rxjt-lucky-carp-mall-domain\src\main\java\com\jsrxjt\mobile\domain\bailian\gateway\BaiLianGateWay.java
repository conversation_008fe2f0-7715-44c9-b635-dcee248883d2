package com.jsrxjt.mobile.domain.bailian.gateway;

import com.jsrxjt.mobile.api.bailian.dto.request.BaiLianShopRequestDTO;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianBrandResponse;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianCategoryResponse;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianShopResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/10/28
 */
public interface BaiLianGateWay {

    /**
     * 获取品牌分类列表
     ** @return 分类列表
     */
    List<BaiLianCategoryResponse> getCategoryList();


    /**
     * 根据分类id获取品牌列表
     *
     * @param categoryId
     * @return
     */
    List<BaiLianBrandResponse> getBrandList(Long categoryId);

    /**
     * 根据品牌id获取门店列表
     *
     * @param request
     * @return
     */
    List<BaiLianShopResponse> getShopList(BaiLianShopRequestDTO request);

}
