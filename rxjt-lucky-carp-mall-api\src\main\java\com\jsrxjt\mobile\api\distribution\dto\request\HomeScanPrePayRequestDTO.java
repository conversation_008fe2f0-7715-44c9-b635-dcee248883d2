package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 预支付请求DTO
 * <AUTHOR>
 * @since 2025/8/8
 */
@Data
@Schema(description = "951预支付请求参数")
public class HomeScanPrePayRequestDTO {
    
    /**
     * 业务订单号
     */
    @NotBlank(message = "订单Id不能为空")
    @Schema(description = "业务订单号",requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderId;

    @Schema(description = "支付标识")
    private String type;

    @Schema(description = "与支付信息")
    private Object object;

}