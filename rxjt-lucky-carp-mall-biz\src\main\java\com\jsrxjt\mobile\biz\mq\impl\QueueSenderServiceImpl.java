package com.jsrxjt.mobile.biz.mq.impl;

import com.jsrxjt.mobile.biz.mq.constants.DestinationConstants;
import com.jsrxjt.mobile.biz.mq.QueueSenderService;
import com.jsrxjt.mobile.domain.gateway.mq.entity.MqMessage;
import com.jsrxjt.mobile.domain.gateway.mq.MqSendGateWay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 *  QueueSenderServiceImpl
 * 
 * <AUTHOR> Fengping
 * 2023/3/16 17:57
 * 
 **/
@Service
@Slf4j
public class QueueSenderServiceImpl implements QueueSenderService {
    private final MqSendGateWay mqSendGateWay;

    public QueueSenderServiceImpl(MqSendGateWay mqSendGateWay) {
        this.mqSendGateWay = mqSendGateWay;
    }

    @Override
    public void send(DestinationConstants.DestinationName destinationName, String messageBody) {
        final String keys = UUID.randomUUID().toString();
        log.info("进入队列发送 : topic {},keys {}, messageBody {}", destinationName.getName(),keys,messageBody);
        mqSendGateWay.syncSend(MqMessage.builder().topic(destinationName.getName())
                .keys(keys).messageBody(messageBody).build());

    }

    @Override
    public void send(DestinationConstants.DestinationName destinationName, String tag, String messageBody) {
        final String keys = UUID.randomUUID().toString();
        log.info("进入队列发送 : topic {}, tag {},keys {}, messageBody {}", destinationName.getName(),tag,keys,messageBody);
        mqSendGateWay.syncSend(MqMessage.builder()
                .topic(destinationName.getName())
                .tag(tag)
                .keys(keys)
                .messageBody(messageBody).build());
    }

    @Override
    public void send(DestinationConstants.DestinationName destinationName, String messageBody, long delayMillisecond) {
        final String keys = UUID.randomUUID().toString();
        log.info("进入延迟队列发送 : topic {},keys {}, messageBody {}", destinationName.getName(),keys,messageBody);
        mqSendGateWay.syncDelaySend(MqMessage.builder()
                .topic(destinationName.getName())
                .keys(keys)
                .messageBody(messageBody).build(),delayMillisecond);

    }

    @Override
    public void send(DestinationConstants.DestinationName destinationName, String tag, String messageBody, long delayMillisecond) {
        final String keys = UUID.randomUUID().toString();
        log.info("进入延迟队列发送 : topic {},tag {},keys {}, messageBody {}", destinationName.getName(),tag,keys,messageBody);
        mqSendGateWay.syncDelaySend(MqMessage.builder()
                .topic(destinationName.getName())
                .tag(tag)
                .keys(keys)
                .messageBody(messageBody).build(),delayMillisecond);

    }

    @Override
    public void asyncSend(DestinationConstants.DestinationName destinationName, String messageBody) {
        final String keys = UUID.randomUUID().toString();
        log.info("进入异步队列发送 : topic {},keys {}, messageBody {}", destinationName.getName(),keys,messageBody);
        mqSendGateWay.asyncSend(MqMessage.builder()
                .topic(destinationName.getName())
                .keys(keys)
                .messageBody(messageBody).build());

    }

    @Override
    public void asyncSend(DestinationConstants.DestinationName destinationName, String tag, String messageBody) {
        final String keys = UUID.randomUUID().toString();
        log.info("进入异步队列发送 : topic {},tag {},keys {}, messageBody {}", destinationName.getName(),tag,keys,messageBody);
        mqSendGateWay.asyncSend(MqMessage.builder()
                .topic(destinationName.getName())
                .tag(tag)
                .keys(keys)
                .messageBody(messageBody).build());

    }

    @Override
    public void asyncSend(DestinationConstants.DestinationName destinationName, String messageBody, long delayMillisecond) {
        final String keys = UUID.randomUUID().toString();
        log.info("进入异步队列发送 : topic {},keys {}, messageBody {}delayMilliseconds{}", destinationName.getName(),keys,messageBody,delayMillisecond);
        mqSendGateWay.asyncSend(MqMessage.builder()
                .topic(destinationName.getName())
                .keys(keys)
                .messageBody(messageBody).build());

    }
}
