package com.jsrxjt.mobile.domain.global.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 全球购券信息
 * <AUTHOR>
 * @Date 2025/9/10
 */
@Data
public class GlobalTicketResponse {

    /**
     * 券ID
     */
    private Long ticketId;

    /**
     * 优惠券名称
     */
    private String lhqName;

    /**
     * 券码
     */
    private String ticketCode;

    /**
     * 使用范围 1限商品 2限店铺 3限分类 4全场通用5线下券
     */
    private Integer usingRange;

    /**
     * 使用范围 1限商品 2限店铺 3限分类 4全场通用5线下券
     */
    private String userRangeName;

    /**
     * 使用状态 1未使用 2已使用 3已过期
     */
    private Integer ticketStatus;

    /**
     * 优惠券类型 1现金 2满减
     */
    private Integer lhqType;

    /**
     * 满减条件 满多少元
     */
    private BigDecimal condition1;

    /**
     * 减多少元或者类型为现金是直接优惠金额
     */
    private BigDecimal reduceValue;

    /**
     * 券有效期
     */
    @JSONField(format = "yyyy.MM.dd HH:mm:ss")
    private Date endTime;

    /**
     * 券有效期
     */
    @JSONField(format = "yyyy.MM.dd HH:mm:ss")
    private Date startTime;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 领取时间
     */
    @JSONField(format = "yyyy.MM.dd HH:mm:ss")
    private Date getTime;

    /**
     * 使用规则
     */
    private String useRules;

}
