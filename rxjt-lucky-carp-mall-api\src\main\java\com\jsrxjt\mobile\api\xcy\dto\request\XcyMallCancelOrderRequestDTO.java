package com.jsrxjt.mobile.api.xcy.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by jeffery.yang on 2023/12/21 17:02
 *
 * @description: 取消订单入参
 * @author: jeffery.yang
 * @date: 2023/12/21 17:02
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XcyMallCancelOrderRequestDTO extends XcyMallBaseRequest {

	/**
	 * 商城订单号
	 * <p>required</p>
	 */
	private String tradeNo;

	/**
	 * 瑞祥侧用户uid，登录时提供
	 * <p>required</p>
	 */
	private String uid;

	/**
	 * 取消时间
	 * <p>required</p>
	 */
	private  String  cancelTime;

}
