package com.jsrxjt.mobile.api.scanPay.types;


/**
 * 扫码付websocket类型枚举
 * <AUTHOR>
 * @date 2025/09/01
 */
public enum ScanWsTypeEnum {
    /**
     * 预付明细
     */
    PRE_PAY_DETAIL("PRE_PAY_DETAIL"),
    /**
     * 确认支付
     */
    CONFIRM_PAY("CONFIRM_PAY"),
    /**
     * 支付失败推送
     */
    PAY_FAIL_PUSH("PAY_FAIL_PUSH"),
    /**
     * 支付成功推送
     */
    PAY_SUCCESS_PUSH("PAY_SUCCESS_PUSH");

    private String type;

    ScanWsTypeEnum(String type) {
        this.type = type;
    }

}
