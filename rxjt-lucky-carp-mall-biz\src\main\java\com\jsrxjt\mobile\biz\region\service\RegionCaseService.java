package com.jsrxjt.mobile.biz.region.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.region.dto.request.UpdateRegionRequest;
import com.jsrxjt.mobile.api.region.dto.request.UserLocationRequest;
import com.jsrxjt.mobile.api.region.dto.response.AllCityResponse;
import com.jsrxjt.mobile.api.region.dto.response.SearchCityResponse;

import java.util.List;
import java.util.Map;

public interface RegionCaseService {
    List<SearchCityResponse> searchCity(String searchQuery);

    AllCityResponse allCity();

    SearchCityResponse userLocation(UserLocationRequest request);

    SearchCityResponse getRegion(Integer regionId);

    Object updateRegionByParentId(UpdateRegionRequest request);

    Object updateALLRegionCache(UpdateRegionRequest request);

    BaseResponse updateAllRegionToTrie(UpdateRegionRequest request);
}
