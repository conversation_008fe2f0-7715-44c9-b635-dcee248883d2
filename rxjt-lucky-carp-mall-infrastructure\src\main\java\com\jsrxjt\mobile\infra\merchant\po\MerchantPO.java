package com.jsrxjt.mobile.infra.merchant.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 商户实体类
 * @Author: ywt
 * @Date: 2025-08-25 09:29
 * @Version: 1.0
 */
@Data
@TableName("shop_agent")
public class MerchantPO {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private Long cityCode;//'城市编码'
    private Integer thirdId;//第三方商户id
    private Integer brandId;//商户品牌id
    private String brandName;//商户品牌
    private Integer groupId;//商户分组id
    private String groupName;//商户分组
    private Integer typeId;//门店类型id
    private String typeName;//门店类型
    private Integer cateId;//商户分类id
    private String cateName;//商户分类'
    private Integer provinceId;//省id
    private Integer cityId;//市id
    private Integer areaId;//区id
    private String no;//门店编号
    private String name;//门店名称
    private String lng;//经度
    private String lat;//纬度
    private String address;//地址
    private String tel;//电话
    private String specialLogo;//特约商户LOGO
            /*`special_type` enum('NONE','EPAY','SYS') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'NONE' COMMENT '联盟商户类别',
            `show_special` enum('Y','N') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否显示在特约商户',
            `show_pick` enum('Y','N') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否显示在扫码提货',
            `is_th_shop` enum('Y','N') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否提货凭证商户',
            `is_th_open` enum('Y','N') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否开启提货凭证支付',
            `invoice_type` enum('','SP','GP','RCPT') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票类型 专票 SP 普票 GP 收据 RCPT',*/
    private String invoiceTax;//开票税率
    private String tag;//商户标签 持凭证提货 CERT_PICK 展码提货 CODE_PICK 持卡支付 CERT_PAY 展码支付 CODE_PAY
    private String icon;//门店LOGO角标
    private String iconValidAt;//角标有效期
    private String label;//门店标签
    private String labelColor;//门店标签颜色
    private String labelBgColor;//门店标签背景色
    private String showSpecial;//是否显示在特约商户
    private Date createdAt;//创建时间
    private Date updatedAt;//更新时间
    private Date deletedAt;//删除时间
}
