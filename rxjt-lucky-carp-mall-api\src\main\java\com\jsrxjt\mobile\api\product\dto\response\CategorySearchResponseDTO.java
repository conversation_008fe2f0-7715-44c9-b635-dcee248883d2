package com.jsrxjt.mobile.api.product.dto.response;

import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.product.dto.CategoryProductDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 分类搜索结果DTO
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
@Data
@Schema(description = "分类产品DTO")
public class CategorySearchResponseDTO {
    @Schema(description = "广告位列表")
    private List<AdvertisementInfoDTO> advertisements;

    @Schema(description = "二级分类详情列表 包括分类id 名称 和搜索到的产品列表")
    private List<CategoryProductDTO> categoryDetails;

}
