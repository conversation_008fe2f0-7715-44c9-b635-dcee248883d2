package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 全球购门店请求参数
 * @Author: ywt
 * @Date: 2025-08-01 16:09
 * @Version: 1.0
 */
@Data
public class MerchantRxShopRequestDTO {
    /*@Schema(description = "城市编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "城市编码不能为空")
    private String cityCode;//城市编码*/
    @Schema(description = "页码,默认第一页", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;
    @Schema(description = "每页数量,默认10", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "每页数量不能为空")
    @Min(value = 1, message = "每页最少1条数据")
    @Max(value = 20, message = "每页最多20条数据")
    private Integer pageSize = 10;
    /*@Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "纬度不能为空")
    private String latitude;
    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "经度不能为空")
    private String longitude;*/
}
