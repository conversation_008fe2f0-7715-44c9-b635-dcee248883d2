package com.jsrxjt.mobile.infra.gateway.distribution.adapter.baisheng;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 百胜分销渠道适配器
 * <AUTHOR>
 * @Date 2025/10/16
 */
@Component
@Slf4j
public class BaiShengDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

    public BaiShengDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                              DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getBaisheng();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.BAISHENG;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            // 校验必填参数
            if (StringUtils.isBlank(request.getUserId()) || StringUtils.isBlank(request.getMobile())) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID和手机号码不能为空")
                        .build();
            }
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("user_id", request.getUserId());
            params.put("mobile", request.getMobile());

            // 添加公共参数和签名
            addCommonParams(params);

            // 构建请求URL
            String url = config.getBaseUrl() + config.getApiPath() + config.getAccessPath();
            String requestUrl = url + "&" + httpClientGateway.buildUrlParams(params);

            return DistAccessResponse.builder()
                    .success(true)
                    .redirectUrl(requestUrl)
                    .build();
        } catch (Exception e) {
            log.error("百胜免登接入异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("百胜免登接入异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        // 调用父类的通用实现，使用紧凑的日期时间格式（yyyyMMddHHmmss）
        return doPaidNotify(request, COMPACT_DAY_TIME_FORMAT);
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        // 需要额外参数 outTradeNo 和 outRefundNo
        return doRefundResultNotify(request, COMPACT_DAY_TIME_FORMAT, (params, req) -> {
            params.put("tradeNo", req.getDistTradeNo());
            params.put("outTradeNo", req.getOutTradeNo());
            params.put("refundNo", req.getDistRefundNo());
            params.put("outRefundNo", req.getOutRefundNo());
            params.put("refundAmount", req.getRefundAmount().toString());
            params.put("status", req.getStatus());

            // 转换时间格式
            if (req.getRefundTime() != null) {
                params.put("refundTime", req.getRefundTime().format(COMPACT_DAY_TIME_FORMAT));
            } else {
                params.put("refundTime", "");
            }
        });
    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        return null;
    }
}
