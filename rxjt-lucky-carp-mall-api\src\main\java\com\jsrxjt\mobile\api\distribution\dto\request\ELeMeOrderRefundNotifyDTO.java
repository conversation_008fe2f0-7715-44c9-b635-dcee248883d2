package com.jsrxjt.mobile.api.distribution.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 饿了么订单退款通知参数
 * <AUTHOR>
 * @since 2025/12/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ELeMeOrderRefundNotifyDTO extends DistributionNotifyCommonDTO {

    /**
     * 支付网关的订单号
     */
    private String transactionId;

    /**
     * 支付网关退款单号
     */
    private String refundNo;

    /**
     * 退款金额 单位分
     */
    private String refundAmount;

    /**
     * 支付金额 单位分
     */
    private String payAmount;

    /**
     * 退款回调地址 异步通知退款成功接口，需在退款成功调用
     */
    private String notifyUrl;
}
