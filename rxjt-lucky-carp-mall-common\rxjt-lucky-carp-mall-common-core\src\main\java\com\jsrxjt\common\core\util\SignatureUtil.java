package com.jsrxjt.common.core.util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 签名工具类
 * 用于客户端生成API接口参数签名
 * 
 * <AUTHOR>
 * @since 2025/1/27
 */
@Slf4j
public class SignatureUtil {

    /**
     * 签名算法类型
     */
    public enum SignAlgorithm {
        MD5
    }

    /**
     * 生成随机nonce
     */
    public static String generateNonce() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成当前时间戳
     */
    public static long generateTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 构建签名参数
     * 
     * @param params 业务参数
     * @param secretKey 密钥
     * @return 包含签名信息的完整参数Map
     */
    public static Map<String, String> buildSignParams(Map<String, String> params, String appid, String secretKey) {
        return buildSignParams(params,appid, secretKey, SignAlgorithm.MD5);
    }

    /**
     * 构建签名参数
     * 
     * @param params 业务参数
     * @param secretKey 密钥
     * @param algorithm 签名算法
     * @return 包含签名信息的完整参数Map
     */
    public static Map<String, String> buildSignParams(Map<String, String> params,
                                                      String appId,
                                                      String secretKey,
                                                      SignAlgorithm algorithm) {
        Map<String, String> signParams = new HashMap<>(params);
        
        // 添加时间戳
        signParams.put("timestamp", String.valueOf(generateTimestamp()));
        
        // 添加nonce
        signParams.put("nonce", generateNonce());
        
        // 计算签名
        String sign = calculateSignature(signParams,appId, secretKey, algorithm);
        signParams.put("sign", sign);
        
        return signParams;
    }

    /**
     * 计算签名
     * 
     * @param params 参数Map
     * @param secretKey 密钥
     * @return 签名
     */
    public static String calculateSignature(Map<String, String> params, String appid, String secretKey) {
        return calculateSignature(params, appid,secretKey, SignAlgorithm.MD5);
    }

    /**
     * 计算签名
     * 
     * @param params 参数Map
     * @param secretKey 密钥
     * @param algorithm 签名算法
     * @return 签名
     */
    public static String calculateSignature(Map<String, String> params,String appId,String secretKey, SignAlgorithm algorithm) {
        String sortedParamStr = buildSortedParamString(params, appId,secretKey);
        return calculateSignature(sortedParamStr, secretKey, algorithm);
    }

    /**
     * 构建排序后的参数字符串
     * 
     * @param params 参数Map
     * @param secretKey 密钥
     * @return 排序后的参数字符串
     */
    public static String buildSortedParamString(Map<String, String> params,String appId, String secretKey) {
        // 按字典序排序
        List<String> sortedKeys = params.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < sortedKeys.size(); i++) {
            String key = sortedKeys.get(i);
            String value = params.get(key);
            if (i > 0) {
                sb.append("&");
            }
            sb.append(key).append("=").append(value);
        }
        if (CharSequenceUtil.isNotBlank(appId)) {
            sb.append("&app_id=").append(appId);
        }
        // 添加密钥
        if (CharSequenceUtil.isNotBlank(secretKey)) {
            sb.append("&key=").append(secretKey);
        }
        
        return sb.toString();
    }

    /**
     * 计算签名
     * 
     * @param paramStr 参数字符串
     * @param secretKey 密钥
     * @param algorithm 签名算法
     * @return 签名
     */
    public static String calculateSignature(String paramStr, String secretKey, SignAlgorithm algorithm) {
        switch (algorithm) {
            case MD5:
                return DigestUtil.md5Hex(paramStr);
            default:
                throw new IllegalArgumentException("不支持的签名算法: " + algorithm);
        }
    }

    /**
     * 验证签名
     * 
     * @param params 参数Map（包含sign）
     * @param secretKey 密钥
     * @return 是否验证通过
     */
    public static boolean verifySignature(Map<String, String> params,String appid, String secretKey) {
        return verifySignature(params, appid,secretKey, SignAlgorithm.MD5);
    }

    /**
     * 验证签名
     * 
     * @param params 参数Map（包含sign）
     * @param secretKey 密钥
     * @param algorithm 签名算法
     * @return 是否验证通过
     */
    public static boolean verifySignature(Map<String, String> params,  String appId,String secretKey, SignAlgorithm algorithm) {
        String receivedSign = params.get("sign");
        if (CharSequenceUtil.isBlank(receivedSign)) {
            return false;
        }
        
        // 移除sign参数，用于计算签名
        Map<String, String> signParams = new HashMap<>(params);
        signParams.remove("sign");
        
        String calculatedSign = calculateSignature(signParams,appId, secretKey, algorithm);
        return Objects.equals(calculatedSign, receivedSign);
    }

    /**
     * 构建GET请求URL
     * 
     * @param baseUrl 基础URL
     * @param params 参数Map
     * @return 完整的GET请求URL
     */
    public static String buildGetUrl(String baseUrl, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return baseUrl;
        }
        
        StringBuilder url = new StringBuilder(baseUrl);
        if (!baseUrl.contains("?")) {
            url.append("?");
        } else if (!baseUrl.endsWith("&") && !baseUrl.endsWith("?")) {
            url.append("&");
        }
        
        List<String> sortedKeys = params.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
        
        for (int i = 0; i < sortedKeys.size(); i++) {
            String key = sortedKeys.get(i);
            String value = params.get(key);
            if (i > 0) {
                url.append("&");
            }
            url.append(key).append("=").append(value);
        }
        
        return url.toString();
    }

    /**
     * 示例：如何使用签名工具类
     */
    public static void main(String[] args) {
        // 示例参数
        Map<String, String> params = new HashMap<>();
//        params.put("code", "111");
//        params.put("orderNo", "11");
//        params.put("externalShopId", "11");
//        params.put("externalShopUserId","11");
//        params.put("price","11");

        params.put("third_id", "1");
        params.put("shop_id", "1");
        params.put("shop_user_id", "1");
        params.put("type", "1");
        params.put("order_no", "1");
        params.put("trade_no", "1");
        params.put("amount", "1");
        params.put("rate", "1");
        params.put("user_code", "1");
        params.put("payment_code", "1");
        params.put( "created_time", "1");
        params.put( "expired_time", "1");
//
        // 密钥本地
       //  String secretKey = "0d6b2235435f41ee3c66ec43db86f19e";
        //提货分销
        String pickAppId="9000443359704963";
        String secretKey="evWZ9dYGoJ53tZsZgvD88srLfQqn5zri";
        
        // 生成签名参数
        Map<String, String> signParams = buildSignParams(params,pickAppId,secretKey);
        
        System.out.println("签名参数: " + signParams);
        
        // 验证签名
        boolean isValid = verifySignature(signParams,pickAppId, secretKey);
        System.out.println("签名验证结果: " + isValid);

    }
} 