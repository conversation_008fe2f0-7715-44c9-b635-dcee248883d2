package com.jsrxjt.mobile.biz.payment.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.api.payment.request.PaymentRequestDTO;
import com.jsrxjt.mobile.api.payment.request.PrePayRequestDTO;
import com.jsrxjt.mobile.api.payment.response.PaymentResponseDTO;
import com.jsrxjt.mobile.api.payment.response.PrePayResponseDTO;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.payment.PaymentCaseService;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.TradeOrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.repository.TradeOrderRepository;
import com.jsrxjt.mobile.domain.payment.gateway.OnlinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.PaymentRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.PrePayRequest;
import com.jsrxjt.mobile.domain.payment.gateway.response.PaymentResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.PrePayResponse;
import com.jsrxjt.mobile.domain.payment.messaging.PaymentSuccessMessageProducer;
import com.jsrxjt.mobile.domain.payment.types.PaymentSuccessMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支付业务服务实现
 * <AUTHOR>
 * @since 2025/8/8
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentCaseServiceImpl implements PaymentCaseService {
    
    private final OrderRepository orderRepository;
    private final TradeOrderRepository tradeOrderRepository;
    private final OnlinePaymentGateway onlinePaymentGateway;
    private final DistributedLock distributedLock;
    private final PaymentSuccessMessageProducer paymentSuccessMessageProducer;
    @Override
    public PrePayResponseDTO prePay(PrePayRequestDTO request) {
        log.info("开始处理预支付业务，订单号：{}", request.getOrderNo());
        
        // 1. 查询订单
        OrderInfoEntity order = queryOrder(request.getOrderNo());
        
        // 2. 校验订单状态
        validateOrderStatus(order);
        
        // 3. 发起预支付
        PrePayResponse response = initiatePrePayment(request);
        
        // 4. 转换响应结果
        PrePayResponseDTO responseDTO = convertToResponseDTO(response);
        
        log.info("预支付业务处理完成，订单号：{}，交易流水号: {}",
                request.getOrderNo(), responseDTO.getTradeNo());
        
        return responseDTO;
    }
    
    /**
     * 查询订单
     */
    private OrderInfoEntity queryOrder(String orderNo) {
        log.info("查询订单信息，订单号：{}", orderNo);
        
        OrderInfoEntity order = orderRepository.findByOrderNo(orderNo);
        if (order == null) {
            log.error("订单不存在，订单号：{}", orderNo);
            throw new BizException("订单不存在");
        }
        
        log.info("订单查询成功，订单号：{}，订单状态：{}", orderNo, order.getOrderStatus());
        return order;
    }
    
    /**
     * 校验订单状态
     */
    private void validateOrderStatus(OrderInfoEntity order) {
        log.info("校验订单状态，订单号：{}，当前状态：{}", order.getOrderNo(), order.getOrderStatus());
        
        // 只有待支付状态的订单才能发起预支付或支付
        if (!OrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getOrderStatus())) {
            log.error("订单状态不允许支付，订单号：{}，当前状态：{}", 
                    order.getOrderNo(), order.getOrderStatus());
            throw new BizException("订单状态不允许支付");
        }
        // 支付状态必须是待支付
        if (!PaymentStatusEnum.UNPAID.getCode().equals(order.getPaymentStatus())) {
            throw new BizException("支付状态不是待支付");
        }
        
        log.info("订单状态校验通过，订单号：{}", order.getOrderNo());
    }
    
    /**
     * 发起预支付
     */
    private PrePayResponse initiatePrePayment(PrePayRequestDTO requestDTO) {
        log.info("发起预支付，订单号：{}", requestDTO.getOrderNo());
        
        // 转换请求参数
        PrePayRequest request = new PrePayRequest();
        BeanUtils.copyProperties(requestDTO, request);
        
        // 调用预支付Gateway
        PrePayResponse response = onlinePaymentGateway.prePayment(request);
        
        log.info("预支付发起成功，订单号：{}，交易流水号：{}", 
                requestDTO.getOrderNo(), response.getTradeNo());
        
        return response;
    }
    
    /**
     * 转换响应结果
     */
    private PrePayResponseDTO convertToResponseDTO(PrePayResponse response) {
        PrePayResponseDTO responseDTO = new PrePayResponseDTO();
        BeanUtils.copyProperties(response, responseDTO);
        responseDTO.setCardCategoryInfo(new ArrayList<>());
        if (response.getIsExistWx()) {
            // 存在微信 将微信支付分摊信息传入cardCategoryInfo
            PrePayResponseDTO.TradeCategoryInfo cardTypeInfoDTO = new PrePayResponseDTO.TradeCategoryInfo();
            cardTypeInfoDTO.setCardType("WX");
            cardTypeInfoDTO.setAmount(response.getWxPayAmount());
            responseDTO.getCardCategoryInfo().add(cardTypeInfoDTO);
        }
        if (response.getTradeTypeInfo() == null) {
            return responseDTO;
        }
        response.getTradeTypeInfo().forEach(cardTypeInfo -> {
            PrePayResponseDTO.TradeCategoryInfo cardTypeInfoDTO = new PrePayResponseDTO.TradeCategoryInfo();
            BeanUtils.copyProperties(cardTypeInfo, cardTypeInfoDTO);
            responseDTO.getCardCategoryInfo().add(cardTypeInfoDTO);
        });
        return responseDTO;
    }

    @Override
    public PaymentResponseDTO pay(PaymentRequestDTO request) {
        log.info("开始处理发起支付业务，订单号：{}，预支付订单号：{}", 
                request.getOrderNo(), request.getPreOrderNo());
        String lockKey = "PAYMENT_LOCK:" + request.getOrderNo();
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey);
            if (!lockAcquired) {
                log.warn("获取支付分布式锁失败，订单号{}", request.getOrderNo());
                throw new BizException("当前订单支付正在处理中，请勿重复请求");
            }
            // 1. 查询订单
            OrderInfoEntity order = queryOrder(request.getOrderNo());

            // 2. 校验订单状态
            validateOrderStatus(order);

            // 3. 发起支付
            PaymentResponse response = initiatePayment(request);

            // 4. 转换响应结果
            PaymentResponseDTO responseDTO = convertToPaymentResponseDTO(response);
//            if ("SUCCESS".equals(response.getPayStatus())) {
//                //支付成功，同步处理支付成功流程
//                log.info("支付成功，开始同步处理订单支付状态 订单号：{}，交易流水号：{}", request.getOrderNo(), responseDTO.getTradeNo());
//                order.setTradeNo(responseDTO.getTradeNo());
//                syncProcessPaymentSuccess(order);
//            }
            return responseDTO;

        }  finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }

    }

    private void syncProcessPaymentSuccess(OrderInfoEntity order) {
        OrderInfoEntity updateParam = new OrderInfoEntity();
        // 设置查询条件
        updateParam.setId(order.getId());
        updateParam.setCustomerId(order.getCustomerId());

        // 更新订单状态为待发货
        updateParam.setOrderStatus(OrderStatusEnum.IN_PROGRESS.getCode());
        updateParam.setPaymentStatus(PaymentStatusEnum.PAID.getCode());
        updateParam.setDeliveryStatus(DeliveryStatusEnum.UNDELIVERED.getCode().intValue());

        // 更新支付相关信息
        // 更新支付相关信息
        List<TradeOrderInfoEntity> tradeOrderInfoEntities =
                tradeOrderRepository.listTradeOrderInfoByTradeNoAndOrderNo(
                        order.getTradeNo(), order.getOrderNo()
                );
        updateParam.setTradeNo(order.getTradeNo());
        String actualPayType = Optional.ofNullable(tradeOrderInfoEntities)
                .orElse(Collections.emptyList())
                .stream()
                .map(TradeOrderInfoEntity::getCardTradeType)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.joining(";"));
        updateParam.setTradeNo(order.getTradeNo());
        updateParam.setActualPayType(actualPayType);
        updateParam.setPaymentTime(LocalDateTime.now());
        updateParam.setModTime(LocalDateTime.now());
        orderRepository.updateOrderCheckCurrentStatus(updateParam,PaymentStatusEnum.UNPAID,OrderStatusEnum.PENDING_PAYMENT);
        log.info("订单支付信息更新成功，订单号：{}，状态：{}，交易号：{}",
                order.getOrderNo(), OrderStatusEnum.IN_PROGRESS.getCode(), order.getTradeNo());

        // 发送支付成功消息
        PaymentSuccessMessage message = new PaymentSuccessMessage(order);
        paymentSuccessMessageProducer.send(message);
    }

    /**
     * 发起支付
     */
    private PaymentResponse initiatePayment(PaymentRequestDTO requestDTO) {
        log.info("发起支付远程调用，订单号：{}，预支付订单号：{}",
                requestDTO.getOrderNo(), requestDTO.getPreOrderNo());
        
        // 转换请求参数
        PaymentRequest request = new PaymentRequest();
        BeanUtils.copyProperties(requestDTO, request);
        
        // 调用支付Gateway
        PaymentResponse response = onlinePaymentGateway.pay(request);
        
        log.info("发起支付远程调用成功，订单号：{}，交易单号：{}，支付状态：{}",
                requestDTO.getOrderNo(), response.getTradeNo(), response.getPayStatus());
        
        return response;
    }

    /**
     * 转换支付响应结果
     */
    private PaymentResponseDTO convertToPaymentResponseDTO(PaymentResponse response) {
        PaymentResponseDTO responseDTO = new PaymentResponseDTO();
        BeanUtils.copyProperties(response, responseDTO);
        return responseDTO;
    }
}
