package com.jsrxjt.mobile.domain.order.service.strategy.aftersale;

import com.jsrxjt.mobile.api.order.dto.response.AfterSaleDetailResponseDTO;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;

import java.util.List;

/**
 * 售后进度构造器
 * @author: Cheng
 * @since 2025-12-02
 */
public interface AfterSaleProgressBuilder {

    /**
     * 是否支持该售后单状态
     * @param status 售后单状态
     * @return true: 支持该售后单状态，false: 不支持该售后单状态
     */
    boolean supports(Integer status);
    /**
     * 构造售后进度
     * @param afterSale 售后单信息
     * @param order 订单信息
     * @return 售后进度详情列表
     */
    List<AfterSaleDetailResponseDTO.ProgressDetailDTO> build(AfterSaleEntity afterSale, OrderInfoEntity order);

}
