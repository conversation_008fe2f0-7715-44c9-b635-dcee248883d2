package com.jsrxjt.mobile.api.config.types;

import com.jsrxjt.common.core.constant.RedisKeyConstants;
import lombok.Getter;

/**
 * @Description: 配置类型
 * @Author: ywt
 * @Date: 2025-04-21 19:22
 * @Version: 1.0
 */
@Getter
public enum SystemConfigTypeEnum {
    BACKENDBG_IMG_TYPE(1, "backendBgImgUrl", "总后台登录页面背景图"),
    /**
     * 总后台 LOGO上传
     */
    BACKENDBG_LOGO_IMG_TYPE(2, "backendLogoImgUrl", "总后台LOGO上传"),
    ORDER_AUTO_CANCEL_HOUR_TYPE(3, RedisKeyConstants.ORDER_AUTO_CANCEL_TIME, "订单自动取消时间"),
    MOD_PHONE_MAX_COUNT_TYPE(4, RedisKeyConstants.MOD_PHONE_MAX_COUNT, "用户修改手机号限制次数"),
    DELETE_USER_MAX_COUNT_TYPE(5, RedisKeyConstants.DELETE_USER_MAX_COUNT, "账户注销次数"),
    REREGISTER_TIME_INTERVAL_TYPE(6, RedisKeyConstants.REREGISTER_TIME_INTERVAL, "注销后再次注册间隔"),
    OUT_GOODS_MIN_NUM(7, RedisKeyConstants.OUT_GOODS_MIN_COUNT, "tab中本地生活和全球购是否显示的商品数阈值"),
    BJ_RECHARGE_LIMIT(8, RedisKeyConstants.BJ_RECHARGE_LIMIT_CACHE, "白金卡账户微信充值限额"),
    HJ_RECHARGE_LIMIT(9, RedisKeyConstants.HJ_RECHARGE_LIMIT_CACHE, "黑金卡账户微信充值限额");

    private final Integer type;
    private final String redisKey;
    private final String info;

    SystemConfigTypeEnum(Integer type, String redisKey, String info) {
        this.type = type;
        this.redisKey = redisKey;
        this.info = info;
    }

    public static String getConfigDescrition(Integer type) {
        for (SystemConfigTypeEnum typeEnum : SystemConfigTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum.getInfo();
            }
        }
        return "";
    }

    public static SystemConfigTypeEnum getConfigEnumByType(Integer type) {
        for (SystemConfigTypeEnum typeEnum : SystemConfigTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        throw new IllegalArgumentException("不支持的配置类型: " + type);
    }
}
