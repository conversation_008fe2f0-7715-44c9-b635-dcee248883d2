package com.jsrxjt.mobile.api.distribution.dto.response;

import com.jsrxjt.common.core.constant.Status;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;


/**
 * 提货券分销平台接口响应 0成功 1失败
 * <AUTHOR>
 * @date 2025/11/11
 */
@ToString
@Schema(title = "通用请求响应结果")
public class PickBaseResponse<T>  {
    @Getter
    @Setter
    @Schema(title = "响应码 0 正确的得到结果 其他表示有异常情况")
    private Integer code;
    @Getter
    @Setter
    @Schema(title = "响应消息内容")
    private String msg;
    @Getter
    @Setter
    @Schema(title = "响应结构")
    private T data;

    @Getter
    @Setter
    @Schema(title = "链路id")
    private String traceId;


    public PickBaseResponse() {
        this.traceId = TraceContext.traceId();
    }

    public PickBaseResponse(Integer code, String msg) {
        this(code, msg, null);
    }


    public PickBaseResponse(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.traceId = TraceContext.traceId();
    }


    public static <T> PickBaseResponse<T> succeed() {
        return new PickBaseResponse<>(0, Status.LSucceed.getMessage());
    }

    public static <T> PickBaseResponse<T> thirdSucceed() {
        return new PickBaseResponse<>(0, "success");
    }


    public static <T> PickBaseResponse<T> succeed(T response) {
        return new PickBaseResponse<>(0, Status.LSucceed.getMessage(), response);
    }


    public static <T> PickBaseResponse<T> fail() {
        return new PickBaseResponse<>(1, Status.LFailed.getMessage());
    }

    public static <T> PickBaseResponse<T> fail(Integer code, String message) {
        return new PickBaseResponse<>(code, message);
    }

    public static <T> PickBaseResponse<T> fail(String message) {
        return new PickBaseResponse<>(1, message);
    }

    public static <T> PickBaseResponse<T> fail(Status status) {
        return new PickBaseResponse<>(status.getCode(), status.getMessage());
    }

    public boolean isFail() {
        return this.code != null? this.getCode().equals(1):false;
    }

    public boolean isSuccess() {
        return this.code != null? this.getCode().equals(0):false;
    }
}
