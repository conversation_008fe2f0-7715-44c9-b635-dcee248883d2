package com.jsrxjt.mobile.api.locallife.dto.response;

import com.jsrxjt.mobile.api.locallife.dto.LocalLifeBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LocalLifeOrderRefundQueryResponse extends LocalLifeBaseDTO {

    private String orderNo;

    private String flqRefundNo;

    private String refundStatus;

    private String refundTime;

    private String refundAmount;
}
