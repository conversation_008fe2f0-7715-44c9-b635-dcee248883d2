package com.jsrxjt.mobile.api.coupon.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 优惠券核销状态变更通知
 * @Author: ywt
 * @Date: 2025-11-07 16:26
 * @Version: 1.0
 */
@Data
public class TicketConsumeStatusNotifyRequestDTO {
    @NotBlank(message = "appid为空错误")
    private String appid;
    @NotBlank(message = "参数为空错误")
    private String nounce;
    @NotBlank(message = "时间戳不能为空")
    private String timestamp;
    @NotBlank(message = "签名不能为空")
    private String signature;
    @NotBlank(message = "优惠券code")
    private String couponNumber;
    private String code;//优惠券卡管的卡号（卡券类型couponType为2或3）
    @NotNull(message = "卡券状态:1已核销")
    private Integer status;
    @Schema(description = "核销数量")
    private Integer num;
}
