package com.jsrxjt.mobile.api.coupon.types;

import java.util.HashMap;
import java.util.Map;

/**
 * 卡券平台充值状态枚举
 * 用于品诺和视听会员充值流程的状态管理
 * 
 * <AUTHOR>
 * @since 2025/6/17
 */
public enum CouponPlatformRechargeStatus {

    PENDING_PAYMENT(0, "待付款"),

    TRADE_CLOSED(1, "交易关闭"),

    PAYMENT_SUCCESS(2, "付款成功"),

    PENDING_RECHARGE(3, "待充值"),

    RECHARGING(4, "充值中"),

    RECHARGE_SUCCESS(5, "充值成功"),

    RECHARGE_FAILED(6, "充值失败"),

    REFUND_SUCCESS(7, "退款成功"),

    RECHARGE_SYSTEM_ERROR(8, "充值系统异常");

    CouponPlatformRechargeStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private final int code;

    private final String description;

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private static final Map<Integer, CouponPlatformRechargeStatus> CODE_MAP = new HashMap<>();

    static {
        for (CouponPlatformRechargeStatus status : CouponPlatformRechargeStatus.values()) {
            CODE_MAP.put(status.getCode(), status);
        }
    }

    /**
     * 根据状态码获取枚举实例
     * 
     * @param code 状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static CouponPlatformRechargeStatus getByCode(int code) {
        return CODE_MAP.get(code);
    }

    /**
     * 判断是否为需要等待处理的状态
     * 包括：付款成功、待充值、充值中、退款成功
     * 
     * @param code 状态码
     * @return true-需要等待处理，false-可以立即处理
     */
    public static boolean isWaitingStatus(int code) {
        return code == PAYMENT_SUCCESS.code || 
               code == PENDING_RECHARGE.code || 
               code == RECHARGING.code || 
               code == REFUND_SUCCESS.code;
    }

    /**
     * 判断是否为充值成功状态
     * 
     * @param code 状态码
     * @return true-充值成功，false-其他状态
     */
    public static boolean isRechargeSuccess(int code) {
        return code == RECHARGE_SUCCESS.code;
    }

    /**
     * 判断是否为充值失败状态
     * 
     * @param code 状态码
     * @return true-充值失败，false-其他状态
     */
    public static boolean isRechargeFailed(int code) {
        return code == RECHARGE_FAILED.code;
    }
} 