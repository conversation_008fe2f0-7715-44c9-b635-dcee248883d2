package com.jsrxjt.mobile.infra.bianlifeng.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025-10-22
 */
@Slf4j
public class BianLiFengSignUtil {

    public static String getSignature(Map<String, Object> params,String signKey) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        // 按照参数名ASCII码排序
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        // 构建签名原始字符串
        StringBuilder sb = new StringBuilder();
        sb.append("signKey=").append(signKey).append("&");
        for (String key : keys) {
            Object value = params.get(key);
            if (value != null && StringUtils.isNotBlank(value.toString())) {
                sb.append(key).append("=").append(value).append("&");
            }
        }

        // 移除末尾的&并拼接secret
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1); // 去掉末尾的&
        }
        return DigestUtils.md5DigestAsHex(sb.toString().getBytes());
    }
}
