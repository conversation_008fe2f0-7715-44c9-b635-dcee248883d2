package com.jsrxjt.mobile.api.enums;

/**
 * @author: zy
 * @Description: 订单类型枚举
 */
public enum OrderTypeEnum {
    COUPON(1, "卡券"),
    ORDINARY_APP(2, "普通应用"),
    RECHARGE_APP(3, "红包/充值"),
    SCAN_APP(4, "扫码付应用");

    private final Integer type;
    private final String description;

    OrderTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据类型获取描述信息
     */
    public static String getDescriptionByType(Integer type) {
        for (OrderTypeEnum orderType : OrderTypeEnum.values()) {
            if (orderType.getType().equals(type)) {
                return orderType.getDescription();
            }
        }
        return null;
    }
}
