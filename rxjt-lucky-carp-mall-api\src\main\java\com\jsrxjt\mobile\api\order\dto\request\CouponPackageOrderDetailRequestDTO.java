package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;


/**
 * 订单页卡包详情请求参数
 * <AUTHOR>
 * @date 2025/07/22
 */
@Data
public class CouponPackageOrderDetailRequestDTO extends BaseParam{

    @Schema(description = "订单号")
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

}
