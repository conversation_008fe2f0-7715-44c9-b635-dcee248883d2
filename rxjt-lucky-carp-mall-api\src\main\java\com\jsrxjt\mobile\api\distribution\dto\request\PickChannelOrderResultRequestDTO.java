package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Schema(description = "调用付款接口")
public class PickChannelOrderResultRequestDTO {
   @Schema(description = "用户id")
   @NotNull(message = "用户id不能为空")
   private Long userId;

   @Schema(description = "订单id")
   @NotNull(message = "订单id不能为空")
   private Long orderId;


}
