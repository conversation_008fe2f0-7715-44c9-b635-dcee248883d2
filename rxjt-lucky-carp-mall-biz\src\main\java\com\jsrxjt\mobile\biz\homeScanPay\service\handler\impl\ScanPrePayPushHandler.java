package com.jsrxjt.mobile.biz.homeScanPay.service.handler.impl;

import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.scanPay.types.WssPushTypeEnum;
import com.jsrxjt.mobile.biz.homeScanPay.service.handler.ScanPushHandler;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 扫码付预支付处理
 * <AUTHOR>
 * @date 2025/10/30
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ScanPrePayPushHandler implements ScanPushHandler<JSONObject> {

    private final OrderRepository orderRepository;

    @Value("${scan.pay.spuId:6}")
    private Long scanPaySpuId;

    @Override
    public boolean supports(WssPushTypeEnum type) {
        return WssPushTypeEnum.PRE_PAY.equals(type);
    }

    /**
     * 增加福鲤圈订单号和用户id
     * @param jsonObject
     * @return {@link BaseResponse}<{@link JSONObject}>
     */
    @Override
    public BaseResponse<JSONObject> handle(JSONObject jsonObject) {
        String tradeNo = jsonObject.getString("trade_no");
        OrderInfoEntity existOrderEntity = orderRepository.findByTradeNoAndAppFlag(tradeNo, DistChannelType.SCANPAY.name());
        if (existOrderEntity == null){
            throw new BizException("未找到订单信息");
        }
        jsonObject.fluentPut("customerId", existOrderEntity.getCustomerId())
                        .fluentPut("flqOrderNo", existOrderEntity.getOrderNo());
        return BaseResponse.succeed(jsonObject);
    }
}
