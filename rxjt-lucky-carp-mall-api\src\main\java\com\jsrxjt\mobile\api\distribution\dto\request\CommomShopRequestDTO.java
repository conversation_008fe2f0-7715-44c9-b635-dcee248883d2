package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 通用码商户列表请求参数
 * @Author: ywt
 * @Date: 2025-12-11 10:49
 * @Version: 1.0
 */
@Data
public class CommomShopRequestDTO {
    @Schema(description = "地址id")
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
}
