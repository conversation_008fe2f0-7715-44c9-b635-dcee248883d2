package com.jsrxjt.mobile.biz.product.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.product.dto.request.DefaultSearchKeywordRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.ProductSearchRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.ProductSuggestionRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.ProductSuggestionResponseDTO;
import com.jsrxjt.mobile.api.product.dto.response.SearchKeywordResponseDTO;
import com.jsrxjt.mobile.biz.product.service.ProductSearchCaseService;
import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;
import com.jsrxjt.mobile.domain.product.entity.SearchKeyword;
import com.jsrxjt.mobile.domain.product.repository.ProductBaseInfoRepository;
import com.jsrxjt.mobile.domain.product.repository.SearchKeywordRepository;
import com.jsrxjt.mobile.domain.product.service.ProductSpuBaseInfoService;
import com.jsrxjt.mobile.domain.riskcontrol.entity.SpuRiskFilterEntity;
import com.jsrxjt.mobile.domain.riskcontrol.service.ProductRiskControlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 产品搜索服务实现类
 * 
 * <AUTHOR> Fengping
 * @since 2025/5/8
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductSearchCaseServiceImpl implements ProductSearchCaseService {

    private final ProductSpuBaseInfoService productSpuBaseInfoService;
    private final ProductRiskControlService productRiskControlService;
    private final ProductBaseInfoRepository productBaseInfoRepository;
    private final SearchKeywordRepository searchKeywordRepository;

    @Override
    public PageDTO<ProductSpuBaseInfo> pageSearchProducts(ProductSearchRequestDTO request) {
        log.info("搜索产品开始，关键词：{}, 页码：{}, 每页大小：{}",
                request.getKeyword(), request.getToPage(), request.getPageRows());

        // 调用领域层搜索产品并直接返回结果
        PageDTO<ProductSpuBaseInfo> result = productBaseInfoRepository.pageProducts(
                request.getKeyword(), request.getToPage(), request.getPageRows());

        log.info("搜索产品完成，共查询到{}条记录", result.getTotal());
        return result;
    }

    @Override
    public List<ProductSpuBaseInfo> searchProducts(ProductSearchRequestDTO request) {
        List<ProductSpuBaseInfo> productSpuBaseInfos = productBaseInfoRepository.listProducts(request.getKeyword());
        if (productSpuBaseInfos == null || productSpuBaseInfos.isEmpty()) {
            log.info("没有搜索到产品 关键词{}", request.getKeyword());
            return List.of();
        }

        List<ProductSpuBaseInfo> result = productSpuBaseInfoService.filterByRegion(request.getRegionId(), productSpuBaseInfos);

        if (result == null || result.isEmpty()) {
            log.info("过滤区域{}之后 剩余没有产品", request.getRegionId());
            return List.of();
        }
        result = filterDisableProducts(result);

        if (result.isEmpty()) {
            log.info("过滤风控禁用商品之后 剩余没有产品");
            return List.of();
        }

        productSpuBaseInfoService.setPromotionLabels(result);

        return result;
    }

    private List<ProductSpuBaseInfo> filterDisableProducts(List<ProductSpuBaseInfo> result) {
        Long customerId = getLoginId();
        List<SpuRiskFilterEntity> riskDisableProducts = productRiskControlService.getRiskDisableProducts(customerId);
        if (riskDisableProducts != null && !riskDisableProducts.isEmpty()) {
            log.info("有禁止展示给用户{}的商品",customerId);
            // 创建风险商品查找表（使用复合键）
            Set<String> riskProductKeys = riskDisableProducts.stream()
                    .map(entity -> entity.getProductSpuId() + "_" + entity.getProductType())
                    .collect(Collectors.toSet());
            result = result.stream()
                    .filter(spu -> !riskProductKeys.contains(spu.getSpuId() + "_" + spu.getProductType()))
                    .collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<ProductSuggestionResponseDTO> getSuggestions(ProductSuggestionRequestDTO request) {
        String keyword = request.getKeyword();
        Integer maxCount = request.getMaxCount();

        log.info("获取产品联想词开始，关键词：{}, 最大返回数：{}", keyword, maxCount);

        // 调用领域层获取联想词并直接返回结果
        List<ProductSuggestionResponseDTO> result = productBaseInfoRepository.getSuggestions(
                keyword, maxCount);

        log.info("获取产品联想词完成，共获取到{}条记录", result.size());
        return result;
    }

    @Override
    public List<SearchKeywordResponseDTO> getDefaultSearchKeywords(DefaultSearchKeywordRequestDTO request) {
        log.info("获取默认搜索词，区域ID：{}", request.getDistrictId());
        
        // 调用领域层获取默认搜索词
        List<SearchKeyword> keywords = searchKeywordRepository.findDefaultSearchKeywords(request.getDistrictId(), request.getType());
        
        // 转换为DTO
        List<SearchKeywordResponseDTO> result = keywords.stream()
                .map(this::convertToDefaultKeywordDTO)
                .collect(Collectors.toList());
        
        log.info("获取默认搜索词完成，共获取到{}条记录", result.size());
        return result;
    }

    @Override
    public void incrementSearchKeywordClickNum(Long id) {
        log.info("开始增加搜索关键词点击量，ID：{}", id);

        if (id == null) {
            log.warn("搜索关键词ID为空，无法增加点击量");
            return;
        }

        try {
            searchKeywordRepository.incrementClickNum(id);
            log.info("搜索关键词点击量增加成功，ID：{}", id);
        } catch (Exception e) {
            log.error("增加搜索关键词点击量失败，ID：{}，错误信息：{}", id, e.getMessage(), e);
            throw new RuntimeException("增加搜索关键词点击量失败", e);
        }
    }

    /**
     * 将领域实体转换为默认搜索词DTO
     */
    private SearchKeywordResponseDTO convertToDefaultKeywordDTO(SearchKeyword keyword) {
        SearchKeywordResponseDTO dto = new SearchKeywordResponseDTO();
        BeanUtil.copyProperties(keyword, dto);
        return dto;
    }

    private Long getLoginId() {
        if (!StpUtil.isLogin()) {
            return null;
        }
        return StpUtil.getLoginIdAsLong();
    }
}
