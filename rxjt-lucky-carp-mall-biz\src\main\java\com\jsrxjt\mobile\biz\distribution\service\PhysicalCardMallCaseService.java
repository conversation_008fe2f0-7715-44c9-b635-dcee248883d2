package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.physicalCardMall.dto.request.PhysicalCardCreateOrderRequestDTO;
import com.jsrxjt.mobile.api.physicalCardMall.dto.request.PhysicalCardRefundRequestDTO;
import com.jsrxjt.mobile.api.physicalCardMall.dto.response.PhysicalCardCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.physicalCardMall.dto.response.PhysicalCardRefundResponseDTO;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-10-23 10:04
 * @Version: 1.0
 */
public interface PhysicalCardMallCaseService {
    /**
     * 实体卡商城下单
     *
     * @param requestDTO
     * @return
     */
    ApiResponse<PhysicalCardCreateOrderResponseDTO> prePay(PhysicalCardCreateOrderRequestDTO requestDTO);

    /**
     * 实体卡商城退款
     *
     * @param requestDTO
     * @return
     */
    ApiResponse<PhysicalCardRefundResponseDTO> refund(PhysicalCardRefundRequestDTO requestDTO);
}
