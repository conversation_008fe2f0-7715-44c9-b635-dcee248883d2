package com.jsrxjt.adapter.order.controller;

import com.jsrxjt.mobile.api.ticket.request.TicketGiveOutNotifyRequest;
import com.jsrxjt.mobile.api.ticket.response.TicketNotifyResponseDTO;
import com.jsrxjt.mobile.biz.ticket.GiftTicketOrderCaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/ticket-notify")
@RequiredArgsConstructor
@Slf4j
public class GiftTicketCallbackController {

    private final GiftTicketOrderCaseService giftTicketOrderCaseService;
    /**
     * 券发放结果通知
     *
     * @param request 请求参数
     * @return
     */
    @PostMapping("/give-out")
    public TicketNotifyResponseDTO giveOutNotify(@RequestBody TicketGiveOutNotifyRequest request) {

        return giftTicketOrderCaseService.processTicketGiveOutNotify(request);
    }
}
