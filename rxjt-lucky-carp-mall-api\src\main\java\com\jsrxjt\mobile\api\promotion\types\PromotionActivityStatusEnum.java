package com.jsrxjt.mobile.api.promotion.types;

/**
 * 卡券活动状态枚举类
 *
 **/
public enum PromotionActivityStatusEnum {

    UN_START(1, "未开始"),

    IN_PROGRESS(2, "进行中"),

    CLOSED(3, "已关闭"),

    ENDED(4, "已结束");

    private final Integer status;

    private final String name;

    PromotionActivityStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }
}
