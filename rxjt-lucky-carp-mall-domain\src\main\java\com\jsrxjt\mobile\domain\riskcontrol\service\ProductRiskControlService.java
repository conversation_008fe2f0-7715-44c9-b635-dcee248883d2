package com.jsrxjt.mobile.domain.riskcontrol.service;

import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.riskcontrol.dto.response.ProductRiskControlResponse;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.riskcontrol.entity.SpuRiskFilterEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/25 15:12
 */
public interface ProductRiskControlService {

    /**
     * 用户下单查询风控策略
     * @param productItemId 产品id
     * @param customerId 用户id
     * @param customerRiskLevel 用户风控等级
     * @return
     */
    ProductRiskControlResponse getProductRiskControl(ProductItemId productItemId, Long customerId, Integer customerRiskLevel);

    //
    /**
     * 获取风控禁用的spu
     * @param customerId 用户id
     * @return
     */
    List<SpuRiskFilterEntity> getRiskDisableProducts(Long customerId);

    /**
     * 判断是否风控禁用的spu
     * @param customer 用户
     * @param spuId spu id
     * @param productType spu 类型
     * @return
     */
    boolean isRiskDisableProduct(CustomerEntity customer, Long spuId, Integer productType);

}
