package com.jsrxjt.mobile.domain.order.entity;

import com.jsrxjt.mobile.domain.order.types.PayChannelEnum;
import com.jsrxjt.mobile.domain.order.types.TradePayStatusEnum;
import com.jsrxjt.mobile.domain.order.types.TradeRefundStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易订单信息领域实体
 * <AUTHOR>
 * @since 2025/8/15
 */
@Data
public class TradeOrderInfoEntity {

    /**
     * 交易ID
     */
    private Long id;

    /**
     * 父-订单ID
     */
    private Long orderId;

    /**
     * 父-订单号
     */
    private String orderNo;

    /**
     * 父-外部订单号
     */
    private String outOrderNo;

    /**
     * 父-支付订单号
     */
    private String tradeNo;

    /**
     * 支付通道
     */
    private PayChannelEnum payChannel;

    /**
     * 子-交易单号（微信交易单号）
     */
    private String tradeInfoNo;

    /**
     * 子-预付卡交易单号
     */
    private String outTradeInfoNo;

    /**
     * 兑换支付ID
     */
    private Long exchangePayId;

    /**
     * 微信关联商户号ID
     */
    private Long wxMchId;

    /**
     * 微信支付openid
     */
    private String wxOpenId;

    /**
     * 微信交易ID（退款需要使用该参数）
     */
    private String wxTransactionId;

    /**
     * 微信支付兑换的卡充值订单
     */
    private String wxExchangeRechargeOrder;

    /**
     * 微信支付配置
     */
    private String wxPayParams;

    /**
     * 微信自动退款单号 原路退回
     */
    private String wxRefundId;

    /**
     * 渠道号
     */
    private String chanId;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 门店号
     */
    private String storeId;

    /**
     * 终端号
     */
    private String termId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 交易卡类型
     */
    private String cardTradeType;

    /**
     * 业务卡类型
     */
    private String cardBusinessType;

    /**
     * 支付金额（元）
     */
    private BigDecimal payAmount;

    /**
     * 支付状态
     */
    private TradePayStatusEnum payStatus;

    /**
     * 已退款金额（元）
     */
    private BigDecimal refundAmount;

    /**
     * 退款状态
     */
    private TradeRefundStatusEnum refundStatus;

    /**
     * 微信支付时间
     */
    private LocalDateTime wxTradeAt;

    /**
     * 支付时间
     */
    private LocalDateTime tradeAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 删除标记
     */
    private Integer deletedAt;

    /**
     * 判断是否为微信支付
     */
    public boolean isWechatPay() {
        return PayChannelEnum.WECHAT_PAY.equals(payChannel) || 
               PayChannelEnum.WECHAT_PAY_EXCHANGE.equals(payChannel);
    }

    /**
     * 判断是否为卡支付
     */
    public boolean isCardPay() {
        return PayChannelEnum.CARD.equals(payChannel);
    }

    /**
     * 判断是否支付成功
     */
    public boolean isPaySuccess() {
        return TradePayStatusEnum.SUCCESS_PAY.equals(payStatus);
    }

    /**
     * 判断是否支付失败
     */
    public boolean isPayFailed() {
        return TradePayStatusEnum.FAIL_PAY.equals(payStatus);
    }

    /**
     * 判断是否待支付
     */
    public boolean isWaitingPay() {
        return TradePayStatusEnum.WAIT_PAY.equals(payStatus);
    }

    /**
     * 判断是否可以退款
     */
    public boolean canRefund() {
        return isPaySuccess() && 
               (TradeRefundStatusEnum.WAIT_REFUND.equals(refundStatus) || 
                TradeRefundStatusEnum.PART_REFUND.equals(refundStatus));
    }

    /**
     * 获取可退款金额
     */
    public BigDecimal getAvailableRefundAmount() {
        if (!canRefund()) {
            return BigDecimal.ZERO;
        }
        return payAmount.subtract(refundAmount != null ? refundAmount : BigDecimal.ZERO);
    }

    /**
     * 更新支付状态
     */
    public void updatePayStatus(TradePayStatusEnum newStatus, LocalDateTime tradeTime) {
        this.payStatus = newStatus;
        this.tradeAt = tradeTime;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 更新退款信息
     */
    public void updateRefundInfo(BigDecimal refundAmount, TradeRefundStatusEnum refundStatus) {
        this.refundAmount = refundAmount;
        this.refundStatus = refundStatus;
        this.updatedAt = LocalDateTime.now();
    }
}
