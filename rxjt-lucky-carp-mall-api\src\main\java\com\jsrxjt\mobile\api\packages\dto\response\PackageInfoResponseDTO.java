package com.jsrxjt.mobile.api.packages.dto.response;

import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.product.dto.ProductExplainResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 套餐信息响应
 * @Author: ywt
 * @Date: 2025-05-09 18:07
 * @Version: 1.0
 */
@Data
@Schema(description = "套餐信息响应")
public class PackageInfoResponseDTO {
    @Schema(description = "套餐spuId")
    private Long id;

    /*@Schema(description = "品牌名称")
    private String  brandName;*/

    @Schema( description = "套餐名称")
    private String packageSpuName;

    @Schema( description = "套餐副标题")
    private String subTitle;

    @Schema( description = "状态 0:下架 1:出售中")
    private Integer packageStatus;

    @Schema(description = "兑换流程富文本")
    private String exchangeProcess;

    @Schema( description = "套餐销量")
    private Integer virtualStock;

    @Schema( description = "套餐转发量")
    private Integer forwardNum;

    @Schema(description = "角标url")
    private String subscriptUrl;

    @Schema( description = "logo图片url")
    private String logoUrl;

    @Schema( description = "卡券图片url")
    private String imgUrl;
    @Schema(description = "卡券服务标签列表")
    private List<String> labelList;
    @Schema( description = "兑换须知")
    private List<ProductExplainResponseDTO> exchangeList;

    @Schema( description = "核销须知")
    private List<ProductExplainResponseDTO> offsetList;
    @Schema( description = "广告列表")
    private List<AdvertisementInfoDTO> advertiseList;
    @Schema( description = "选中/第一个可售的sku信息")
    private PackageSkuInfoResponseDTO skuInfoResponseDTO;
}
