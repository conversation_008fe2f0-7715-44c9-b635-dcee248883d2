package com.jsrxjt.adapter.distribution.config;

import com.jsrxjt.common.adapter.interceptor.WsHandshakeInterceptor;
import com.jsrxjt.mobile.biz.homeScanPay.service.impl.ScanWebSocketHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.support.HttpSessionHandshakeInterceptor;

@Configuration
@EnableWebSocket
@RequiredArgsConstructor
public class ScanWebSocketConfig implements WebSocketConfigurer {

    @Value("${spring.application.name}")
    private String applicationName;

    private final ScanWebSocketHandler scanWebSocketHandler;

    private final WsHandshakeInterceptor wsHandshakeInterceptor;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(scanWebSocketHandler,  "/ws/v1/flpscan-ws")
                .addInterceptors(
                        new HttpSessionHandshakeInterceptor(),
                        wsHandshakeInterceptor
                )
                .setAllowedOrigins("*");
    }
}