package com.jsrxjt.mobile.api.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 便利蜂详情响应
 * @Author: ywt
 * @Date: 2025-10-21 14:02
 * @Version: 1.0
 */
@Data
@Schema(description = "便利蜂直充应用信息响应")
public class BianlfAppResponse {
    @Schema(description = "应用SPU ID")
    private Long appSpuId;

    @Schema(description = "应用SPU名称")
    private String appSpuName;

    @Schema(description = "logo图片URL")
    private String logoUrl;

    @Schema(description = "主图&列表图url")
    private String imgUrl;

    @Schema(description = "副标题")
    private String subTitle;

    @Schema(description = "角标id")
    private Long subscriptId;

    @Schema(description = "角标url")
    private String subscriptUrl;

    @Schema(description = "销量（虚拟）")
    private Integer virtualStock = 0;

    @Schema(description = "转发量（虚拟）")
    private Integer forwardNum = 0;

    @Schema(description = "应用说明信息")
    List<AlipayAppExplainResponse> explainEntityList;

    @Schema(description = "便利蜂直充sku列表")
    List<BianlfAppSkuResponse> skuList;
}
