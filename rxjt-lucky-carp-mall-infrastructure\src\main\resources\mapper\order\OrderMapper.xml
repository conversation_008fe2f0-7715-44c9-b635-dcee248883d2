<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsrxjt.mobile.infra.order.persistent.mapper.OrderMapper">

    <!-- 订单项结果映射 -->
    <resultMap id="OrderItemResultMap" type="com.jsrxjt.mobile.infra.order.persistent.po.OrderItemPO">
        <id property="id" column="item_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="productName" column="product_name"/>
        <result property="brandName" column="brand_name"/>
        <result property="sellPrice" column="sell_price"/>
        <result property="quantity" column="quantity"/>
        <result property="productLogo" column="product_logo"/>
        <result property="faceAmount" column="face_amount"/>
        <result property="productType" column="product_type"/>
        <result property="flatProductType" column="flat_product_type"/>
        <result property="productId" column="product_id"/>
        <result property="productSkuId" column="product_sku_id"/>
        <result property="brandId" column="brand_id"/>
        <result property="firstCategoryId" column="first_category_id"/>
        <result property="categoryId" column="category_id"/>
    </resultMap>

    <!-- 订单及订单项结果映射 -->
    <resultMap id="OrderWithItemsResultMap" type="com.jsrxjt.mobile.infra.order.persistent.po.OrderWithItemsPO">
        <id property="id" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="customerId" column="customer_id"/>
        <result property="orderStatus" column="order_status"/>
        <result property="deliveryStatus" column="delivery_status"/>
        <result property="afterSaleStatus" column="after_sale_status"/>
        <result property="paymentStatus" column="payment_status"/>
        <result property="paymentAmount" column="payment_amount"/>
        <result property="totalServiceFee" column="total_service_fee"/>
        <result property="exceedFee" column="exceed_fee"/>
        <result property="rechargeAccount" column="recharge_account"/>
        <result property="createTime" column="order_create_time"/>
        <result property="payExpireTimestamp" column="pay_expire_timestamp"/>
        <result property="orderDetailUrl" column="order_detail_url"/>
        <result property="externalPayResultUrl" column="external_pay_result_url"/>
        <!-- 一对多关联 -->
        <collection property="orderItems" ofType="com.jsrxjt.mobile.infra.order.persistent.po.OrderItemPO" 
                   resultMap="OrderItemResultMap"/>
    </resultMap>
    <!-- 分页查询订单及订单项 -->
    <select id="selectOrderListWithItemsPage" resultMap="OrderWithItemsResultMap">
        SELECT 
            o.id as order_id,
            o.order_no,
            o.customer_id,
            o.order_status,
            o.delivery_status,
            o.after_sale_status,
            o.payment_status,
            o.payment_amount,
            o.recharge_account,
            o.total_service_fee,
            o.exceed_fee,
            o.create_time as order_create_time,
            o.pay_expire_timestamp,
            o.order_detail_url,
            oi.id as item_id,
            oi.product_name,
            oi.brand_name,
            oi.brand_id,
            oi.sell_price,
            oi.quantity,
            oi.product_logo,
            oi.face_amount,
            oi.product_type,
            oi.flat_product_type,
            oi.product_id,
            oi.product_sku_id,
            oi.first_category_id,
            oi.category_id
        FROM t_order o
        LEFT JOIN t_order_item oi ON o.id = oi.order_id
        ${ew.customSqlSegment}
    </select>

    <!-- 查询用户在指定活动下指定商品的已购买数量（排除已取消订单） -->
    <select id="selectUserActivityPurchasedQuantity" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(oi.quantity), 0) as total_quantity
        FROM t_order o
        INNER JOIN t_order_item oi ON o.id = oi.order_id
        WHERE o.customer_id = #{customerId}
          AND oi.promotion_activity_id = #{activityId}
          AND oi.product_type = #{productType}
          AND oi.product_sku_id = #{skuId}
          AND o.del_flag = 0
          AND o.order_status NOT IN (40, 41)
    </select>

    <select id="selectUnPayScanPayOrder" resultType="com.jsrxjt.mobile.infra.order.persistent.po.OrderWithVipPO">
        SELECT
            o.id as order_id,
            o.order_no,
            o.customer_id,
            o.order_status,
            o.delivery_status,
            o.after_sale_status,
            o.payment_status,
            o.payment_amount,
            o.trade_no,
            o.recharge_account,
            o.total_service_fee,
            o.exceed_fee,
            o.create_time,
            c.vip_id as vipId
        FROM t_order o
        INNER JOIN customer c ON o.customer_id = c.id
        ${ew.customSqlSegment}
    </select>
</mapper>

