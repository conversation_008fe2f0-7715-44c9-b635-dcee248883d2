package com.jsrxjt.mobile.infra.gateway.distribution.adapter.meituan;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 美团分销渠道适配器基类
 * 封装美团渠道的通用业务逻辑，避免代码重复
 **/
@Slf4j
public abstract class AbstractMeiTuanDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

    public AbstractMeiTuanDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                                     DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        // 调用父类的通用实现，使用分隔的日期时间格式（yyyy-MM-dd HH:mm:ss）
        return doPaidNotify(request, DAY_TIME_SPLIT_FORMAT);
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        // 这是第三方应用提供的接口，需要在应用内实现
        // SDK只提供接口定义，不提供具体实现
//        throw new DistributionApiException("此接口为通卡数科平台向接入方推送的接口，作为接入方需自行提供此接口");
        return DistRefundResultNotifyResponse.builder()
                .success(true)
                .status(SUCCESS_CODE)
                .build();
    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        // 使用父类通用实现，提供特殊的响应解析逻辑以处理美团特有的字段
        return doQueryOrderDetail(request, null, (result, channelName) -> {
            JSONObject data = result.getJSONObject("data");
            JSONObject order = data.getJSONObject("order");

            return DistOrderDetailQueryResponse.builder()
                    .success(true)
                    .distOrderNo(order.getString("order_no"))
                    .outOrderNo(order.getString("store_order_no"))
                    .origOrderId(order.getString("orig_order_id"))
                    .orderType(order.getString("sqt_type"))
                    .tradeAmount(new BigDecimal(order.getString("trade_amount")))
                    .goodsName(order.getString("goods_name"))
                    .serviceFeeAmount(new BigDecimal(order.getString("service_fee_amount")))
                    .detailPageUrl(order.getString("return_url"))
                    .payStatus(order.getString("pay_status"))
                    .orderStatus(order.getString("order_status"))
                    .createTime(order.getString("create_time"))
                    .baseInfo(JSON.parseObject(order.getString("base_info")))
                    .extraInfo(JSON.parseObject(order.getString("extra_info")))
                    .build();
        });
    }
}