package com.jsrxjt.mobile.domain.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * 交易退款信息实体
 * <AUTHOR>
 * @since 2025/9/26
 */
@Data
public class TradeRefundInfoEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 爷-业务订单号
     */
    private String orderNo;

    /**
     * 爷-外部业务订单号
     */
    private String outOrderNo;

    /**
     * 爷-交易订单ID
     */
    private Long tradeId;

    /**
     * 父-交易单号
     */
    private String tradeNo;

    /**
     * 父-退款单ID
     */
    private Long refundId;

    /**
     * 父-退款单
     */
    private String refundNo;

    /**
     * 父-外部退款单
     */
    private String outRefundNo;

    /**
     * 父-交易明细单ID
     */
    private Long tradeInfoId;

    /**
     * 父-交易明细单号
     */
    private String tradeInfoNo;

    /**
     * 父-外部交易明细单号
     */
    private String outTradeInfoNo;

    /**
     * 子-退款单号
     */
    private String refundInfoNo;

    /**
     * 支付通道 CARD 卡系统 WECHAT_PAY 微信支付
     */
    private String refundChannel;

    /**
     * 微信交易ID（退款需要使用该参数）
     */
    private String wxTransactionId;

    /**
     * 微信退款单号
     */
    private String wxRefundId;

    /**
     * 微信支付金额（分）
     */
    private Long wxPayAmount;

    /**
     * 渠道号
     */
    private String chanId;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 门店号
     */
    private String storeId;

    /**
     * 终端号
     */
    private String termId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 微信退款状态 SUCCESS 成功 FAIL 失败 REFUNDING 退款中
     */
    private String wxRefundStatus;

    /**
     * 退款状态 SUCCESS 成功 FAIL 失败 REFUNDING 退款中
     */
    private String refundStatus;

    /**
     * 退款金额（分）
     */
    private Long refundAmount;

    /**
     * 交易卡类型 RB 黑金卡 RW 白金卡 RBIZ 商联卡 RR 红卡 WX 微信 LT 工会凭证 VT 提货凭证
     */
    private String cardTradeType;

    /**
     * 业务卡类型 RXHK 瑞祥红卡 SL 商联卡 HJ 黑金卡 BJ 白金卡
     */
    private String cardBusinessType;

    /**
     * 售后时间
     */
    private LocalDateTime refundAt;

    /**
     * 微信退款时间
     */
    private LocalDateTime wxRefundAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 删除时间（逻辑删除）
     */
    private LocalDateTime deletedAt;

    /**
     * 获取退款金额（元）
     */
    public BigDecimal getRefundAmountInYuan() {
        if (refundAmount == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(refundAmount).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

}
