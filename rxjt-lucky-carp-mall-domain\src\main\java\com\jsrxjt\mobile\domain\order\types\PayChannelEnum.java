package com.jsrxjt.mobile.domain.order.types;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付通道枚举
 * <AUTHOR>
 * @since 2025/8/15
 */
@Getter
@AllArgsConstructor
public enum PayChannelEnum {
    
    CARD("CARD", "卡系统"),
    WECHAT_PAY("WECHAT_PAY", "微信支付"),
    WECHAT_PAY_EXCHANGE("WECHAT_PAY_EXCHANGE", "微信支付兑换");
    
    private final String code;
    private final String desc;

    public static PayChannelEnum fromCode(String payChannel) {
        for (PayChannelEnum value : values()) {
            if (value.code.equals(payChannel)) {
                return value;
            }
        }
        return null;
    }
}