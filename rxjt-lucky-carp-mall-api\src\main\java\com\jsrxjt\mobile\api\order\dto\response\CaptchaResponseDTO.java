package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 图形验证码响应DTO
 * 
 * <AUTHOR>
 * @since 2025/11/25
 */
@Data
@Schema(description = "图形验证码响应DTO")
public class CaptchaResponseDTO {

    /**
     * 验证码唯一标识
     */
    @Schema(description = "验证码唯一标识，用于后续验证", example = "captcha_123456789")
    private String captchaId;

    /**
     * 验证码图片Base64编码
     */
    @Schema(description = "验证码图片Base64编码", example = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
    private String captchaImage;

    /**
     * 验证码过期时间（秒）
     */
    @Schema(description = "验证码过期时间（秒）", example = "300")
    private Integer expireTime;
}
