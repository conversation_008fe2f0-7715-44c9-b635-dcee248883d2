
package com.jsrxjt.mobile.api.customer.types;

/**
 * 用户状态枚举
 */
public enum CustomerStatusEnum {

    DISABLE(0, "禁用"),

    ENABLE(1, "启用"),

    DEACTIVATED(2, "已注销");

    CustomerStatusEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    private final int type;

    private final String name;

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

}
