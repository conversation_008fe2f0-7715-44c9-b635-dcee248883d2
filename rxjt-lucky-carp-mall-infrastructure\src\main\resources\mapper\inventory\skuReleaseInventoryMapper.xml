<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsrxjt.mobile.infra.inventory.persistent.mapper.SkuReleaseInventoryMapper">
    <resultMap id="SkuReleaseInventoryResult" type="com.jsrxjt.mobile.infra.inventory.persistent.po.SkuReleaseInventoryPO">
        <id column="id" property="id"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="product_type" property="productType"/>
        <result column="plan_id" property="planId"/>
        <result column="policy_id" property="policyId"/>
        <result column="release_status" property="releaseStatus"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="remaining_quantity" property="remainingQuantity"/>
        <result column="sold_quantity" property="soldQuantity"/>
        <result column="release_start_time" property="releaseStartTime"/>
        <result column="release_end_time" property="releaseEndTime"/>
        <result column="create_time" property="createTime"/>
        <result column="mod_time" property="modTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="mod_id" property="modId"/>
        <result column="create_id" property="createId"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="base_columns">
        id, product_sku_id, product_type, plan_id, policy_id, release_status,
        total_quantity, remaining_quantity, sold_quantity,
        release_start_time, release_end_time,
        create_time, mod_time, del_flag, mod_id, create_id
    </sql>

    <!-- 批量插入SKU放量库存记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sku_release_inventory (
            product_sku_id, product_type, plan_id, policy_id, release_status,
            total_quantity, remaining_quantity, sold_quantity,
            release_start_time, release_end_time,
            create_time, mod_time, mod_id, create_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.productSkuId}, #{item.productType}, #{item.planId}, #{item.policyId}, #{item.releaseStatus},
            #{item.totalQuantity}, #{item.remainingQuantity}, #{item.soldQuantity},
            #{item.releaseStartTime}, #{item.releaseEndTime},
            #{item.createTime}, #{item.modTime}, #{item.modId}, #{item.createId}
            )
        </foreach>
    </insert>
    
    <!-- 更新已开始但未结束的放量库存状态为已生效(1) -->
    <update id="updateStartedInventoryStatus">
        UPDATE sku_release_inventory
        SET release_status = 1, mod_time = NOW()
        WHERE product_type = #{productType}
          AND release_status = 0
          AND release_start_time &lt;= #{currentTime}
          AND release_end_time > #{currentTime}
          AND del_flag = 0
    </update>
    
    <!-- 更新已结束的放量库存状态为已结束(2) -->
    <update id="updateEndedInventoryStatus">
        UPDATE sku_release_inventory
        SET release_status = 2, mod_time = NOW()
        WHERE product_type = #{productType}
          AND release_status IN (0, 1)
          AND release_end_time &lt;= #{currentTime}
          AND del_flag = 0
    </update>

    <!-- 根据计划ID和政策ID查询未结束的放量库存记录 -->
    <select id="selectListUnfinishedByPlanId" resultMap="SkuReleaseInventoryResult">
        SELECT
        <include refid="base_columns"/>
        FROM sku_release_inventory
        WHERE plan_id = #{planId}
          AND policy_id = #{policyId}
          AND release_status IN (0, 1)
          AND del_flag = 0
    </select>

    <!-- 批量更新放量库存记录 -->
    <update id="batchUpdateReleaseInventories" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE sku_release_inventory
            SET release_start_time = #{item.releaseStartTime},
                release_end_time = #{item.releaseEndTime},
                total_quantity = #{item.totalQuantity},
                remaining_quantity = #{item.remainingQuantity},
                mod_time = #{item.modTime}
            WHERE id = #{item.id}
              AND del_flag = 0
        </foreach>
    </update>

    <!-- 更新未结束的放量库存记录为删除状态 -->
    <update id="updateUnfinishedAsDeleted">
        UPDATE sku_release_inventory
        SET del_flag = 1,
            mod_time = #{modTime}
        WHERE plan_id = #{planId}
          AND release_status IN (0, 1)
          AND del_flag = 0
    </update>

    <!-- 检查指定计划ID和放量日期的放量库存记录是否存在 -->
    <select id="countByPlanIdAndDateRange" resultType="int">
        SELECT COUNT(1)
        FROM sku_release_inventory
        WHERE plan_id = #{planId}
          AND DATE(release_start_time) >= #{startDate}
          AND DATE(release_start_time) &lt;= #{endDate}
          AND del_flag = 0
    </select>

    <!-- 检查指定计划ID、政策ID和放量日期的放量库存记录是否存在 -->
    <select id="countByPlanIdAndPolicyIdAndDateRange" resultType="int">
        SELECT COUNT(1)
        FROM sku_release_inventory
        WHERE plan_id = #{planId}
          AND policy_id = #{policyId}
          AND DATE(release_start_time) >= #{startDate}
          AND DATE(release_start_time) &lt;= #{endDate}
          AND del_flag = 0
    </select>

    <!-- 根据产品类型、SKU ID、政策ID查询有效的放量库存记录 -->
    <select id="selectValidByProductTypeAndSkuIdAndPolicyId" resultMap="SkuReleaseInventoryResult">
        SELECT
        <include refid="base_columns"/>
        FROM sku_release_inventory
        WHERE product_type = #{productType}
          AND product_sku_id = #{skuId}
          AND policy_id = #{policyId}
          AND release_status In (0, 1)
          AND release_start_time &lt;= NOW()
          AND release_end_time > NOW()
          AND del_flag = 0
        ORDER BY id ASC
    </select>

    <!-- 根据ID扣减库存数量 -->
    <update id="reduceInventoryById">
        UPDATE sku_release_inventory
        SET remaining_quantity = remaining_quantity - #{quantity},
            sold_quantity = sold_quantity + #{quantity},
            mod_time = NOW()
        WHERE id = #{id}
          AND remaining_quantity >= #{quantity}
          AND del_flag = 0
    </update>

    <!-- 批量根据ID扣减库存数量 -->
    <update id="batchReduceInventoryById" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE sku_release_inventory
            SET remaining_quantity = remaining_quantity - #{item.quantity},
                sold_quantity = sold_quantity + #{item.quantity},
                mod_time = NOW()
            WHERE id = #{item.id}
              AND remaining_quantity >= #{item.quantity}
              AND del_flag = 0
        </foreach>
    </update>

    <!-- 根据ID恢复库存数量 -->
    <update id="recoverInventoryById">
        UPDATE sku_release_inventory
        SET remaining_quantity = remaining_quantity + #{quantity},
            sold_quantity = sold_quantity - #{quantity},
            mod_time = NOW()
        WHERE id = #{id}
          AND sold_quantity >= #{quantity}
          AND del_flag = 0
    </update>

    <!-- 批量根据ID恢复库存数量 -->
    <update id="batchRecoverInventoryById" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE sku_release_inventory
            SET remaining_quantity = remaining_quantity + #{item.quantity},
                sold_quantity = sold_quantity - #{item.quantity},
                mod_time = NOW()
            WHERE id = #{item.id}
              AND sold_quantity >= #{item.quantity}
              AND del_flag = 0
        </foreach>
    </update>

    <!-- 根据产品SKU ID、产品类型、政策ID和日期查询当天的放量库存记录 -->
    <select id="selectTodayReleaseInventoryBySkuAndPolicy" resultMap="SkuReleaseInventoryResult">
        SELECT
        <include refid="base_columns"/>
        FROM sku_release_inventory
        WHERE product_sku_id = #{productSkuId}
          AND product_type = #{productType}
          AND policy_id = #{policyId}
          AND DATE(release_start_time) = #{releaseDate}
          AND del_flag = 0
        ORDER BY release_start_time ASC
    </select>

</mapper>