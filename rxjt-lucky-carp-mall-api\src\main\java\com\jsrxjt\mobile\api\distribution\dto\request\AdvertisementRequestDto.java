package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 广告点击请求参数
 * @Author: ywt
 * @Date: 2025-06-10 16:00
 * @Version: 1.0
 */
@Data
public class AdvertisementRequestDto {
    @Schema(description = "广告id")
    @NotNull(message = "广告id为空错误")
    private Long advId;
}
