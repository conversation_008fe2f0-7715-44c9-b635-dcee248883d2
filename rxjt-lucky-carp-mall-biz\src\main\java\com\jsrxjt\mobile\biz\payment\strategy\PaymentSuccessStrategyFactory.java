package com.jsrxjt.mobile.biz.payment.strategy;

import com.jsrxjt.common.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 支付成功策略工厂
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/1/27
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PaymentSuccessStrategyFactory {

    private final List<PaymentSuccessStrategy> strategies;

    /**
     * 根据扁平化产品类型获取对应的处理策略
     * 
     * @param flatProductType 扁平化产品类型
     * @return 对应的处理策略
     * @throws BizException 如果找不到对应策略
     */
    public PaymentSuccessStrategy getStrategy(Integer flatProductType) {
        for (PaymentSuccessStrategy strategy : strategies) {
            if (strategy.supports(flatProductType)) {
                log.debug("找到扁平化产品类型 {} 对应的处理策略: {}",
                        flatProductType, strategy.getClass().getSimpleName());
                return strategy;
            }
        }

        log.error("未找到扁平化产品类型 {} 对应的支付成功处理策略", flatProductType);
        throw new BizException("未找到扁平化产品类型 " + flatProductType + " 对应的支付成功处理策略");
    }
}