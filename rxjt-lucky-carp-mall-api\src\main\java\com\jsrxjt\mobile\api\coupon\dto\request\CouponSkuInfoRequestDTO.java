package com.jsrxjt.mobile.api.coupon.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 卡券sku信息请求参数
 * @Author: ywt
 * @Date: 2025-04-29 09:34
 * @Version: 1.0
 */
@Data
@Schema(description = "卡券sku信息请求参数")
public class CouponSkuInfoRequestDTO {
    @Schema(description = "卡券的spuid")
    @NotNull(message = "卡券spuid为空错误")
    private Long couponSpuId;
    @Schema(description = "三级地址id")
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
    @Schema(description = "卡券的skuid，本字段用于标记选中的sku，若不传默认选中第一个sku")
    private Long couponSkuId;
}
