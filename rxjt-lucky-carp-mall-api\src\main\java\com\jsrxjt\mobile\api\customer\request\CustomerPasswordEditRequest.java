package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CustomerPasswordEditRequest extends BaseParam {

    @Schema(description = "用户密码")
    @NotBlank(message = "用户密码不能为空")
    private String newPassword;

    @Schema(description = "验证码 初始化密码时不传 编辑密码时必传")
    private String verificationCode;

}
