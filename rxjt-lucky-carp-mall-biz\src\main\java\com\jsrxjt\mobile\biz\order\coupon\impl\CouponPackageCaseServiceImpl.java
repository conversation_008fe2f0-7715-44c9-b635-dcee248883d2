package com.jsrxjt.mobile.biz.order.coupon.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageDelRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPayDetailPageRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.*;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.order.dto.request.HisCouponQueryRequest;
import com.jsrxjt.mobile.api.order.dto.response.HisCouponInfoResponse;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.order.coupon.CouponPackageCaseService;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.coupon.repository.CouponGoodsRepository;
import com.jsrxjt.mobile.domain.coupon.repository.CouponGoodsSkuRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.gateway.FuliquanShopTicketGateway;
import com.jsrxjt.mobile.domain.customer.gateway.request.HisCouponQueryRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.HisCouponListResponseDTO;
import com.jsrxjt.mobile.domain.order.entity.*;
import com.jsrxjt.mobile.domain.order.query.CouponPackageListQuery;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderItemRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.packages.entity.PackageSkuSubJobEntity;
import com.jsrxjt.mobile.domain.packages.repository.PackageSubSkuRepository;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformCardCodeRequest;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformGateway;
import com.jsrxjt.mobile.domain.pickplatform.request.PickCardItemRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayInfoRequest;
import com.jsrxjt.mobile.domain.pickplatform.response.*;
import com.jsrxjt.mobile.domain.product.entity.ProductBrandEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductOffsetPageEntity;
import com.jsrxjt.mobile.domain.product.repository.ProductBrandRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductDialogRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductOffsetPageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 卡包服务实现类
 * <AUTHOR>
 * @date 2025/07/22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CouponPackageCaseServiceImpl implements CouponPackageCaseService {

    private final OrderDeliveryRepository orderDeliveryRepository;
    private final OrderRepository orderRepository;
    private final ProductOffsetPageRepository productOffsetPageRepository;
    private final OrderItemRepository orderItemRepository;
    private final CouponGoodsSkuRepository couponGoodsSkuRepository;
    private final CouponGoodsRepository couponGoodsRepository;
    private final PackageSubSkuRepository packageSubSkuRepository;
    private final PickPlatformGateway pickPlatformGateway;
    private final ProductBrandRepository productBrandRepository;
    private final ProductDialogRepository productDialogRepository;
    private final FuliquanShopTicketGateway fuliquanShopTicketGateway;
    private final CustomerService customerService;


    @Override
    public CouponPackagePageDTO<CouponPackageListResponseDTO> getCouponPackageList(Long customerId, CouponPackageListRequestDTO request) {

        CouponPackageListQuery query = CouponPackageListQuery.builder()
                .customerId(customerId)
                .delFlag(request.getDelFlag())
                .queryName(request.getQueryName())
                .pageNum(request.getToPage())
                .pageSize(request.getPageRows())
                .build();

        PageDTO<CouponPackageEntity> couponPackagePage = orderDeliveryRepository.getCustomerCouponPackageList(query);

        PageDTO<CouponPackageListResponseDTO> couponPackageListPage = PageDTO.<CouponPackageListResponseDTO>builder()
                .current(couponPackagePage.getCurrent())
                .size(couponPackagePage.getSize())
                .pages(couponPackagePage.getPages())
                .total(couponPackagePage.getTotal())
                .build();
        if (CollectionUtil.isNotEmpty(couponPackagePage.getRecords())){
            couponPackageListPage.setRecords(couponPackagePage.getRecords().stream().map(this::convertToDTO).collect(Collectors.toList()));
        }
        CouponPackagePageDTO couponPackagePageDTO = new CouponPackagePageDTO(couponPackageListPage);
        couponPackagePageDTO.setTotalCouponNum(orderDeliveryRepository.getCustomerCouponPackageNum(query));
        return couponPackagePageDTO;
    }

    /**
     * 卡包详情
     *
     * @param couponPackageId
     * @return
     */
    @Override
    public CouponPackageDetailResponseDTO getCouponPackageDetail(Long couponPackageId) {
        CouponPackageDetailResponseDTO dto = new CouponPackageDetailResponseDTO();
        OrderDeliveryEntity orderDelivery = orderDeliveryRepository.findById(couponPackageId);
        if(orderDelivery.getDelFlag()==1){
            throw new BizException("该卡券已删除");
        }
        String orderNo = orderDelivery.getOrderNo();
        OrderInfoEntity byOrderNo = orderRepository.findByOrderNo(orderNo);
        if(byOrderNo == null){
            throw new BizException("订单不存在");
        }
        //获取sku名称
        Long orderItemId = orderDelivery.getOrderItemId();
        OrderItemEntity orderItem = orderItemRepository.findById(orderItemId);
        if(orderItem == null){
            throw new BizException("未找到该订单明细");
        }
        //获取使用说明
        Long productSpuId = byOrderNo.getProductSpuId();
        Long subSkuId = orderDelivery.getMiniSkuId();
        Integer flatProductType = orderItem.getFlatProductType();
        dto.setCouponPackageId(orderDelivery.getId());
        dto.setCouponCode(orderDelivery.getCouponCode());
        dto.setCouponPin(orderDelivery.getCouponPin());
        dto.setCouponUrl(orderDelivery.getCouponUrl());
        dto.setCouponUrlPass(orderDelivery.getCouponUrlPass());
        dto.setCouponCrc(orderDelivery.getCouponCrc());
        dto.setLogoUrl(orderItem.getProductLogo());
        dto.setAmountName(orderItem.getFaceAmount().stripTrailingZeros().toPlainString()+"元");
        dto.setAmount(orderItem.getFaceAmount());
        dto.setSpuId(productSpuId);
        dto.setSkuId(orderItem.getProductSkuId());
        //获取核销类型
        SelCouponNameResponseDTO couponInfo1 = getCouponInfo(byOrderNo, orderItem, orderDelivery);
        dto.setFlqType(couponInfo1.getFlgType());
        dto.setCouponName(couponInfo1.getBrandName() + couponInfo1.getCouponAmountName());
        dto.setUseManual(couponInfo1.getRemark());
        dto.setBackgroundColor(couponInfo1.getBackgroundColor());
        dto.setOffsetDialog(couponInfo1.getOffsetDialog());
        dto.setFlatProductType(flatProductType);
        dto.setAmountName(couponInfo1.getAmount().stripTrailingZeros().toPlainString()+"元");
        dto.setAmount(couponInfo1.getAmount());
        dto.setCouponVerificationType(orderDelivery.getCouponVerificationType());
        //自发券动态码-购券明细
        if(couponInfo1.getIsSelfCoupon() != null && couponInfo1.getIsSelfCoupon().equals(1)){
           dto.setCouponName(couponInfo1.getSpuName());  //自发券名称使用spu name
           SelCouponCodeResponseDTO selCouponCodeResponseDTO = convertToDTO(orderDelivery, byOrderNo, couponInfo1);
           dto.setSelCouponCodeResponseDTO(selCouponCodeResponseDTO);
        }
        return dto;
    }

    /**
     * 消费明细
     *
     * @param request
     */
    @Override
    public PageDTO<SelCouponPayDetailResponseDTO> consumeList(CouponPayDetailPageRequestDTO request) {
        Long spuId = request.getSpuId();
        Long customerId = StpUtil.getLoginIdAsLong();
       // Long customerId = 378632859374850L;
        CouponGoodsEntity couponInfo = couponGoodsRepository.getCouponInfo(spuId);
        if(couponInfo == null){
            throw new BizException("未找到该卡券");
        }
        Integer pickProductId = couponInfo.getPickProductId();

        PickPlatformPayInfoRequest payInfoRequest = new PickPlatformPayInfoRequest();
        payInfoRequest.setUserNo(String.valueOf(customerId));
        payInfoRequest.setProductId(String.valueOf(pickProductId));
        payInfoRequest.setPageSize(request.getPageSize());
        payInfoRequest.setPage(request.getPageNum());
        PickPlatformPayInfoPageResponse cardPayInfo = pickPlatformGateway.getCardPayInfo(payInfoRequest);
        if(cardPayInfo == null){
           return PageDTO.emptyBuild(request.getPageNum().longValue(),request.getPageSize().longValue());
        }
        List<PickPlatformPayInfoResponse> list = cardPayInfo.getList();
        List<SelCouponPayDetailResponseDTO> selCouponPayDetailResponseDTOS = BeanUtil.copyToList(list, SelCouponPayDetailResponseDTO.class);
        return PageDTO.build(selCouponPayDetailResponseDTOS,cardPayInfo.getCount().longValue(),request.getPageSize().longValue(), request.getPageNum().longValue());
    }


    private SelCouponCodeResponseDTO  convertToDTO(OrderDeliveryEntity entity,OrderInfoEntity orderInfoEntity, SelCouponNameResponseDTO couponInfo){
        SelCouponCodeResponseDTO dto = new SelCouponCodeResponseDTO();
        Long customerId = StpUtil.getLoginIdAsLong();
        //Long customerId = 378632859374850L;
        String firstCouponUrl = entity.getCouponUrl();
        String firstCouponOffsetCode = firstCouponUrl.substring(firstCouponUrl.lastIndexOf("=") + 1);

        //核销编号List
        List<String> couponOffsetCodeList = new ArrayList<>();
        //Long skuId = orderInfoEntity.getProductSkuId();
        List<SelOrderDeliveryDetailEntity> selOrderDeliveryDetailList = orderDeliveryRepository.getSelOrderDeliveryDetailList(customerId, couponInfo.getPickProductId());
        if(CollectionUtil.isEmpty(selOrderDeliveryDetailList)){
            log.error("未找到该用户的卡券");
           return dto;
        }
        couponOffsetCodeList = selOrderDeliveryDetailList.stream()
                .filter(selOrderDeliveryDetailEntity -> StrUtil.isNotBlank(selOrderDeliveryDetailEntity.getCheckCode()))
                .map(SelOrderDeliveryDetailEntity::getCheckCode)
                .collect(Collectors.toList());
        //将couponOffsetCodeList中firstCouponOffsetCode放到第一位
        couponOffsetCodeList.remove(firstCouponOffsetCode);
        couponOffsetCodeList.add(0, firstCouponOffsetCode);
        //获取动态码
        PickPlatformCardCodeRequest request = new PickPlatformCardCodeRequest();
        request.setUserNo(String.valueOf(customerId));
        request.setTimestamp(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        request.setNonce(RandomUtil.randomString(16));
        //couponOffsetCodeList转字符串
        request.setCheckNo(couponOffsetCodeList.stream().collect(Collectors.joining(",")));
        PickPlatformCardCodeResponse cardCode = pickPlatformGateway.getCardCode(request);
        if(cardCode == null){
          log.error("获取动态码失败");
          return dto;
        }
        dto.setCode(cardCode.getCode());
        if (cardCode.getBalance() != null){
            dto.setBalance(String.valueOf(Double.valueOf(cardCode.getBalance())/100));
        }else {
            dto.setBalance("0");
        }
        dto.setValidTime(cardCode.getValidTime());
        //获取购卡明细
        PickCardItemRequest pickCardItemRequest = new PickCardItemRequest();
        pickCardItemRequest.setCheckNo(couponOffsetCodeList.stream().collect(Collectors.joining(",")));
        pickCardItemRequest.setTimestamp(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        pickCardItemRequest.setNonce(RandomUtil.randomString(16));
        PickCardItemResponse cardInfo = pickPlatformGateway.getCardInfo(pickCardItemRequest);
        if(cardInfo != null && CollectionUtil.isNotEmpty(cardInfo.getList()) ){
            Map<String, SelOrderDeliveryDetailEntity> map =
                    selOrderDeliveryDetailList.stream()
                            .filter(d -> StrUtil.isNotBlank(d.getCheckCode()))
                            .collect(Collectors.toMap(
                                    SelOrderDeliveryDetailEntity::getCheckCode,
                                    v -> v,
                                    (k1, k2) -> k1));
            List<SelCouponCodeDetailResponseDTO> detailList = new ArrayList<>();
            List<SelCouponCodeDetailResponseDTO> historyList = new ArrayList<>();
            List<PickCardItemDetailResponse> list = cardInfo.getList();
            for (PickCardItemDetailResponse detail : list) {
                SelOrderDeliveryDetailEntity selOrderDeliveryDetailEntity = map.get(detail.getCheckNo());
                if(selOrderDeliveryDetailEntity != null){
                    if("USABLE".equals(detail.getCardStatus())){
                        SelCouponCodeDetailResponseDTO detailDTO = new SelCouponCodeDetailResponseDTO();
                        detailDTO.setCouponCode(detail.getCardNo());
                        detailDTO.setCouponName(selOrderDeliveryDetailEntity.getProductName());
                        detailDTO.setOrderDate(selOrderDeliveryDetailEntity.getOrderTime());
                        detailDTO.setCouponUrl(selOrderDeliveryDetailEntity.getCouponUrl());
                        detailDTO.setValidTime(detail.getValidTime());
                        detailDTO.setCouponImgUrl(couponInfo.getImgUrl());
                        if (detail.getBalance() != null){
                            detailDTO.setBalance(String.valueOf(Double.valueOf(detail.getBalance())/100));
                        }else {
                            detailDTO.setBalance("0");
                        }
                        detailDTO.setCardStatus(detail.getCardStatus());
                        detailList.add(detailDTO);
                   }else{
                        SelCouponCodeDetailResponseDTO historyDTO = new SelCouponCodeDetailResponseDTO();
                        historyDTO.setCouponCode(detail.getCardNo());
                        historyDTO.setCouponName(selOrderDeliveryDetailEntity.getProductName());
                        historyDTO.setOrderDate(selOrderDeliveryDetailEntity.getOrderTime());
                        historyDTO.setCouponUrl(selOrderDeliveryDetailEntity.getCouponUrl());
                        historyDTO.setValidTime(detail.getValidTime());
                        historyDTO.setCouponImgUrl(couponInfo.getImgUrl());
                        if (detail.getBalance() != null){
                            historyDTO.setBalance(String.valueOf(Double.valueOf(detail.getBalance())/100));
                        }else {
                            historyDTO.setBalance("0");
                        }
                        historyDTO.setCardStatus(detail.getCardStatus());
                        historyList.add(historyDTO);
                   }
                }
            }
            dto.setDetailList(detailList);
            dto.setHistoryList(historyList);
        }
        return dto;
    }

    private SelCouponNameResponseDTO getCouponInfo(OrderInfoEntity orderInfoEntity
            ,OrderItemEntity orderItemEntity,OrderDeliveryEntity orderDeliveryEntity){
        SelCouponNameResponseDTO dto = new SelCouponNameResponseDTO();

        Integer flgType = null;
        Integer isSelfConpon = null;
        Integer flatProductType = orderItemEntity.getFlatProductType();
        String brandName = null;
        Long brandId = null;
        String spuName = null;
        String couponAmountName = null;
        String backgroundColor = null;
        String remark = null;
        Integer pickProductId = null;
        Integer productType = null;
        BigDecimal amount = null;
        String imgUrl = null;
        if(FlatProductTypeEnum.NORMAL_COUPON.getType().equals(flatProductType) || FlatProductTypeEnum.PINO_COUPON.getType().equals(flatProductType)){
            CouponGoodsEntity couponInfo = couponGoodsRepository.getCouponInfoIncludeDel(orderInfoEntity.getProductSpuId());
            if(couponInfo != null){
                flgType = couponInfo.getFlqType();
                isSelfConpon = couponInfo.getIsSelfCoupon();
                brandId = couponInfo.getBrandId();
                spuName = couponInfo.getCouponSpuName();
                pickProductId = couponInfo.getPickProductId();
                imgUrl = couponInfo.getImgUrl();
                CouponGoodsSkuEntity couponGoodsSkuEntity = couponGoodsSkuRepository.getCouponSkuInfoIncludeDel(orderItemEntity.getProductSkuId());
                couponAmountName = couponGoodsSkuEntity.getAmountName();
                List<ProductOffsetPageEntity> offsetList = productOffsetPageRepository.findBySpuIdWithDel(orderInfoEntity.getProductSpuId(), (byte)1);
                if(CollectionUtil.isNotEmpty(offsetList)){
                    ProductOffsetPageEntity offset = offsetList.get(0);
                    backgroundColor = offset.getBackgroundColor();
                    remark = offset.getRemark();
                }
                amount = couponGoodsSkuEntity.getAmount();
                productType = ProductTypeEnum.COUPON.getType();
            }

        }else if(FlatProductTypeEnum.PACKAGE.getType().equals(flatProductType)){
            PackageSkuSubJobEntity packageSubSkuById = packageSubSkuRepository.getPackageSubSkuIncludeDelById(orderDeliveryEntity.getMiniSkuId());
            if(packageSubSkuById != null){
                flgType = packageSubSkuById.getFlqType();
                couponAmountName = packageSubSkuById.getAmountName();
                backgroundColor = packageSubSkuById.getBackgroundColor();
                remark = packageSubSkuById.getScenarios();
                productType = ProductTypeEnum.PACKAGE.getType();
                amount = packageSubSkuById.getAmount();
            }
        }
        String offsetDialog = productDialogRepository.getDialogWithDel(orderInfoEntity.getProductSpuId(), productType, 2);
        if (brandId == null && orderInfoEntity.getBrandId() != null){
            brandId = orderInfoEntity.getBrandId();
        }
        if (brandId != null){
            ProductBrandEntity productBrandEntity = productBrandRepository.getBrandInfoIncludeDelById(brandId);
            brandName = productBrandEntity.getBrandName();
        }
        SelCouponNameResponseDTO nameDto = new SelCouponNameResponseDTO();
        nameDto.setBackgroundColor(backgroundColor);
        nameDto.setRemark(remark);
        nameDto.setFlgType(flgType);
        nameDto.setIsSelfCoupon(isSelfConpon);
        nameDto.setBrandName(brandName);
        nameDto.setCouponAmountName(couponAmountName);
        nameDto.setSpuName(spuName);
        nameDto.setPickProductId(pickProductId);
        nameDto.setOffsetDialog(offsetDialog);
        nameDto.setAmount(amount);
        nameDto.setImgUrl(imgUrl);
        return nameDto;
    }

    /**
     * 放入回收站
     *
     * @param request
     */
    @Override
    public void putInRecycleBin(CouponPackageDelRequestDTO request) {
        List<Long> couponPackageIdList = request.getCouponPackageIdList();
        //回收站
        orderDeliveryRepository.updateOrderDelivery(couponPackageIdList,0,2);
    }

    /**
     * 回收站恢复
     *
     * @param request
     */
    @Override
    public void restoreRecycleBin(CouponPackageDelRequestDTO request) {
        List<Long> couponPackageIdList = request.getCouponPackageIdList();
        //回收站
        orderDeliveryRepository.updateOrderDelivery(couponPackageIdList,2,0);
    }

    /**
     * 删除卡包
     *
     * @param request
     */
    @Override
    public void deleteCouponPackage(CouponPackageDelRequestDTO request) {
        List<Long> couponPackageIdList = request.getCouponPackageIdList();
        //回收站
        orderDeliveryRepository.updateOrderDelivery(couponPackageIdList,2,1);
    }

    @Override
    public Long getCouponPackageIdForOrder(String orderNo) {
        Long couponPackageId = orderDeliveryRepository.getCouponPackageId(orderNo);
        if (couponPackageId != null){
            return couponPackageId;
        }
        return null;
    }

    @Override
    public  List<HisCouponInfoResponse> getHisCouponList(HisCouponQueryRequest request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        CustomerEntity customerEntity = customerService.getCustomerById(customerId);
        if (customerEntity == null || customerEntity.getDelFlag() == 1 || customerEntity.getStatus() != 1) {
            throw new BizException("该会员账号不存在或注销");
        }
        HisCouponQueryRequestDTO requestDTO = new HisCouponQueryRequestDTO();
        requestDTO.setVipId(customerEntity.getVipId());
        requestDTO.setLabel(request.getLabel());
        requestDTO.setPage(Long.valueOf(request.getPageNum()));
        requestDTO.setNonce(IdUtil.fastSimpleUUID());
        requestDTO.setTimestamp(Instant.now().getEpochSecond());
        List<HisCouponListResponseDTO> hisCouponList = fuliquanShopTicketGateway.getHisCouponList(requestDTO);
        if (CollectionUtils.isEmpty(hisCouponList)) {
            return null;
        }
        return hisCouponList.stream()
                .map(this::convertToHisCouponInfoResponse)
                .collect(Collectors.toList());
    }

    private HisCouponInfoResponse convertToHisCouponInfoResponse(HisCouponListResponseDTO responseDTO) {
        HisCouponInfoResponse response = new HisCouponInfoResponse();
        response.setLabel(responseDTO.getLabel());
        response.setCouponName(responseDTO.getFullName());
        response.setCouponImg(responseDTO.getPic());
        response.setCouponUrl(responseDTO.getCouponUrl());
        response.setCreateTime(new Date(responseDTO.getCreateTime()*1000));
        return response;
    }

    private CouponPackageListResponseDTO convertToDTO(CouponPackageEntity entity) {
        return  CouponPackageListResponseDTO.builder()
                .brandName(entity.getBrandName())
                .couponNum(entity.getCouponNum())
                .brandLogo(entity.getBrandLogo())
                .couponList(entity.getCouponList().stream().map(couponPackageListEntity ->{
                            CouponPackageResponseDTO couponPackageResponseDTO = new CouponPackageResponseDTO();
                            BeanUtil.copyProperties(couponPackageListEntity, couponPackageResponseDTO);
                            return couponPackageResponseDTO;}
                        ).collect(Collectors.toList()))
                .build();
    }
}
