package com.jsrxjt.adapter.pay.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.jsrxjt.mobile.biz.payment.service.PaymentSuccessCaseService;
import com.jsrxjt.mobile.domain.payment.types.PaymentSuccessMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.MessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 赠券发送消息监听器
 * 
 * <AUTHOR>
 * @date 2025/09/22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendGiftTicketsMessageListener implements MessageListener {

    private final PaymentSuccessCaseService paymentSuccessCaseService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        String msgBody = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();

        log.info("send_gift_tickets_consumer_group received message {}", messageView);
        PaymentSuccessMessage event = JSON.parseObject(msgBody, PaymentSuccessMessage.class);
        paymentSuccessCaseService.handleGiftTicketsSend(event);
        log.info("send_gift_tickets_consumer_group 消费完成 message {}", messageView);
        return ConsumeResult.SUCCESS;
    }
}
