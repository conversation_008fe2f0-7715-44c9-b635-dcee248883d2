package com.jsrxjt.mobile.api.distribution.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ScanPayWssPushDTO {

    private Long customerId;
    @JsonProperty("type")
    private String type;
    @JsonProperty("out_order_no")
    private String outOrderNo;
    @JsonProperty("trade_no")
    private String tradeNo;
    @JsonProperty("is_show")
    private int isShow;
    @JsonProperty("shop_id")
    private Integer shopId;
    @JsonProperty("shop_user_id")
    private Integer shopUserId;
    @JsonProperty("shop_name")
    private String shopName;
    @JsonProperty("pay_channel")
    private String payChannel;
    @JsonProperty("is_set_pay_pass")
    private boolean isSetPayPass;
    @JsonProperty("is_password_free_payment")
    private boolean isPasswordFreePayment;
    @JsonProperty("pay_list")
    private List<ScanPayCardListDTO> payList;
    private String flqOrderNo;
}
