package com.jsrxjt.mobile.infra.gateway.distribution.adapter.xcyMall;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import com.jsrxjt.mobile.infra.gateway.distribution.config.XcyMallChannelConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Created by jeffery.yang on 2025/10/22 19:47
 *
 * @description: 祥采云1688适配器
 * @author: jeffery.yang
 * @date: 2025/10/22 19:47
 * @version: 1.0
 */
@Component
@Slf4j
public class XcyMallAlibabaDistributionChannelAdapter extends AbstractXcyMalltDistributionChannelAdapter {

	protected XcyMallAlibabaDistributionChannelAdapter(HttpClientGateway httpClientGateway, DistributionConfig distributionConfig) {
		super(httpClientGateway, distributionConfig);
	}

	@Override
	protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
		return distributionConfig.getXcyAlibaba();
	}

	@Override
	public DistChannelType getChannelType() {
		return DistChannelType.XCY_ALIBABA;
	}

}
