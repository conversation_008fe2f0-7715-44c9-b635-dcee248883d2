package com.jsrxjt.mobile.api.product.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 多个类目查询请求参数
 */
@Data
public class MultiCategoryRequestDTO  extends BaseParam {

    @Schema(description = "二级类目ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "类目ID列表不能为空")
    private List<String> categoryIds;

    @Schema(description = "区域ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "区域ID不能为空")
    private Integer regionId;
}
