
package com.jsrxjt.mobile.api.customer.types;

/**
 * 验证码发送类型枚举
 */
public enum VerificationCodeSendTypeEnum {

    REGISTER(1, "注册"),
    LOGIN(2, "登录"),
    CHANGE_INFO(3, "修改信息"),
    CHANGE_PHONE(4, "更换手机号"),
    DELETE_ACCOUNT(5, "注销账户");

    private final int code;
    private final String desc;

    VerificationCodeSendTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public static VerificationCodeSendTypeEnum fromCode(int code) {
        for (VerificationCodeSendTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("不支持的验证码类型: " + code);
    }

}
