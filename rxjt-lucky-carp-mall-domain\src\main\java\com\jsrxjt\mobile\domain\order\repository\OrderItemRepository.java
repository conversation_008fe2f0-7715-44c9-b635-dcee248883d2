package com.jsrxjt.mobile.domain.order.repository;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.entity.SubSkuOrderEntity;
import com.jsrxjt.mobile.domain.order.query.OrderListQuery;

/**
 * 订单相关模块
 */
public interface OrderItemRepository {

    OrderItemEntity findById(Long orderItemId);
}
