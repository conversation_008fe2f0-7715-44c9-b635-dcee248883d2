package com.jsrxjt.mobile.domain.ticket.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 优惠券与门店关联表
 */
@Data
public class TicketStoreRelationEntity {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "门店id")
    private Long storeId;

    @Schema(description = "优惠券id")
    private Long ticketId;

    @Schema(description = "门店类型名称")
    private Integer storeTypeId;

    @Schema(description = "门店类型名称")
    private String storeTypeName;

    @Schema(description = "门店地址")
    private String storeDetail;

    @Schema(description = "门店编号")
    private String storeNo;

    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "创建人id")
    private Long createId;

    @Schema(description = "编辑人id")
    private Long modId;

    @Schema(description = "编辑时间")
    private Date modTime;

    @Schema(description = "是否删除标志(0:否 1:是)")
    private Byte delFlag;

    @Schema(description = "删除人id")
    private Integer delId;

    @Schema(description = "删除时间")
    private Date delTime;

}
