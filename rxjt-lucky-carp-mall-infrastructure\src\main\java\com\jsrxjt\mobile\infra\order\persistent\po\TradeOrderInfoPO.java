package com.jsrxjt.mobile.infra.order.persistent.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 交易订单信息表实体类
 * <AUTHOR>
 * @since 2025/8/15
 */
@Data
@TableName("trade_order_info")
public class TradeOrderInfoPO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 父-订单ID
     */
    private Long orderId;

    /**
     * 父-订单号
     */
    private String orderNo;

    /**
     * 父-外部订单号
     */
    private String outOrderNo;

    /**
     * 父-支付订单号
     */
    private String tradeNo;

    /**
     * 支付通道 CARD 卡系统 WECHAT_PAY 微信支付 WECHAT_PAY_EXCHANGE 微信支付兑换
     */
    private String payChannel;

    /**
     * 子-交易单号 微信交易单号
     */
    private String tradeInfoNo;

    /**
     * 子-预付卡交易单号
     */
    private String outTradeInfoNo;

    /**
     * 兑换支付ID
     */
    private Long exchangePayId;

    /**
     * 微信关联商户号ID
     */
    private Long wxMchId;

    /**
     * 微信支付openid
     */
    private String wxOpenId;

    /**
     * 微信交易ID（退款需要使用该参数）
     */
    private String wxTransactionId;

    /**
     * 微信支付兑换的卡充值订单
     */
    private String wxExchangeRechargeOrder;

    /**
     * 微信支付配置
     */
    private String wxPayParams;

    /**
     * 微信自动退款单号 原路退回
     */
    private String wxRefundId;

    /**
     * 渠道号
     */
    private String chanId;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 门店号
     */
    private String storeId;

    /**
     * 终端号
     */
    private String termId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 交易卡类型 RX_RED_CARD 商联商户 RX_BLACK_CARD 黑金商户 RX_WHITE_CARD 白金商户 RX_PICK_CARD 凭证商户
     */
    private String cardTradeType;

    /**
     * 业务卡类型 RXHK 瑞祥红卡 SL 商联卡 HJ 黑金卡 BJ 白金卡
     */
    private String cardBusinessType;

    /**
     * 支付金额（分）
     */
    private Long payAmount;

    /**
     * 支付状态 WAIT_PAY 待支付 SUCCESS_PAY 支付成功 FAIL_PAY 支付失败
     */
    private String payStatus;

    /**
     * 已退款金额（分）
     */
    private Long refundAmount;

    /**
     * 退款状态 WAIT_REFUND 无退款 PART_REFUND 部分退款 FULL_REFUND 全部退款
     */
    private String refundStatus;

    /**
     * 微信支付时间
     */
    private LocalDateTime wxTradeAt;

    /**
     * 支付时间
     */
    private LocalDateTime tradeAt;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 删除时间（逻辑删除）
     */
    @TableLogic("0")
    @TableField("deleted_at")
    private Integer deletedAt;
}