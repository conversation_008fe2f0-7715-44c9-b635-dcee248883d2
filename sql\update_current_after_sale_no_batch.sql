-- 分批刷新t_order表中的current_after_sale_no字段
-- 执行时间：2025-11-03
-- 作者：系统
-- 描述：分批次安全更新订单表中的当前售后单号字段，避免长时间锁表

-- 设置批次大小（每次更新1000条记录）
SET @batch_size = 1000;
SET @total_updated = 0;

-- 创建临时表存储需要更新的数据
DROP TEMPORARY TABLE IF EXISTS temp_order_after_sale_update;
CREATE TEMPORARY TABLE temp_order_after_sale_update (
    order_id BIGINT PRIMARY KEY,
    order_no VARCHAR(64),
    current_after_sale_no VARCHAR(64),
    new_after_sale_no VARCHAR(64),
    INDEX idx_order_id (order_id)
);

-- 插入需要更新的数据到临时表
INSERT INTO temp_order_after_sale_update (order_id, order_no, current_after_sale_no, new_after_sale_no)
SELECT 
    o.id as order_id,
    o.order_no,
    o.current_after_sale_no,
    latest_after_sale.after_sale_no as new_after_sale_no
FROM t_order o
INNER JOIN (
    SELECT 
        a1.order_id,
        a1.after_sale_no,
        ROW_NUMBER() OVER (PARTITION BY a1.order_id ORDER BY a1.create_time DESC, a1.id DESC) as rn
    FROM t_after_sale a1
    WHERE a1.del_flag = 0
) latest_after_sale ON o.id = latest_after_sale.order_id AND latest_after_sale.rn = 1
WHERE o.del_flag = 0
  AND o.after_sale_status IS NOT NULL
  AND (o.current_after_sale_no IS NULL OR o.current_after_sale_no != latest_after_sale.after_sale_no);

-- 显示将要更新的记录数
SELECT COUNT(*) as records_to_update FROM temp_order_after_sale_update;

-- 分批更新的存储过程
DELIMITER $$

DROP PROCEDURE IF EXISTS UpdateCurrentAfterSaleNoBatch$$
CREATE PROCEDURE UpdateCurrentAfterSaleNoBatch()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE batch_count INT DEFAULT 0;
    DECLARE current_batch INT DEFAULT 0;
    DECLARE total_records INT DEFAULT 0;
    
    -- 获取总记录数
    SELECT COUNT(*) INTO total_records FROM temp_order_after_sale_update;
    
    -- 计算批次数
    SET batch_count = CEIL(total_records / @batch_size);
    
    -- 输出开始信息
    SELECT CONCAT('开始分批更新，总记录数: ', total_records, ', 批次数: ', batch_count, ', 每批次: ', @batch_size) as info;
    
    -- 分批更新循环
    WHILE current_batch < batch_count DO
        SET current_batch = current_batch + 1;
        
        -- 执行当前批次的更新
        UPDATE t_order o
        INNER JOIN (
            SELECT order_id, new_after_sale_no
            FROM temp_order_after_sale_update
            ORDER BY order_id
            LIMIT @batch_size OFFSET (current_batch - 1) * @batch_size
        ) batch_data ON o.id = batch_data.order_id
        SET 
            o.current_after_sale_no = batch_data.new_after_sale_no,
            o.mod_time = NOW();
        
        -- 记录当前批次更新的记录数
        SET @total_updated = @total_updated + ROW_COUNT();
        
        -- 输出进度信息
        SELECT CONCAT('完成批次 ', current_batch, '/', batch_count, ', 已更新记录数: ', @total_updated) as progress;
        
        -- 短暂休息，避免长时间占用资源
        DO SLEEP(0.1);
        
    END WHILE;
    
    -- 输出完成信息
    SELECT CONCAT('批量更新完成，总共更新记录数: ', @total_updated) as final_result;
    
END$$

DELIMITER ;

-- 执行分批更新
CALL UpdateCurrentAfterSaleNoBatch();

-- 验证更新结果
SELECT 
    '更新完成后的统计' as description,
    COUNT(*) as total_orders_with_after_sale,
    COUNT(current_after_sale_no) as orders_with_current_after_sale_no,
    COUNT(*) - COUNT(current_after_sale_no) as orders_still_null
FROM t_order 
WHERE del_flag = 0 
  AND after_sale_status IS NOT NULL;

-- 检查更新后的数据样例
SELECT 
    '更新后的数据样例' as description,
    o.id as order_id,
    o.order_no,
    o.after_sale_status,
    o.current_after_sale_no,
    a.after_sale_status as current_after_sale_status,
    a.create_time as after_sale_create_time
FROM t_order o
LEFT JOIN t_after_sale a ON o.current_after_sale_no = a.after_sale_no AND a.del_flag = 0
WHERE o.del_flag = 0 
  AND o.current_after_sale_no IS NOT NULL
ORDER BY o.mod_time DESC
LIMIT 10;

-- 清理临时表和存储过程
DROP TEMPORARY TABLE IF EXISTS temp_order_after_sale_update;
DROP PROCEDURE IF EXISTS UpdateCurrentAfterSaleNoBatch;

-- 最终验证：检查是否有数据不一致的情况
SELECT 
    '数据一致性检查' as description,
    COUNT(*) as inconsistent_records
FROM t_order o
LEFT JOIN t_after_sale a ON o.current_after_sale_no = a.after_sale_no AND a.del_flag = 0
WHERE o.del_flag = 0 
  AND o.current_after_sale_no IS NOT NULL
  AND a.after_sale_no IS NULL;
