package com.jsrxjt.mobile.api.order.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 创建订单成功的响应参数
 * <AUTHOR> Fengping
 * @since 2025/6/16
 **/
@Getter
@Setter
@Schema(description = "创建订单成功的响应参数")
public class OrderCreatedDTO {
    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 订单支付金额
     */
    @Schema(description = "订单支付金额")
    private  BigDecimal paymentAmount;


    /**
     * 加点手续费
     */
    @Schema(description = "加点手续费")
    private BigDecimal totalServiceFee;

    /**
     * 总销售价超额手续费
     */
    @Schema(description = "超额手续费")
    private BigDecimal exceedFee;

    /**
     * 订单支付方式
     */
    @Schema(description = "订单支持的支付方式")
    private String payType;

    /**
     * 订单支付超时秒级时间戳
     */
    @Schema(description = "订单支付超时秒级时间戳")
    private Long payExpireTimestamp;

    /**
     * 下单时间
     */
    @Schema(description = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 赠送券列表
     */
    @Schema(description = "赠送券列表")
    List<GiftTicketResponseDTO> giftTicketList;




}
