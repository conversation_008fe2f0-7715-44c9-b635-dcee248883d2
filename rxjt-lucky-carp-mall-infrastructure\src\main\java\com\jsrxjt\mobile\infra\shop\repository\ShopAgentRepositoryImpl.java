package com.jsrxjt.mobile.infra.shop.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.shop.entity.ShopAgentEntity;
import com.jsrxjt.mobile.domain.shop.repository.ShopAgentRepository;
import com.jsrxjt.mobile.infra.shop.mapper.ShopAgentMapper;
import com.jsrxjt.mobile.infra.shop.po.ShopAgentPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/9/15
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ShopAgentRepositoryImpl implements ShopAgentRepository {

    private final ShopAgentMapper shopAgentMapper;

    @Override
    public ShopAgentEntity findByShopNo(String no) {
        LambdaQueryWrapper<ShopAgentPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopAgentPO::getNo, no)
                .isNull(ShopAgentPO::getDeletedAt);
        ShopAgentPO shopAgentPO = shopAgentMapper.selectOne(queryWrapper);
        return BeanUtil.copyProperties(shopAgentPO, ShopAgentEntity.class);
    }

    @Override
    public List<ShopAgentEntity> getCommonShopList(List<String> adcodeList, String lng, String lat) {
        if (CollectionUtils.isEmpty(adcodeList)
                || StringUtils.isEmpty(lng)
                || StringUtils.isEmpty(lat)) {
            return null;
        }
        List<ShopAgentPO> shopAgentPOList = shopAgentMapper.getCommonShopList(adcodeList, lat, lng);
        return BeanUtil.copyToList(shopAgentPOList, ShopAgentEntity.class);
    }
}
