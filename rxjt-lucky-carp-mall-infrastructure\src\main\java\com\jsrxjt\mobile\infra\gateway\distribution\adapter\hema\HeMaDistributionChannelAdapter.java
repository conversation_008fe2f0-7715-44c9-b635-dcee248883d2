package com.jsrxjt.mobile.infra.gateway.distribution.adapter.hema;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 盒马企业付分销渠道适配器
 * <AUTHOR>
 * @Date 2025/10/16
 */
@Component
@Slf4j
public class HeMaDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

    public HeMaDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                          DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getHema();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.HEMA;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            // 校验必填参数
            if (StringUtils.isBlank(request.getUserId()) || StringUtils.isBlank(request.getMobile())) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID和手机号码不能为空")
                        .build();
            }
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("user_id", request.getUserId());
            params.put("mobile", request.getMobile());

            // 添加公共参数和签名
            addCommonParams(params);

            // 构建请求URL
            String url = config.getBaseUrl() + config.getApiPath() + config.getAccessPath();
            String requestUrl = url + "&" + httpClientGateway.buildUrlParams(params);

            return DistAccessResponse.builder()
                    .success(true)
                    .redirectUrl(requestUrl)
                    .build();
        } catch (Exception e) {
            log.error("盒马免登接入异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("盒马免登接入异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        return DistPaidNotifyResponse.builder()
                .success(true)
                .status(SUCCESS_CODE)
                .build();
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        return DistRefundResultNotifyResponse.builder()
                .success(true)
                .status(SUCCESS_CODE)
                .build();
    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        return null;
    }
}
