package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.hutool.json.JSONUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.biz.distribution.service.DistributionAfterSalesService;
import com.jsrxjt.mobile.domain.distribution.types.DistributionOrderRefundMessage;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/7/29 17:14
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DistributionAfterSalesServiceImpl implements DistributionAfterSalesService {

    private final UnifiedDistributionApi unifiedDistributionApi;


    @Override
    public void handleDistributionRefundCallBack(DistributionOrderRefundMessage event) {

        log.info("开始处理分销订单退款回调，订单号：{}，应用类型：{}",
                event.getFlqOrderNo(), event.getAppFlag());
        String appFlag = event.getAppFlag();
        if (StringUtils.isBlank(appFlag)){
            log.error("订单{}的appFlag为空", event.getOrderNo());
            return;
        }
        DistChannelType distChannelType = DistChannelType.getByCode(appFlag);
        if (distChannelType == null) {
            log.error("订单{}的appFlag{}对应的分销渠道类型为空", event.getFlqOrderNo(), appFlag);
            return;
        }
        int refundStatus = 12;
        if (Objects.equals(RefundStatusEnum.REFUND_SUCCESS.getCode(),event.getRefundStatus())) {
            refundStatus = 10;
        }
        DistRefundResultNotifyRequest request = DistRefundResultNotifyRequest.builder()
                .channelType(distChannelType)
                .distOrderNo(event.getOrderNo())
                .outOrderNo(event.getFlqOrderNo())
                .distTradeNo(event.getDisTradeNo())
                .outTradeNo(event.getFlqTradeNo())
                .distRefundNo(event.getRefundNo())
                .outRefundNo(event.getFlqRefundNo())
                .refundAmount(event.getRefundAmount())
                .refundTime(event.getRefundTime())
                .status(refundStatus)
                .build();
        log.info("分销应用退款请求参数为:{}", JSONUtil.toJsonStr(request));
        DistRefundResultNotifyResponse distRefundResultNotifyResponse = unifiedDistributionApi.refundResultNotify(request);
        log.info("分销应用退款结果参数为:{}", JSONUtil.toJsonStr(distRefundResultNotifyResponse));
        if (distRefundResultNotifyResponse == null || !distRefundResultNotifyResponse.isSuccess()){
            log.error("分销订单退款回调失败，订单号：{}，响应结果：{}", event.getFlqOrderNo(), distRefundResultNotifyResponse);
            throw new BizException("分销应用退款通知异常");
        }
    }
}
