package com.jsrxjt.mobile.api.locallife.dto.request;

import com.jsrxjt.mobile.api.locallife.dto.LocalLifeBaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/6/17 14:17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "本地生活下单请求参数")
public class LocalLifeCreateOrderDTO extends LocalLifeBaseDTO {

    @Schema(description = "本地生活侧订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tradeAmount;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userId;

    /**
     * 1：线上订单 2：门店买单
     */
    private Integer orderType;

    /**
     * 只有orderType=1才会有值
     */
    private String detailUrl;

}
