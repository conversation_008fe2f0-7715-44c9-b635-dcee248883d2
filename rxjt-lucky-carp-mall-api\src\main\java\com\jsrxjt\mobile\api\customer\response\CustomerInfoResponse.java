package com.jsrxjt.mobile.api.customer.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户信息返回结果")
public class CustomerInfoResponse {

    @Schema(description = "用户Id")
    private Long id;

    @Schema(description = "瑞祥会员中心会员id")
    private Long vipId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户头像")
    private String userUrl;

    @Schema(description = "unionid")
    private String unionid;

    @Schema(description = "小程序openid")
    private String miniOpenid;

    @Schema(description = "移动应用openid")
    private String openid;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "生日")
    private String birthday;

    @Schema(description = "用户卡包数量")
    private int couponNum;

    @Schema(description = "用户优惠券数量")
    private int ticketNum;

    @Schema(description = "未读消息数量")
    private int unReadMsgNum;

    @Schema(description = "待付款订单数量")
    private int pendingPaymentOrderNum;

    @Schema(description = "进行中订单数量")
    private int inProcessOrderNum;

    @Schema(description = "退款/售后订单数量")
    private int afterSaleOrderNum;

    @Schema(description = "1开启小额免密 0关闭小额免密")
    private Integer openPasswordFreePayment;

}
