package com.jsrxjt.mobile.infra.ticket.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketProductPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 优惠券与卡券关联mapper
 * @Author: ywt
 * @Date: 2025-08-15 15:37
 * @Version: 1.0
 */
@Mapper
public interface TicketProductMapper extends CommonBaseMapper<TicketProductPO> {
    List<TicketProductPO> selectTickets(@Param("productSkUID") Long productSkUID, @Param("productType") Integer productType);
}
