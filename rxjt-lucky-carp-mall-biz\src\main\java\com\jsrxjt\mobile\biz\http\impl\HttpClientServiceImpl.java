package com.jsrxjt.mobile.biz.http.impl;

import com.jsrxjt.mobile.biz.http.HttpClientService;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *  HttpClientServiceImpl
 * 
 * <AUTHOR> <PERSON>ping
 * 2023/8/17 9:55
 * 
 **/

@RequiredArgsConstructor
@Slf4j
@Service
public class HttpClientServiceImpl  implements HttpClientService {
    private final HttpClientGateway httpClientGateway;


    @Override
    public void asyncPostForJson(String url, String jsonBody) {
        httpClientGateway.asyncPostForJson(url,jsonBody);
    }
}
