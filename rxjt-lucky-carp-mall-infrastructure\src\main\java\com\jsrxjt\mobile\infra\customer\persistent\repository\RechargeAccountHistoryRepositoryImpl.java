package com.jsrxjt.mobile.infra.customer.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jsrxjt.mobile.domain.customer.entity.RechargeAccountHistoryEntity;
import com.jsrxjt.mobile.domain.customer.repository.RechargeAccountHistoryRepository;
import com.jsrxjt.mobile.infra.customer.persistent.mapper.RechargeAccountHistoryMapper;
import com.jsrxjt.mobile.infra.customer.persistent.po.RechargeAccountHistoryPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户历史充值账号Repository实现
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Repository
@RequiredArgsConstructor
public class RechargeAccountHistoryRepositoryImpl implements RechargeAccountHistoryRepository {

    private final RechargeAccountHistoryMapper rechargeAccountHistoryMapper;

    @Override
    public RechargeAccountHistoryEntity save(RechargeAccountHistoryEntity entity) {
        RechargeAccountHistoryPO po = new RechargeAccountHistoryPO();
        BeanUtil.copyProperties(entity, po);

        if (po.getId() == null) {
            rechargeAccountHistoryMapper.insert(po);
        } else {
            rechargeAccountHistoryMapper.updateById(po);
        }

        entity.setId(po.getId());
        return entity;
    }

    @Override
    public List<RechargeAccountHistoryEntity> findByCustomerAndProduct(Long customerId, Long productSpuId,
                                                                       Integer productType, Integer accountType) {
        List<RechargeAccountHistoryPO> poList = rechargeAccountHistoryMapper
                .selectByCustomerAndProduct(customerId, productSpuId, productType, accountType);

        return poList.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<RechargeAccountHistoryEntity> findByCustomerId(Long customerId) {
        List<RechargeAccountHistoryPO> poList = rechargeAccountHistoryMapper.selectByCustomerId(customerId);

        return poList.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    @Override
    public RechargeAccountHistoryEntity findById(Long id) {
        RechargeAccountHistoryPO po = rechargeAccountHistoryMapper.selectById(id);
        return po != null ? convertToEntity(po) : null;
    }

    /**
     * 删除所有历史充值账户
     */
    @Override
    public Integer DelHisRechargeAccount(Long customerId, Long id) {
        RechargeAccountHistoryPO accountHistoryPO = rechargeAccountHistoryMapper.selectById(id);
        if (Objects.isNull(accountHistoryPO)) {
            return 0;
        }
        return rechargeAccountHistoryMapper.update(null, new LambdaUpdateWrapper<RechargeAccountHistoryPO>()
//                .eq(RechargeAccountHistoryPO::getId, id)
                .eq(RechargeAccountHistoryPO::getCustomerId, customerId)
                .eq(RechargeAccountHistoryPO::getProductSpuId, accountHistoryPO.getProductSpuId())
                .eq(RechargeAccountHistoryPO::getProductType, accountHistoryPO.getProductType())
                .eq(RechargeAccountHistoryPO::getAccountType, accountHistoryPO.getAccountType())
                .set(RechargeAccountHistoryPO::getDelFlag, 1)
                .set(RechargeAccountHistoryPO::getModTime, new Date()));
    }

    /**
     * PO转Entity
     */
    private RechargeAccountHistoryEntity convertToEntity(RechargeAccountHistoryPO po) {
        RechargeAccountHistoryEntity entity = new RechargeAccountHistoryEntity();
        BeanUtil.copyProperties(po, entity);
        return entity;
    }

    @Override
    public List<RechargeAccountHistoryEntity> findByCriteria(RechargeAccountHistoryEntity query) {
        RechargeAccountHistoryPO queryPO = new RechargeAccountHistoryPO();
        BeanUtil.copyProperties(query, queryPO);

        List<RechargeAccountHistoryPO> poList = rechargeAccountHistoryMapper.selectByCriteria(queryPO);

        return poList.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }
}
