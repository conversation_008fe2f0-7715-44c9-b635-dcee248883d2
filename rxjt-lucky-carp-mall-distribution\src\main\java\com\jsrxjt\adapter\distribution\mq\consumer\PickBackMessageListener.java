package com.jsrxjt.adapter.distribution.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.biz.homeScanPay.service.PickScanCaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.MessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 提货券回调消息监听器
 * <AUTHOR>
 * @date 2025/11/13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PickBackMessageListener implements MessageListener {

  private final PickScanCaseService pickScanCaseService;

  @Override
  public ConsumeResult consume(MessageView messageView) {
    try {
      // 解析消息内容
      String msgBody = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
      String tag = messageView.getTag().get();
      log.info("展码付回调 id是{}: 消息内容是{}",messageView.getMessageId(), msgBody);
      pickScanCaseService.sendBackMessage(tag, JSONObject.parseObject(msgBody));
      return ConsumeResult.SUCCESS;
    } catch (Exception e) {
      log.error("处理展码付回调发布消息异常", e);
      return ConsumeResult.FAILURE;
    }
  }
}