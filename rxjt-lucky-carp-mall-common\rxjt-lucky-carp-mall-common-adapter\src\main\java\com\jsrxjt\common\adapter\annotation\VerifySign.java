package com.jsrxjt.common.adapter.annotation;

import java.lang.annotation.*;

/**
 *  VerifySign
 *  类上加上此注解 表示任何的方法都需要验签，方法上加上了此注解，表示该方法现需要验签
 * <AUTHOR>
 * 2023/3/13 11:18
 * 
 **/
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
public @interface VerifySign {
    /**
     * 是否需要验证token,默认为否
     * @return
     */
    boolean hasToken() default false;
}
