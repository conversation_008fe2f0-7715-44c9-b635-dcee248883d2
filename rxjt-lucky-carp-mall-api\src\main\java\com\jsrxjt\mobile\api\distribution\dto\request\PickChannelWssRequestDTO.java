package com.jsrxjt.mobile.api.distribution.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.jsrxjt.common.core.vo.SignRequest;
import com.jsrxjt.mobile.api.scanPay.types.ScanWsTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 展码付/线下扫码应用websocket推送
 * <AUTHOR>
 * @date 2025/09/28
 */
@Data
@Schema(description = "展码付/线下扫码应用websocket推送")
public class PickChannelWssRequestDTO extends SignRequest {

    @Schema(description = "用户id")
    @JSONField(name = "customerId")
    @NotNull(message = "用户id不能为空")
    private Long customerId;

    @Schema(description = "推送类型")
    @JSONField(name = "messageType")
    private ScanWsTypeEnum messageType;

    @Schema(description = "支付码")
    @JSONField(name = "code")
    @NotBlank (message = "支付码不能为空")
    private String code;

    @Schema(description = "推送内容")
    @JSONField(name = "body")
    @NotNull (message = "推送内容不能为空")
    private JSONObject body;

}
