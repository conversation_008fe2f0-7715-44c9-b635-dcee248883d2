package com.jsrxjt.mobile.api.product.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 产品规格请求参数
 * @Author: ywt
 * @Date: 2025-05-12 15:09
 * @Version: 1.0
 */
@Data
@Schema(description = "产品规格请求参数")
public class ProductSpecRequestDTO {
    @Schema(description = "产品的spuid")
    @NotNull(message = "产品spuid为空错误")
    private Long productSpuId;
    @Schema(description = "产品类型，1卡券 2套餐")
    @NotNull(message = "产品类型为空错误")
    private Integer productType;
}
