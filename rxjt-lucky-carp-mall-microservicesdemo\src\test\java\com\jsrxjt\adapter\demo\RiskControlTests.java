package com.jsrxjt.adapter.demo;

import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.riskcontrol.dto.response.ProductRiskControlResponse;
import com.jsrxjt.mobile.domain.riskcontrol.service.ProductRiskControlService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class RiskControlTests {

    @Autowired
    private  ProductRiskControlService productRiskControlService;

    @Test
    public void testGetProductRiskControl() {
        // 获取商品风控信息
        ProductItemId productItemId = ProductItemId.of(255L, 150L, 1);
        Long customerId = 367458774045954L;
        ProductRiskControlResponse response = productRiskControlService.getProductRiskControl(productItemId, customerId,0);
        System.out.println(response);
        Assertions.assertNotNull(response);
    }
}
