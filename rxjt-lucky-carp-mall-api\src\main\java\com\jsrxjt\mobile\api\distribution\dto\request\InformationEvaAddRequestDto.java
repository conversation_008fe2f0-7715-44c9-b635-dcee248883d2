package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Description: 提交资讯评价请求参数
 * @Author: ywt
 * @Date: 2025-06-11 09:31
 * @Version: 1.0
 */
@Data
public class InformationEvaAddRequestDto {
    /*@Schema(description = "用户id")
    @NotNull(message = "用户id为空错误")
    private Long userId;*/

    @Schema(description = "资讯id")
    @NotNull(message = "资讯id为空错误")
    private Integer informationId;

    @Schema(description = "评价内容")
    @NotBlank(message = "评价内容为空错误")
    @Size(max = 500, message = "最多上传500个字符")
    private String content;

}
