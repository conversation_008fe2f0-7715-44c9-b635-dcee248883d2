package com.jsrxjt.mobile.api.customer.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.YearMonth;

@Data
@Schema(name="CustomerCardTradeRequest", description="卡交易记录查询参数")
public class CustomerCardTradeRequest {

    @Schema(description = "卡号")
    @NotBlank(message = "卡号不能为空")
    private String cardNo;

    @Schema(description = "年-月份，格式：yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private YearMonth yearMonth;

    @Schema(description = "页码")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    @Schema(description = "每页数量")
    @NotNull(message = "每页数量不能为空")
    @Min(value = 1, message = "每页最少1条数据")
    private Integer pageSize;
}