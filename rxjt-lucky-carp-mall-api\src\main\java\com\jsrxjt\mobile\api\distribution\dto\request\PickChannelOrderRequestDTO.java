package com.jsrxjt.mobile.api.distribution.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jsrxjt.common.core.vo.SignRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 扫码提货的付款码请求参数
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "外部调用创建订单接口")
public class PickChannelOrderRequestDTO extends SignRequest {

    @Schema(description = "第三方商户ID，默认值：1-瑞祥商户")
    @JSONField(name = "third_id")
    @NotBlank (message = "第三方商户ID不能为空")
    private String third_id;

    @Schema(description = "商户ID")
    @JSONField(name = "shop_id")
    @NotBlank (message = "商户ID不能为空")
    private String shop_id;

    @Schema(description = "商户收银员ID")
    @JSONField(name = "shop_user_id")
    @NotBlank (message = "商户收银员ID不能为空")
    private String shop_user_id;

    @Schema(description = "类型描述，例如：place-分销订单 yike-逸刻 watsons-屈臣氏 ")
    @JSONField(name = "type")
    @NotBlank (message = "平台交易流水号不能为空")
    private String type;

    @Schema(description = "平台订单流水号")
    @JSONField(name = "order_no")
    @NotBlank (message = "订单流水号不能为空")
    private String order_no;

    @Schema(description = "平台交易流水号")
    @JSONField(name = "trade_no")
    @NotBlank (message = "平台交易流水号不能为空")
    private String trade_no;

    @Schema(description = "订单金额")
    @JSONField(name = "amount")
    @NotBlank (message = "订单金额不能为空")
    private String amount;

    @Schema(description = "商户费率")
    @JSONField(name = "rate")
    private String rate;

    @Schema(description = "用户编码")
    @JSONField(name = "user_code")
    @NotBlank (message = "用户编码不能为空")
    private String user_code;

    @Schema(description = "付款码")
    @JSONField(name = "payment_code")
    @NotBlank (message = "付款码不能为空")
    private String payment_code;

    @Schema(description = "下单时间")
    @JSONField(name = "created_time")
    @NotBlank (message = "下单时间不能为空")
    private String created_time;

    @Schema(description = "订单过期时间")
    @JSONField(name = "expired_time")
    @NotBlank (message = "订单过期时间不能为空")
    private String expired_time;

}
