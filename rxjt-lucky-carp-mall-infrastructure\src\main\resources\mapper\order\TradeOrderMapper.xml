<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsrxjt.mobile.infra.order.persistent.mapper.TradeOrderMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jsrxjt.mobile.infra.order.persistent.po.TradeOrderPO">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_no" property="orderNo"/>
        <result column="out_order_no" property="outOrderNo"/>
        <result column="trade_no" property="tradeNo"/>
        <result column="pay_type" property="payType"/>
        <result column="user_id" property="userId"/>
        <result column="order_source" property="orderSource"/>
        <result column="order_type" property="orderType"/>
        <result column="order_channel" property="orderChannel"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_status" property="payStatus"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_status" property="refundStatus"/>
        <result column="card_pay_amount" property="cardPayAmount"/>
        <result column="third_pay_amount" property="thirdPayAmount"/>
        <result column="trade_time" property="tradeTime"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted_at" property="deletedAt"/>
    </resultMap>

    <!-- 根据订单号和交易号查询交易订单信息 -->
    <select id="selectByOrderNoAndTradeNo" resultMap="BaseResultMap">
        SELECT * FROM trade_order 
        WHERE order_no = #{orderNo} AND trade_no = #{tradeNo} AND deleted_at IS NULL
    </select>

    <!-- 根据订单号查询交易订单信息列表 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT * FROM trade_order 
        WHERE order_no = #{orderNo} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据交易号查询交易订单信息 -->
    <select id="selectByTradeNo" resultMap="BaseResultMap">
        SELECT * FROM trade_order 
        WHERE trade_no = #{tradeNo} AND deleted_at IS NULL
    </select>

</mapper>