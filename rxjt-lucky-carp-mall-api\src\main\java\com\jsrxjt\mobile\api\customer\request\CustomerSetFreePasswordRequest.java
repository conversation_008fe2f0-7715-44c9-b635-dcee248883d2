package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "小额免密设置参数")
public class CustomerSetFreePasswordRequest extends BaseParam {

    @Schema(description = "用户ID")
    @NotNull(message = "用户Id不能为空")
    private Long customerId;

    @Schema(description = "1开启小额免密 0关闭小额免密")
    @Range(min = 0, max = 1, message = "只能选择开启或关闭")
    @NotNull(message = "请选择是否开启小额免密")
    private Integer openFreePayment;

    @Schema(description = "用户密码，开启小额免密需要输入密码")
    private String password;

}
