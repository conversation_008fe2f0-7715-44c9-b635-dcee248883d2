package com.jsrxjt.mobile.api.distribution;

/**
 * 分销渠道类型枚举类
 *
 * <AUTHOR>
 * @since 2025/3/24
 **/
public enum DistChannelType {
    /**
     * 美团外卖
     */
    MEITUAN,

    /**
     * 叮咚买菜
     */
    DINGDONG,

    /**
     * M会员店
     */
    MVIP,

    /**
     * 大润发小时达
     */
    RTMART,

    /**
     *瑞祥全球购自行对接
     */
    RXQQG,

    /**
     * 大润发扫码付
     */
    RTMARTSCANPAY,

    /**
     * 中免日上
     */
    CDFSUNRISE,
    /**
     * 团油
     */
    TUANYOU,

    /**
     * 西橙影视
     */
    XICHENG,
    /**
     * 视听
     */
    SHITING,
    /**
     * 永辉超市
     */
    YONGHUI,
    /**
     * 卫岗
     */
    WEIGANG,

    /**
     * 食行生鲜
     */
    SHIXING,
    /**
     * 同程商旅
     */
    TONGCHENG,
    /**
     * 清美
     */
    QINGMEI,
    /**
     * 水韵江苏
     */
    SHUIYUN,
    /**
     * 物美北京线上
     */
    WUMEI_BEIJING,
    /**
     * 物美北京线下
     */
    WUMEI_OFFLINE_BEIJING,
    /**
     * 物美天津线上
     */
    WUMEI_TIANJIN,
    /**
     * 物美天津线下
     */
    WUMEI_OFFLINE_TIANJIN,
    /**
     * 物美华东线上
     */
    WUMEI_HUADONG,
    /**
     * 物美华东线下
     */
    WUMEI_OFFLINE_HUADONG,
    /**
     * 本地生活
     */
    LOCALLIFE,
    /**
     * 话费充值
     */
    PHONEBILL,
    /**
     * 沃尔玛企业付
     */
    WALMART,
    /**
     * 小象超市
     */
    XIAOXIANG,
    /**
     * 百胜
     */
    BAISHENG,
    /**
     * 盒马企业付
     */
    HEMA,
    /**
     * 屈臣氏到家
     */
    WATSONS,
    /**
     * 全家集享卡
     */
    FAMILYMART,
    /**
     * 百联
     */
    BAILIAN,
    /**
     * 实体卡商城
     */
    PHYSICALCARDMALL,

    /**
     * 祥采云1688
     */
    XCY_ALIBABA,

    /**
     * 祥采云开市客
     */
    XCY_KSK,

    /**
     * 祥采云山姆
     */
    XCY_SM,

    /**
     * 线下扫码
     */
    SCANPAY,

    /**
     * 饿了么
     */
    ELEME,

    /**
     * 光明随心订
     */
    GUANGMING;

    /**
     * 通过编码获取分销渠道类型
     *
     * @param code 渠道编码
     * @return 分销渠道类型
     */
    public static DistChannelType getByCode(String code) {
        for (DistChannelType channelType : DistChannelType.values()) {
            if (channelType.name().equals(code)) {
                return channelType;
            }
        }
        return null;
    }

}