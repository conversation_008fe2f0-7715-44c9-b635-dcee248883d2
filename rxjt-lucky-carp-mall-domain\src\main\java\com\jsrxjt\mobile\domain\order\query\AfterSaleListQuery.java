package com.jsrxjt.mobile.domain.order.query;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 售后单列表查询条件
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@Builder
public class AfterSaleListQuery {

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 售后状态
     */
    private Integer afterSaleStatus;

    /**
     * 售后类型
     */
    private Integer afterSaleType;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 商品一级分类ID
     */
    private Long firstCategoryId;

    /**
     * 商品名称（关键字搜索）
     */
    private String productName;

    /**
     * 订单号（关键字搜索）
     */
    private String orderNo;

    /**
     * 售后单号（关键字搜索）
     */
    private String afterSaleNo;

    /**
     * 是否显示
     */
    private Boolean isShow;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;
}