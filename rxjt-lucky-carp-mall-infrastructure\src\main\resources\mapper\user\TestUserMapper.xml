<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsrxjt.mobile.infra.user.persistent.mapper.UserMapper">
	<resultMap type="com.jsrxjt.mobile.infra.user.persistent.po.UserPO"
		id="UserResult">
		<id column="user_id" property="userId" />
		<result column="user_name" property="userName" />
		<result column="unionid" property="unionid" />
		<result column="nick_name"  property="nickName" />
		<result column="password" property="password" />
		<result column="phone" property="phone" />
		<result column="sex" property="sex" />
		<result column="birthday" property="birthday" />
		<result column="user_url" property="userUrl" />
		<result column="user_grade_id" property="userGradeId" />
		<result column="is_enable" property="enable" />
		<result column="login_ip" property="loginIp" />
		<result column="login_time" property="loginTime" />
		<result column="login_num" property="loginNum" />
		<result column="login_error_count" property="loginErrorCount" />
		<result column="login_lock_time" property="loginLockTime" />
		<result column="continue_login_count" property="continueLoginCount" />
		<result column="phone_token" property="phoneToken" />
		<result column="login_status" property="loginStatus" />
		<result column="origin" property="origin" />
		<result column="schq" property="schq" />
        <result column="old_type" property="oldType" />
		<result column="create_time" property="createTime" />
		<result column="create_id" property="createId" />
		<result column="mod_time" property="modTime" />
		<result column="mod_id" property="modId" />
		<result column="user_real_name" property="userRealName" />
		<result column="user_id_card" property="userIdCard" />
		<result column="user_auth_status" property="userAuthStatus" />
		<result column="del_flag" property="delFlag" />
		<result column="mch_id" jdbcType="INTEGER" property="mchId" />
		<result column="mini_user_id" jdbcType="BIGINT" property="miniUserId" />
		<result column="card_no" jdbcType="VARCHAR" property="cardNo" />
		<result column="createDate"  property="createDate" />
	</resultMap>
	<select id="selectUserById" resultMap="UserResult" parameterType="long">
		select * from t_test_user where user_id = #{userId}
	</select>
	<select id="selectUserPage" resultType="com.jsrxjt.mobile.infra.user.persistent.po.UserPO">
		select * from t_test_user
		<where>
			<if test="user.userName != null and user.userName != ''">
				and user_name like concat('%',#{user.userName},'%')
			</if>
		</where>
	</select>


</mapper>