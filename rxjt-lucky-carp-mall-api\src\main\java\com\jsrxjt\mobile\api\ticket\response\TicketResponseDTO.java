package com.jsrxjt.mobile.api.ticket.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 优惠券响应数据
 * @Author: ywt
 * @Date: 2025-08-15 17:00
 * @Version: 1.0
 */
@Data
@Schema(description = "优惠券响应数据")
public class TicketResponseDTO {
    @Schema(description = "优惠券id")
    private Long ticketId;
    /*@Schema(description = "优惠券名称")
    private String ticketName;
    @Schema(description = "优惠券类型: 0商家自发优惠券 1全球购线上商城优惠券 2瑞祥代发优惠券")
    private Integer ticketType;
    @Schema(description = "优惠券品牌id")
    private Long brandId;
    @Schema(description = "优惠券分类 0满减券 1折扣券 2满赠券 3权益包 4无门槛券")
    private Integer ticketCatCode;
    @Schema(description = "自发券后有效期日")
    private Integer ticketValidDate;*/
    @Schema(description = "规格图片url")
    private String specPicUrl;
   /* @Schema(description = "使用说明")
    private String useManual;*/
}
