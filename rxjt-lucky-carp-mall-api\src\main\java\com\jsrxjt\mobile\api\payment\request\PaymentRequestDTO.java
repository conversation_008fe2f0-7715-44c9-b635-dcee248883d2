package com.jsrxjt.mobile.api.payment.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 发起支付请求DTO
 * <AUTHOR>
 * @since 2025/8/15
 */
@Data
public class PaymentRequestDTO {
    
    /**
     * 业务订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    
    /**
     * 预支付订单号
     */
    @NotBlank(message = "预支付订单号不能为空")
    private String preOrderNo;

    /**
     * 密码
     */
    @Schema(description = "支付密码 非免密支付时不能为空，免密支付时不传次参数")
    private String pass;

    @Schema(description = "是否使用密码 true 跳过密码 false 不跳过密码 默认false")
    private boolean withPass;
}