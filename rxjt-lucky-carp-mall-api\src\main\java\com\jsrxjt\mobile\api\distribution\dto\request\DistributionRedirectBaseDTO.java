package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DistributionRedirectBaseDTO {
    @Schema(description = "分销应用类型")
    @NotBlank(message = "应用类型不能为空")
    private String distributionType;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "经度")
    private String longitude;

    /**
     * 跳转类型
     * INDEX(0, "index"),
     * SHOP(10, "shopDetail"),
     * COUPON(20, "couponDetail"),
     * PACKAGE(30, "goodsDetail"),
     * ORDER_LIST(100, "orderList");
     */
    @Schema(description = "  * 跳转类型\n" +
            "     * INDEX(0, \"index\"),\n" +
            "     * SHOP(10, \"shopDetail\"),\n" +
            "     * COUPON(20, \"couponDetail\"),\n" +
            "     * PACKAGE(30, \"goodsDetail\"),\n" +
            "     * ORDER_LIST(100, \"orderList\");")
    private Integer localLifeRedirectType = 0;
    /**
     * 跳转参数值
     *  SHOP(10, "shopDetail"),取site_id值
     *  COUPON(20, "couponDetail"),取sku_id值
     *  PACKAGE(30, "goodsDetail"),取sku_id值
     *  ORDER_LIST(100, "orderList"),取order_id值
     */
    @Schema(description = "* 跳转参数值\n" +
            "     *  SHOP(10, \"shopDetail\"),取site_id值\n" +
            "     *  COUPON(20, \"couponDetail\"),取sku_id值\n" +
            "     *  PACKAGE(30, \"goodsDetail\"),取sku_id值\n" +
            "     *  ORDER_LIST(100, \"orderList\"),取order_id值")
    private String localLifeRedirectParamValue;
}
