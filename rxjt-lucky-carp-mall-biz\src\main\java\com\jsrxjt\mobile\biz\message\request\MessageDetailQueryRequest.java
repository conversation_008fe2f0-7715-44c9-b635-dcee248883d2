package com.jsrxjt.mobile.biz.message.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(name="MessageDetailRequest", description = "瑞祥通知消息条件")
public class MessageDetailQueryRequest {

    @Schema(description = "消息id")
    @NotNull(message = "消息Id不能为空")
    private Long messageId;
    @Schema(description = "消息类型 0瑞祥消息 1客服消息")
    @NotNull(message = "消息类型不能为空")
    private Byte type;
}
