package com.jsrxjt.mobile.domain.bianlifeng.request;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025-10-22
 */
@Data
@Builder
public class BianLiFengRechargeRequest {

    /**
     * 商户号
     * 数字，10位
     */
    private String merchant;

    /**
     * 商户订单号 - 字母或数字，最长40位
     */
    private String orderNo;

    /**
     * 请求时间
     */
    private String orderDate;

    /**
     * 绑定用户手机号 - 数字，11位
     */
    private String mobilePhone;

    /**
     * 卡面值 - 数字，小数点后最多两位
     */
    private BigDecimal cardAmount;

    /**
     * 预付费卡批次号 - 固定值：
     * 字母或数字，最长40位
     */
    private String bachNo;


}
