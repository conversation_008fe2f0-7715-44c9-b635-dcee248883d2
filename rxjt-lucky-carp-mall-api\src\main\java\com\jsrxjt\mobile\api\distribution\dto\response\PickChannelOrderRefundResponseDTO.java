package com.jsrxjt.mobile.api.distribution.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 扫码提货创建订单返回参数
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "外部调用订单退款接口")
@AllArgsConstructor
@NoArgsConstructor
public class PickChannelOrderRefundResponseDTO {

    @Schema(description = "外部退款单号")
    @JSONField(name = "out_refund_sn")
    private String out_refund_sn;


    
}
