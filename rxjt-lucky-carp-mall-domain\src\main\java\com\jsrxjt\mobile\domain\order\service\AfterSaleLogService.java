package com.jsrxjt.mobile.domain.order.service;

import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleLogEntity;

/**
 * 售后日志领域服务
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface AfterSaleLogService {

    /**
     * 创建售后操作日志
     * 
     * @param afterSale 售后实体
     * @param operationType 操作类型
     * @param operationContent 操作内容
     * @param operator 操作人
     * @param operatorId 操作人ID
     * @return 售后日志实体
     */
    AfterSaleLogEntity createAfterSaleLog(AfterSaleEntity afterSale, Integer operationType, 
                                          String operationContent, String operator, Long operatorId);
}