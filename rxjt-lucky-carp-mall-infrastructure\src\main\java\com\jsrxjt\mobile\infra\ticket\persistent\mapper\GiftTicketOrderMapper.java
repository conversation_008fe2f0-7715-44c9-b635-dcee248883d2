package com.jsrxjt.mobile.infra.ticket.persistent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.GiftTicketOrderPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 下单赠送券订单Mapper
 *
 * <AUTHOR>
 * @date 2025/09/22
 */
@Mapper
public interface GiftTicketOrderMapper extends BaseMapper<GiftTicketOrderPO> {

  /**
   * 批量插入赠券订单
   *
   * @param giftTicketOrders 赠券订单列表
   * @return 插入条数
   */
  int batchInsert(List<GiftTicketOrderPO> giftTicketOrders);
}
