package com.jsrxjt.mobile.biz.coupon.service;


import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.coupon.dto.request.*;
import com.jsrxjt.mobile.api.coupon.dto.response.*;

public interface CouponCaseService {
    /**
     * 定时更新全量卡券(卡管中普通卡,直冲,品诺)
     */
    void updateAllCoupon();
    BaseResponse<CouponInfoResponseDTO> couponInfo(CouponInfoRequestDTO requestDTO);
    CouponSkusResponseDTO skusInfo(CouponSkuInfoRequestDTO requestDTO, boolean needPromotion);
//    CouponSkusResponseDTO skusAndExtraInfo(CouponSkuInfoRequestDTO requestDTO);
    CouponSkuExtralInfoResponseDTO skuExtraInfo(CouponSkuExtraInfoRequestDTO requestDTO);
    CouponShopDataResponseDTO getShopList(CouponShopRequestDTO requestDTO);
    CouponCardCodeResponseDTO getCardCode(CouponCardCodeRequestDTO requestDTO);
    BaseResponse<VideoCouponInfoResponseDTO> videoRechargeCouponsInfo(CouponSkuInfoRequestDTO requestDTO);
    BaseResponse<VideoCouponPolymerizeDetailResponseDTO> rechargeCouponDetail(CouponSkuInfoRequestDTO requestDTO);
    BaseResponse<CouponSkusResponseDTO> getVideoSkuByAccount(AccountSkuInfoRequestDTO requestDTO);
    BaseResponse<VedioShowTemplateResponseDTO> getVideoShowTemplate(VedioShowTemplateRequestDTO requestDTO);
//    List<CouponGoodsVideoResponseDTO> getVedioRechargeCoupons();
}
