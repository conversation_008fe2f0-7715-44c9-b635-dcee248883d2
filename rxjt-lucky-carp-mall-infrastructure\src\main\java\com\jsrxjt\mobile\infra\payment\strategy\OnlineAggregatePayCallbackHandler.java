package com.jsrxjt.mobile.infra.payment.strategy;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.enums.PaymentChannelEnum;
import com.jsrxjt.mobile.api.payment.dto.request.PaymentCallbackRequestDTO;
import com.jsrxjt.mobile.api.payment.dto.response.PaymentCallbackHandlerDTO;
import com.jsrxjt.mobile.domain.order.entity.TradeOrderEntity;
import com.jsrxjt.mobile.domain.order.repository.TradeOrderRepository;
import com.jsrxjt.mobile.domain.payment.strategy.PaymentCallbackHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class OnlineAggregatePayCallbackHandler implements PaymentCallbackHandler {

    private final TradeOrderRepository tradeOrderRepository;

    @Override
    public PaymentChannelEnum supportedChannel() {
        return PaymentChannelEnum.ONLINE_AGGREGATE;
    }

    @Override
    public PaymentCallbackHandlerDTO handleNotify(PaymentCallbackRequestDTO request) {
        PaymentCallbackHandlerDTO result = new PaymentCallbackHandlerDTO();
        Map<String, Object> requestBody = request.getRequestBody();
        result.setOrderNo(requestBody.get("order_no").toString());
        result.setTradeNo(requestBody.get("trade_no").toString());
        result.setPaymentChannel(supportedChannel());
        String payStatus = requestBody.get("pay_status").toString();
        // 校验状态 查询trade_order表，校验状态是否一致
        TradeOrderEntity tradeOrder = tradeOrderRepository.findTradeOrderByOrderNoAndTradeNo(result.getOrderNo(),
                result.getTradeNo());
        if(tradeOrder == null) {
            log.error("聚合支付回调失败，交易单不存在，orderNo: {}, tradeNo: {}", result.getOrderNo(), result.getTradeNo());
            throw new BizException("聚合支付回调失败，交易单"+ result.getTradeNo()+ "不存在");
        }
        if(!Objects.equals(payStatus, tradeOrder.getPayStatus())) {
            log.error("聚合支付回调校验失败，状态不一致，orderNo: {}, tradeNo: {}, payStatus: {}, tradeOrderPayStatus: {}",
                    result.getOrderNo(), result.getTradeNo(), payStatus, tradeOrder.getPayStatus());
            throw new BizException("聚合支付回调校验失败，状态不一致");
        }
        result.setPayStatus(payStatus);
        if(tradeOrder.isPaySuccess()) {
            // 设置支付时间
            result.setPaymentTime(tradeOrder.getTradeTime());
            // 设置支付金额
            result.setPaymentAmount(tradeOrder.getPayAmount());
        }

        return result;
    }

    @Override
    public PaymentCallbackHandlerDTO handleRefundNotify(PaymentCallbackRequestDTO request) {
        PaymentCallbackHandlerDTO result = new PaymentCallbackHandlerDTO();
        Map<String, Object> requestBody = request.getRequestBody();
        result.setOrderNo(requestBody.get("order_no").toString());
        result.setTradeNo(requestBody.get("trade_no").toString());
        result.setRefundNo(requestBody.get("out_refund_no").toString());
        result.setPaymentChannel(supportedChannel());
        String refundStatus = requestBody.get("refund_status").toString();
        // 校验状态 查询trade_order表，校验状态是否一致
        TradeOrderEntity tradeOrder = tradeOrderRepository.findTradeOrderByOrderNoAndTradeNo(result.getOrderNo(),
                result.getTradeNo());
        if(tradeOrder == null) {
            log.error("退款回调失败，交易单不存在，orderNo: {}, tradeNo: {}", result.getOrderNo(), result.getTradeNo());
            throw new BizException("退款支付回调失败，交易单"+ result.getTradeNo()+ "不存在");
        }
        if (!tradeOrder.canRefund()) {
            log.error("退款回调失败，交易单状态不允许退款，orderNo: {}, tradeNo: {}", result.getOrderNo(), result.getTradeNo());
            throw new BizException("退款支付回调失败，交易单"+ result.getTradeNo()+ "状态不允许退款");
        }
        if(!Objects.equals(refundStatus, tradeOrder.getRefundStatus())) {
            log.error("聚合支付退款状态校验失败，状态不一致，orderNo: {}, tradeNo: {}, refundStatus: {}, tradeOrderRefundStatus: {}",
                    result.getOrderNo(), result.getTradeNo(), refundStatus, tradeOrder.getRefundStatus());
            throw new BizException("聚合支付退款回调校验失败，状态不一致");
        }
        result.setRefundStatus(refundStatus);
        result.setRefundAmount(tradeOrder.getRefundAmount());
        result.setRefundTime(tradeOrder.getUpdatedAt());
        return result;
    }

    @Override
    public PaymentCallbackHandlerDTO parseCallbackParams(String rawParams) {
        return null;
    }
}
