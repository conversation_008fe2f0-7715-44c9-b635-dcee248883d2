package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.bailian.dto.request.BaiLianShopRequestDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianBrandResponseDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianCategoryResponseDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianShopResponseDTO;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.bailian.dto.request.BaiLianQRCodeRequestDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianQRCodeResponseDTO;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.distribution.service.BaiLianCaseService;
import com.jsrxjt.mobile.domain.bailian.gateway.BaiLianGateWay;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianAccessResponse;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianBrandResponse;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianCategoryResponse;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianShopResponse;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/10/28
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BaiLianCaseServiceImpl implements BaiLianCaseService {

    private final CustomerService customerService;

    private final UnifiedDistributionApi unifiedDistributionApi;

    private final BaiLianGateWay baiLianGateWay;

    @Override
    public BaseResponse<BaiLianQRCodeResponseDTO> getPaymentQRCode(BaiLianQRCodeRequestDTO requestDTO) {

        Long userId = StpUtil.getLoginIdAsLong();
        CustomerEntity customerEntity = customerService.getCustomerById(userId);
        if (customerEntity == null) {
            throw new BizException(Status.USER_NOT_EXIST);
        }
        // 判断用户是否开启小额免密
        if (customerEntity.getOpenPasswordFreePayment() == null || customerEntity.getOpenPasswordFreePayment() == 0) {
            return BaseResponse.fail(Status.IS_NOT_OPEN_PASSWORD_FREE_PAYMENT);
        }
        DistChannelType distChannelType = DistChannelType.getByCode(requestDTO.getDistributionType());
        if (distChannelType == null) {
            return BaseResponse.fail(Status.NOT_SUPPORT_REDIRECT);
        }
        DistAccessRequest request = DistAccessRequest.builder()
                .channelType(distChannelType)
                .userId(String.valueOf(userId))
                .mobile(customerEntity.getPhone())
                .source(requestDTO.getSource())
                .build();
        DistAccessResponse distAccessResponse = unifiedDistributionApi.access(request);
        if (distAccessResponse.isSuccess()){
            BaiLianAccessResponse baiLianAccessResponse = distAccessResponse.getBaiLianAccessResponse();
            BaiLianQRCodeResponseDTO responseDTO = new BaiLianQRCodeResponseDTO();
            BeanUtils.copyProperties(baiLianAccessResponse,responseDTO);
            return BaseResponse.succeed(responseDTO);
        }
        return BaseResponse.fail(Status.NOT_SUPPORT_REDIRECT);
    }

    @Override
    public BaseResponse<List<BaiLianCategoryResponseDTO>> getCategoryList() {
        List<BaiLianCategoryResponse> responseList = baiLianGateWay.getCategoryList();
        List<BaiLianCategoryResponseDTO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(responseList)){
            for (BaiLianCategoryResponse baiLianCategoryResponse : responseList) {
                BaiLianCategoryResponseDTO responseDTO = new BaiLianCategoryResponseDTO();
                BeanUtils.copyProperties(baiLianCategoryResponse,responseDTO);
                list.add(responseDTO);
            }
        }
        return BaseResponse.succeed(list);
    }

    @Override
    public BaseResponse<List<BaiLianBrandResponseDTO>> getBrandList(Long categoryId) {
        List<BaiLianBrandResponse> responseList = baiLianGateWay.getBrandList(categoryId);
        List<BaiLianBrandResponseDTO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(responseList)) {
            for (BaiLianBrandResponse baiLianBrandResponse : responseList) {
                if (baiLianBrandResponse != null && baiLianBrandResponse.getIsDelete() == 0 && baiLianBrandResponse.getIsEnable() == 1) {
                    BaiLianBrandResponseDTO responseDTO = new BaiLianBrandResponseDTO();
                    responseDTO.setBrandId(baiLianBrandResponse.getBrandId());
                    responseDTO.setBrandName(baiLianBrandResponse.getBrandName());
                    if (baiLianBrandResponse.getImageResponse() != null) {
                        responseDTO.setImage(baiLianBrandResponse.getImageResponse().getPreviewUrl());
                    }
                    list.add(responseDTO);
                }
            }
        }
        return BaseResponse.succeed(list);
    }

    @Override
    public BaseResponse<List<BaiLianShopResponseDTO>> getShopList(BaiLianShopRequestDTO requestDTO) {
        List<BaiLianShopResponse> responseList = baiLianGateWay.getShopList(requestDTO);
        List<BaiLianShopResponseDTO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(responseList)) {
            for (BaiLianShopResponse baiLianShopResponse : responseList) {
                if (baiLianShopResponse != null && baiLianShopResponse.getIsDelete() == 0 && baiLianShopResponse.getIsEnable() == 1) {
                    BaiLianShopResponseDTO responseDTO = new BaiLianShopResponseDTO();
                    responseDTO.setId(baiLianShopResponse.getId());
                    responseDTO.setShopName(baiLianShopResponse.getShopName());
                    responseDTO.setAddress(baiLianShopResponse.getAddress());
                    if (baiLianShopResponse.getImageResponse() != null) {
                        responseDTO.setImage(baiLianShopResponse.getImageResponse().getPreviewUrl());
                    }
                    list.add(responseDTO);
                }
            }
        }
        return BaseResponse.succeed(list);
    }
}
