package com.jsrxjt.mobile.infra.gateway.distribution.adapter.watsons;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 屈臣氏分销渠道适配器
 *
 * <AUTHOR>
 * @Date 2025/10/16
 */
@Component
@Slf4j
public class WatsonsDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

    public WatsonsDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                             DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getWatsons();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.WATSONS;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            if (StringUtils.isBlank(request.getUserId())) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID不能为空")
                        .build();
            }

            String baseUrl = config.getBaseUrl() + "/api/login/thirdLogin";
            String url = baseUrl + "?app_id=" + config.getAppId() +
                    "&user_code=" + request.getUserId();

            return DistAccessResponse.builder()
                    .success(true)
                    .redirectUrl(url)
                    .build();
        } catch (Exception e) {
            log.error("屈臣氏免登接入异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("屈臣氏免登接入异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        // 调用父类的通用实现，使用分隔的日期时间格式（yyyy-MM-dd HH:mm:ss）
        return doPaidNotify(request, DAY_TIME_SPLIT_FORMAT);
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        if (Objects.equals(10, request.getStatus())) {
            return doRefundResultNotify(request, COMPACT_DAY_TIME_FORMAT, (params, req) -> {
                params.put("tradeNo", req.getDistTradeNo());
                params.put("orderNo", req.getDistOrderNo());
                params.put("refundOrderNo", req.getDistRefundNo());
                params.put("thirdRefundOrderNo", req.getOutRefundNo());
                params.put("refundAmount", req.getRefundAmount().toString());

                // 转换时间格式
                if (req.getRefundTime() != null) {
                    params.put("refundSuccessTime", req.getRefundTime().format(COMPACT_DAY_TIME_FORMAT));
                } else {
                    params.put("refundSuccessTime", "");
                }
            });
        } else {
            return DistRefundResultNotifyResponse.builder()
                    .success(true)
                    .status(SUCCESS_CODE)
                    .build();
        }

    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        return null;
    }
}
