package com.jsrxjt.mobile.domain.promotion.service.validator.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.product.entity.ProductItem;
import com.jsrxjt.mobile.domain.promotion.service.validator.PromotionActivityValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 促销活动校验服务实现
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/10/24
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PromotionActivityValidationServiceImpl implements PromotionActivityValidationService {

    private final OrderRepository orderRepository;

    @Override
    public void validateActivityQuota(ProductItem productItem, Long customerId, Integer quantity) {
        // 检查是否有促销活动信息
        if (productItem.getPromotionSkuInfo() == null) {
            if (log.isDebugEnabled()) {
                log.debug("商品无促销活动信息，跳过活动限购校验，skuId：{}", productItem.getSkuId());
            }

            return;
        }

        PromotionSkuInfo promotionSkuInfo = productItem.getPromotionSkuInfo();
        
        // 检查是否设置了限购数量
        if (promotionSkuInfo.getQuotaQty() == null || promotionSkuInfo.getQuotaQty() <= 0) {
            if (log.isDebugEnabled()) {
                log.debug("商品无促销活动限购数量，跳过活动限购校验，skuId：{}", productItem.getSkuId());
            }
            return;
        }

        Long activityId = promotionSkuInfo.getActivityId();
        Integer productType = productItem.getProductType();
        Long skuId = productItem.getSkuId();
        Integer quotaQty = promotionSkuInfo.getQuotaQty();

        log.info("开始活动限购校验，customerId：{}，activityId：{}，productType：{}，skuId：{}，requestQuantity：{}，quotaQty：{}",
                customerId, activityId, productType, skuId, quantity, quotaQty);

        // 查询用户在该活动下该商品的已购买数量（排除已取消订单）
        Integer purchasedQuantity = orderRepository.getUserActivityPurchasedQuantity(
                customerId, activityId, productType, skuId);

        log.info("用户活动限购校验，customerId：{}，activityId：{}，skuId：{}，已购买数量：{}，限购数量：{}，本次购买数量：{}",
                customerId, activityId, skuId, purchasedQuantity, quotaQty, quantity);

        // 计算购买后的总数量
        Integer totalQuantityAfterPurchase = purchasedQuantity + quantity;

        // 校验1：如果用户已经买完了限购额度，直接不卖
        if (purchasedQuantity >= quotaQty) {
            log.warn("用户已达到活动限购额度，不允许购买，customerId：{}，activityId：{}，skuId：{}，已购买：{}，限购：{}",
                    customerId, activityId, skuId, purchasedQuantity, quotaQty);
            throw new BizException("您已达到该活动商品的限购额度，无法继续购买");
        }

        // 校验2：如果用户超过了限购额度，提示调整购买数量
        if (totalQuantityAfterPurchase > quotaQty) {
            Integer maxAllowedQuantity = quotaQty - purchasedQuantity;
            log.warn("用户购买数量超过活动限购额度，customerId：{}，activityId：{}，skuId：{}，已购买：{}，本次购买：{}，限购：{}，最大可购买：{}",
                    customerId, activityId, skuId, purchasedQuantity, quantity, quotaQty, maxAllowedQuantity);
            throw new BizException(String.format("购买数量超过限购额度，您最多还可以购买%d件", maxAllowedQuantity));
        }

        log.info("活动限购校验通过，customerId：{}，activityId：{}，skuId：{}，购买后总数量：{}，限购数量：{}",
                customerId, activityId, skuId, totalQuantityAfterPurchase, quotaQty);
    }
}
