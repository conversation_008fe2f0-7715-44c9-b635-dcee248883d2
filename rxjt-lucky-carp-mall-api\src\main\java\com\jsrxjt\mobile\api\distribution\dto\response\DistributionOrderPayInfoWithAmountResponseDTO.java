package com.jsrxjt.mobile.api.distribution.dto.response;

import lombok.Data;

@Data
public class DistributionOrderPayInfoWithAmountResponseDTO {

    /**
     * 分销业务中心订单号
     */
    private String orderNo;

    /**
     * 分销业务中心交易号
     */
    private String tradeNo;

    /**
     * 第三方订单号，标识该笔订单在接入方的订单号
     */
    private String thirdOrderNo;

    /**
     * 支付状态 01待支付 00支付成功 02支付失败(含取消支付)
     */
    private String tradeStatus;

    /**
     * 支付时间，10位UTC时间戳，非支付成功状态统一传0
     */
    private Integer tradeTime;

    /**
     * 支付金额，若订单未支付，传0.00即可
     */
    private String payAmount;

}
