package com.jsrxjt.mobile.infra.ticket.persistent.repository;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.domain.ticket.entity.TicketOffsetRecordEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketOffsetRecordRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketOffsetRecordMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketOffsetRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @Date 2025/9/15
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TicketOffsetRecordRepositoryImpl implements TicketOffsetRecordRepository {

    private final TicketOffsetRecordMapper ticketOffsetRecordMapper;

    @Override
    public int save(TicketOffsetRecordEntity ticketOffsetRecord) {
        TicketOffsetRecordPO ticketOffsetRecordPO = new TicketOffsetRecordPO();
        BeanUtils.copyProperties(ticketOffsetRecord, ticketOffsetRecordPO);
        return ticketOffsetRecordMapper.insert(ticketOffsetRecordPO);
    }
}
