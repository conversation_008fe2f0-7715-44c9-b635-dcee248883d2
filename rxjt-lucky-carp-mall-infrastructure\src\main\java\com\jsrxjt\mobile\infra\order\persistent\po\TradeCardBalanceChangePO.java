package com.jsrxjt.mobile.infra.order.persistent.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 卡交易流水
 * <AUTHOR>
 * @date 2025/10/10
 */
@Data
@TableName("trade_card_balance_change")
public class TradeCardBalanceChangePO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 业务订单号
     */
    private String orderNo;

    /**
     * 外部业务单号
     */
    private String outOrderNo;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 子-交易单号 微信交易单号
     */
    private String tradeInfoNo;

    /**
     * 子-预付卡交易单号
     */
    private String outTradeInfoNo;

    /**
     * 子-交易退款单号
     */
    private String tradeRefundInfoNo;

    /**
     * 子-外部交易退款单号
     */
    private String outTradeRefundInfoNo;

    /**
     * 变动类型  TRADE 交易  REFUND 退款 CANCEL 冲正 RECHARGE 充值
     */
    private String type;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 交易金额
     */
    private Long tradePrice;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 删除时间
     */
    @TableLogic("0")
    @TableField("deleted_at")
    private Integer deletedAt;
}
