package com.jsrxjt.mobile.api.xcy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Created by jeffery.yang on 2025/10/21 11:12
 *
 * @description: 祥采云通用返参
 * @author: jeffery.yang
 * @date: 2025/10/21 11:12
 * @version: 1.0
 */
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XcyMallBaseResponse {

	/**
	 * appid
	 */
	private String appid;

	/**
	 * 时间戳
	 * 格式yyyyMMddHHmmss，时区GMT+8，例如
	 * 20230922085000
	 */
	private String timestamp;

	/**
	 * 随机字符串
	 */
	private String nounce;

	/**
	 * 签名值
	 */
	private String sign;

	/**
	 * 响应码
	 * 0 成功
	 * 1 失败
	 */
	private Integer resCode;

	/**
	 * 相应信息
	 */
	private String resDesc;
}
