package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单列表响应DTO
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/7/17
 */
@Getter
@Setter
public class OrderListResponseDTO {
    
    @Schema(description = "订单号")
    private String orderNo;
    
    @Schema(description = "品牌ID")
    private Long brandId;
    
    @Schema(description = "品牌名称")
    private String brandName;
    
    @Schema(description = "商品名称")
    private String productName;
    
    @Schema(description = "商品SPU ID")
    private Long productSpuId;
    
    @Schema(description = "商品SKU ID")
    private Long productSkuId;

    @Schema(description = "商品logo")
    private String productLogo;

    @Schema(description = "商品类型")
    private Integer productType;

    @Schema(description = "商品细分类型")
    private Integer flatProductType;

    @Schema(description = "商品价格")
    private BigDecimal sellPrice;
    


    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;
    
    @Schema(description = "充值账户")
    private String rechargeAccount;

    @Schema(description = "手续费")
    private BigDecimal totalServiceFee;

    @Schema(description = "超额手续费")
    private BigDecimal exceedFee;
    
    @Schema(description = "面值")
    private BigDecimal faceValue;
    
    @Schema(description = "数量")
    private Integer quantity;
    
    @Schema(description = "订单状态: 0-待付款 10-进行中 20-交易成功 30-交易关闭 40-超时取消 41-手动取消")
    private Integer orderStatus;

    
    @Schema(description = "发货状态：0-未发货/未充值  1-发货中/充值中 2-已发货/已充值 3-发货失败/充值失败")
    private Integer deliveryStatus;
    
    @Schema(description = "售后状态: 1-待审核 20-审核通过 30-审核驳回 32-退款驳回 33-售后撤销 34-售后完成")
    private Integer afterSaleStatus;

    @Schema(description = "支付状态: 0-未支付 1-已支付 2-部分支付 3-部分退款中 4-已部分退款 5-已全额退款 6-已全额退款")
    private Integer paymentStatus;
    
    @Schema(description = "下单时间")
    private LocalDateTime createTime;
    
    @Schema(description = "支付超时时间戳-秒")
    private Long paymentExpireTime;
    
    @Schema(description = "订单详情URL")
    private String orderDetailUrl;
}