package com.jsrxjt.mobile.domain.distribution.messaging.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.jsrxjt.mobile.domain.distribution.messaging.DistributionOrderRefundMessageProducer;
import com.jsrxjt.mobile.domain.distribution.types.DistributionOrderRefundMessage;
import com.jsrxjt.mobile.domain.gateway.mq.MqSendGateWay;
import com.jsrxjt.mobile.domain.gateway.mq.entity.MqMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
@Slf4j
public class DistributionOrderRefundMessageProducerImpl implements DistributionOrderRefundMessageProducer {

    private static final String DISTRIBUTION_ORDER_REFUND_TOPIC = "distribution_order_refund_topic";


    private final MqSendGateWay mqSendGateWay;


    @Override
    public void sendOrderRefundDelayCallBack(DistributionOrderRefundMessage message) {
        log.info("Send distribution order refund delay message, message={}", JSONUtil.toJsonStr(message));
        mqSendGateWay.syncDelaySend(MqMessage.builder()
                .topic(DISTRIBUTION_ORDER_REFUND_TOPIC)
                .keys(UUID.randomUUID().toString())
                .messageBody(JSON.toJSONString(message))
                .build(),2000);
    }
}
