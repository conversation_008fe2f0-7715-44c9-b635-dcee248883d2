package com.jsrxjt.mobile.api.distribution.dto.request;


import com.jsrxjt.common.core.vo.SignRequest;
import lombok.Data;

@Data
public class PickChannelOrderUpdateRequestDTO extends SignRequest {
    //第三方id 默认1-瑞祥商户
    private String third_id;
    //描述 place:分销订单
    private String type;
    //平台订单流水号
    private String order_no;
    //平台交易流水号
    private String trade_no;
   //交易状态 00订单完成 01订单未支付已取消
   private String trade_status;
}
