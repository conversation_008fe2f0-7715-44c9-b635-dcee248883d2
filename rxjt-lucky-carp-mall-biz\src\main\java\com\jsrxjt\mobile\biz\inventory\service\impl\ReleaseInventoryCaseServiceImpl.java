package com.jsrxjt.mobile.biz.inventory.service.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.inventory.types.ReleasePlanOperationType;
import com.jsrxjt.mobile.biz.inventory.service.ReleaseInventoryCaseService;
import com.jsrxjt.mobile.domain.inventory.entity.SkuRegionReleasePlanEntity;
import com.jsrxjt.mobile.domain.inventory.entity.SkuReleaseInventoryEntity;
import com.jsrxjt.mobile.domain.inventory.entity.SkuReleasePlanEntity;
import com.jsrxjt.mobile.domain.inventory.repository.SkuRegionReleasePlanRepository;
import com.jsrxjt.mobile.domain.inventory.repository.SkuReleaseInventoryRepository;
import com.jsrxjt.mobile.domain.inventory.repository.SkuReleasePlanRepository;
import com.jsrxjt.mobile.domain.inventory.types.SkuReleaseMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 定时放量库存业务实现类
 * 
 * <AUTHOR> Fengping
 * @since 2025/3/11
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ReleaseInventoryCaseServiceImpl implements ReleaseInventoryCaseService {

    private final SkuReleasePlanRepository skuReleasePlanRepository;
    private final SkuReleaseInventoryRepository skuReleaseInventoryRepository;
    private final SkuRegionReleasePlanRepository skuRegionReleasePlanRepository;

    @Override
    public List<Long> getSkusWithReleasePlan(Integer productType) {
        return skuReleasePlanRepository.listSkusWithReleasePlan(productType);
    }

    @Override
    public List<SkuReleasePlanEntity> getSkuReleasePlan(Long skuId, Integer productType) {
        return skuReleasePlanRepository.listSkuReleasePlan(skuId, productType);
    }

    @Override
    public List<SkuRegionReleasePlanEntity> getSkuRegionReleasePlan(Long skuId, Integer productType) {
        return skuRegionReleasePlanRepository.listSkuRegionReleasePlan(skuId, productType);
    }

    @Override
    public List<SkuReleaseInventoryEntity> generateReleaseInventoryRecords(
            List<SkuReleasePlanEntity> skuReleasePlanList, LocalDate releaseDate) {
        if (CollectionUtils.isEmpty(skuReleasePlanList)) {
            return List.of();
        }

        List<SkuReleaseInventoryEntity> inventoryRecords = new ArrayList<>();

        // 按放量时间排序，确保时间段不重叠
        List<SkuReleasePlanEntity> sortedPlans = skuReleasePlanList.stream()
                .sorted(Comparator.comparing(SkuReleasePlanEntity::getReleaseTime))
                .toList();

        // 为每个放量计划生成库存记录，计算正确的结束时间
        for (int i = 0; i < sortedPlans.size(); i++) {
            SkuReleasePlanEntity currentPlan = sortedPlans.get(i);
            LocalTime nextReleaseTime;

            // 计算下一个放量时间
            if (i < sortedPlans.size() - 1) {
                // 不是最后一个计划，使用下一个计划的开始时间
                nextReleaseTime = sortedPlans.get(i + 1).getReleaseTime();
            } else {
                // 最后一个计划，使用次日第一个计划的开始时间
                nextReleaseTime = sortedPlans.get(0).getReleaseTime();
            }

            processReleasePlanWithEndTime(currentPlan, releaseDate, nextReleaseTime, i == sortedPlans.size() - 1,
                    inventoryRecords);
        }

        return inventoryRecords;
    }

    private void processReleasePlanWithEndTime(SkuReleasePlanEntity plan, LocalDate releaseDate,
            LocalTime nextReleaseTime, boolean isLastPlan, List<SkuReleaseInventoryEntity> results) {
        LocalTime releaseTime = plan.getReleaseTime();
        SkuReleaseInventoryEntity inventory = new SkuReleaseInventoryEntity();
        inventory.setProductSkuId(plan.getProductSkuId());
        inventory.setProductType(plan.getProductType());
        inventory.setPlanId(plan.getId()); // 设置放量计划ID
        inventory.setPolicyId(0L); // 基础放量使用0作为policyId

        // 设置开始时间
        inventory.setReleaseStartTime(releaseDate.atTime(releaseTime));

        // 计算结束时间
        LocalDateTime endTime;
        if (isLastPlan) {
            // 最后一个计划：到次日第一个计划开始前1秒
            endTime = releaseDate.plusDays(1).atTime(nextReleaseTime);
        } else {
            // 非最后一个计划：到当天下一个计划开始前1秒
            endTime = releaseDate.atTime(nextReleaseTime);
        }
        inventory.setReleaseEndTime(endTime);

        inventory.setReleaseStatus(0); // 未开始
        inventory.setTotalQuantity(plan.getReleaseQuantity());
        inventory.setRemainingQuantity(plan.getReleaseQuantity());
        inventory.setSoldQuantity(0);
        inventory.setCreateTime(LocalDateTime.now());
        inventory.setModTime(LocalDateTime.now());
        results.add(inventory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int executeNextDayReleaseTask(Integer productType) {
        // 1. 获取所有需要放量的SKU
        List<Long> skuIds = getSkusWithReleasePlan(productType);
        if (CollectionUtils.isEmpty(skuIds)) {
            return 0;
        }

        int successCount = 0;
        LocalDate nextDay = LocalDate.now().plusDays(1);

        // 2. 遍历处理每个SKU
        for (Long skuId : skuIds) {
            try {
                if (processSkuReleasePlan(skuId, productType, nextDay)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("处理SKU放量计划失败, skuId: {}, productType: {}", skuId, productType, e);
            }
        }

        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processSkuReleasePlan(Long skuId, Integer productType, LocalDate releaseDate) {
        // 1. 获取SKU的基础放量计划，没有基础放量也就不存在特定区域的额外放量计划，直接返回false
        List<SkuReleasePlanEntity> releasePlans = getSkuReleasePlan(skuId, productType);
        if (CollectionUtils.isEmpty(releasePlans)) {
            log.info("SKU没有放量计划, skuId: {}, productType: {}", skuId, productType);
            return false;
        }

        // 2. 生成放量库存记录
        List<SkuRegionReleasePlanEntity> extraPlans = getSkuRegionReleasePlan(skuId, productType);
        List<SkuReleaseInventoryEntity> inventoryRecords = generateReleaseInventoryRecords(releasePlans, releaseDate);
        if (CollectionUtils.isEmpty(inventoryRecords)) {
            log.info("SKU生成放量库存记录为空, skuId: {}, productType: {}", skuId, productType);
            return false;
        }

        // 3. 如果有额外放量计划，则生成额外的库存记录
        if (!CollectionUtils.isEmpty(extraPlans)) {
            List<SkuReleaseInventoryEntity> extraInventoryRecords = generateExtraReleaseInventoryRecords(extraPlans,
                    releaseDate);
            inventoryRecords.addAll(extraInventoryRecords);
        }

        // 4. 保存放量库存记录
        int savedCount = batchSaveReleaseInventoryRecords(inventoryRecords);
        boolean success = savedCount > 0;
        log.info("SKU放量库存记录保存{}, skuId: {}, productType: {}, savedCount: {}",
                success ? "成功" : "失败", skuId, productType, savedCount);
        return success;
    }

    @Override
    public int batchSaveReleaseInventoryRecords(List<SkuReleaseInventoryEntity> releaseInventoryRecords) {
        return skuReleaseInventoryRepository.batchSaveReleaseInventories(releaseInventoryRecords);
    }

    @Override
    public int autoUpdateReleaseInventoryStatus(Integer productType) {
        log.info("开始自动更新放量库存状态, productType: {}", productType);

        LocalDateTime currentTime = LocalDateTime.now();
        int updatedCount = 0;

        try {
            // 1. 更新已开始但未结束的放量库存状态为已生效(1)
            int startedCount = skuReleaseInventoryRepository.updateStartedInventoryStatus(productType, currentTime);
            log.info("更新已开始放量库存状态成功, 更新数量: {}", startedCount);

            // 2. 更新已结束的放量库存状态为已结束(2)
            int endedCount = skuReleaseInventoryRepository.updateEndedInventoryStatus(productType, currentTime);
            log.info("更新已结束放量库存状态成功, 更新数量: {}", endedCount);

            updatedCount = startedCount + endedCount;
        } catch (Exception e) {
            log.error("自动更新放量库存状态失败, productType: {}", productType, e);
            throw e;
        }

        log.info("自动更新放量库存状态完成, productType: {}, 总更新数量: {}", productType, updatedCount);
        return updatedCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleReleasePlanChange(SkuReleaseMessage releaseMessage) {
        if (releaseMessage == null || releaseMessage.getOperationType() == null) {
            log.error("放量计划变更消息为空或操作类型为空");
            return false;
        }

        try {
            // 获取基础放量计划列表
            List<SkuReleasePlanEntity> releasePlans = Collections.emptyList();
            // 获取额外放量计划列表
            List<SkuRegionReleasePlanEntity> extraPlans = Collections.emptyList();
            // 如果不是删除操作
            if (releaseMessage.getOperationType() != ReleasePlanOperationType.DELETE) {
                if (!CollectionUtils.isEmpty(releaseMessage.getPlanIds())) {
                    releasePlans = skuReleasePlanRepository.listSkuReleasePlanByIds(releaseMessage.getPlanIds());
                }

                if (!CollectionUtils.isEmpty(releaseMessage.getExtraPlanIds())) {
                    extraPlans = skuRegionReleasePlanRepository
                            .listSkuRegionReleasePlanByIds(releaseMessage.getExtraPlanIds());
                }
                // 两种计划都为空，则返回失败
                if (CollectionUtils.isEmpty(releasePlans) && CollectionUtils.isEmpty(extraPlans)) {
                    log.error("未找到需要处理的放量计划，planIds={}, extraPlanIds={}",
                            releaseMessage.getPlanIds(), releaseMessage.getExtraPlanIds());
                    return false;
                }
            }

            // 根据操作类型处理放量计划
            switch (releaseMessage.getOperationType()) {
                case ADD ->
                    addSkuReleaseInventoryByPlans(releasePlans, extraPlans);
                case EDIT ->
                    updateSkuReleaseInventoryByPlans(releasePlans, extraPlans);

                case DELETE -> {
                    List<Long> planIds = releaseMessage.getPlanIds();
                    List<Long> extraPlanIds = releaseMessage.getExtraPlanIds();
                    deleteSkuReleaseInventoryByPlanIds(planIds, extraPlanIds);
                }
                default -> {
                    log.error("不支持的操作类型：{}", releaseMessage.getOperationType());
                    return false;
                }
            }

            // 集中更新当天该商品的放量起止时间
            updateTodayReleaseTimesForAffectedProducts(releasePlans, extraPlans);

            return true;
        } catch (Exception e) {
            log.error("处理放量计划变更失败", e);
            throw new BizException("处理放量计划变更失败", e);
        }
    }

    /**
     * 集中更新当天该商品的放量起止时间
     *
     * @param releasePlans 基础放量计划列表
     * @param extraPlans   额外放量计划列表
     */
    private void updateTodayReleaseTimesForAffectedProducts(List<SkuReleasePlanEntity> releasePlans,
            List<SkuRegionReleasePlanEntity> extraPlans) {
        LocalDate today = LocalDate.now();
        Set<ProductSkuKey> affectedProducts = new HashSet<>();

        // 收集受影响的商品信息
        if (!CollectionUtils.isEmpty(releasePlans)) {
            for (SkuReleasePlanEntity plan : releasePlans) {
                affectedProducts.add(new ProductSkuKey(plan.getProductSkuId(), plan.getProductType(), 0L));
            }
        }

        if (!CollectionUtils.isEmpty(extraPlans)) {
            for (SkuRegionReleasePlanEntity plan : extraPlans) {
                affectedProducts
                        .add(new ProductSkuKey(plan.getProductSkuId(), plan.getProductType(), plan.getPolicyId()));
            }
        }

        // 为每个受影响的商品更新当天的放量起止时间
        for (ProductSkuKey productKey : affectedProducts) {
            updateTodayReleaseTimesForProduct(productKey.productSkuId, productKey.productType, productKey.policyId,
                    today);
        }
    }

    /**
     * 更新指定商品当天的放量起止时间
     *
     * @param productSkuId 产品SKU ID
     * @param productType  产品类型
     * @param policyId     政策ID
     * @param releaseDate  放量日期
     */
    private void updateTodayReleaseTimesForProduct(Long productSkuId, Integer productType, Long policyId,
            LocalDate releaseDate) {
        // 查询当天该商品的所有放量库存记录
        List<SkuReleaseInventoryEntity> todayInventories = skuReleaseInventoryRepository
                .findTodayReleaseInventoryBySkuAndPolicy(productSkuId, productType, policyId, releaseDate);

        if (CollectionUtils.isEmpty(todayInventories)) {
            log.info("未找到当天的放量库存记录，跳过更新，productSkuId={}, productType={}, policyId={}",
                    productSkuId, productType, policyId);
            return;
        }

        // 获取该商品的所有放量计划，重新计算正确的起止时间
        if (policyId == 0L) {
            // 基础放量计划
            List<SkuReleasePlanEntity> allPlans = getSkuReleasePlan(productSkuId, productType);
            if (!CollectionUtils.isEmpty(allPlans)) {
                updateBasicReleaseInventoryTimes(allPlans, todayInventories, releaseDate);
            }
        } else {
            // 额外放量计划
            List<SkuRegionReleasePlanEntity> allExtraPlans = getSkuRegionReleasePlan(productSkuId, productType);
            if (!CollectionUtils.isEmpty(allExtraPlans)) {
                updateExtraReleaseInventoryTimes(allExtraPlans, todayInventories, releaseDate, policyId);
            }
        }
    }

    /**
     * 更新基础放量库存记录的时间
     */
    private void updateBasicReleaseInventoryTimes(List<SkuReleasePlanEntity> allPlans,
            List<SkuReleaseInventoryEntity> todayInventories, LocalDate releaseDate) {
        // 按放量时间排序
        List<SkuReleasePlanEntity> sortedPlans = allPlans.stream()
                .sorted(Comparator.comparing(SkuReleasePlanEntity::getReleaseTime))
                .toList();

        // 为每个库存记录重新计算时间
        List<SkuReleaseInventoryEntity> updatedInventories = new ArrayList<>();

        for (SkuReleaseInventoryEntity inventory : todayInventories) {
            // 找到对应的放量计划
            SkuReleasePlanEntity matchedPlan = findMatchedPlan(inventory.getPlanId(), sortedPlans);
            if (matchedPlan != null) {
                int planIndex = sortedPlans.indexOf(matchedPlan);
                LocalTime nextReleaseTime;

                if (planIndex < sortedPlans.size() - 1) {
                    // 不是最后一个计划，使用下一个计划的开始时间
                    nextReleaseTime = sortedPlans.get(planIndex + 1).getReleaseTime();
                } else {
                    // 最后一个计划，使用次日第一个计划的开始时间
                    nextReleaseTime = sortedPlans.get(0).getReleaseTime();
                }

                updateInventoryRecordsWithEndTime(List.of(inventory), matchedPlan, nextReleaseTime,
                        planIndex == sortedPlans.size() - 1);
                updatedInventories.add(inventory);
            }
        }

        if (!updatedInventories.isEmpty()) {
            skuReleaseInventoryRepository.batchUpdateReleaseInventoryTimes(updatedInventories);
            log.info("更新基础放量库存时间完成，数量: {}", updatedInventories.size());
        }
    }

    /**
     * 更新额外放量库存记录的时间
     */
    private void updateExtraReleaseInventoryTimes(List<SkuRegionReleasePlanEntity> allExtraPlans,
            List<SkuReleaseInventoryEntity> todayInventories, LocalDate releaseDate, Long policyId) {
        // 筛选出指定政策的计划
        List<SkuRegionReleasePlanEntity> policyPlans = allExtraPlans.stream()
                .filter(plan -> plan.getPolicyId().equals(policyId))
                .sorted(Comparator.comparing(SkuRegionReleasePlanEntity::getReleaseTime))
                .toList();

        if (CollectionUtils.isEmpty(policyPlans)) {
            return;
        }

        // 为每个库存记录重新计算时间
        List<SkuReleaseInventoryEntity> updatedInventories = new ArrayList<>();

        for (SkuReleaseInventoryEntity inventory : todayInventories) {
            // 找到对应的放量计划
            SkuRegionReleasePlanEntity matchedPlan = findMatchedExtraPlan(inventory.getPlanId(), policyPlans);
            if (matchedPlan != null) {
                int planIndex = policyPlans.indexOf(matchedPlan);
                LocalTime nextReleaseTime;

                if (planIndex < policyPlans.size() - 1) {
                    // 不是最后一个计划，使用下一个计划的开始时间
                    nextReleaseTime = policyPlans.get(planIndex + 1).getReleaseTime();
                } else {
                    // 最后一个计划，使用次日第一个计划的开始时间
                    nextReleaseTime = policyPlans.get(0).getReleaseTime();
                }

                updateExtraInventoryRecordsWithEndTime(List.of(inventory), matchedPlan, nextReleaseTime,
                        planIndex == policyPlans.size() - 1);
                updatedInventories.add(inventory);
            }
        }

        if (!updatedInventories.isEmpty()) {
            skuReleaseInventoryRepository.batchUpdateReleaseInventoryTimes(updatedInventories);
            log.info("更新额外放量库存时间完成，数量: {}", updatedInventories.size());
        }
    }

    /**
     * 查找匹配的基础放量计划
     */
    private SkuReleasePlanEntity findMatchedPlan(Long planId, List<SkuReleasePlanEntity> plans) {
        return plans.stream()
                .filter(plan -> plan.getId().equals(planId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查找匹配的额外放量计划
     */
    private SkuRegionReleasePlanEntity findMatchedExtraPlan(Long planId, List<SkuRegionReleasePlanEntity> plans) {
        return plans.stream()
                .filter(plan -> plan.getId().equals(planId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 商品SKU标识类，用于去重
     */
    private static class ProductSkuKey {
        private final Long productSkuId;
        private final Integer productType;
        private final Long policyId;

        public ProductSkuKey(Long productSkuId, Integer productType, Long policyId) {
            this.productSkuId = productSkuId;
            this.productType = productType;
            this.policyId = policyId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            ProductSkuKey that = (ProductSkuKey) o;
            return Objects.equals(productSkuId, that.productSkuId) &&
                    Objects.equals(productType, that.productType) &&
                    Objects.equals(policyId, that.policyId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(productSkuId, productType, policyId);
        }
    }

    private void deleteSkuReleaseInventoryByPlanIds(List<Long> planIds, List<Long> extraPlanIds) {
        LocalDateTime now = LocalDateTime.now();
        // 处理基础放量计划
        for (Long planId : planIds) {
            skuReleaseInventoryRepository.deleteUnfinishedByPlanId(planId, now);
        }
        // 处理额外放量计划
        for (Long extraPlanId : extraPlanIds) {
            skuReleaseInventoryRepository.deleteUnfinishedByPlanId(extraPlanId, now);
        }
    }

    private void updateSkuReleaseInventoryByPlans(List<SkuReleasePlanEntity> releasePlans,
            List<SkuRegionReleasePlanEntity> extraPlans) {
        // 处理基础放量计划
        if (!CollectionUtils.isEmpty(releasePlans)) {
            // 按放量时间排序，确保时间段不重叠
            List<SkuReleasePlanEntity> sortedPlans = releasePlans.stream()
                    .sorted(Comparator.comparing(SkuReleasePlanEntity::getReleaseTime))
                    .toList();

            // 为每个放量计划计算正确的结束时间并更新
            for (int i = 0; i < sortedPlans.size(); i++) {
                SkuReleasePlanEntity currentPlan = sortedPlans.get(i);
                // 基础放量计划使用 policyId = 0
                Long policyId = 0L;
                List<SkuReleaseInventoryEntity> unfinishedRecords = skuReleaseInventoryRepository
                        .findUnfinishedByPlanId(currentPlan.getId(), policyId);
                if (!CollectionUtils.isEmpty(unfinishedRecords)) {
                    // 计算下一个放量时间
                    LocalTime nextReleaseTime;
                    if (i < sortedPlans.size() - 1) {
                        // 不是最后一个计划，使用下一个计划的开始时间
                        nextReleaseTime = sortedPlans.get(i + 1).getReleaseTime();
                    } else {
                        // 最后一个计划，使用次日第一个计划的开始时间
                        nextReleaseTime = sortedPlans.get(0).getReleaseTime();
                    }

                    updateInventoryRecordsWithEndTime(unfinishedRecords, currentPlan, nextReleaseTime,
                            i == sortedPlans.size() - 1);
                    skuReleaseInventoryRepository.batchUpdateReleaseInventories(unfinishedRecords);
                }
            }
        }

        // 处理额外放量计划 - 按policyId分组使用连续时间窗口策略
        if (!CollectionUtils.isEmpty(extraPlans)) {
            // 按policyId分组
            Map<Long, List<SkuRegionReleasePlanEntity>> extraPlansByPolicy = extraPlans.stream()
                    .collect(Collectors.groupingBy(SkuRegionReleasePlanEntity::getPolicyId));

            for (Map.Entry<Long, List<SkuRegionReleasePlanEntity>> entry : extraPlansByPolicy.entrySet()) {
                List<SkuRegionReleasePlanEntity> policyPlans = entry.getValue();

                // 按放量时间排序，确保时间段不重叠
                List<SkuRegionReleasePlanEntity> sortedPolicyPlans = policyPlans.stream()
                        .sorted(Comparator.comparing(SkuRegionReleasePlanEntity::getReleaseTime))
                        .toList();

                // 为每个放量计划计算正确的结束时间
                for (int i = 0; i < sortedPolicyPlans.size(); i++) {
                    SkuRegionReleasePlanEntity currentPlan = sortedPolicyPlans.get(i);
                    List<SkuReleaseInventoryEntity> unfinishedRecords = skuReleaseInventoryRepository
                            .findUnfinishedByPlanId(currentPlan.getId(), currentPlan.getPolicyId());

                    if (!CollectionUtils.isEmpty(unfinishedRecords)) {
                        // 计算下一个放量时间
                        LocalTime nextReleaseTime;
                        if (i < sortedPolicyPlans.size() - 1) {
                            // 不是最后一个计划，使用下一个计划的开始时间
                            nextReleaseTime = sortedPolicyPlans.get(i + 1).getReleaseTime();
                        } else {
                            // 最后一个计划，使用次日第一个计划的开始时间
                            nextReleaseTime = sortedPolicyPlans.get(0).getReleaseTime();
                        }

                        updateExtraInventoryRecordsWithEndTime(unfinishedRecords, currentPlan, nextReleaseTime,
                                i == sortedPolicyPlans.size() - 1);
                        skuReleaseInventoryRepository.batchUpdateReleaseInventories(unfinishedRecords);
                    }
                }
            }
        }
    }

    private void addSkuReleaseInventoryByPlans(List<SkuReleasePlanEntity> releasePlans,
            List<SkuRegionReleasePlanEntity> extraPlans) {
        // 处理基础放量计划
        if (!CollectionUtils.isEmpty(releasePlans)) {
            LocalDate today = LocalDate.now();
            List<SkuReleaseInventoryEntity> inventoryRecords = new ArrayList<>();

            // 按放量时间排序，确保时间段不重叠
            List<SkuReleasePlanEntity> sortedPlans = releasePlans.stream()
                    .sorted(Comparator.comparing(SkuReleasePlanEntity::getReleaseTime))
                    .toList();

            // 为每个放量计划计算正确的结束时间
            for (int i = 0; i < sortedPlans.size(); i++) {
                SkuReleasePlanEntity currentPlan = sortedPlans.get(i);

                // 基础放量计划使用 policyId = 0
                Long policyId = 0L;
                if (skuReleaseInventoryRepository.existsReleaseInventoryByPlanIdAndPolicyIdAndDate(
                        currentPlan.getId(), policyId, today)) {
                    log.info("放量计划已存在当天的放量库存记录，跳过处理. planId: {}, policyId: {}, skuId: {}, date: {}",
                            currentPlan.getId(), policyId, currentPlan.getProductSkuId(), today);
                    continue;
                }

                // 计算下一个放量时间
                LocalTime nextReleaseTime;
                if (i < sortedPlans.size() - 1) {
                    // 不是最后一个计划，使用下一个计划的开始时间
                    nextReleaseTime = sortedPlans.get(i + 1).getReleaseTime();
                } else {
                    // 最后一个计划，使用次日第一个计划的开始时间
                    nextReleaseTime = sortedPlans.get(0).getReleaseTime();
                }

                processReleasePlanWithEndTime(currentPlan, today, nextReleaseTime, i == sortedPlans.size() - 1,
                        inventoryRecords);
            }

            if (!inventoryRecords.isEmpty()) {
                skuReleaseInventoryRepository.batchSaveReleaseInventories(inventoryRecords);
                log.info("基础放量计划生成放量库存记录成功, 数量: {}", inventoryRecords.size());
            }
        }

        // 处理额外放量计划 - 按policyId分组使用连续时间窗口策略
        if (!CollectionUtils.isEmpty(extraPlans)) {
            LocalDate today = LocalDate.now();
            List<SkuReleaseInventoryEntity> extraInventoryRecords = new ArrayList<>();

            // 按policyId分组
            Map<Long, List<SkuRegionReleasePlanEntity>> extraPlansByPolicy = extraPlans.stream()
                    .collect(Collectors.groupingBy(SkuRegionReleasePlanEntity::getPolicyId));

            for (Map.Entry<Long, List<SkuRegionReleasePlanEntity>> entry : extraPlansByPolicy.entrySet()) {
                List<SkuRegionReleasePlanEntity> policyPlans = entry.getValue();

                // 按放量时间排序，确保时间段不重叠
                List<SkuRegionReleasePlanEntity> sortedPolicyPlans = policyPlans.stream()
                        .sorted(Comparator.comparing(SkuRegionReleasePlanEntity::getReleaseTime))
                        .toList();

                // 为每个放量计划计算正确的结束时间
                for (int i = 0; i < sortedPolicyPlans.size(); i++) {
                    SkuRegionReleasePlanEntity currentPlan = sortedPolicyPlans.get(i);

                    // 幂等处理：先检查是否已经生成过放量库存记录
                    if (skuReleaseInventoryRepository.existsReleaseInventoryByPlanIdAndPolicyIdAndDate(
                            currentPlan.getId(), currentPlan.getPolicyId(), today)) {
                        log.info("额外放量计划已存在当天的放量库存记录，跳过处理. planId: {}, policyId: {}, skuId: {}, date: {}",
                                currentPlan.getId(), currentPlan.getPolicyId(), currentPlan.getProductSkuId(), today);
                        continue;
                    }

                    // 计算下一个放量时间
                    LocalTime nextReleaseTime;
                    if (i < sortedPolicyPlans.size() - 1) {
                        // 不是最后一个计划，使用下一个计划的开始时间
                        nextReleaseTime = sortedPolicyPlans.get(i + 1).getReleaseTime();
                    } else {
                        // 最后一个计划，使用次日第一个计划的开始时间
                        nextReleaseTime = sortedPolicyPlans.get(0).getReleaseTime();
                    }

                    processExtraReleasePlanWithEndTime(currentPlan, today, nextReleaseTime,
                            i == sortedPolicyPlans.size() - 1, extraInventoryRecords);
                }
            }

            if (!extraInventoryRecords.isEmpty()) {
                skuReleaseInventoryRepository.batchSaveReleaseInventories(extraInventoryRecords);
                log.info("额外放量计划生成放量库存记录成功, 数量: {}", extraInventoryRecords.size());
            }
        }
    }

    /**
     * 处理额外放量计划 - 使用连续时间窗口策略
     */
    private void processExtraReleasePlanWithEndTime(SkuRegionReleasePlanEntity plan, LocalDate releaseDate,
            LocalTime nextReleaseTime, boolean isLastPlan, List<SkuReleaseInventoryEntity> results) {
        SkuReleaseInventoryEntity inventory = new SkuReleaseInventoryEntity();
        // 设置基本信息
        inventory.setProductSkuId(plan.getProductSkuId());
        inventory.setProductType(plan.getProductType());
        inventory.setPlanId(plan.getId());
        inventory.setPolicyId(plan.getPolicyId());
        inventory.setReleaseStatus(0);

        // 设置数量
        inventory.setTotalQuantity(plan.getExtraQuantity());
        inventory.setRemainingQuantity(plan.getExtraQuantity());
        inventory.setSoldQuantity(0);

        // 设置时间 - 使用连续时间窗口策略
        LocalDateTime releaseStartTime = releaseDate.atTime(plan.getReleaseTime());
        LocalDateTime releaseEndTime;
        if (isLastPlan) {
            // 最后一个计划：到次日第一个计划开始前1秒
            releaseEndTime = releaseDate.plusDays(1).atTime(nextReleaseTime);
        } else {
            // 非最后一个计划：到当天下一个计划开始前1秒
            releaseEndTime = releaseDate.atTime(nextReleaseTime);
        }
        inventory.setReleaseStartTime(releaseStartTime);
        inventory.setReleaseEndTime(releaseEndTime);

        // 设置创建和修改时间
        LocalDateTime now = LocalDateTime.now();
        inventory.setCreateTime(now);
        inventory.setModTime(now);

        results.add(inventory);
    }

    /**
     * 更新额外放量库存记录 - 使用连续时间窗口策略
     */
    private void updateExtraInventoryRecordsWithEndTime(List<SkuReleaseInventoryEntity> records,
            SkuRegionReleasePlanEntity plan, LocalTime nextReleaseTime, boolean isLastPlan) {
        for (SkuReleaseInventoryEntity entity : records) {
            // 更新放量时间和数量
            LocalDate releaseDate = entity.getReleaseStartTime().toLocalDate();
            LocalDateTime releaseStartTime = releaseDate.atTime(plan.getReleaseTime());
            LocalDateTime releaseEndTime;
            if (isLastPlan) {
                // 最后一个计划：到次日第一个计划开始前1秒
                releaseEndTime = releaseDate.plusDays(1).atTime(nextReleaseTime);
            } else {
                // 非最后一个计划：到当天下一个计划开始前1秒
                releaseEndTime = releaseDate.atTime(nextReleaseTime);
            }
            entity.setReleaseStartTime(releaseStartTime);
            entity.setReleaseEndTime(releaseEndTime);
            entity.setTotalQuantity(plan.getExtraQuantity());
            entity.setRemainingQuantity(Math.max(plan.getExtraQuantity() - entity.getSoldQuantity(), 0));
            entity.setModTime(LocalDateTime.now());
        }
    }

    /**
     * 生成额外放量库存记录 - 使用连续时间窗口策略
     */
    private List<SkuReleaseInventoryEntity> generateExtraReleaseInventoryRecords(
            List<SkuRegionReleasePlanEntity> extraPlans, LocalDate releaseDate) {
        if (CollectionUtils.isEmpty(extraPlans)) {
            return Collections.emptyList();
        }

        List<SkuReleaseInventoryEntity> results = new ArrayList<>();

        // 按policyId分组
        Map<Long, List<SkuRegionReleasePlanEntity>> extraPlansByPolicy = extraPlans.stream()
                .collect(Collectors.groupingBy(SkuRegionReleasePlanEntity::getPolicyId));

        for (Map.Entry<Long, List<SkuRegionReleasePlanEntity>> entry : extraPlansByPolicy.entrySet()) {
            List<SkuRegionReleasePlanEntity> policyPlans = entry.getValue();

            // 按放量时间排序，确保时间段不重叠
            List<SkuRegionReleasePlanEntity> sortedPolicyPlans = policyPlans.stream()
                    .sorted(Comparator.comparing(SkuRegionReleasePlanEntity::getReleaseTime))
                    .toList();

            // 为每个放量计划计算正确的结束时间
            for (int i = 0; i < sortedPolicyPlans.size(); i++) {
                SkuRegionReleasePlanEntity currentPlan = sortedPolicyPlans.get(i);

                // 计算下一个放量时间
                LocalTime nextReleaseTime;
                if (i < sortedPolicyPlans.size() - 1) {
                    // 不是最后一个计划，使用下一个计划的开始时间
                    nextReleaseTime = sortedPolicyPlans.get(i + 1).getReleaseTime();
                } else {
                    // 最后一个计划，使用次日第一个计划的开始时间
                    nextReleaseTime = sortedPolicyPlans.get(0).getReleaseTime();
                }

                processExtraReleasePlanWithEndTime(currentPlan, releaseDate, nextReleaseTime,
                        i == sortedPolicyPlans.size() - 1, results);
            }
        }

        return results;
    }

    private void updateInventoryRecordsWithEndTime(List<SkuReleaseInventoryEntity> records, SkuReleasePlanEntity plan,
            LocalTime nextReleaseTime, boolean isLastPlan) {
        for (SkuReleaseInventoryEntity entity : records) {
            // 更新放量时间和数量
            LocalDate releaseDate = entity.getReleaseStartTime().toLocalDate();
            LocalDateTime releaseStartTime = releaseDate.atTime(plan.getReleaseTime());
            LocalDateTime releaseEndTime;
            if (isLastPlan) {
                // 最后一个计划：到次日第一个计划开始前1秒
                releaseEndTime = releaseDate.plusDays(1).atTime(nextReleaseTime);
            } else {
                // 非最后一个计划：到当天下一个计划开始前1秒
                releaseEndTime = releaseDate.atTime(nextReleaseTime);
            }
            entity.setReleaseStartTime(releaseStartTime);
            entity.setReleaseEndTime(releaseEndTime);
            entity.setTotalQuantity(plan.getReleaseQuantity());
            entity.setRemainingQuantity(Math.max(plan.getReleaseQuantity() - entity.getSoldQuantity(), 0));
            entity.setModTime(LocalDateTime.now());
        }
    }
}
