package com.jsrxjt.mobile.biz.airRecharge.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.mobile.api.airRecharge.AirRechargeRequest;
import com.jsrxjt.mobile.api.customer.response.AirRechargeInfoResponse;
import com.jsrxjt.mobile.biz.airRecharge.service.AirRechargeService;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.domain.airRecharge.entity.AirRechargeEntity;
import com.jsrxjt.mobile.domain.airRecharge.repository.AirRechargeRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.order.entity.TradeCardBalanceChangeEntity;
import com.jsrxjt.mobile.domain.order.repository.TradeCardBalanceChangeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 空中充值
 * @Author: ywt
 * @Date: 2025-08-26 15:07
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AirRechargeServiceImpl implements AirRechargeService {
    private final AirRechargeRepository airRechargeRepository;
    private final CustomerService customerService;
    private final TradeCardBalanceChangeRepository tradeCardBalanceChangeRepository;

    @Override
    public AirRechargeInfoResponse getAirRechargeInfo() {
        List<AirRechargeEntity> list = null;
        Long customId = StpUtil.getLoginIdAsLong();
        CustomerEntity entity = customerService.getCustomerById(customId);
        if (Objects.nonNull(entity) && StringUtils.isNotEmpty(entity.getPhone())) {
            list = airRechargeRepository.getAirRechargeInfo(entity.getPhone());
        }
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        List<AirRechargeEntity> sortedList = list.stream()
                .sorted(Comparator.comparing(
                        AirRechargeEntity::getTruePrice,
                        Comparator.nullsLast(Comparator.reverseOrder())
                ))
                .collect(Collectors.toList());
        AirRechargeEntity bigOne = sortedList.get(0);//取金额最大的一个
        AirRechargeInfoResponse response = BeanUtil.copyProperties(bigOne, AirRechargeInfoResponse.class);
        if (bigOne.getNeedSign() == 1 && bigOne.getIsWindows() == 2) {
            response.setNeedSign(1);
        }
        return response;
    }

    @Override
    public boolean updateAirRechargeStatus(AirRechargeRequest request) {
        log.info("空中充值-更新已读状态， id:{}, 签名url:{}", request.getId(), request.getSignPicUrl());
        AirRechargeEntity airRechargeEntity = airRechargeRepository.getAirRechargeInfoById(request.getId());
        if (Objects.isNull(airRechargeEntity)) {
            return false;
        }
        if (airRechargeEntity.getIsMsg() == 1) {
            log.info("空中充值-更新已读状态-重复更新错误");
            return false;
        }
        if (airRechargeEntity.getNeedSign() == 1 && StringUtils.isEmpty(request.getSignPicUrl())) {
            log.info("空中充值-更新已读状态-缺少签名错误");
            return false;
        } else if (airRechargeEntity.getNeedSign() != 1 && StringUtils.isNotEmpty(request.getSignPicUrl())) {
            log.info("空中充值-更新已读状态-签名参数错误");
            return false;
        }
        int result = airRechargeRepository.updateAirRechargeStatus(request.getId(), request.getSignPicUrl());

        if (result >= 1
                && Objects.nonNull(airRechargeEntity.getTruePrice())
                && Objects.nonNull(airRechargeEntity.getCardNo())) {
            log.info("空中充值保存到卡交易记录表中，手机号：{}，卡号：{}，单号：{}", airRechargeEntity.getMobile(), airRechargeEntity.getCardNo(), airRechargeEntity.getOrderNo());
            TradeCardBalanceChangeEntity tradeCardBalanceChangeEntity = new TradeCardBalanceChangeEntity();
            tradeCardBalanceChangeEntity.setCardNo(airRechargeEntity.getCardNo());
            tradeCardBalanceChangeEntity.setOrderNo(airRechargeEntity.getOrderNo());
            tradeCardBalanceChangeEntity.setTradeInfoNo(airRechargeEntity.getOrderNo());//空充没有这个值，但这个字段又是必填，所以填入订单号充数
            BigDecimal fenPrice = airRechargeEntity.getTruePrice().multiply(new BigDecimal("100")).setScale(0, RoundingMode.HALF_UP);
            tradeCardBalanceChangeEntity.setTradePrice(fenPrice.longValue());
            tradeCardBalanceChangeEntity.setType("AIR_RECHARGE");
            Date date = new Date(airRechargeEntity.getCreateTime() * 1000L);
            tradeCardBalanceChangeEntity.setTradeTime(date);
            int res = tradeCardBalanceChangeRepository.saveAirRecharge(tradeCardBalanceChangeEntity);
            if (res <= 0) {
                log.info("空中充值保存到卡交易记录表中发生错误，手机号：{}，卡号：{}，单号：{}", airRechargeEntity.getMobile(), airRechargeEntity.getCardNo(), airRechargeEntity.getOrderNo());
            }
        }

        return result >= 1 ? true : false;
    }
}
