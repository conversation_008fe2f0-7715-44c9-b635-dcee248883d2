package com.jsrxjt.mobile.infra.ticket.util;

import java.security.NoSuchAlgorithmException;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.security.MessageDigest;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-10-16 19:19
 * @Version: 1.0
 */
public class TicketPlateformUtil {
    private final static String[] hexDigits = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };

    public static String getSignature(String appSecret, SortedMap<Object, Object> parameters) {
        StringBuffer sb = new StringBuffer();
        StringBuffer sbkey = new StringBuffer();
        Set es = parameters.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            Object v = entry.getValue();
            //空值不传递，不参与签名组串
            if (null != v && !"".equals(v)) {
                sb.append(k + "=" + v + "&");
                sbkey.append(k + "=" + v + "&");
            }
        }
        sbkey = sbkey.append("key=").append(appSecret);
        String encodeUpper = UpperMD5Encode(sbkey.toString());
        return encodeUpper;
    }

    public static String UpperMD5Encode(String origin) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            return toHexString(md.digest(origin.getBytes())).toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            // not process
        }
        return null;
    }

    public static String toHexString(byte[] b) {
        StringBuffer sb = new StringBuffer(b.length * 2);
        for (int i = 0; i < b.length; i++) {
            sb.append(hexDigits[(b[i] & 0xf0) >>> 4]);
            sb.append(hexDigits[b[i] & 0x0f]);
        }
        return sb.toString();
    }
}
