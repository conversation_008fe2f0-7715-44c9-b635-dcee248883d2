package com.jsrxjt.mobile.domain.customer.repository;

import com.jsrxjt.mobile.domain.customer.entity.RechargeAccountHistoryEntity;

import java.util.List;

/**
 * 用户历史充值账号Repository
 * 
 * <AUTHOR>
 * @since 2025/8/5
 */
public interface RechargeAccountHistoryRepository {
    
    /**
     * 保存历史充值账号
     * 
     * @param entity 历史充值账号实体
     * @return 保存后的实体
     */
    RechargeAccountHistoryEntity save(RechargeAccountHistoryEntity entity);
    
    /**
     * 根据客户ID和产品信息查询历史充值账号
     * 
     * @param customerId 客户ID
     * @param productSpuId 产品SPU ID
     * @param productType 产品类型
     * @param accountType 账户类型
     * @return 历史充值账号列表
     */
    List<RechargeAccountHistoryEntity> findByCustomerAndProduct(Long customerId, Long productSpuId, 
                                                               Integer productType, Integer accountType);

    /**
     * 条件查询历史充值账号列表
     * @param query 查询参数
     * @return 历史充值账号列表
     */
    List<RechargeAccountHistoryEntity> findByCriteria(RechargeAccountHistoryEntity query);
    
    /**
     * 根据客户ID查询历史充值账号
     * 
     * @param customerId 客户ID
     * @return 历史充值账号列表
     */
    List<RechargeAccountHistoryEntity> findByCustomerId(Long customerId);
    
    /**
     * 根据ID查询历史充值账号
     * 
     * @param id 主键ID
     * @return 历史充值账号实体
     */
    RechargeAccountHistoryEntity findById(Long id);

    /**
     * 删除历史充值账户记录
     *
     * @param id 主键ID
     */
    Integer DelHisRechargeAccount(Long customerId, Long id);
}
