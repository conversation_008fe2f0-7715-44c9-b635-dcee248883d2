package com.jsrxjt.mobile.biz.promotion.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.util.date.DatetimeUtils;
import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.api.promotion.types.PromotionActivityStatusEnum;
import com.jsrxjt.mobile.biz.promotion.service.PromotionActivityService;
import com.jsrxjt.mobile.domain.promotion.entity.PromotionActivityEntity;
import com.jsrxjt.mobile.domain.promotion.entity.PromotionActivitySkuEntity;
import com.jsrxjt.mobile.domain.promotion.repository.PromotionActivityRepository;
import com.jsrxjt.mobile.domain.promotion.repository.PromotionActivitySkuRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Service
@Slf4j
@RequiredArgsConstructor
public class PromotionActivityServiceImpl implements PromotionActivityService {

    private final PromotionActivityRepository promotionActivityRepository;

    private final PromotionActivitySkuRepository promotionActivitySkuRepository;

    private final RedisUtil redisUtil;

    @Override
    public List<PromotionActivityEntity> listShouldStartActivity() {
        return promotionActivityRepository.listShouldStartActivity();
    }

    @Override
    public List<PromotionActivityEntity> listShouldExpireActivity() {
        return promotionActivityRepository.listShouldExpireActivity();
    }

    @Override
    public void startActivity(PromotionActivityEntity entity) {
        if (!isCanToStart(entity)){
            log.warn("卡券活动{}活动状态不需要或不允许被激活", entity.getId());
            return;
        }
        PromotionActivityEntity updateEntity = new PromotionActivityEntity();
        updateEntity.setId(entity.getId());
        updateEntity.setActivityStatus(PromotionActivityStatusEnum.IN_PROGRESS.getStatus());
        updateEntity.setModTime(new Date());
        updateEntity.setModId(9999L);
        promotionActivityRepository.updatePromotionActivity(updateEntity);
        entity.setActivityStatus(PromotionActivityStatusEnum.IN_PROGRESS.getStatus());
        List<PromotionActivitySkuEntity> skuList = promotionActivitySkuRepository.getPromotionActivitySkulist(entity.getId());
        if (CollectionUtils.isEmpty(skuList)) {
            log.warn("卡券活动{}没有设置商品", entity.getId());
            return;
        }
        long expire = DatetimeUtils.getSecondsBetweenDates(entity.getEndTime(),new Date());
        redisUtil.set(RedisKeyConstants.PROMOTION_ACTIVITY_INFO + entity.getId(), JSON.toJSONString(entity), Math.toIntExact(expire));
    }

    @Override
    public void expireActivity(PromotionActivityEntity entity) {
        // 判断是否可以过期
        if (!isCanToExpire(entity)) {
            log.warn("卡券活动{}活动状态不需要或不允许被过期", entity.getId());
            return;
        }
        // 更新状态为已结束
        PromotionActivityEntity updateEntity = new PromotionActivityEntity();
        updateEntity.setId(entity.getId());
        updateEntity.setActivityStatus(PromotionActivityStatusEnum.ENDED.getStatus());
        updateEntity.setModTime(new Date());
        updateEntity.setModId(9999L);
        promotionActivityRepository.updatePromotionActivity(updateEntity);
    }

    public boolean isCanToStart(PromotionActivityEntity entity) {
        if (!Objects.equals(entity.getActivityStatus(), PromotionActivityStatusEnum.UN_START.getStatus())) {
            return false;
        }
        Date now = new Date();
        return now.compareTo(entity.getStartTime()) >= 0 && now.compareTo(entity.getEndTime()) <= 0;
    }

    public boolean isCanToExpire(PromotionActivityEntity entity) {
        if ((Objects.equals(entity.getActivityStatus(), PromotionActivityStatusEnum.ENDED.getStatus())
                || Objects.equals(entity.getActivityStatus(), PromotionActivityStatusEnum.CLOSED.getStatus()))) {
            return false;
        }
        Date now = new Date();
        return now.after(entity.getEndTime());
    }


}
