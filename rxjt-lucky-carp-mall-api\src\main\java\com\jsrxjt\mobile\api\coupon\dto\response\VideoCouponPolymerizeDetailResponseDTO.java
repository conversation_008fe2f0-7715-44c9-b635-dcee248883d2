package com.jsrxjt.mobile.api.coupon.dto.response;

import com.jsrxjt.mobile.api.product.dto.ProductExplainResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 聚合页视听卡券详情
 * @Author: ywt
 * @Date: 2025-07-23 13:50
 * @Version: 1.0
 */
@Data
public class VideoCouponPolymerizeDetailResponseDTO {
    @Schema(description = "视频直充卡券id")
    private Long couponSpuId;
    @Schema(description = "卡券名称")
    private String couponSpuName;
    @Schema(description = "logo图片url")
    private String logoUrl;
    @Schema(description = "品牌名")
    private String brandName;
    /*@Schema( description = "1-腾讯视频 2-爱奇艺 3-优酷视频 4-芒果TV 5-中石化权益包 6-便利蜂 7-咪咕视频 8-喜马拉雅 9-搜狐视频，前端可用来判断详情页使用哪种模版样式显示，仅用于直充类卡券")
    private Integer showTemplate;*/
    @Schema(description = "是否选中， 0--未选中 1--选中")
    private Integer isSelected;
    @Schema(description = "使用须知，仅isSelected为1有效")
    List<ProductExplainResponseDTO> userDetailList;
    @Schema(description = "温馨提示，仅isSelected为1有效")
    List<ProductExplainResponseDTO> warmToastList;
    @Schema(description = "充值类型列表，列表已按accountType大小倒序排序，仅isSelected为1有效")
    private List<VideoCouponAccountTypeDTO> accountTypeList;
}
