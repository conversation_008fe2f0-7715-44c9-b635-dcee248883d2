<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.product.persistent.mapper.ProductSkuSellRegionMapper">

    <select id="selectBySpuIdsAndTypeAndRegion" resultType="com.jsrxjt.mobile.infra.product.persistent.po.ProductSkuSellRegionPO">
        SELECT DISTINCT product_spu_id, product_type
        FROM product_sku_sell_region
        WHERE del_flag = 0
        AND (
        (
        region_id = #{params.regionId}
        <if test="params.regionType != null and params.regionType == 2">
            AND is_all = 1
        </if>
        )
        OR
        (region_id = 0)
        )
        AND (
        <foreach collection="params.typeConditions" item="cond" separator=" OR ">
            (
            product_type = #{cond.productType}
            AND product_spu_id IN
            <foreach collection="cond.spuIds" item="spuId" open="(" separator="," close=")">
                #{spuId}
            </foreach>
            )
        </foreach>
        )
    </select>




</mapper>