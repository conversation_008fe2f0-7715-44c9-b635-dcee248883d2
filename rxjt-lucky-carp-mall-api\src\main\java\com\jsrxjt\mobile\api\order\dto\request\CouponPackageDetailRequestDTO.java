package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 卡包详情请求参数
 * <AUTHOR>
 * @date 2025/07/22
 */
@Data
public class CouponPackageDetailRequestDTO extends BaseParam{

    @Schema(description = "卡包ID")
    @NotNull(message = "卡包ID不能为空")
    private Long couponPackageId;

}
