package com.jsrxjt.mobile.domain.ticket.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-11-24 09:27
 * @Version: 1.0
 */
@Data
public class TicketProductEntity {
    private Long id;
    @Schema(description = "卡券skuid")
    private Long productSkuId;
    @Schema(description = "产品类型：1卡券 2套餐")
    private Integer productType;
    @Schema(description = "优惠券id")
    private Long ticketId;
    /*@Schema(description = "状态 1启用 0禁用")
    @TableField("status")
    private Integer status;*/
    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "创建人id")
    private Long createId;

    @Schema(description = "编辑人id")
    private Long modId;

    @Schema(description = "编辑时间")
    private Date modTime;

    @Schema(description = "是否删除标志(0:否 1:是)")
    private Byte delFlag;

    @Schema(description = "删除人id")
    private Long delId;

    @Schema(description = "删除时间")
    private Date delTime;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "修改人姓名")
    private String modName;
}
