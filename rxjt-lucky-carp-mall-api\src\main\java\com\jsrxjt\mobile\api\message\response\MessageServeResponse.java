package com.jsrxjt.mobile.api.message.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 客服消息
 * </p>
 *
 */
@Getter
@Setter

@Schema(name = "MessageAccount", description = "客服消息内容")
public class MessageServeResponse {


    private Long serveMsgId;

    @Schema(description = "客户id")
    private Long customerId;

    @Schema(description = "缩略图")
    private String thumbUrl;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "副标题")
    private String subTitle;

    @Schema(description = "是否已读 0否 1是")
    private Integer isRead;

    @Schema(description = "类型（待定）")
    private Integer type;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "支付方式")
    private Integer payType;

    @Schema(description = "连接")
    private String url;




}
