package com.jsrxjt.mobile.domain.order.repository;

import com.jsrxjt.mobile.domain.order.entity.TradeOrderEntity;
import com.jsrxjt.mobile.domain.order.entity.TradeOrderInfoEntity;

import java.util.List;

/**
 * 交易订单仓储接口
 * <AUTHOR>
 * @since 2025/8/15
 */
public interface TradeOrderRepository {

    TradeOrderEntity findTradeOrderByOrderNoAndTradeNo(String orderNo, String tradeNo);

    /**
     * 根据交易号和订单号查询交易订单信息列表
     * 
     * @param tradeNo 交易号
     * @param orderNo 订单号
     * @return 交易订单信息列表
     */
    List<TradeOrderInfoEntity> listTradeOrderInfoByTradeNoAndOrderNo(String tradeNo, String orderNo);

}