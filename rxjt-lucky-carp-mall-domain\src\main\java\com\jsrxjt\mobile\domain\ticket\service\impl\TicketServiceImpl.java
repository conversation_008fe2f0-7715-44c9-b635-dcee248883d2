package com.jsrxjt.mobile.domain.ticket.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.ticket.types.TicketTypeEnum;
import com.jsrxjt.mobile.domain.product.entity.SpuLimitStrategyRegionEntity;
import com.jsrxjt.mobile.domain.ticket.config.TicketPlatformConfig;
import com.jsrxjt.mobile.domain.ticket.entity.*;
import com.jsrxjt.mobile.domain.ticket.gateway.TicketPlatformGateway;
import com.jsrxjt.mobile.domain.ticket.gateway.cmd.GiveOutTicketCmd;
import com.jsrxjt.mobile.domain.ticket.repository.*;
import com.jsrxjt.mobile.domain.ticket.service.TicketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 优惠券领域服务
 * @Author: ywt
 * @Date: 2025-08-15 15:53
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TicketServiceImpl implements TicketService {
    private final TicketProductRepository ticketProductRepository;
    private final TicketRepository ticketRepository;
    private final TicketRegionRepository ticketRegionRepository;
    private final TicketBrandRepository ticketBrandRepository;
    private final RedisUtil redisUtil;
    private final TicketPlatformConfig ticketPlatformConfig;
    private final TicketPlatformGateway ticketPlatformGateway;
    private final TicketDeliveryRepository ticketDeliveryRepository;

    @Override
//    @Cacheable(cacheNames = "product:sku:ticket", key = "#productType + ':' + #productSkUID", unless = "#result == null")
    public List<TicketEntity> getTicketsBySkuIdAndPrdType(Long productSkUID, Integer productType) {
        List<TicketEntity> resultList = null;
        String key = String.format(RedisKeyConstants.PRODUCT_SKU_TICKET, productType, productSkUID);
        String content = redisUtil.get(key);
        if (StringUtils.isNotEmpty(content)) {
            resultList = JSONArray.parseArray(content, TicketEntity.class);
        } else {
            List<Long> ticketIds = ticketProductRepository.getTicketsIdBySkuIdAndPrdType(productSkUID, productType);
            if (CollectionUtil.isEmpty(ticketIds)) {
                return null;
            }
            resultList = ticketRepository.getTicketsByIds(ticketIds);
            redisUtil.set(key, JSONArray.toJSONString(resultList), true);
        }
        return resultList;
    }

    @Override
    public boolean isOnlineInRegion(Long ticketId, Integer regionId) {
        List<TicketRegionEntity> regionEntityList = ticketRegionRepository.getRegionById(ticketId);
        if (CollectionUtil.isEmpty(regionEntityList)) {
            return false;
        }

        Map<Integer, TicketRegionEntity> messageUserMap = regionEntityList.stream().collect(Collectors.toMap(TicketRegionEntity::getRegionId, region -> region));
        TicketRegionEntity regionEntity = messageUserMap.get(regionId);
        if (Objects.isNull(regionEntity)) {
            return false;
        }
        if (Objects.nonNull(regionEntity.getRegionType()) && regionEntity.getRegionType() >= 3) {
            //三级地址
            return true;
        }
        if (Objects.nonNull(regionEntity.getIsAll()) && regionEntity.getIsAll() == 1) {
            //一级或二级地址 且 子地址是全选
            return true;
        }

        /*Set<Integer> regionSet = regionEntityList.stream()
                .map(TicketRegionEntity::getRegionId)
                .collect(Collectors.toSet());
        if (regionSet.contains(regionId)) {
            return true;
        }*/
        return false;
    }

    @Override
    public List<TicketBrandEntity> getTicketBrandsByIds(List<Long> brandIds) {
        return ticketBrandRepository.getTicketBrandsByIds(brandIds);
    }

    @Override
    public List<TicketEntity> getTicketsBySkuIdsAndTicketIds(Long productSkUID, Integer productType, List<Long> ticketIds) {
        if (CollectionUtil.isEmpty(ticketIds)) {
            return null;
        }
        List<TicketEntity> resultList = new ArrayList<>();
        List<TicketEntity> list = getTicketsBySkuIdAndPrdType(productSkUID, productType);
        if (CollectionUtil.isEmpty(list)) {
            log.error("此sku没有优惠券:{},{}", productSkUID, productType);
            throw new BizException("此sku没有优惠券");
        }
        Map<Long, TicketEntity> ticketMap = list.stream().collect(Collectors.toMap(TicketEntity::getTicketId, ticket -> ticket));
        ticketIds.forEach(item -> {
            TicketEntity entity = ticketMap.get(item);
            if (Objects.isNull(entity)) {
                log.error("没有查到指定的优惠券:{}", item);
                throw new BizException("没有查到指定的优惠券信息");
            }
            resultList.add(entity);
        });
        return resultList;
    }

    @Override
    public BaseResponse callback(TicketCallBackEntity callBackEntity) {
        if (!callBackEntity.getAppid().equals(ticketPlatformConfig.getAppId())) {
            log.error("营销中台-优惠券状态回调-appid错误，中台：{} --- 福鲤圈：{}", callBackEntity.getAppid(), ticketPlatformConfig.getAppId());
            return BaseResponse.fail("营销中台-优惠券状态回调-Appid参数错误");
        }
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("appid", callBackEntity.getAppid());
        requestMap.put("nounce", callBackEntity.getNounce());
        requestMap.put("timestamp", callBackEntity.getTimestamp());
        requestMap.put("couponId", callBackEntity.getCouponId());
        requestMap.put("status", callBackEntity.getStatus());
        String sign = ticketRepository.getPlateformSign(requestMap);
        if (!sign.equals(callBackEntity.getSignature())) {
            log.info("营销中台-优惠券状态回调-签名校验失败-中台：{}--福鲤圈：{}", callBackEntity.getSignature(), sign);
            return BaseResponse.fail("签名校验失败");
        }
        List<TicketEntity> entityList = ticketRepository.getTicketByCenterId(callBackEntity.getCouponId().toString());
        if (CollectionUtil.isNotEmpty(entityList)) {
            for (TicketEntity entity : entityList) {
                if (Objects.isNull(entity)) {
                    log.info("营销中台-优惠券状态回调-优惠券不存在或已删除-{}", callBackEntity.getCouponId());
                    return BaseResponse.fail("优惠券不存在或已删除");
                }
                if (Objects.nonNull(entity.getManualDown()) && entity.getManualDown() == 1 && callBackEntity.getStatus() == 1) {
                    log.info("营销中台-优惠券状态回调-优惠券已被运营下架-{}", callBackEntity.getCouponId());
                    return BaseResponse.fail("优惠券已被运营下架");
                }
                //删除缓存
                if (callBackEntity.getStatus() == 2) {
                    //删除优惠券的城市缓存
                    redisUtil.delete(RedisKeyConstants.TICKET_REGION + entity.getTicketId());
                }
                List<TicketProductEntity> ticketProductList = ticketProductRepository.getProductsByTicket(entity.getTicketId());
                for (TicketProductEntity ticketProductRelation : ticketProductList) {
                    String key = String.format(RedisKeyConstants.PRODUCT_SKU_TICKET, ticketProductRelation.getProductType(), ticketProductRelation.getProductSkuId());
                    redisUtil.delete(key);
                }

                //更新数据库
                ticketRepository.updateTicketStatusByPlateform(entity.getTicketId(), callBackEntity.getStatus());
            }
        }
        return new BaseResponse(200, "请求成功");
    }

    @Override
    public BaseResponse consumeCallback(TicketConsumeCallBackEntity entity) {
        if (!entity.getAppid().equals(ticketPlatformConfig.getAppId())) {
            log.error("营销中台-优惠券核销状态回调-appid错误，中台：{} --- 福鲤圈：{}", entity.getAppid(), ticketPlatformConfig.getAppId());
            return BaseResponse.fail("营销中台-优惠券核销状态回调-Appid参数错误");
        }
        if (entity.getStatus() != 1) {
            return BaseResponse.fail("营销中台-优惠券核销状态回调-核销状态不为1错误");
        }
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("appid", entity.getAppid());
        requestMap.put("nounce", entity.getNounce());
        requestMap.put("timestamp", entity.getTimestamp());
        requestMap.put("couponNumber", entity.getCouponNumber());
        requestMap.put("num", entity.getNum());
        requestMap.put("status", entity.getStatus());
        if (StringUtils.isNotEmpty(entity.getCode())) {
            requestMap.put("code", entity.getCode());
        }
        String sign = ticketRepository.getPlateformSign(requestMap);
        if (!sign.equals(entity.getSignature())) {
            log.info("营销中台-优惠券核销状态回调-签名校验失败-中台：{}--福鲤圈：{}", entity.getSignature(), sign);
            return BaseResponse.fail("签名校验失败");
        }
        List<TicketDeliveryEntity> deliveryEntityList = ticketDeliveryRepository.getByCenterNumber(entity.getCouponNumber());
        if (CollectionUtil.isEmpty(deliveryEntityList)) {
            log.info("营销中台-优惠券核销状态回调-查不到优惠券信息：{}", entity.getCouponNumber());
            return BaseResponse.fail("查不到优惠券信息");
        }
        Boolean result = false;
        for (TicketDeliveryEntity ticketDeliveryEntity : deliveryEntityList) {
            if (ticketDeliveryEntity.getTicketType() == TicketTypeEnum.MERCHANT_COUPON.getCode()
                    || ticketDeliveryEntity.getTicketType() == TicketTypeEnum.RX_DISTRIBUTION_COUPON.getCode()) {
                if (StringUtils.isEmpty(entity.getCode())) {
                    log.info("营销中台-优惠券核销状态回调-卡管的code为空错误：{}", entity.getCouponNumber());
                    return BaseResponse.fail("卡管的code为空错误");
                }
                //商家自发券和瑞祥代发券核销依据是卡管卡号
                if (StringUtils.isNotEmpty(ticketDeliveryEntity.getTicketCode())
                        && ticketDeliveryEntity.getTicketCode().equals(entity.getCode())) {
                    result = ticketDeliveryRepository.updateTicketStatusToUsed(ticketDeliveryEntity.getId());
                }
            } else if (ticketDeliveryEntity.getTicketType() == TicketTypeEnum.GLOBAL_MALL_COUPON.getCode()
                    || ticketDeliveryEntity.getTicketType() == TicketTypeEnum.RX_SHOP_COUPON.getCode()) {
                //全球购商城和营销案核销依据是营销中台的卡券号
                result = ticketDeliveryRepository.updateTicketStatusToUsed(ticketDeliveryEntity.getId());
            }
        }
        if (!result) {
            log.info("营销中台-优惠券核销状态回调-核销状态变更失败：{}", entity.getCouponNumber());
            return BaseResponse.fail("核销状态变更失败");
        }
        return BaseResponse.succeed();
    }

    @Override
    public List<String> receiveBirthCoupon(String ticketId, Integer number, Long timeStamp, String nonce) {
        GiveOutTicketCmd cmd = GiveOutTicketCmd.builder()
                .userCode(StpUtil.getLoginIdAsString())
                .ticketId(ticketId)
                .number(number)
                .timestamp(timeStamp)
                .nonce(nonce)
                .build();
        return ticketPlatformGateway.giveOutTicket(cmd, (code, msg) -> {
            log.warn("获取生日券失败 code{}, message:{}", code, msg);
        });
    }
}
