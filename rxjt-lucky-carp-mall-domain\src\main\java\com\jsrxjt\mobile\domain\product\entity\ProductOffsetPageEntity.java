package com.jsrxjt.mobile.domain.product.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ProductOffsetPageEntity {
    @Schema(description = "核销页核销须知id")
    private Long id;

    @Schema(description = "产品spuid")
    private Long productSpuId;

    @Schema(description = "产品类型 1 卡券 2 套餐")
    private Byte productType;

    @Schema(description = "适用场景")
    private String scenarios;


    @Schema(description = "背景色")
    private String backgroundColor;

    @Schema(description = "核销页有效期")
    private String validityDate;

    @Schema(description = "备注 使用说明")
    private String remark;

}
