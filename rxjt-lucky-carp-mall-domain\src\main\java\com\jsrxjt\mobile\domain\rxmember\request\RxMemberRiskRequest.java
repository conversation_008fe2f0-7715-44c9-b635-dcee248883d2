package com.jsrxjt.mobile.domain.rxmember.request;

import com.jsrxjt.mobile.api.riskcontrol.types.RiskBusinessTypeEnum;
import lombok.Data;
import java.math.BigDecimal;

/**
 * @Description: 用户风险等级请求参数
 * @Author: ywt
 * @Date: 2025-09-25 13:55
 * @Version: 1.0
 */
@Data
public class RxMemberRiskRequest {
    /**
     * 用户Id
     */
    private Long customerId;
    /**
     * 客户端IP地址(ipv4格式),若无法拿到ip，可以传入一个定值 unknown 代表为空
     */
    private String IP;
    /**
     * 业务id（在易盾后台取）
     */
    private String businessId;
    /**
     * 前端集成易盾sdk后可获取到该值
     */
    private String token;
    private RiskBusinessTypeEnum typeEnum;
    /**
     * 注册或登录数据
     */
    private RegisterOrLogData registerOrLogData;
    /**
     * 下单数据
     */
    private PlaceOrderData placeOrderData;
    /**
     * 绑卡数据
     */
    private BindCardData bindCardData;

    @Data
    public static class RegisterOrLogData {
        /**
         * 用户使用的应用来源渠道（或者说是app的上架渠道）,如用户是从GooglePlay下载应用，则可传入：googleplay。如业务无法获知当前应用的来源渠道，可传入：unknown
         */
        private String appChannel;
        /**
         * 注册或登录方式,可选参数内容如下：
         *  - userPassword：用户名密码注册或登录
         *  - phoneAuth：手机号一键注册或登录（包含短信验证码方式注册方式）
         *  - otherPlatformAuth：第三方平台授权注册或登录（如微信授权注册等）
         *  - unknown：未知注册或登录方式
         */
        private String registerOrLogType;
        /**
         * 标记本次行为属性,是注册还是登录-register：本次行为为注册行为 -log：本次行为为登录行为
         */
        private String operationType;
        /**
         * 如果是“第三方平台授权注册或登录”,可传入注册或登录渠道,可选参数内容如下：
         *  - qq：qq授权注册或登录
         *  - weixin：微信授权注册或登录
         *  - alipay：支付宝授权注册或登录
         *  - weibo：微博授权注册或登录
         *  - other：未知/其他平台授权注册或登录
         */
        private String registerOrLogChannel;
        /**
         * 微信小程序用户唯一标识：openID
         */
        private String openId;
    }

    @Data
    public static class PlaceOrderData {
        /**
         * 卡券ID
         */
        private Long goodsId;
        /**
         * 卡券数量
         */
        private Integer goodsCount;
        /**
         * 消费金额(元)
         */
        private BigDecimal consumption;
    }

    @Data
    public static class BindCardData {
        /**
         * 瑞祥卡号
         */
        private String cardNo;
    }
}
