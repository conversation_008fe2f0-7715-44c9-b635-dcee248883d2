package com.jsrxjt.mobile.api.customer.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "卡交易信息")
public class CustomerCardTradeResponse {

    @Schema(description = "卡号")
    private String cardNo;

    @Schema(description = "交易时间")
    private Date tradeTime;

    @Schema(description = "业务订单号")
    private String orderNo;

    @Schema(description = "交易产品名称")
    private String tradeName;

    @Schema(description = "变动类型  TRADE交易  REFUND退款 CANCEL冲正 RECHARGE充值")
    private String type;

    @Schema(description = "交易金额")
    private BigDecimal tradePrice;

}
