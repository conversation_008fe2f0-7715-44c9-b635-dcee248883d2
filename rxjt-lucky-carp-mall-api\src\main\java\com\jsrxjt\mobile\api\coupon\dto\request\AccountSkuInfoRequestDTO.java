package com.jsrxjt.mobile.api.coupon.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 充值账户类型sku请求参数
 * @Author: ywt
 * @Date: 2025-07-24 10:52
 * @Version: 1.0
 */
@Data
public class AccountSkuInfoRequestDTO {
    @Schema(description = "卡券的spuid")
    @NotNull(message = "卡券spuid为空错误")
    private Long couponSpuId;
    @Schema(description = "三级地址id")
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
    @Schema(description = "充值类型,0其他 1手机号 2QQ号")
    @NotNull(message = "充值类型为空错误")
    private Integer accountType;
    @Schema(description = "卡券的skuid，本字段用于标记选中的sku，若不传默认选中第一个sku")
    private Long couponSkuId;
}
