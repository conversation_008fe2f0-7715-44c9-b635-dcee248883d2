package com.jsrxjt.mobile.api.product.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.YearMonth;

/**
 * @Description: 充值记录请求参数
 * @Author: ywt
 * @Date: 2025-07-28 14:33
 * @Version: 1.0
 */
@Data
public class AccountRechargeRecordRequestDTO extends BaseParam {
    @Schema(description = "102-视听会员充值 402-支付宝红包 407-便利蜂直充")
    @NotNull(message = "产品类型不能为空")
    private Integer productType;
    @Schema(description = "时间，格式:yyyy-MM，若不传就取当前月的数据")
    @DateTimeFormat(pattern = "yyyy-MM")
    private YearMonth yearMonth;
    @Schema(description = "产品的spuid, 注意：视听会员充值产品必填，支付宝红包不需要填")
    private Long productSpuId;

    public YearMonth getYearMonth() {
        return yearMonth != null ? yearMonth : YearMonth.now(); //默认当前年月
    }
}
