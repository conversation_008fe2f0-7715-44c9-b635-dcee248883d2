package com.jsrxjt.mobile.api.order.types;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后操作类型枚举
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Getter
@AllArgsConstructor
public enum AfterSaleOperationTypeEnum {
    
    /**
     * 申请售后
     */
    APPLY_AFTER_SALE(1, "申请售后"),
    
    /**
     * 审核通过
     */
    AUDIT_PASSED(2, "审核通过"),
    
    /**
     * 审核拒绝
     */
    AUDIT_REJECTED(3, "审核拒绝"),
    
    /**
     * 退款成功
     */
    REFUND_SUCCESS(4, "退款成功"),
    
    /**
     * 退款失败
     */
    REFUND_FAILED(5, "退款失败"),
    
    /**
     * 撤销售后单
     */
    CANCEL_AFTER_SALE(6, "撤销售后单"),
    
    /**
     * 拒绝退款
     */
    REJECT_REFUND(7, "拒绝退款");
    
    private final Integer code;
    private final String desc;
    
    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static AfterSaleOperationTypeEnum getByCode(Integer code) {
        for (AfterSaleOperationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 