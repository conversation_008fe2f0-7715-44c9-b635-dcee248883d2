package com.jsrxjt.mobile.domain.order.types;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交易退款状态枚举
 * <AUTHOR>
 * @since 2025/8/15
 */
@Getter
@AllArgsConstructor
public enum TradeRefundStatusEnum {
    
    WAIT_REFUND("NONE", "无退款"),
    PART_REFUND("PART", "部分退款"),
    FULL_REFUND("FULL", "全部退款");
    
    private final String code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static TradeRefundStatusEnum fromCode(String code) {
        for (TradeRefundStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
