package com.jsrxjt.mobile.infra.order.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.entity.SubSkuOrderEntity;
import com.jsrxjt.mobile.domain.order.query.OrderListQuery;
import com.jsrxjt.mobile.domain.order.repository.OrderItemRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.infra.order.persistent.mapper.OrderItemMapper;
import com.jsrxjt.mobile.infra.order.persistent.mapper.OrderMapper;
import com.jsrxjt.mobile.infra.order.persistent.mapper.SubSkuOrderMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.OrderItemPO;
import com.jsrxjt.mobile.infra.order.persistent.po.OrderPO;
import com.jsrxjt.mobile.infra.order.persistent.po.OrderWithItemsPO;
import com.jsrxjt.mobile.infra.order.persistent.po.SubSkuOrderPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单仓储实现类
 *
 * <AUTHOR> Fengping
 * @since 2025/6/14
 **/
@Slf4j
@Repository
@RequiredArgsConstructor
public class OrderItemRepositoryImpl implements OrderItemRepository {


    private final OrderItemMapper orderItemMapper;


    /**
     * @param orderItemId
     * @return
     */
    @Override
    public OrderItemEntity findById(Long orderItemId) {
        OrderItemPO orderItemPO = orderItemMapper.selectById(orderItemId);
        if (orderItemPO != null) {
            OrderItemEntity orderItemEntity = new OrderItemEntity();
            BeanUtils.copyProperties(orderItemPO, orderItemEntity);
            return orderItemEntity;
        }
        return null;
    }
}
