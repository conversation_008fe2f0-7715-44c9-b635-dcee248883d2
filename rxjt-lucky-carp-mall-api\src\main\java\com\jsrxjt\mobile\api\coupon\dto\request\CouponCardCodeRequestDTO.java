package com.jsrxjt.mobile.api.coupon.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 自发券的动态码请求参数
 * @Author: ywt
 * @Date: 2025-05-19 19:48
 * @Version: 1.0
 */
@Data
@Schema(description = "自发券的动态码请求参数")
public class CouponCardCodeRequestDTO {
    @Schema(description = "核销编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "核销编号为空错误")
    private String checkNo;
    @Schema(description = "用户Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户Id为空错误")
    private String userId;
    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "时间戳为空错误")
    private String timestamp;
    @Schema(description = "32位随机字符串", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "参数nonce为空错误")
    private String nonce;
}
