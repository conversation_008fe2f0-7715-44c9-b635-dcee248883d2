package com.jsrxjt.mobile.api.product.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 产品联想词请求DTO
 * 
 * <AUTHOR>
 * @since 2025/5/9
 **/
@Getter
@Setter
@Schema(description = "产品联想词请求")
public class ProductSuggestionRequestDTO extends BaseParam {

    /**
     * 搜索关键词
     */
    @Schema(description = "搜索关键词")
    private String keyword;
    
    /**
     * 最大返回条数
     */
    @Schema(description = "最大返回条数，默认10条")
    @Min(value = 1, message = "最少返回1条")
    @Max(value = 20, message = "最多返回20条")
    private Integer maxCount = 10;
} 