package com.jsrxjt.mobile.api.customer.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 空充信息响应
 * @Author: ywt
 * @Date: 2025-08-26 14:32
 * @Version: 1.0
 */
@Data
@Schema(description = "空充信息响应")
public class AirRechargeInfoResponse {
    @Schema(description = "主键id")
    private Integer id;
    @Schema(description = "手机号")
    private String mobile;
    @Schema(description = "真实已经成功充值的金额")
    private BigDecimal truePrice;
    @Schema(description = "充值卡号")
    private String cardNo;
    @Schema(description = "充值类型：1黑金主卡 2白金主卡")
    private Integer rechargeType;
    @Schema(description = "是否需要签名 1:需要 2：不需要")
    private Integer needSign;
    @Schema(description = "公司ID")
    private Integer companyId;
}
