package com.jsrxjt.mobile.api.distribution.dto.response;

import lombok.Data;

/**
 * 饿了么订单支付状态查询结果
 * <AUTHOR>
 * @since 2025/12/9
 */
@Data
public class ELeMeOrderQueryResponseDTO {

    /**
     * 支付网关的订单号
     */
    private String transactionId;

    /**
     * 福鲤圈订单号
     */
    private String outTradeNo;

    /**
     * 订单支付金额 单位分
     */
    private String payAmount;

    /**
     * 支付状态 NOTPAY=未支付；SUCCESS=支付成功；CLOSED= 已关闭
     */
    private String payStatus;

}
