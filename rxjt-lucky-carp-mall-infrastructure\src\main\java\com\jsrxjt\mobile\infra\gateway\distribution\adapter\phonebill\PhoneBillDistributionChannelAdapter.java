package com.jsrxjt.mobile.infra.gateway.distribution.adapter.phonebill;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 话费充值分销渠道适配器
 * <AUTHOR>
 * @Date 2025/10/16
 */
@Component
@Slf4j
public class PhoneBillDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

    public PhoneBillDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                               DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getPhonebill();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.PHONEBILL;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            if (StringUtils.isBlank(request.getUserId())) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID不能为空")
                        .build();
            }
            String baseUrl = config.getBaseUrl() + config.getAccessPath();
            String url = baseUrl + "?app_id=" + config.getAppId() +
                    "&user_code=" + request.getUserId();
            return DistAccessResponse.builder()
                    .success(true)
                    .redirectUrl(url)
                    .build();
        } catch (Exception e) {
            log.error("话费充值免登接入异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("话费充值免登接入异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        // 调用父类的通用实现，使用分隔的日期时间格式（yyyy-MM-dd HH:mm:ss）
        return doPaidNotify(request, DAY_TIME_SPLIT_FORMAT);
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        return DistRefundResultNotifyResponse.builder()
                .success(true)
                .status(SUCCESS_CODE)
                .build();
    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        return null;
    }
}
