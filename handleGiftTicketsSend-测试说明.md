# handleGiftTicketsSend 方法测试说明

## 测试概述

为 `PaymentSuccessCaseServiceImpl.handleGiftTicketsSend` 方法编写了全面的单元测试，覆盖了该方法的所有主要业务逻辑分支和边界情况。

**重要说明**：该方法的实现已经统一使用营销中台接口，所有券类型（包括全球购券）都通过 `TicketPlatformGateway.giveOutTicket` 方法发放，不再使用 `GlobalGateWay`。

## 测试文件位置

```
rxjt-lucky-carp-mall-biz/src/test/java/com/jsrxjt/mobile/biz/payment/service/impl/PaymentSuccessCaseServiceImplTest.java
```

## 测试方法列表

### 1. 成功场景测试

| 测试方法 | 测试场景 | 验证要点 |
|---------|---------|---------|
| `testHandleGiftTicketsSend_Success_OnlyGlobalTickets` | 只有全球购券的发放 | 验证全球购券通过营销中台发放（使用unionId作为userCode） |
| `testHandleGiftTicketsSend_OnlyCouponTickets_Success` | 只有卡管券的发放 | 验证卡管券处理逻辑 |
| `testHandleGiftTicketsSend_MixedTickets_Success` | 混合券类型发放 | 验证多种券类型同时处理 |
| `testHandleGiftTicketsSend_MarketingTickets_Success` | 营销中台券发放成功 | 验证营销中台发券接口调用 |
| `testHandleGiftTicketsSend_AllTicketTypes_Mixed` | 所有券类型混合发放 | 验证完整的业务流程 |

### 2. 失败场景测试

| 测试方法 | 测试场景 | 验证要点 |
|---------|---------|---------|
| `testHandleGiftTicketsSend_NoCacheData` | 缓存数据不存在 | 验证早期返回逻辑 |
| `testHandleGiftTicketsSend_LockFailed` | 分布式锁获取失败 | 验证锁机制和资源清理 |
| `testHandleGiftTicketsSend_OrderNotFound` | 订单不存在 | 验证订单校验逻辑 |
| `testHandleGiftTicketsSend_MarketingTickets_Failed` | 营销中台发券失败 | 验证发券失败处理 |
| `testHandleGiftTicketsSend_TicketPlatform_Failed` | 营销中台发券失败 | 验证营销中台发券失败处理 |

### 3. 边界情况测试

| 测试方法 | 测试场景 | 验证要点 |
|---------|---------|---------|
| `testHandleGiftTicketsSend_EmptyGiftTickets` | 赠券信息为空 | 验证空数据处理 |
| `testHandleGiftTicketsSend_InvalidGiftTicketJson` | 无效JSON格式 | 验证JSON解析异常处理 |
| `testHandleGiftTicketsSend_NullGiftTicketJson` | JSON解析为null | 验证null值处理 |
| `testHandleGiftTicketsSend_CustomerNotFound` | 客户不存在 | 验证客户校验逻辑 |
| `testHandleGiftTicketsSend_CustomerWithoutUnionId` | 客户无unionId | 验证unionId校验逻辑（影响全球购券userCode） |
| `testHandleGiftTicketsSend_MarketingTickets_Exception` | 营销中台异常 | 验证异常处理机制 |

## 测试数据构造方法

### 券类型说明
- **类型1**：全球购券 - 通过TicketPlatformGateway发放，使用unionId作为userCode
- **类型2**：瑞祥代发券 - 通过TicketPlatformGateway发放，使用customerId作为userCode，需要notifyUrl
- **类型3**：商家自发券 - 通过TicketPlatformGateway发放，使用customerId作为userCode，需要notifyUrl
- **类型4**：营销中台券 - 通过TicketPlatformGateway发放，使用customerId作为userCode

### 数据构造方法
- `createGlobalTicketInfos()` - 创建全球购券测试数据
- `createCouponTicketInfos()` - 创建卡管券测试数据
- `createMixedTicketInfos()` - 创建混合券类型测试数据
- `createMarketingTicketInfos()` - 创建营销中台券测试数据
- `createAllTypesTicketInfos()` - 创建包含所有券类型的测试数据

## Mock配置

### 主要Mock对象
- `OrderRepository` - 订单数据访问
- `DistributedLock` - 分布式锁
- `RedisUtil` - Redis缓存操作
- `GiftTicketOrderRepository` - 赠券订单数据访问
- `CustomerRepository` - 客户数据访问
- `TicketPlatformConfig` - 营销中台配置
- `TicketPlatformGateway` - 营销中台发券网关（统一发券接口）

## 运行测试

### 运行单个测试类
```bash
mvn test -Dtest=PaymentSuccessCaseServiceImplTest -f rxjt-lucky-carp-mall-biz/pom.xml
```

### 运行特定测试方法
```bash
mvn test -Dtest=PaymentSuccessCaseServiceImplTest#testHandleGiftTicketsSend_Success_OnlyGlobalTickets -f rxjt-lucky-carp-mall-biz/pom.xml
```

## 测试覆盖率

该测试套件覆盖了 `handleGiftTicketsSend` 方法的以下执行路径：
- ✅ 缓存数据获取和验证
- ✅ 分布式锁获取和释放
- ✅ 订单信息查询和验证
- ✅ 赠券信息解析和处理
- ✅ 统一营销中台发券逻辑（所有券类型）
- ✅ 不同券类型的userCode处理逻辑
- ✅ 客户信息查询和验证
- ✅ 异常处理和资源清理
- ✅ 缓存清理逻辑

## 注意事项

1. **业务逻辑变更**：测试代码已根据最新的业务实现进行修正，统一使用营销中台接口
2. **依赖注入**：确保所有Mock对象正确注入到被测试类中
3. **异常处理**：测试中包含了对各种异常情况的验证
4. **资源清理**：每个测试都验证了分布式锁的正确释放
5. **用户标识**：注意不同券类型使用不同的userCode（unionId vs customerId）
