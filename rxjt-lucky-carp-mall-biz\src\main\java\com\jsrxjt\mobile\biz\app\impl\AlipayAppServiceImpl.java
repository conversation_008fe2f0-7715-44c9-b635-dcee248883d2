package com.jsrxjt.mobile.biz.app.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.app.request.AlipayAppRequest;
import com.jsrxjt.mobile.api.app.request.BianlfAppRequest;
import com.jsrxjt.mobile.api.app.response.*;
import com.jsrxjt.mobile.api.enums.AppTypeEnum;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.app.AlipayAppService;
import com.jsrxjt.mobile.biz.order.AutoRefundCaseService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.alipay.gateway.AlipayVoucherGateway;
import com.jsrxjt.mobile.domain.alipay.request.AlipayVoucherSendRequest;
import com.jsrxjt.mobile.domain.alipay.response.AlipayVoucherSendResponse;
import com.jsrxjt.mobile.domain.app.entity.AlipayTabCategoryEntity;
import com.jsrxjt.mobile.domain.app.entity.AppCouponExplainEntity;
import com.jsrxjt.mobile.domain.app.entity.AppCouponGoodsEntity;
import com.jsrxjt.mobile.domain.app.entity.AppCouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.app.repository.AlipayTabCategoryRepository;
import com.jsrxjt.mobile.domain.app.repository.AppCouponGoodsRepository;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderDeliveryEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;
import com.jsrxjt.mobile.domain.product.entity.SpuLimitStrategyEntity;
import com.jsrxjt.mobile.domain.product.entity.SpuLimitStrategyRegionEntity;
import com.jsrxjt.mobile.domain.product.repository.SpuLimitStrategyRegionRepository;
import com.jsrxjt.mobile.domain.product.repository.SpuLimitStrategyRepository;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.domain.product.service.ProductSpuBaseInfoService;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AlipayAppServiceImpl implements AlipayAppService {

    private final AlipayTabCategoryRepository alipayTabCategoryRepository;

    private final ProductSkuSellRegionService productSkuSellRegionService;

    private final AppCouponGoodsRepository appCouponGoodsRepository;

    private final SpuLimitStrategyRepository spuLimitStrategyRepository;

    private final SpuLimitStrategyRegionRepository spuLimitStrategyRegionRepository;

    private final CustomerRepository customerRepository;

    private final OrderCaseService orderCaseService;

    private final OrderRepository orderRepository;

    private final OrderDeliveryRepository orderDeliveryRepository;

    private final AutoRefundCaseService autoRefundCaseService;

    private final RegionRepository regionRepository;

    private final AlipayVoucherGateway alipayVoucherGateway;

    private final ProductSpuBaseInfoService productSpuBaseInfoService;

    /**
     * 获取支付宝红包分类
     *
     * @return
     */
    @Override
    public List<AlipayAppCatResponse> getAlipayAppCats(Integer regionId) {
        List<AlipayTabCategoryEntity> usedTabCategory = alipayTabCategoryRepository.getUsedTabCategory();
        if (CollectionUtils.isEmpty(usedTabCategory)) {
            return Collections.emptyList();
        }
        // 批量获取所有相关的活动商品信息
        List<Long> tabCatIds = usedTabCategory.stream().map(AlipayTabCategoryEntity::getId).toList();
        List<AppCouponGoodsEntity> allActivity = appCouponGoodsRepository.findAlipayActivityByTabCatId(tabCatIds);
        Map<Long, List<AppCouponGoodsEntity>> groupedByCatId = allActivity.stream().collect(Collectors.groupingBy(AppCouponGoodsEntity::getAlipayTabCatId));

        List<AlipayAppCatResponse> responseList = new ArrayList<>();
        for (AlipayTabCategoryEntity entity : usedTabCategory) {
            List<AppCouponGoodsEntity> list = groupedByCatId.getOrDefault(entity.getId(), Collections.emptyList());

            List<ProductSpuBaseInfo> products = list.stream().map(appCouponGoodsEntity -> {
                ProductSpuBaseInfo productSpuBaseInfo = new ProductSpuBaseInfo();
                productSpuBaseInfo.setSpuId(String.valueOf(appCouponGoodsEntity.getAppSpuId()));
                productSpuBaseInfo.setProductType(ProductTypeEnum.COUPON_APP.getType());
                productSpuBaseInfo.setFlatProductType(FlatProductTypeEnum.ALIPAY_RED_PACKET.getType());
                return productSpuBaseInfo;
            }).collect(Collectors.toList());
            List<ProductSpuBaseInfo> filterList = productSpuBaseInfoService.filterByRegion(regionId, products);

            if (CollectionUtils.isNotEmpty(filterList)) {
                AlipayAppCatResponse alipayAppCatResponse = new AlipayAppCatResponse();
                BeanUtils.copyProperties(entity, alipayAppCatResponse);
                responseList.add(alipayAppCatResponse);
            }
        }
        return responseList;
    }

    /**
     * 获取支付宝红包详情信息
     *
     * @param request
     * @return
     */
    @Override
    public AlipayAppResponse getAlipayAppInfo(AlipayAppRequest request) {
        AlipayAppResponse response = new AlipayAppResponse();
        Byte isSelect = request.getIsSelect();
        Integer regionId = request.getRegionId();
        Long alipayTabCatId = request.getAlipayTabCatId();
        Long appSpuId = request.getAppSpuId();
        if (isSelect == 0 && alipayTabCatId == null) {
            log.error("alipayTabCatId is null");
            return null;
        }
        if (isSelect == 1 && appSpuId != null) {
            AppCouponGoodsEntity appCouponGoodsEntity = appCouponGoodsRepository.getAppCouponGoodsInfo(request.getAppSpuId());
            alipayTabCatId = appCouponGoodsEntity.getAlipayTabCatId();
        }
        AlipayTabCategoryEntity alipayTabCategoryEntity = alipayTabCategoryRepository.getTabCategoryDetail(alipayTabCatId);
        response.setAlipayTabCatId(alipayTabCatId);
        response.setAttribute(alipayTabCategoryEntity.getAttribute());
        List<AppCouponGoodsEntity> appCouponGoodsListByType = appCouponGoodsRepository.getAppCouponGoodsListByTypeAndCatId(2, alipayTabCatId);
        if (CollectionUtil.isEmpty(appCouponGoodsListByType)) {
            log.error("appCouponGoodsListByType is empty");
            return null;
        }
        List<AppCouponGoodsEntity> newAppCouponGoodsList = new ArrayList<>();
        for (AppCouponGoodsEntity entity : appCouponGoodsListByType) {
            boolean sellableInRegion = productSkuSellRegionService.isSellableInRegion(entity.getAppSpuId(), null, ProductTypeEnum.COUPON_APP, regionId);
            if (sellableInRegion) {
                newAppCouponGoodsList.add(entity);
            }
        }
        if (CollectionUtil.isEmpty(newAppCouponGoodsList)) {
            log.error("newAppCouponGoodsList is empty");
            return null;
        }
        //初始化 未选中
        if (isSelect == 0) {
            appSpuId = newAppCouponGoodsList.get(0).getAppSpuId();
        }
        List<AlipayAppSpuResponse> alipayAppSpuResponseList = BeanUtil.copyToList(newAppCouponGoodsList, AlipayAppSpuResponse.class);
        for (AlipayAppSpuResponse spuNameResponse : alipayAppSpuResponseList) {
            if (spuNameResponse.getAppSpuId().equals(appSpuId)) {
                //如果选中
                if (isSelect == 1) {
                    spuNameResponse.setIsSpuSelect((byte) 1);
                }
            }
            //只放入选中的说明
            AppCouponGoodsEntity appCouponGoodsEntity = appCouponGoodsRepository.getAppCouponGoodsInfo(spuNameResponse.getAppSpuId());
            List<AppCouponExplainEntity> explainEntityList = appCouponGoodsEntity.getExplainEntityList();
            spuNameResponse.setExplainEntityList(BeanUtil.copyToList(explainEntityList, AlipayAppExplainResponse.class));
            spuNameResponse.setSubscriptUrl(appCouponGoodsEntity.getSubscriptUrl());
        }

        //获取sku信息
        List<AppCouponGoodsSkuEntity> appCouponGoodsSkuListBySpuId = appCouponGoodsRepository.getAppCouponGoodsSkuListBySpuId(appSpuId);
        List<AlipayAppSkuResponse> alipayAppInfoResponseList = BeanUtil.copyToList(appCouponGoodsSkuListBySpuId, AlipayAppSkuResponse.class);
        for (AlipayAppSkuResponse alipayAppSkuResponse : alipayAppInfoResponseList) {
            if (request.getAppSkuId() != null && alipayAppSkuResponse.getAppSkuId().equals(request.getAppSkuId())) {
                //如果选中
                if (isSelect == 1) {
                    alipayAppSkuResponse.setIsSkuSelect((byte) 1);
                }
            }
        }
        //获取多元使用场景
        SpuLimitStrategyRegionEntity byProductAndRegion = spuLimitStrategyRegionRepository.findByProductAndRegion(appSpuId, ProductTypeEnum.COUPON_APP.getType(), regionId);
        if (byProductAndRegion != null) {
            SpuLimitStrategyEntity limitStrategy = spuLimitStrategyRepository
                    .findByStrategyId(byProductAndRegion.getLimitStrategyId(),
                            ProductTypeEnum.COUPON_APP.getType(), appSpuId);
            if (limitStrategy != null) {
                response.setDiverseUsageScene(limitStrategy.getDiverseUsageScene());
            }
        }
        response.setAlipayAppSkuResponseList(alipayAppInfoResponseList);
        response.setAlipayAppSpuResponseList(alipayAppSpuResponseList);
        return response;
    }

    @Override
    public BaseResponse<BianlfAppResponse> getBianlfAppInfo(BianlfAppRequest request) {
        BianlfAppResponse response = new BianlfAppResponse();
        AppCouponGoodsEntity appCouponGoodsEntity = appCouponGoodsRepository.getAppCouponGoodsInfo(request.getAppSpuId());
        if (Objects.isNull(appCouponGoodsEntity)) {
            log.error("没有查询到产品：{}", request.getAppSkuId());
            return BaseResponse.fail("产品已下架。");
        }
        if (appCouponGoodsEntity.getAppType() != AppTypeEnum.BIANLF_RECHARGE.getChannel()) {
            log.error("参数AppSpuId：{}错误，非便利蜂id", request.getAppSkuId());
            return BaseResponse.fail("请求失败，联系客服。");
        }
        BeanUtil.copyProperties(appCouponGoodsEntity, response);
        if (CollectionUtil.isNotEmpty(appCouponGoodsEntity.getExplainEntityList())) {
            List<AlipayAppExplainResponse> explainEntityList = BeanUtil.copyToList(appCouponGoodsEntity.getExplainEntityList(), AlipayAppExplainResponse.class);
            response.setExplainEntityList(explainEntityList);
        }

        //获取sku信息
        List<AppCouponGoodsSkuEntity> appCouponGoodsSkuList = appCouponGoodsRepository.getAppCouponGoodsSkuListBySpuId(request.getAppSpuId());
        if (CollectionUtil.isNotEmpty(appCouponGoodsSkuList)) {
            List<BianlfAppSkuResponse> skuList = BeanUtil.copyToList(appCouponGoodsSkuList, BianlfAppSkuResponse.class);
            response.setSkuList(skuList);
            if (Objects.isNull(request.getAppSkuId())) {
                skuList.get(0).setIsSkuSelect(1);
            } else {
                for (BianlfAppSkuResponse item : skuList) {
                    if (item.getAppSkuId().longValue() == request.getAppSkuId().longValue()) {
                        item.setIsSkuSelect(1);
                        break;
                    }
                }
            }
        }
        return BaseResponse.succeed(response);
    }

    @Override
    public String getLastRechargeAccount(Long customerId) {
        return orderRepository.getLastRechargeAccount(customerId, OrderChannelEnum.ALIPAY_RED_ENVELOPE);
    }

    @Override
    public void processDeliveringAlipayVoucherOrder(List<OrderInfoEntity> list) {
        for (OrderInfoEntity order : list){
            log.info("支付宝订单重试开始，订单号：{}", order.getOrderNo());
            AlipayVoucherSendResponse alipayVoucherSendResponse = pushAlipayVoucherOrder(order);
            if (alipayVoucherSendResponse == null ){
                log.info("支付宝红包发放失败，订单号：{}", order.getOrderNo());
                updateOrderToRechargeFailed(order);
            } else {
                if (StringUtils.isNotEmpty(alipayVoucherSendResponse.getOutVoucherId())){
                    log.info("支付宝红包发放成功，订单号：{}，外部订单号：{}", order.getOrderNo(), alipayVoucherSendResponse.getOutOrderId());
                    updateOrderToDelivered(order, alipayVoucherSendResponse.getOutOrderId());
                } else{
                    if (alipayVoucherSendResponse.getNeedRetry()){
                        log.info("支付宝红包发放失败，需要联系支付宝技术处理，订单号：{}", order.getOrderNo());
                        updateOrderToRechargeFailed(order);
                    } else {
                        updateOrderToRechargeFailed(order);
                        createAfterSaleForRechargeFailed(order);
                    }
                }
            }
            saveDeliveryInfo(order, alipayVoucherSendResponse);
        }
    }

    /**
     * 支付宝下单发券
     */
    private AlipayVoucherSendResponse pushAlipayVoucherOrder(OrderInfoEntity order) {
        AlipayVoucherSendRequest alipayVoucherSendRequest = new AlipayVoucherSendRequest();
        alipayVoucherSendRequest.setOrderNo(order.getOrderNo());
        alipayVoucherSendRequest.setOutActivityId(order.getOrderItems().get(0).getOutGoodsId());
        alipayVoucherSendRequest.setLogonId(order.getRechargeAccount());
        return alipayVoucherGateway.sendAlipayVoucher(alipayVoucherSendRequest);
    }

    /**
     * 更新订单状态为发货失败
     */
    private void updateOrderToRechargeFailed(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);
        log.info("订单发货状态更新为发货失败，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERY_FAILED.getDescription());
    }

    /**
     * 更新订单状态为交易完成  发货状态为已发货
     */
    private void updateOrderToDelivered(OrderInfoEntity order, String externalOrderNo) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setExternalOrderNo(externalOrderNo);
        updateOrder.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        updateOrder.setDeliveryTime(LocalDateTime.now());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);
        log.info("支付宝红包订单发货状态更新成功，订单号：{}，发货状态：{}，外部订单号：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERED.getDescription(), externalOrderNo);
    }

    /**
     * 更新发货信息表
     */
    private void saveDeliveryInfo(OrderInfoEntity orderInfo,AlipayVoucherSendResponse alipayVoucherSendResponse) {
        log.info("开始保存支付宝红包发货信息，订单号：{}", orderInfo.getOrderNo());

        List<OrderDeliveryEntity> list = orderDeliveryRepository.findByOrderNo(orderInfo.getOrderNo());
        boolean isUpdate = CollectionUtil.isNotEmpty(list);
        OrderDeliveryEntity deliveryEntity = isUpdate ? list.get(0) : new OrderDeliveryEntity();
        deliveryEntity.setDeliveryRemark("定时任务重试");
        if (!isUpdate) {
            // 订单项记录
            OrderItemEntity orderItem = orderInfo.getOrderItems().get(0);
            // 基本订单信息
            deliveryEntity.setOrderId(orderInfo.getId());
            deliveryEntity.setOrderNo(orderInfo.getOrderNo());
            deliveryEntity.setOrderItemId(orderItem.getId());
            deliveryEntity.setMiniSkuId(orderItem.getProductSkuId());
            deliveryEntity.setCustomerId(orderInfo.getCustomerId());
            deliveryEntity.setDeliveryType(3);
            deliveryEntity.setDeliveryTime(LocalDateTime.now());
            // 收货信息（从订单信息复制）
            deliveryEntity.setReceiverName(orderInfo.getReceiverName());
            deliveryEntity.setReceiverMobile(orderInfo.getReceiverMobile());
            deliveryEntity.setReceiverAddress(orderInfo.getReceiverAddress());
            deliveryEntity.setRechargeAccount(orderInfo.getRechargeAccount());
            // 发货备注
            deliveryEntity.setDeliveryRemark("定时任务重试");
        }
        // 发货信息
        if (alipayVoucherSendResponse != null){
            if (alipayVoucherSendResponse.getOutOrderId() != null){
                deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
                deliveryEntity.setCouponCode(alipayVoucherSendResponse.getOutVoucherId());
            } else {
                deliveryEntity.setDeliveryErrorCode(alipayVoucherSendResponse.getSubCode());
                deliveryEntity.setDeliveryErrorMsg(alipayVoucherSendResponse.getSubMsg());
                deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
            }
        } else {
            deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
            deliveryEntity.setDeliveryErrorMsg("接口请求异常");
        }
        try {
            if (isUpdate) {
                orderDeliveryRepository.updateOrderDelivery(deliveryEntity);
            } else {
                orderDeliveryRepository.saveOrderDelivery(deliveryEntity);
            }
            log.info("支付宝红包发货信息保存成功，订单号：{}", orderInfo.getOrderNo());
        } catch (Exception e) {
            log.error("支付宝红包发货信息保存失败，订单号：{}，错误信息：{}", orderInfo.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 发放失败时自动创建售后单并退款
     *
     * @param orderInfo 订单信息
     */
    private void createAfterSaleForRechargeFailed(OrderInfoEntity orderInfo) {
        log.info("开始为支付宝红包发放失败订单创建售后单并自动退款，订单号：{}", orderInfo.getOrderNo());
        AutoRefundRequestDTO autoRefundRequestDTO = new AutoRefundRequestDTO();
        autoRefundRequestDTO.setOrderNo(orderInfo.getOrderNo());
        autoRefundRequestDTO.setApplyRefundAmount(orderInfo.getPaymentAmount());
        autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode());
        AfterSaleEntity afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
        log.info ("支付宝红包发放失败售后单退款成功，订单号：{}，售后单号：{}", orderInfo.getOrderNo(), afterSaleEntity.getAfterSaleNo());
    }
}
