package com.jsrxjt.mobile.biz.account.impl;

import com.jsrxjt.mobile.api.account.dto.request.AccountTransferDTO;
import com.jsrxjt.mobile.api.dp.*;
import com.jsrxjt.mobile.biz.account.TransferCaseService;
import com.jsrxjt.mobile.domain.account.entity.Account;
import com.jsrxjt.mobile.domain.account.gateway.ExchangeRateGateway;
import com.jsrxjt.mobile.domain.account.messaging.AuditMessageProducer;
import com.jsrxjt.mobile.domain.account.repository.AccountRepository;
import com.jsrxjt.mobile.domain.account.service.AccountTransferService;
import com.jsrxjt.mobile.domain.account.types.AuditMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransferCaseServiceImpI implements TransferCaseService {

    private final AccountRepository accountRepository;
    private final AuditMessageProducer auditMessageProducer;
    private final ExchangeRateGateway exchangeRateGateway;
    private final AccountTransferService accountTransferService;

    @Override
    public Boolean transfer(AccountTransferDTO accountTransferDTO) {
        log.info("[入口参数]转账业务编排接口,入参:accountTransferDTO {}",accountTransferDTO);
        Long sourceUserId = accountTransferDTO.getSourceUserId();
        String targetAccountNumber = accountTransferDTO.getTargetAccountNumber();
        BigDecimal targetAmount = accountTransferDTO.getTargetAmount();
        String targetCurrency = accountTransferDTO.getTargetCurrency();

        // 构建DP
        Money targetMoney = new Money(targetAmount, new Currency(targetCurrency));

        // 读数据
        Account sourceAccount = accountRepository.find(new UserId(sourceUserId));
        Account targetAccount = accountRepository.find(new AccountNumber(targetAccountNumber));
        log.info("[第三方服务调用]汇率查询，请求参数 源账户币种={}，目标账户币种={}",sourceAccount.getCurrency(),targetMoney.getCurrency());
        ExchangeRate exchangeRate = exchangeRateGateway.getExchangeRate(sourceAccount.getCurrency(), targetMoney.getCurrency());
        log.info("[第三方服务调用]汇率查询，返回结果exchangeRate={}",exchangeRate);
        // 业务逻辑
        log.info("[业务逻辑]转账业务接口,入参:sourceAccount={},targetAccount={},targetMoney={},exchangeRate={}",sourceAccount,targetAccount,targetMoney,exchangeRate);
        accountTransferService.transfer(sourceAccount, targetAccount, targetMoney, exchangeRate);

        // 保存数据
        accountRepository.save(sourceAccount);
        accountRepository.save(targetAccount);

        // 发送审计消息
        AuditMessage message = new AuditMessage(sourceAccount, targetAccount, targetMoney);
        auditMessageProducer.send(message);

        return true;
    }
}
