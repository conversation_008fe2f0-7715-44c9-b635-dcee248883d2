package com.jsrxjt.mobile.api.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * 分页参数
 * @since 2024/4/18
 **/

@Getter
@Builder
@Setter
@Schema(title = "分页信息")
public class PageDTO <T> {
    @Schema(description = "当前页数据")
    private List<T> records;

    @Schema(title = "总数目")
    private Long total;
    @Schema(title = "每页数量")
    private Long  size;
    @Schema(title = "总页数")
    private Long  pages;
    @Schema(title = "当前页")
    private Long  current;

    public static <T> PageDTO<T> emptyBuild(Long  current,Long size){
        return PageDTO.<T>builder()
                .records(List.of())
                .total(0L)
                .size(size)
                .pages(0L)
                .current(current)
                .build();
    }

    public static <T> PageDTO<T> build(List<T> records,Long  total,Long  size,Long  current){
        return PageDTO.<T>builder()
                .records(records)
                .total(total)
                .size(size)
                .pages(total % size == 0 ? total / size : total / size + 1)
                .current(current)
                .build();
    }

    public static <T> PageDTO<T> build(List<T> records,Long  total,Long  size,Long  current,Long  pages){
        return PageDTO.<T>builder()
                .records(records)
                .total(total)
                .size(size)
                .pages(pages)
                .current(current)
                .build();
    }

}
