package com.jsrxjt.mobile.biz.order;

import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.order.dto.request.GiftTicketRequestDTO;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketInfo;

import java.util.List;

/**
 * 赠送券校验服务
 * 
 * <AUTHOR>
 * @since 2025/1/20
 */
public interface GiftTicketValidationService {

    /**
     * 校验和处理赠送券信息
     * 
     * @param productItemId 产品信息标识
     * @param giftTickets 赠送券请求列表
     * @param quantity 购买数量
     * @return 赠送券信息
     */
    List<GiftTicketInfo> validateAndProcessGiftTickets(ProductItemId productItemId,
                                                       List<GiftTicketRequestDTO> giftTickets,
                                                       Integer quantity);
}