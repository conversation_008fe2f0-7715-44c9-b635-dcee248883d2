package com.jsrxjt.mobile.domain.ticket.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 优惠券
 * @Author: ywt
 * @Date: 2025-08-15 15:09
 * @Version: 1.0
 */
@Data
public class TicketEntity {
    private Long ticketId;
    private String ticketName;//优惠券名称
    private Integer ticketType;//优惠券类型：1全球购线上商城优惠券 2商家自发优惠券  3瑞祥代发优惠券 4门店优惠券
    private Long brandId;//优惠券品牌id
    private String brandName;//营销中台的品牌名
    private String centerCouponId;//卡管/商城coupon_id
    /**
     * 营销中台的优惠券id
     */
    private String centerTicketId;
    private Integer ticketCatCode;//优惠券类型 1满减券 2无门槛券
    private BigDecimal discountAmount;//优惠金额 (ticket_type值为3或4存在此值)
    private BigDecimal thresholdAmount;//满减券优惠门槛金额 (ticket_type值为3或4存在此值)
    private Integer ticketValidDate;//自发券后有效期日
    private String specPicUrl;//规格图片url
    private String offsetLogo;//核销页的logo
    private String useManual;//使用说明
    private Integer offsetPageType;//核销页样式 0卡号 1转码
    private Integer status;//状态 0禁用 1启用
    private Integer centerStatus;//营销中台的状态，1开启 0停用
    private Integer manualDown;//是否手动下架卡券(0:否 1:是)
    private Integer isNationwide;//上线城市：0非全国  1全国
}
