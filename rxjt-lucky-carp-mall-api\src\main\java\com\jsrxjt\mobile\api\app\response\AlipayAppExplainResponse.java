package com.jsrxjt.mobile.api.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 支付宝话费说明表
 */
@Getter
@Setter
@Schema( description = "支付宝说明表")
public class AlipayAppExplainResponse {

    @Schema(description = "说明类型 1:适用范围 2:充值使用说明 3:温馨提示（下单确认提示） 4兑换须知 5跑马灯内容")
    private Integer explainType;

    @Schema(description = "说明内容富文本")
    private String content;


}
