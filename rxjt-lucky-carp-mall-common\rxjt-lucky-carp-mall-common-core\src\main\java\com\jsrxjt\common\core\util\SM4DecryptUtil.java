package com.jsrxjt.common.core.util;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.HexFormat;

/**
 * 扫码结果解密工具
 * <AUTHOR>
 * @since 2025/12/8
 */
@Slf4j
public class SM4DecryptUtil {

    static {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    // 密钥、IV
    private static final byte[] KEY = "3B986B2E6E6926DA".getBytes(StandardCharsets.UTF_8);
    private static final byte[] IV = "8A3D2F784F8F2BF5".getBytes(StandardCharsets.UTF_8);

    public static String decryptSM4CFB(String encryptedHex) throws Exception {

        // 分离CVV密文和全文密文
        String cvvCipherHex = encryptedHex.substring(0, 6);
        String fullCipherHex = encryptedHex.substring(6);

        byte[] cvvCipher = hexStringToByteArray(cvvCipherHex);
        String cvv = decrypt(cvvCipher, KEY, IV);
        log.info("解密CVV: {}", cvv);

        byte[] fullCipher = hexStringToByteArray(fullCipherHex);
        String fullText = decrypt(fullCipher, KEY, IV);
        log.info("解密全文: {}", fullText);

        String[] parts = fullText.split("\\|");
        if (parts.length != 3) {
            throw new IllegalArgumentException("格式错误，期望: 卡号|密码|CVV");
        }

        if (!parts[2].equals(cvv)) {
            throw new IllegalArgumentException("CVV验证失败");
        }
        return fullText;
    }

    private static String decrypt(byte[] ciphertext, byte[] key, byte[] iv) throws Exception {
        try {
            // 尝试不同的算法名称（Bouncy Castle可能有多种命名）
            String[] algorithms = {
                    "SM4/CFB/NoPadding",      // 标准命名，优先尝试
                    "SM4/CFB/NOPADDING",      // 全大写变体
                    "SM4/CFB64/NoPadding",    // 64位CFB模式
                    "SM4/CFB128/NoPadding"    // 128位CFB模式
            };

            Exception lastException = null;
            for (String algorithm : algorithms) {
                try {
                    log.info("尝试算法: {}", algorithm);
                    return decryptWithAlgorithm(ciphertext, key, iv, algorithm);
                } catch (Exception e) {
                    lastException = e;
                    log.info("算法 {} 失败: {}", algorithm, e.getMessage());
                }
            }
            throw new RuntimeException("所有算法尝试失败", lastException);
        } catch (Exception e) {
            throw new Exception("解密失败: " + e.getMessage(), e);
        }
    }

    private static String decryptWithAlgorithm(byte[] ciphertext, byte[] key, byte[] iv, String algorithm)
            throws Exception {

        // 创建密钥和IV
        SecretKeySpec secretKey = new SecretKeySpec(key, "SM4");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        // 获取Cipher实例
        Cipher cipher;
        try {
            // 先尝试用BC provider
            cipher = Cipher.getInstance(algorithm, "BC");
        } catch (Exception e) {
            // 再尝试不用指定provider
            cipher = Cipher.getInstance(algorithm);
        }

        // 初始化并解密
        cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
        byte[] decrypted = cipher.doFinal(ciphertext);

        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * 16进制字符串转字节数组
     */
    private static byte[] hexStringToByteArray(String hex) {
        HexFormat hexFormat = HexFormat.of();
        return hexFormat.parseHex(hex);
    }

    public static void main(String[] args) {
        try {
            // 示例加密数据
            String encryptedData = "41be4c4bb84593b40fd97d37b4dbe16be87a2c416b6b29bd175c4ee82679ee22c2";
            // 解密
            String result = decryptSM4CFB(encryptedData);
            System.out.println("解密成功: " + result);
        } catch (Exception e) {
            System.err.println("解密失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
