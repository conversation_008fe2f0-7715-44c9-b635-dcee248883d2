package com.jsrxjt.mobile.infra.order.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.TradeRefundInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交易退款信息表Mapper
 * <AUTHOR>
 * @since 2025/9/26
 */
@Mapper
public interface TradeRefundInfoMapper extends CommonBaseMapper<TradeRefundInfoPO> {

    /**
     * 根据外部退款单号查询退款信息列表
     * 
     * @param outRefundNo 外部退款单号
     * @return 退款信息列表
     */
    List<TradeRefundInfoPO> selectByOutRefundNo(@Param("outRefundNo") String outRefundNo);
}
