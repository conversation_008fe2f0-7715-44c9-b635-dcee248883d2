package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 售后详情查询请求参数
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@Setter
@Schema(description = "售后详情查询请求参数")
public class AfterSaleDetailRequestDTO extends BaseParam {
    
    @Schema(description = "售后单号")
    @NotBlank(message = "售后单号不能为空")
    private String afterSaleNo;
}