package com.jsrxjt.mobile.domain.ticket.repository;

import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;

import java.util.List;
import java.util.Map;

/**
 * @Description: 优惠券
 * @Author: ywt
 * @Date: 2025-08-15 15:15
 * @Version: 1.0
 */
public interface TicketRepository {
    List<TicketEntity> getTicketsByIds(List<Long> ids);

    /**
     * 根据ticketId查询单个优惠券
     * 
     * @param ticketId 优惠券ID
     * @return 优惠券实体
     */
    TicketEntity getTicketById(Long ticketId);

    /**
     * 根据ticketId查询单个优惠券，删除的优惠券也会返回
     *
     * @param ticketId 优惠券ID
     * @return 优惠券实体
     */
    TicketEntity getTicketIncludeDelById(Long ticketId);

    /**
     * 根据centerTicketId查询单个优惠券
     *
     * @param centerTicketId 营销中台的优惠券ID
     * @return 优惠券实体
     */
    List<TicketEntity> getTicketByCenterId(String centerTicketId);
    /**
     * 跟随营销中台更新优惠券状态
     * 参数status: 0暂停使用 1正常使用 2删除卡券
     */
    Integer updateTicketStatusByPlateform(Long ticketId, Integer status);
    /**
     * 对营销中台的请求参数进行签名，并返回结果
     */
    String getPlateformSign(Map<String, Object> requestMap);
}
