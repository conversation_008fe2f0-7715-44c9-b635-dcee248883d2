package com.jsrxjt.mobile.domain.ticket.repository;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.ticket.entity.TicketDeliveryEntity;

import java.util.List;

/**
 * @Description: 优惠券发放
 */
public interface TicketDeliveryRepository {
    /**
     * 商家优惠券
     * 获取用户优惠券发放列表
     *
     * @param userId
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageDTO<TicketDeliveryEntity> getMerchantTicketByUserId(Long userId, Long pageNum, Long pageSize);
    /**
     * 门店或全球购商城优惠券
     * 获取用户优惠券发放列表
     * 
     * @param userId
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageDTO<TicketDeliveryEntity> getTicketDeliveryByUserId(Long userId, Integer ticketType, Long pageNum, Long pageSize);

    /**
     * 获取优惠券详情
     * 
     * @param id
     * @return
     */
    TicketDeliveryEntity getTicketDeliveryById(Long coustomerId, Long id);

    /**
     * 删除优惠券发放
     * 
     * @param id
     */
    void delTicketDelivery(Long coustomerId, Long id);

    /**
     * 根据券码获取优惠券详情
     * 
     * @param ticketCode 卡管卡号（优惠券码）
     * @return 券详情
     */
    TicketDeliveryEntity getByTicketCode(String ticketCode);

    /**
     * 根据券码获取优惠券详情
     *
     * @param CenterNumber 营销中台的券码
     * @return 券详情
     */
    List<TicketDeliveryEntity> getByCenterNumber(String CenterNumber);

    /**
     * 原子性更新券状态为已使用
     * 
     * @param ticketDeliveryId 券发放ID
     * @return 更新是否成功
     */
    boolean updateTicketStatusToUsed(Long ticketDeliveryId);

    /**
     * 批量保存优惠券发放记录
     * 
     * @param ticketDeliveries 优惠券发放记录列表
     */
    void batchSave(List<TicketDeliveryEntity> ticketDeliveries);

    /**
     * 获取生日券
     */
    TicketDeliveryEntity getBirthdayTicket(Long customId);

    /**
     * 获取用户未核销优惠券数量
     * @param customerId 客户id
     * @return 优惠券数量
     */
    int countCustomerTicketNum(Long customerId);

}
