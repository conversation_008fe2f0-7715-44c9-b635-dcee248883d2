package com.jsrxjt.mobile.infra.ticket.persistent.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jsrxjt.mobile.domain.ticket.repository.TicketStoreRelationRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketStoreRelationMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketStoreRelationPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;


@Repository
@RequiredArgsConstructor
public class TicketStoreRelationRepositoryImpl implements TicketStoreRelationRepository {

    private final TicketStoreRelationMapper ticketStoreRelationMapper;

    @Override
    public Boolean checkTicketStoreRelation(Long ticketId, String storeNo) {
        LambdaQueryWrapper<TicketStoreRelationPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TicketStoreRelationPO::getTicketId,ticketId);
        queryWrapper.eq(TicketStoreRelationPO::getStoreNo,storeNo);
        queryWrapper.eq(TicketStoreRelationPO::getDelFlag, NumberUtils.INTEGER_ZERO);
        TicketStoreRelationPO ticketStoreRelationPO = ticketStoreRelationMapper.selectOne(queryWrapper);
        return ticketStoreRelationPO != null;
    }
}
