package com.jsrxjt.mobile.api.physicalCardMall.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 实体卡创建订单请求参数
 * @Author: ywt
 * @Date: 2025-10-23 09:26
 * @Version: 1.0
 */
@Data
public class PhysicalCardCreateOrderRequestDTO extends PhysicalCardBaseDTO {
    @Schema(description = "实体卡侧订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "支付金额不能为空")
    private String tradeAmount;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户id不能为空")
    private String userId;

    @Schema(description = "商品总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private String totalPrice;

    @Schema(description = "手续费", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fee;
}
