package com.jsrxjt.mobile.domain.thirdcard.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/11/4
 */
@Data
public class CardTradeQueryResponse {

    /**
     * 业务结果
     */
    @JSONField(name = "result_code")
    private String resultCode;

    /**
     * 返回状态码
     */
    @JSONField(name = "return_code")
    private String returnCode;

    /**
     * 返回信息
     */
    @JSONField(name = "return_msg")
    private String returnMsg;

    /**
     * 总页数
     */
    @JSONField(name = "page_count")
    private Integer pageCount;

    /**
     * 交易记录总数量
     */
    @JSONField(name = "trade_count")
    private Integer tradeCount;

    /**
     * 交易数据集合
     */
    @JSONField(name = "trans_data")
    private List<CardTradeInfoResponse> transData;
}
