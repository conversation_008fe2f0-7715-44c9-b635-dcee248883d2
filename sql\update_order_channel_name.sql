-- 更新t_order表中的channel_name字段
-- 条件：product_type = 2 且 channel_name为null时，根据channel_id去渠道表查询并更新渠道名称

-- ========================================
-- 第一步：检查需要更新的数据情况
-- ========================================

-- 1. 查看当前需要更新的订单数量
SELECT 
    COUNT(*) as total_need_update,
    COUNT(DISTINCT channel_id) as distinct_channel_count
FROM t_order 
WHERE product_type = 2 
  AND channel_name IS NULL 
  AND channel_id IS NOT NULL;

-- 2. 查看各个渠道ID对应的订单数量
SELECT 
    o.channel_id,
    pc.channel_name,
    COUNT(*) as order_count
FROM t_order o
LEFT JOIN product_channel pc ON o.channel_id = pc.id AND pc.del_flag = 0
WHERE o.product_type = 2 
  AND o.channel_name IS NULL 
  AND o.channel_id IS NOT NULL
GROUP BY o.channel_id, pc.channel_name
ORDER BY order_count DESC;

-- 3. 检查是否存在无效的channel_id（在渠道表中不存在或已删除）
SELECT 
    o.channel_id,
    COUNT(*) as order_count,
    CASE 
        WHEN pc.id IS NULL THEN '渠道不存在'
        WHEN pc.del_flag = 1 THEN '渠道已删除'
        ELSE '渠道正常'
    END as channel_status
FROM t_order o
LEFT JOIN product_channel pc ON o.channel_id = pc.id
WHERE o.product_type = 2 
  AND o.channel_name IS NULL 
  AND o.channel_id IS NOT NULL
GROUP BY o.channel_id, pc.id, pc.del_flag
HAVING channel_status != '渠道正常'
ORDER BY order_count DESC;

-- ========================================
-- 第二步：预览将要更新的数据
-- ========================================

-- 预览前10条将要更新的订单记录
SELECT 
    o.id,
    o.order_no,
    o.channel_id,
    o.channel_name as current_channel_name,
    pc.channel_name as new_channel_name,
    o.product_type,
    o.create_time
FROM t_order o
INNER JOIN product_channel pc ON o.channel_id = pc.id AND pc.del_flag = 0
WHERE o.product_type = 2 
  AND o.channel_name IS NULL 
  AND o.channel_id IS NOT NULL
ORDER BY o.create_time DESC
LIMIT 10;

-- ========================================
-- 第三步：执行更新操作
-- ========================================

-- 更新订单表的渠道名称
UPDATE t_order o
INNER JOIN product_channel pc ON o.channel_id = pc.id AND pc.del_flag = 0
SET 
    o.channel_name = pc.channel_name,
    o.mod_time = NOW()
WHERE o.product_type = 2 
  AND o.channel_name IS NULL 
  AND o.channel_id IS NOT NULL;

-- ========================================
-- 第四步：验证更新结果
-- ========================================

-- 1. 检查更新后的结果统计
SELECT 
    '更新完成后统计' as description,
    COUNT(*) as total_orders,
    SUM(CASE WHEN channel_name IS NOT NULL THEN 1 ELSE 0 END) as has_channel_name,
    SUM(CASE WHEN channel_name IS NULL THEN 1 ELSE 0 END) as no_channel_name
FROM t_order 
WHERE product_type = 2;

-- 2. 查看更新后仍然为空的记录（可能是无效的channel_id）
SELECT 
    o.id,
    o.order_no,
    o.channel_id,
    o.channel_name,
    o.create_time,
    CASE 
        WHEN pc.id IS NULL THEN '渠道不存在'
        WHEN pc.del_flag = 1 THEN '渠道已删除'
        ELSE '其他原因'
    END as reason
FROM t_order o
LEFT JOIN product_channel pc ON o.channel_id = pc.id
WHERE o.product_type = 2 
  AND o.channel_name IS NULL 
  AND o.channel_id IS NOT NULL
ORDER BY o.create_time DESC
LIMIT 20;

-- 3. 验证更新成功的记录样例
SELECT 
    o.id,
    o.order_no,
    o.channel_id,
    o.channel_name,
    pc.channel_name as channel_table_name,
    o.mod_time,
    o.create_time
FROM t_order o
INNER JOIN product_channel pc ON o.channel_id = pc.id AND pc.del_flag = 0
WHERE o.product_type = 2 
  AND o.channel_name IS NOT NULL
  AND o.mod_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)  -- 最近1小时更新的记录
ORDER BY o.mod_time DESC
LIMIT 10;

-- ========================================
-- 第五步：异常数据处理建议
-- ========================================

-- 如果存在无效的channel_id，可以考虑以下处理方式：

-- 方式1：将无效channel_id的订单的channel_name设置为默认值
-- UPDATE t_order 
-- SET channel_name = '未知渠道', mod_time = NOW()
-- WHERE product_type = 2 
--   AND channel_name IS NULL 
--   AND channel_id IS NOT NULL
--   AND channel_id NOT IN (SELECT id FROM product_channel WHERE del_flag = 0);

-- 方式2：记录无效channel_id的订单到日志表（如果有的话）
-- INSERT INTO order_error_log (order_id, order_no, error_type, error_message, create_time)
-- SELECT 
--     o.id, 
--     o.order_no, 
--     'INVALID_CHANNEL_ID', 
--     CONCAT('无效的渠道ID: ', o.channel_id), 
--     NOW()
-- FROM t_order o
-- WHERE o.product_type = 2 
--   AND o.channel_name IS NULL 
--   AND o.channel_id IS NOT NULL
--   AND o.channel_id NOT IN (SELECT id FROM product_channel WHERE del_flag = 0);
