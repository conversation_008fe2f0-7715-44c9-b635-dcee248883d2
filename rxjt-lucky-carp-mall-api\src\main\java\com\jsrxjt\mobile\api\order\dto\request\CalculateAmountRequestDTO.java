package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.mobile.api.order.dp.ProductItemId;

import java.math.BigDecimal;

/**
 * 订单计算金额请求参数
 * <AUTHOR>
 * @since 2025/6/12
 **/
public class CalculateAmountRequestDTO {
    
    /**
     * 商品信息
     */
    private ProductItemId productItemId;
    
    /**
     * 已购买金额
     */
    private BigDecimal purchasedAmount;
    
    /**
     * 购买数量
     */
    private Integer buyNum;
    
    /**
     * 区域ID
     */
    private Integer regionId;

}
