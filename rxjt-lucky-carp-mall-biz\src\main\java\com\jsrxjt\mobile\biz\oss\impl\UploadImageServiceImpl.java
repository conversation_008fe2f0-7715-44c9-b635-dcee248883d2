package com.jsrxjt.mobile.biz.oss.impl;

import cn.hutool.core.codec.Base64Decoder;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.biz.oss.UploadImageService;
import com.jsrxjt.mobile.domain.gateway.oss.OssGateway;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 *  UploadAOImpl
 *  上传文件实现类
 * <AUTHOR> <PERSON>
 * 2023/3/20 15:16
 * 
 **/

@Service
public class UploadImageServiceImpl implements UploadImageService {
    private final OssGateway ossGateway;

    public UploadImageServiceImpl(OssGateway ossGateway) {
        this.ossGateway = ossGateway;
    }

    @Override
    public BaseResponse<Object> uploadImage(String imageUrl, byte[] data) {
        String url = ossGateway.upLoadFile(imageUrl, data);
        if ( Objects.isNull(url) ) {
            return BaseResponse.fail(Status.LFailed);
        }
        return BaseResponse.succeed(url);
    }

    @Override
    public BaseResponse<Object> uploadImage(String imageUrl, String base64Data) {
        return uploadImage(imageUrl, Base64Decoder.decode(base64Data));
    }
}
