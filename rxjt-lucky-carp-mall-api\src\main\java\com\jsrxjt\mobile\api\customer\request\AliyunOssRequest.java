package com.jsrxjt.mobile.api.customer.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2025/8/11
 */
@Data
@Schema(description = "上传图片请求参数")
public class AliyunOssRequest {

    @Schema(description = "图片数据")
    @NotBlank(message = "图片数据不能为空")
    private String data;

    @Schema(description = "图片类型jpg/jpeg/png/gif")
    @NotBlank(message = "图片类型不能为空")
    private String fileType;
}
