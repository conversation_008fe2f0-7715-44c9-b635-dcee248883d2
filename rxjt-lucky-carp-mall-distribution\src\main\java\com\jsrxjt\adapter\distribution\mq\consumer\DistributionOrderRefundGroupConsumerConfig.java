package com.jsrxjt.adapter.distribution.mq.consumer;

import lombok.SneakyThrows;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;

/**
 * 分销应用退款通知消息队列消费者配置
 * <AUTHOR>
 * @date 2025-10-13
 */
@Configuration
@RefreshScope
public class DistributionOrderRefundGroupConsumerConfig {

    @SneakyThrows
    @Bean(destroyMethod = "close")
    public PushConsumer paidGroupPushConsumer(ClientConfiguration clientConfiguration,
        DistributionOrderRefundTopicMessageListener distributionOrderRefundTopicMessageListener) {
        FilterExpression filterExpression = new FilterExpression("*", FilterExpressionType.TAG);
        String consumerGroup = "distribution_order_refund_consumer_group";
        String topic = "distribution_order_refund_topic";
        return ClientServiceProvider.loadService().newPushConsumerBuilder()
                .setClientConfiguration(clientConfiguration)
                .setConsumerGroup(consumerGroup)
                .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
                .setMessageListener(distributionOrderRefundTopicMessageListener)
                .build();
    }
}
