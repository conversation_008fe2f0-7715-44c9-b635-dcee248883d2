package com.jsrxjt.adapter.demo;

import com.alibaba.fastjson2.JSON;
import com.jsrxjt.mobile.domain.payment.gateway.OnlinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.PaymentRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.PrePayRequest;
import com.jsrxjt.mobile.domain.payment.gateway.response.PaymentResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.PrePayResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PayGatewayTests {

    @Autowired
    private  OnlinePaymentGateway onlinePaymentGateway;

    @Test
    public  void testPrePayment() {
        PrePayRequest request = new PrePayRequest();
        request.setOrderNo("103778390743667220035");
        request.setSource("WX_MINI");
        PrePayResponse prePayResponse = onlinePaymentGateway.prePayment(request);
        System.out.println("预支付返回结果：" + JSON.toJSONString(prePayResponse));
    }

    @Test
    public  void testPay() {
        PaymentRequest request = new PaymentRequest();
        request.setOrderNo("103794064825617970005");
        request.setPreOrderNo("PRE1954832758253555712");
        PaymentResponse paymentResponse = onlinePaymentGateway.pay(request);
        System.out.println("支付返回结果：" + JSON.toJSONString(paymentResponse));
    }
}
