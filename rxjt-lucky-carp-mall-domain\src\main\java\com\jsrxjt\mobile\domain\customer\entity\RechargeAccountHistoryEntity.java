package com.jsrxjt.mobile.domain.customer.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户历史充值账号领域实体
 * 
 * <AUTHOR>
 * @since 2025/8/5
 */
@Data
public class RechargeAccountHistoryEntity {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 产品SPU ID
     */
    private Long productSpuId;
    
    /**
     * 产品类型
     */
    private Integer productType;
    
    /**
     * 充值类型 0-其他 1-手机号 2-QQ号
     */
    private Integer accountType;
    
    /**
     * 账户
     */
    private String account;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    private LocalDateTime modTime;
}