package com.jsrxjt.adapter.order;

import com.alibaba.fastjson2.JSON;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.biz.payment.service.PaymentSuccessCaseService;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.payment.types.PaymentSuccessMessage;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class PaymentSuccessCaseAggrateTests {
    @Autowired
    private PaymentSuccessCaseService paymentSuccessCaseService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private RedisUtil redisUtil;

    @Test
    public void testHandlePaymentSucceeded() {
        OrderInfoEntity orderInfo = orderRepository.findByOrderNo("109159815702610280005");
        PaymentSuccessMessage event = new PaymentSuccessMessage(orderInfo);

        List<GiftTicketInfo> giftTickets = new ArrayList<>();
        GiftTicketInfo giftTicketInfo = new GiftTicketInfo();
        giftTicketInfo.setTicketId(41L);
        giftTicketInfo.setTicketName("诺心电子券");
        giftTicketInfo.setSpecPicUrl("https://lucky-pic.rxlpzj.com/flq/backend/202511141541472680.gif");
        giftTicketInfo.setTicketType(2);
        giftTicketInfo.setBrandId(13L);
        giftTicketInfo.setCenterCouponId("330");
        giftTicketInfo.setCenterTicketId("5");
        giftTicketInfo.setTicketNum(1);
        giftTickets.add(giftTicketInfo);
        String value = JSON.toJSONString(giftTickets);
        redisUtil.set(RedisKeyConstants.ORDER_GIFT_TICKET_KEY + orderInfo.getOrderNo(), value,true);

        paymentSuccessCaseService.handleGiftTicketsSend(event);
    }
}
