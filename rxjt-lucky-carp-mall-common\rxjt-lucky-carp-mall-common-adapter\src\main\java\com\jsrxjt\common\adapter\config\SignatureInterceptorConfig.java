package com.jsrxjt.common.adapter.config;

import com.jsrxjt.common.adapter.interceptor.SignatureInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 签名拦截器配置
 */
@Slf4j
@Configuration
public class SignatureInterceptorConfig implements WebMvcConfigurer {

    @Bean
    public SignatureInterceptor signatureInterceptor() {
        return new SignatureInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(signatureInterceptor())
                .addPathPatterns("/**")  // 拦截所有请求
                .excludePathPatterns(
                        "/error",
                        "/favicon.ico",
                        "/static/**",
                        "/public/**",
                        "/swagger-ui/**",
                        "/swagger-resources/**",
                        "/v2/api-docs",
                        "/v3/api-docs",
                        "/webjars/**",
                        "/actuator/**"
                );
        
        log.info("签名拦截器已注册");
    }
} 