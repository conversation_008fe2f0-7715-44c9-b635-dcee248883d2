package com.jsrxjt.mobile.infra.ticket.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Description: 优惠券mapper
 * @Author: ywt
 * @Date: 2025-08-15 15:23
 * @Version: 1.0
 */
@Mapper
public interface TicketMapper extends CommonBaseMapper<TicketPO> {
    Integer removeById(Long ticketId);
    TicketPO getTicketIncludeDelById(Long ticketId);
}
