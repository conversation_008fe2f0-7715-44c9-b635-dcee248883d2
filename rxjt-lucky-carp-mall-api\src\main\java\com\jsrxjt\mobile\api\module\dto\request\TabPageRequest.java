package com.jsrxjt.mobile.api.module.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "tab页面详情请求")
public class TabPageRequest extends BaseParam {

    @Schema(description = "组件详情id")
    private Integer moduleDetailId;

    @Schema(description = "组件详情类型,此时不需要moduleDetailId参数,(用于获取全球购,本地生活tab 本地生活:11 全球购:12)")
    private Integer detailType;

    @Schema(description = "区域id")
    @NotNull
    private Integer regionId;

    @Schema(description = "活动页id(0:首页)")
    @NotNull
    private Integer activityId;

    @Schema(description = "纬度")
    private String lat;

    @Schema(description = "经度")
    private String lng;
}
