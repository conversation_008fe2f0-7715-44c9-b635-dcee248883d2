package com.jsrxjt.mobile.biz.homeScanPay.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.PickChannelWssRequestDTO;
import com.jsrxjt.mobile.api.scanPay.types.WssPushTypeEnum;
import com.jsrxjt.mobile.api.scanPay.types.ScanWsTypeEnum;
import com.jsrxjt.mobile.biz.homeScanPay.service.ScanPayWssHandlerFactory;
import com.jsrxjt.mobile.biz.homeScanPay.service.ScanPayWssService;
import com.jsrxjt.mobile.biz.mq.QueueSenderService;
import com.jsrxjt.mobile.biz.mq.constants.DestinationConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;


@RequiredArgsConstructor
@Slf4j
@Service
public class ScanPayWssServiceImpl implements ScanPayWssService {

    private final QueueSenderService queueSenderService;

    private final RedisUtil redisUtil;

    private final ScanPayWssHandlerFactory scanPayWssHandlerFactory;

    @Override
    public void sendMessageToCustomer(Long customerId, String payCode, String message) {
        if (customerId == null || StringUtils.isBlank(payCode) || StringUtils.isBlank(message)){
            log.error("参数错误");
            throw new BizException("参数错误");
        }
        JSONObject jsonObject = new JSONObject()
                .fluentPut("customerId", customerId)
                .fluentPut("payCode", payCode)
                .fluentPut("message", message);
        queueSenderService.send(DestinationConstants.DestinationName.SCAN_PAY_WSS_TOPIC,  jsonObject.toJSONString());
    }


    @Override
    public boolean isConnected(Long customerId, String payCode) {
        if (customerId == null || StringUtils.isBlank(payCode)){
            log.error("参数错误");
            throw new BizException("参数错误");
        }
        String sourceCode = redisUtil.get(RedisKeyConstants.WSS_SCAN_PAY_CODE + payCode);
        if (StringUtils.isEmpty(sourceCode)){
            log.error("未获取到支付码{}对应的随机来源码", payCode);
            return false;
        }
        if (!this.sessionInfoExist(customerId, sourceCode)){
            return false;
        }
        return true;
    }

    @Override
    public BaseResponse pushWssInfo(PickChannelWssRequestDTO requestDTO) {
        Long customerId = requestDTO.getCustomerId();
        String code = requestDTO.getCode();
        JSONObject body = requestDTO.getBody();
        ScanWsTypeEnum messageType = requestDTO.getMessageType();
        log.info("扫码付推送信息：{}",body.toString());
        if (!this.isConnected(customerId, code)){
            throw new BizException("服务繁忙，请稍后再试");
        }
        try {
            BaseResponse response = scanPayWssHandlerFactory.handlePushMessage(WssPushTypeEnum.getByType(body.get("type").toString()), body);
            this.sendMessageToCustomer(customerId, code, JSONObject.toJSONString(response.getResponse()));
        } catch (BizException e){
            log.error("扫码付推送业务异常：{}",e.getMessage());
            throw e;
        } catch (Exception e){
            log.error("扫码付推送信息异常：{}",e.getMessage());
            throw new BizException("系统异常,请稍微再试");
        }
        return BaseResponse.succeed();
    }

    private boolean sessionInfoExist(Long customerId, String sourceCode) {
        return redisUtil.exists(RedisKeyConstants.WSS_SCAN_CUSTOMER.formatted(customerId, sourceCode));
    }
}
