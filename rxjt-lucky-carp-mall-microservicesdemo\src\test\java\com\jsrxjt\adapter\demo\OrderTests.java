package com.jsrxjt.adapter.demo;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.order.dto.request.AfterSaleListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.OrderListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.AfterSaleDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.AfterSaleListResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderListResponseDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.biz.order.AfterSaleCaseService;
import com.jsrxjt.mobile.biz.order.AutoRefundCaseService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.service.CalculateAmountService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;

/**
 * <AUTHOR> Fengping
 * @description 订单测试类
 * @since 2025/7/21
 **/
@SpringBootTest
public class OrderTests {

    @Autowired
    private OrderCaseService orderCaseService;

    @Autowired
    private AfterSaleCaseService afterSaleCaseService;

    @Autowired
    private CalculateAmountService calculateAmountService;

    @Autowired
    private AutoRefundCaseService autoRefundCaseService;

    @Test
    public void testPageOrderList() {
        System.out.println("=== 测试订单列表分页查询 ===");

        // 测试用户ID（可以根据实际情况修改）
        Long customerId = 367609010476291L;

        // 创建查询请求
        OrderListRequestDTO request = new OrderListRequestDTO();
        request.setOrderStatus(-1); // -1表示查询所有状态
        request.setToPage(1); // 第1页
        request.setPageRows(10); // 每页10条
        // request.setKeyword("103947476667176090005");

        System.out.println("查询参数：");
        System.out.println("  客户ID: " + customerId);
        System.out.println("  订单状态: " + request.getOrderStatus());
        System.out.println("  页码: " + request.getToPage());
        System.out.println("  每页条数: " + request.getPageRows());

        // 执行查询
        PageDTO<OrderListResponseDTO> result = orderCaseService.pageOrderList(customerId, request);

        // 输出结果
        System.out.println("\n查询结果：");
        System.out.println("  总记录数: " + result.getTotal());
        System.out.println("  当前页: " + result.getCurrent());
        System.out.println("  每页大小: " + result.getSize());
        System.out.println("  订单数量: " + result.getRecords().size());

        // 输出订单详情
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            System.out.println("\n订单详情：");
            for (int i = 0; i < result.getRecords().size(); i++) {
                OrderListResponseDTO order = result.getRecords().get(i);
                System.out.println("  第" + (i + 1) + "个订单:");
                System.out.println("    订单号: " + order.getOrderNo());
                System.out.println("    商品名称: " + order.getProductName());
                System.out.println("    品牌名称: " + order.getBrandName());

                System.out.println("    下单时间: " + order.getCreateTime());
                System.out.println("    ---");
            }
        } else {
            System.out.println("  暂无订单数据");
        }

        System.out.println("=== 订单列表分页查询测试完成 ===");
    }

    @Test
    public void testPageOrderListWithStatus() {
        System.out.println("=== 测试按状态筛选订单列表 ===");

        Long customerId = 1115965283836637203L;

        // 测试不同订单状态
        int[] statusArray = { 1, 2, 3, 4, 5 }; // 根据实际业务状态调整

        for (int status : statusArray) {
            OrderListRequestDTO request = new OrderListRequestDTO();
            request.setOrderStatus(status);
            request.setToPage(1);
            request.setPageRows(5);

            System.out.println("\n查询订单状态 " + status + " 的订单：");

            try {
                PageDTO<OrderListResponseDTO> result = orderCaseService.pageOrderList(customerId, request);
                System.out.println("  状态 " + status + " 的订单数量: " + result.getTotal());

                if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                    System.out.println("  前几个订单号: ");
                    result.getRecords().stream()
                            .limit(3)
                            .forEach(order -> System.out.println("    " + order.getOrderNo()));
                }
            } catch (Exception e) {
                System.out.println("  查询状态 " + status + " 时出现异常: " + e.getMessage());
            }
        }

        System.out.println("=== 按状态筛选订单列表测试完成 ===");
    }

    @Test
    public void testPageOrderListPagination() {
        System.out.println("=== 测试订单列表分页功能 ===");

        Long customerId = 1115965283836637203L;

        // 测试分页
        for (int page = 1; page <= 3; page++) {
            OrderListRequestDTO request = new OrderListRequestDTO();
            request.setOrderStatus(-1); // 查询所有状态
            request.setToPage(page);
            request.setPageRows(5);

            System.out.println("\n查询第 " + page + " 页：");

            PageDTO<OrderListResponseDTO> result = orderCaseService.pageOrderList(customerId, request);

            System.out.println("  页码: " + result.getCurrent());
            System.out.println("  本页记录数: " + result.getRecords().size());
            System.out.println("  总记录数: " + result.getTotal());

            if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                System.out.println("  本页订单号: ");
                result.getRecords().forEach(order -> System.out.println("    " + order.getOrderNo()));
            }

            // 如果没有更多数据，停止测试
            if (result.getRecords().isEmpty()) {
                System.out.println("  第 " + page + " 页无数据，停止分页测试");
                break;
            }
        }

        System.out.println("=== 订单列表分页功能测试完成 ===");
    }

    @Test
    public void testPageOrderListWithTimeRange() {
        System.out.println("=== 测试按时间范围查询订单列表 ===");

        Long customerId = 1115965283836637203L;

        OrderListRequestDTO request = new OrderListRequestDTO();
        request.setOrderStatus(-1); // 查询所有状态
        request.setCreateTimeStart("2025-07-01 00:00:00");
        request.setCreateTimeEnd("2025-07-31 23:59:59");
        request.setToPage(1);
        request.setPageRows(10);

        System.out.println("查询参数：");
        System.out.println("  时间范围: " + request.getCreateTimeStart() + " ~ " + request.getCreateTimeEnd());

        PageDTO<OrderListResponseDTO> result = orderCaseService.pageOrderList(customerId, request);

        System.out.println("查询结果：");
        System.out.println("  符合时间范围的订单数: " + result.getTotal());

        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            result.getRecords().forEach(
                    order -> System.out.println("  订单号: " + order.getOrderNo() + ", 创建时间: " + order.getCreateTime()));
        }

        System.out.println("=== 按时间范围查询订单列表测试完成 ===");
    }

    @Test
    public void testPageOrderListWithCategory() {
        System.out.println("=== 测试按分类查询订单列表 ===");

        Long customerId = 1115965283836637203L;
        Long categoryId = 95L; // 根据实际分类ID调整

        OrderListRequestDTO request = new OrderListRequestDTO();
        request.setOrderStatus(-1);
        request.setFirstCategoryId(categoryId);
        request.setToPage(1);
        request.setPageRows(10);

        System.out.println("查询参数：");
        System.out.println("  分类ID: " + request.getFirstCategoryId());

        PageDTO<OrderListResponseDTO> result = orderCaseService.pageOrderList(customerId, request);

        System.out.println("查询结果：");
        System.out.println("  该分类的订单数: " + result.getTotal());

        System.out.println("=== 按分类查询订单列表测试完成 ===");
    }

    @Test
    public void testGetOrderDetail() {
        System.out.println("=== 测试订单详情查询 ===");

        Long customerId = 1115965283836637203L;
        String orderNo = "ORD202406010001"; // 使用实际存在的订单号

        System.out.println("查询参数：");
        System.out.println("  客户ID: " + customerId);
        System.out.println("  订单号: " + orderNo);

        try {
            OrderDetailResponseDTO result = orderCaseService.getOrderDetail(customerId, orderNo);

            System.out.println("查询结果：");
            System.out.println("  订单号: " + result.getOrderNo());
            System.out.println("  订单状态: " + result.getOrderStatus());
            System.out.println("  支付状态: " + result.getPaymentStatus());
            System.out.println("  发货状态: " + result.getDeliveryStatus());
            System.out.println("  售后状态: " + result.getAfterSaleStatus());
            System.out.println("  支付金额: " + result.getPaymentAmount());
            System.out.println("  订单金额: " + result.getOrderAmount());
            System.out.println("  总售价: " + result.getTotalSellAmount());
            System.out.println("  手续费: " + result.getTotalServiceFee());
            System.out.println("  超额费: " + result.getExceedFee());
            System.out.println("  充值账号: " + result.getRechargeAccount());
            System.out.println("  品牌名称: " + result.getBrandName());
            System.out.println("  渠道名称: " + result.getChannelName());
            System.out.println("  创建时间: " + result.getCreateTime());
            System.out.println("  支付时间: " + result.getPaymentTime());
            System.out.println("  完成时间: " + result.getCompleteTime());

            if (result.getOrderItems() != null && !result.getOrderItems().isEmpty()) {
                System.out.println("  订单项数量: " + result.getOrderItems().size());
                result.getOrderItems().forEach(item -> {
                    System.out.println("    订单项ID: " + item.getId());
                    System.out.println("    商品名称: " + item.getProductName());
                    System.out.println("    品牌名称: " + item.getBrandName());
                    System.out.println("    销售价格: " + item.getSellPrice());
                    System.out.println("    面额: " + item.getFaceAmount());
                    System.out.println("    数量: " + item.getQuantity());
                    System.out.println("    手续费: " + item.getServiceFee());
                    System.out.println("    商品类型: " + item.getProductType());
                    System.out.println("    分类ID: " + item.getCategoryId());
                    System.out.println("    ----");
                });
            }

        } catch (Exception e) {
            System.out.println("查询失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("=== 订单详情查询测试完成 ===");
    }

    @Test
    public void testAfterSaleDetail() {
        System.out.println("=== 测试售后单详情查询 ===");
        Long customerId = 383235660272641L;
        String afterSaleNo = "20250926206395599003020820016";
        System.out.println("查询参数：");
        System.out.println("  客户ID: " + customerId);
        System.out.println("  售后单号: " + afterSaleNo);
        AfterSaleDetailResponseDTO result = afterSaleCaseService.getAfterSaleDetail(afterSaleNo, customerId);
        System.out.println("查询结果：");
        System.out.println("  售后金额: " + result.getRefundInfo().getAfterSaleNo());

    }

    @Test
    public void testPageAfterSaleList() {
        System.out.println("=== 测试售后单列表分页查询 ===");

        // 测试用户ID（可以根据实际情况修改）
        Long customerId = 1115965283836637203L;

        // 创建查询请求
        AfterSaleListRequestDTO request = new AfterSaleListRequestDTO();
        request.setAfterSaleStatus(-1); // -1表示查询所有状态
        request.setToPage(1); // 第1页
        request.setPageRows(10); // 每页10条

        System.out.println("查询参数：");
        System.out.println("  客户ID: " + customerId);
        System.out.println("  售后状态: " + request.getAfterSaleStatus());
        System.out.println("  页码: " + request.getToPage());
        System.out.println("  每页条数: " + request.getPageRows());

        // 执行查询
        PageDTO<AfterSaleListResponseDTO> result = afterSaleCaseService.pageAfterSaleList(customerId, request);

        // 输出结果
        System.out.println("\n查询结果：");
        System.out.println("  总记录数: " + result.getTotal());
        System.out.println("  当前页: " + result.getCurrent());
        System.out.println("  每页大小: " + result.getSize());
        System.out.println("  售后单数量: " + result.getRecords().size());

        // 输出售后单详情
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            System.out.println("\n售后单详情：");
            for (int i = 0; i < result.getRecords().size(); i++) {
                AfterSaleListResponseDTO afterSale = result.getRecords().get(i);
                System.out.println("  第" + (i + 1) + "个售后单:");
                System.out.println("    售后单号: " + afterSale.getAfterSaleNo());
                System.out.println("    订单号: " + afterSale.getOrderNo());
                System.out.println("    商品名称: " + afterSale.getProductName());
                System.out.println("    品牌名称: " + afterSale.getBrandName());
                System.out.println("    退款金额: " + afterSale.getRefundAmount());
                System.out.println("    申请数量: " + afterSale.getApplyQuantity());
                System.out.println("    申请时间: " + afterSale.getCreateTime());
                System.out.println("    ---");
            }
        } else {
            System.out.println("  暂无售后单数据");
        }

        System.out.println("=== 售后单列表分页查询测试完成 ===");
    }

    @Test
    public void testPageAfterSaleListWithStatus() {
        System.out.println("=== 测试按状态筛选售后单列表 ===");

        Long customerId = 1115965283836637203L;

        // 测试不同售后状态 1-待审核 20-审核通过 30-审核驳回 32-退款驳回 33-售后撤销 34-售后完成
        int[] statusArray = { -1, 1, 20, 30, 32, 33, 34 };

        for (int status : statusArray) {
            AfterSaleListRequestDTO request = new AfterSaleListRequestDTO();
            request.setAfterSaleStatus(status);
            request.setToPage(1);
            request.setPageRows(5);

            System.out.println("\n查询售后状态 " + status + " 的售后单：");

            try {
                PageDTO<AfterSaleListResponseDTO> result = afterSaleCaseService.pageAfterSaleList(customerId, request);
                System.out.println("  状态 " + status + " 的售后单数量: " + result.getTotal());

                if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                    System.out.println("  前几个售后单号: ");
                    result.getRecords().stream()
                            .limit(3)
                            .forEach(afterSale -> System.out.println("    " + afterSale.getAfterSaleNo() +
                                    " - " + afterSale.getAfterSaleStatus()));
                }
            } catch (Exception e) {
                System.out.println("  查询状态 " + status + " 时出现异常: " + e.getMessage());
            }
        }

        System.out.println("=== 按状态筛选售后单列表测试完成 ===");
    }

    @Test
    public void testPageAfterSaleListWithType() {
        System.out.println("=== 测试按类型筛选售后单列表 ===");

        Long customerId = 1115965283836637203L;

        // 测试不同售后类型
        int[] typeArray = { 1, 2 }; // 1-仅退款 2-退货退款

        for (int type : typeArray) {
            AfterSaleListRequestDTO request = new AfterSaleListRequestDTO();
            request.setAfterSaleStatus(-1); // 查询所有状态
            request.setAfterSaleType(type);
            request.setToPage(1);
            request.setPageRows(5);

            System.out.println("\n查询售后类型 " + type + " 的售后单：");

            try {
                PageDTO<AfterSaleListResponseDTO> result = afterSaleCaseService.pageAfterSaleList(customerId, request);
                System.out.println("  类型 " + type + " 的售后单数量: " + result.getTotal());

                if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                    System.out.println("  前几个售后单: ");
                    result.getRecords().stream()
                            .limit(3)
                            .forEach(afterSale -> System.out.println("    " + afterSale.getAfterSaleNo() +
                                    " - " + afterSale.getAfterSaleType() +
                                    " - " + afterSale.getRefundAmount() + "元"));
                }
            } catch (Exception e) {
                System.out.println("  查询类型 " + type + " 时出现异常: " + e.getMessage());
            }
        }

        System.out.println("=== 按类型筛选售后单列表测试完成 ===");
    }

    @Test
    public void testPageAfterSaleListPagination() {
        System.out.println("=== 测试售后单列表分页功能 ===");

        Long customerId = 1115965283836637203L;

        // 测试分页
        for (int page = 1; page <= 3; page++) {
            AfterSaleListRequestDTO request = new AfterSaleListRequestDTO();
            request.setAfterSaleStatus(-1); // 查询所有状态
            request.setToPage(page);
            request.setPageRows(5);

            System.out.println("\n查询第 " + page + " 页：");

            PageDTO<AfterSaleListResponseDTO> result = afterSaleCaseService.pageAfterSaleList(customerId, request);

            System.out.println("  页码: " + result.getCurrent());
            System.out.println("  本页记录数: " + result.getRecords().size());
            System.out.println("  总记录数: " + result.getTotal());

            if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                System.out.println("  本页售后单号: ");
                result.getRecords().forEach(afterSale -> System.out.println("    " + afterSale.getAfterSaleNo() +
                        " - " + afterSale.getAfterSaleStatus()));
            }

            // 如果没有更多数据，停止测试
            if (result.getRecords().isEmpty()) {
                System.out.println("  第 " + page + " 页无数据，停止分页测试");
                break;
            }
        }

        System.out.println("=== 售后单列表分页功能测试完成 ===");
    }

    @Test
    public void testMonthlyPurchaseQuantity() {
        System.out.println("=== 测试获取月度采购数量 ===");
        Long customerId = 390238459616001L;
        ProductItemId productItemId = ProductItemId.of(289L, 230L, 1);
        System.out.println("  月度采购数量: " + calculateAmountService.getMonthlyPurchaseQuantity(customerId, productItemId));
    }

    @Test
    public void testDailyPurchaseQuantity() {
        System.out.println("=== 测试每日购买数量功能 ===");
        Long customerId = 390238459616001L;
        ProductItemId productItemId = ProductItemId.of(289L, 230L, 1);

        // 1. 测试获取当日购买数量（初始应该为0）
        Integer initialQuantity = calculateAmountService.getDailyPurchaseQuantity(customerId, productItemId);
        System.out.println("  初始当日购买数量: " + initialQuantity);

        // 2. 测试增加购买数量
        calculateAmountService.updateDailyPurchaseQuantity(customerId, productItemId, 5, "ADD");
        Integer afterAddQuantity = calculateAmountService.getDailyPurchaseQuantity(customerId, productItemId);
        System.out.println("  增加5张后的当日购买数量: " + afterAddQuantity);

        // 3. 测试再次增加购买数量
        calculateAmountService.updateDailyPurchaseQuantity(customerId, productItemId, 3, "ADD");
        Integer afterSecondAddQuantity = calculateAmountService.getDailyPurchaseQuantity(customerId, productItemId);
        System.out.println("  再增加3张后的当日购买数量: " + afterSecondAddQuantity);

        // 4. 测试减少购买数量
        calculateAmountService.updateDailyPurchaseQuantity(customerId, productItemId, 2, "SUBTRACT");
        Integer afterSubtractQuantity = calculateAmountService.getDailyPurchaseQuantity(customerId, productItemId);
        System.out.println("  减少2张后的当日购买数量: " + afterSubtractQuantity);

        // 5. 验证结果
        assert afterAddQuantity == (initialQuantity + 5) : "第一次增加后数量不正确";
        assert afterSecondAddQuantity == (initialQuantity + 5 + 3) : "第二次增加后数量不正确";
        assert afterSubtractQuantity == (initialQuantity + 5 + 3 - 2) : "减少后数量不正确";

        System.out.println("=== 每日购买数量功能测试完成 ===");
    }

    @Test
    public void testAutoRefund() {
        System.out.println("=== 测试自动退款功能 ===");
        AutoRefundRequestDTO request = new AutoRefundRequestDTO();
        request.setOrderNo("105592350898547220005");
        request.setExternalRefundNo("testautoexternal105592353");
        request.setApplyRefundAmount(new BigDecimal("0.09"));
        request.setAfterSaleType(AfterSaleTypeEnum.PARTIAL_REFUND.getCode());
        request.setAfterSaleQuantity(1);
        AfterSaleEntity result = autoRefundCaseService.autoRefund(request);
        System.out.println("  自动退款结果: " + result);
    }

    @Test
    public void testCancelOrderTimeLogic() {
        System.out.println("=== 测试取消订单时间逻辑 ===");

        // 注意：这个测试主要是验证逻辑，实际运行需要有真实的订单数据
        // 在实际环境中，可以创建不同时间的测试订单来验证

        System.out.println("测试场景说明：");
        System.out.println("1. 当月创建的订单取消时，应该恢复当月购买金额和数量统计");
        System.out.println("2. 当日创建的订单取消时，应该恢复当日购买数量统计");
        System.out.println("3. 非当月创建的订单取消时，不应该恢复当月统计");
        System.out.println("4. 非当日创建的订单取消时，不应该恢复当日统计");

        // 这里可以添加具体的测试逻辑，比如：
        // 1. 创建不同时间的测试订单
        // 2. 取消这些订单
        // 3. 验证统计数据的变化是否符合预期

        System.out.println("=== 取消订单时间逻辑测试完成 ===");
    }

}
