package com.jsrxjt.mobile.api.packages.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 套餐请求参数
 * @Author: ywt
 * @Date: 2025-05-09 18:02
 * @Version: 1.0
 */
@Data
@Schema(description = "套餐信息请求参数")
public class PackageInfoRequestDTO {
    @Schema(description = "套餐的spuid")
    @NotNull(message = "套餐spuid为空错误")
    private Long packageSpuId;
    @Schema(description = "三级地址id")
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
    @Schema(description = "套餐的skuid，本字段用于标记选中的sku，若不传默认选中第一个sku")
    private Long packageSkuId;
}
