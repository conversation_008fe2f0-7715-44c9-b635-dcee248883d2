package com.jsrxjt.mobile.api.enums;

/**
 * @Description: 中心返回type转渠道枚举
 * @Author: zy
 * @Date: 2025/6/5 16:05
 */
public enum PickTypeToChannelEnum {

    PLACE("place", 4),//普通分销
    YIKE("yike", 9),//逸刻
    WATSONS("watsons", 10);//屈臣氏

    PickTypeToChannelEnum(String type, Integer channel) {
        this.type = type;
        this.channel = channel;
    }
    private String type;
    private Integer channel;

    public String getType() {
        return type;
    }
    public Integer getChannel() {
        return channel;
    }
    public static PickTypeToChannelEnum getByType(String type) {
        for (PickTypeToChannelEnum value : PickTypeToChannelEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
