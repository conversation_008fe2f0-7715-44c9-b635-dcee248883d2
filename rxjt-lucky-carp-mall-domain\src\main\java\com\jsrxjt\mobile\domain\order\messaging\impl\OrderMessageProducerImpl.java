package com.jsrxjt.mobile.domain.order.messaging.impl;

import com.alibaba.fastjson2.JSON;
import com.jsrxjt.mobile.domain.gateway.mq.MqSendGateWay;
import com.jsrxjt.mobile.domain.gateway.mq.entity.MqMessage;
import com.jsrxjt.mobile.domain.order.messaging.OrderMessageProducer;
import com.jsrxjt.mobile.domain.order.types.OrderMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class OrderMessageProducerImpl implements OrderMessageProducer {

    private static final String TOPIC_DELAY_ORDER = "delay_order_topic";


    private final MqSendGateWay mqSendGateWay;
    @Override
    public void sendOrderDelayAutoCancel(OrderMessage message, long payExpireSecondsTimestamp) {
        long delayMillis = payExpireSecondsTimestamp * 1000 - System.currentTimeMillis();
        delayMillis = delayMillis > 0 ? delayMillis : 1L;
        log.info("Send order delay auto cancel message, message={} 延迟{}毫秒", message,delayMillis);
        mqSendGateWay.syncDelaySend(MqMessage.builder()
                .topic(TOPIC_DELAY_ORDER)
                .tag("tag_cancel")
                .keys(message.getOrderNo())
                .messageBody(JSON.toJSONString(message))
                .build(),delayMillis);
    }
}
