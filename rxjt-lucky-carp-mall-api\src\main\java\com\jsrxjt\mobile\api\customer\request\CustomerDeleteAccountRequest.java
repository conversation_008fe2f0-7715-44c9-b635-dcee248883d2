package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "用户注销账号请求参数")
public class CustomerDeleteAccountRequest extends BaseParam {

    @Schema(description = "用户id")
    @NotNull(message = "用户id不能为空")
    private Long customerId;

    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String verificationCode;

}
