package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 资讯分类响应
 * @Author: ywt
 * @Date: 2025-06-12 14:26
 * @Version: 1.0
 */
@Data
public class InformationCatResponseDto {
    @Schema(description = "分类id")
    private Integer catId;
    @Schema(description = "分类名称")
    private String catName;
    @Schema(description = "排序(数字越大排序越靠前)")
    private Integer sort;
}
