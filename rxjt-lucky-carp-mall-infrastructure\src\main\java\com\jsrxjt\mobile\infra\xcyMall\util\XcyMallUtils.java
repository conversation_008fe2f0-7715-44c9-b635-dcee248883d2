package com.jsrxjt.mobile.infra.xcyMall.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;

/**
 * Created by jeffery.yang on 2023/12/23 10:19
 *
 * @description: 祥采云平台工具类
 * @author: jeffery.yang
 * @date: 2023/12/23 10:19
 * @version: 1.0
 */
@Slf4j
public class XcyMallUtils {

	public static Boolean verifySignature(Map<String, Object> verifyMap,String secret) {
		String requestSign = verifyMap.get("sign").toString();
		// 签名字段不参与验签
		verifyMap.put("sign",null);
		SortedMap<Object,Object> parameters = JSONObject.parseObject(JSONObject.toJSONString(verifyMap), SortedMap.class);
		String sign = createSign("UTF-8", parameters, secret);
		if(!StringUtils.equals(sign, requestSign)){
			log.warn("### 祥采云平台验签失败");
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	public static String createSign(String characterEncoding, SortedMap<Object, Object> parameters, String key) {
		StringBuffer sb = new StringBuffer();
		StringBuffer sbkey = new StringBuffer();
		//所有参与传参的参数按照accsii排序（升序）
		Set es = parameters.entrySet();
		Iterator it = es.iterator();
		while (it.hasNext()) {
			Map.Entry entry = (Map.Entry) it.next();
			String k = (String) entry.getKey();
			Object v = entry.getValue();
			//空值不传递，不参与签名组串
			if (null != v && !"".equals(v)) {
				sb.append(k + "=" + v + "&");
				sbkey.append(k + "=" + v + "&");
			}
		}
		sbkey = sbkey.append("secret=").append(key);
		//MD5加密,结果转换为大写字符
		return MD5Encode(sbkey.toString(), characterEncoding).toUpperCase();
	}

	public static String MD5Encode(String origin, String charsetname) {
		String resultString = null;
		try {
			resultString = new String(origin);
			MessageDigest md = MessageDigest.getInstance("MD5");
			if (charsetname == null || "".equals(charsetname)) {
				resultString = byteArrayToHexString(md.digest(resultString.getBytes()));
			} else {
				resultString = byteArrayToHexString(md.digest(resultString.getBytes(charsetname)));
			}
		} catch (Exception exception) {
		}
		return resultString;
	}
	public static String byteArrayToHexString(byte b[]) {
		StringBuffer resultSb = new StringBuffer();
		for (int i = 0; i < b.length; i++) {
			resultSb.append(byteToHexString(b[i]));
		}

		return resultSb.toString();
	}
	public static String byteToHexString(byte b) {
		int n = b;
		if (n < 0) {
			n += 256;
		}
		int d1 = n / 16;
		int d2 = n % 16;
		return hexDigits[d1] + hexDigits[d2];
	}
	private static final String hexDigits[] = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d",
		"e", "f"};
}
