package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 用户一次性token请求
 */
@Getter
@Setter
@Schema(description = "用户一次性token请求参数")
public class CustomerOnceTokenRequest extends BaseParam {
    @NotBlank(message = "来源标识不能为空")
    @Schema(description = "来源标识 APP_ANDROID,APP_IOS,WX_MINI",requiredMode = Schema.RequiredMode.REQUIRED)
    private String source;

    @Schema(description = "APP标识 不传会默认为全球购应用")
    private String appFlag= "RX_GLOBAL_MALL";
}
