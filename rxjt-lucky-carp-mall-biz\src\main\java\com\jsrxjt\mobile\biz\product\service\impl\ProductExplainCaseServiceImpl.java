package com.jsrxjt.mobile.biz.product.service.impl;

import com.jsrxjt.mobile.api.app.types.AppCouponExplainTypeEnum;
import com.jsrxjt.mobile.api.product.dto.request.RechargeUsageInstructionsRequestDTO;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductExplainTypeEnum;
import com.jsrxjt.mobile.biz.product.service.ProductExplainCaseService;
import com.jsrxjt.mobile.domain.app.entity.AppCouponExplainEntity;
import com.jsrxjt.mobile.domain.app.repository.AppCouponExplainRepository;
import com.jsrxjt.mobile.domain.product.entity.ProductExplainEntity;
import com.jsrxjt.mobile.domain.product.repository.ProductExplainRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductExplainCaseServiceImpl implements ProductExplainCaseService {

    private final ProductExplainRepository productExplainRepository;

    private final AppCouponExplainRepository appCouponExplainRepository;

    @Override
    public String getRechargeUsageInstructions(RechargeUsageInstructionsRequestDTO requestDTO) {
        if (FlatProductTypeEnum.VIDEO_DIRECT_RECHARGE.getType().equals(requestDTO.getProductType())) {
            List<ProductExplainEntity> explainEntities = productExplainRepository.findBySpuIdAndExplainType(requestDTO.getProductSpuId(), ProductExplainTypeEnum.USE_DETAIL);
            if (explainEntities != null && !explainEntities.isEmpty()) {
                return explainEntities.get(0).getContent();
            }
        } else if (FlatProductTypeEnum.ALIPAY_RED_PACKET.getType().equals(requestDTO.getProductType())) {
            AppCouponExplainEntity explainEntity = appCouponExplainRepository.getBySpuIdAndExplainType(requestDTO.getProductSpuId(), AppCouponExplainTypeEnum.RECHARGE_USAGE_INSTRUCTIONS);
            if (explainEntity != null) {
                return explainEntity.getContent();
            }
        }
        return "";
    }
}
