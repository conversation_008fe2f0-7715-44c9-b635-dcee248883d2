package com.jsrxjt.mobile.biz.message;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.message.request.MessageUpdateReadRequest;
import com.jsrxjt.mobile.api.message.response.MessageCountResponse;
import com.jsrxjt.mobile.api.message.response.MessageDetailResponse;
import com.jsrxjt.mobile.api.message.response.MessageServeResponse;
import com.jsrxjt.mobile.biz.message.request.MessageDetailQueryRequest;
import com.jsrxjt.mobile.biz.message.request.MessageDetailRequest;
import com.jsrxjt.mobile.domain.message.entity.MessageServeEntity;

import java.util.List;

public interface MessageService {

    /**
     * 瑞祥通知-用户创建后消息列表
     * @param request
     * @return
     */
    PageDTO<MessageDetailResponse> findAllSysByTime(MessageDetailRequest request);

    /**
     * 详细详情
     * @param request
     * @return
     */
    MessageDetailResponse findByMsgId(MessageDetailQueryRequest request);

    /**
     * 查询客服消息内容
     * @param request
     * @return
     */
    PageDTO<MessageServeResponse> findAllServeMsg(MessageDetailRequest request);

    /**
     * 更新所有用户消息已读
     * @param customerId 客户id
     */
    void updateAllIsRead(Long customerId);

    /**
     * 更新单个瑞祥消息已读
     */
    void updateSysIsRead(MessageUpdateReadRequest request);

    /**
     * 更新单个客服消息已读
     */
    void updateServeIsRead(MessageUpdateReadRequest request);

    /**
     * 客户所有消息数量
     */
    MessageCountResponse countAllMsg(Long customerId);


}
