package com.jsrxjt.mobile.domain.ticket.gateway;

import com.jsrxjt.mobile.domain.ticket.gateway.cmd.GiveOutTicketCmd;

import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * @Description: 优惠券中台网关接口
 * @Author: ywt
 * @Date: 2025-10-16 14:45
 * @Version: 1.0
 */
public interface TicketPlatformGateway {

    /**
     * 发放优惠券
     * 
     * @param cmd     发放命令
     * @param onError 错误回调
     * @return 券号列表
     */
    List<String> giveOutTicket(GiveOutTicketCmd cmd, BiConsumer<Integer, String> onError);

    /**
     * 验证签名
     * 
     * @param requestMap 请求参数
     * @return 验签结果
     */
    boolean verifySign(Map<String, Object> requestMap);
}
