package com.jsrxjt.mobile.domain.inventory.repository;

import com.jsrxjt.mobile.domain.inventory.entity.SkuReleaseInventoryEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * SKU放量库存记录仓储接口
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/3/11
 **/
public interface SkuReleaseInventoryRepository {
    /**
     * 批量保存放量库存记录
     * 
     * @param releaseInventoryRecords 放量库存记录列表
     * @return 保存成功的记录数
     */
    int batchSaveReleaseInventories(List<SkuReleaseInventoryEntity> releaseInventoryRecords);

    /**
     * 更新已开始但未结束的放量库存状态为已生效(1)
     * 
     * @param productType 产品类型
     * @param currentTime 当前时间
     * @return 更新成功的记录数
     */
    int updateStartedInventoryStatus(Integer productType, LocalDateTime currentTime);

    /**
     * 更新已结束的放量库存状态为已结束(2)
     * 
     * @param productType 产品类型
     * @param currentTime 当前时间
     * @return 更新成功的记录数
     */
    int updateEndedInventoryStatus(Integer productType, LocalDateTime currentTime);

    /**
     * 根据计划ID和政策ID查询未结束的放量库存记录
     * 
     * @param planId   放量计划ID
     * @param policyId 政策ID
     * @return 未结束的放量库存记录列表
     */
    List<SkuReleaseInventoryEntity> findUnfinishedByPlanId(Long planId, Long policyId);

    /**
     * 批量更新放量库存记录
     * 
     * @param releaseInventoryRecords 放量库存记录列表
     * @return 更新成功的记录数
     */
    int batchUpdateReleaseInventories(List<SkuReleaseInventoryEntity> releaseInventoryRecords);

    /**
     * 标记未结束的放量库存记录为删除状态
     * 
     * @param planId  放量计划ID
     * @param modTime 修改时间
     * @return 更新成功的记录数
     */
    int deleteUnfinishedByPlanId(Long planId, LocalDateTime modTime);

    /**
     * 检查指定计划ID和放量日期的放量库存记录是否存在
     * 
     * @param planId      放量计划ID
     * @param releaseDate 放量日期
     * @return 是否存在记录
     */
    boolean existsReleaseInventoryByPlanIdAndDate(Long planId, LocalDate releaseDate);

    /**
     * 检查指定计划ID、政策ID和放量日期的放量库存记录是否存在
     * 
     * @param planId      放量计划ID
     * @param policyId    政策ID
     * @param releaseDate 放量日期
     * @return 是否存在记录
     */
    boolean existsReleaseInventoryByPlanIdAndPolicyIdAndDate(Long planId, Long policyId, LocalDate releaseDate);

    /**
     * 根据产品类型、SKU ID、政策ID和指定时间范围查询放量库存记录
     *
     * @param productType 产品类型
     * @param skuId       SKU ID
     * @param policyId    政策ID
     * @return 放量库存记录列表
     */
    List<SkuReleaseInventoryEntity> findValidByProductTypeAndSkuIdAndPolicyId(Integer productType, Long skuId,
            long policyId);

    /**
     * 根据ID扣减库存数量
     * 
     * @param id       库存记录ID
     * @param quantity 扣减数量
     * @return 更新成功的记录数
     */
    int reduceInventoryById(Long id, Integer quantity);

    /**
     * 批量根据ID扣减库存数量
     * 
     * @param reductionList 扣减列表，包含ID和扣减数量
     * @return 更新成功的记录数
     */
    int batchReduceInventoryById(List<InventoryReduction> reductionList);

    /**
     * 根据订单号查询库存扣减记录
     * 
     * @param orderNo 订单号
     * @return 库存扣减记录列表
     */
    List<InventoryReduction> findReductionsByOrderNo(String orderNo);

    /**
     * 根据ID恢复库存数量
     * 
     * @param id       库存记录ID
     * @param quantity 恢复数量
     * @return 更新成功的记录数
     */
    int recoverInventoryById(Long id, Integer quantity);

    /**
     * 批量根据ID恢复库存数量
     *
     * @param reductionList 恢复列表，包含ID和恢复数量
     * @return 更新成功的记录数
     */
    int batchRecoverInventoryById(List<InventoryReduction> reductionList);

    /**
     * 根据产品SKU ID、产品类型、政策ID和日期查询当天的放量库存记录
     *
     * @param productSkuId 产品SKU ID
     * @param productType  产品类型
     * @param policyId     政策ID
     * @param releaseDate  放量日期
     * @return 当天的放量库存记录列表
     */
    List<SkuReleaseInventoryEntity> findTodayReleaseInventoryBySkuAndPolicy(Long productSkuId, Integer productType,
            Long policyId, LocalDate releaseDate);

    /**
     * 批量更新放量库存记录的时间字段（只更新时间，不更新库存）
     *
     * @param releaseInventoryRecords 放量库存记录列表
     * @return 更新成功的记录数
     */
    int batchUpdateReleaseInventoryTimes(List<SkuReleaseInventoryEntity> releaseInventoryRecords);

    /**
     * 库存扣减记录
     */
    class InventoryReduction {
        private Long id;
        private Integer quantity;

        public InventoryReduction() {
        }

        public InventoryReduction(Long id, Integer quantity) {
            this.id = id;
            this.quantity = quantity;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
    }
}
