package com.jsrxjt.mobile.biz.distribution.service.impl;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.api.locallife.dto.request.*;
import com.jsrxjt.mobile.api.locallife.dto.response.*;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.distribution.service.LocalLifeCaseService;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.order.AfterSaleCaseService;
import com.jsrxjt.mobile.biz.order.AutoRefundCaseService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.locallife.gateway.LocalLifeGateWay;
import com.jsrxjt.mobile.domain.locallife.types.LocalLifeGoodsEnum;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.LocalLifeOrderInfoBuilder;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class LocalLifeCaseServiceImpl implements LocalLifeCaseService {

    private final UnifiedDistributionApi unifiedDistributionApi;

    private final LocalLifeGateWay localLifeGateWay;

    private final OrderCaseService orderCaseService;

    private final LocalLifeOrderInfoBuilder localLifeOrderInfoBuilder;

    private final OrderRepository orderRepository;

    private final CustomerService customerService;

    private final DistributedLock distributedLock;

    private final RegionRepository regionRepository;

    private final AutoRefundCaseService autoRefundCaseService;

    private final AfterSaleRepository afterSaleRepository;

    private final AfterSaleCaseService afterSaleCaseService;

    private static final String LOCALLIFE_ORDER_LOCK_PREFIX = "localLife_order_notify_lock:";

    private static final String LOCALLIFE_ORDER_REFUND_LOCK_PREFIX = "localLife_order_refund_notify_lock:";

    @Value("${flq.cashier.url}")
    private String cashierUrl;

    @Override
    public ApiResponse<LocalLifeUserInfoResponse> vipInfo(LocalLifeUserInfoRequest requestDTO) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(requestDTO.getUserCode())
                || StringUtils.isEmpty(requestDTO.getSignature())
                || StringUtils.isEmpty(requestDTO.getTimestamp())
        ) {
            return ApiResponse.fail("参数不全");
        }
        // 验签
        Map<String, Object> map = JSONObject.parseObject(JSONUtil.toJsonStr(requestDTO), Map.class);
        if (!localLifeGateWay.checkLocalLifeSign(map)) {
            log.error("local_life/vipInfo_sign: {}", JSONUtil.toJsonStr(map));
            return ApiResponse.fail("签名验证失败");
        }
        String userId = localLifeGateWay.decodeLocalLifeUserId(requestDTO.getUserCode());
        if (StringUtils.isEmpty(userId)) {
            return ApiResponse.fail(700, "用户不存在");
        }
        CustomerEntity customer = null;
        try {
            customer = customerService.getCustomerById(Long.valueOf(userId));
        } catch (Exception e) {
            return ApiResponse.fail(700, "用户不存在");
        }
        if (Objects.isNull(customer)) {
            return ApiResponse.fail(700, "用户不存在");
        }
        Map<String, Object> vipInfoResponse = new HashMap<>();
        vipInfoResponse.put("userId", customer.getId());
        vipInfoResponse.put("nickname", customer.getUserName());
        vipInfoResponse.put("phone", customer.getPhone());
        vipInfoResponse = localLifeGateWay.addLocalLifeCommonParams(vipInfoResponse);
        LocalLifeUserInfoResponse localLifeUserInfoResponse = JSON.parseObject(JSON.toJSONString(vipInfoResponse), LocalLifeUserInfoResponse.class);
        return ApiResponse.success(localLifeUserInfoResponse, "OK");
    }

    @Override
    public ApiResponse<LocalLifeCreateOrderResponse> prePay(LocalLifeCreateOrderDTO requestDTO) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(requestDTO.getUserId())
                || StringUtils.isEmpty(requestDTO.getOrderNo())
                || requestDTO.getOrderType() == null
                || StringUtils.isEmpty(requestDTO.getSignature())
                || StringUtils.isEmpty(requestDTO.getTimestamp())
                || StringUtils.isEmpty(requestDTO.getTradeAmount())
        ) {
            return ApiResponse.fail("参数不全");
        }
        // 校验金额是否异常
        BigDecimal tradeAmount = new BigDecimal(requestDTO.getTradeAmount());
        if (tradeAmount.compareTo(BigDecimal.ZERO) < 0) {
            return ApiResponse.fail("订单金额非法");
        }
        // 验签
        Map<String, Object> map = JSONObject.parseObject(JSONUtil.toJsonStr(requestDTO), Map.class);
        if (!localLifeGateWay.checkLocalLifeSign(map)) {
            log.error("local_life/prePay_sign: {}", JSONUtil.toJsonStr(map));
            return ApiResponse.fail("签名验证失败");
        }
        String outOrderSn = requestDTO.getOrderNo();
        LocalLifeCreateOrderResponse localLifeCreateOrderResponse = new LocalLifeCreateOrderResponse();
        String lockKey = LOCALLIFE_ORDER_LOCK_PREFIX + outOrderSn;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取本地生活订单创建通知处理锁失败，订单号：{}", outOrderSn);
                return ApiResponse.fail("系统繁忙，请稍后重试");
            }

            OrderInfoEntity existOrderEntity = orderRepository.findByExternalOrderNoAndAppFlag(outOrderSn, DistChannelType.LOCALLIFE.name());
            // 幂等校验
            if (existOrderEntity != null) {
                log.error("此订单已存在,三方订单号,{}", outOrderSn);
                localLifeCreateOrderResponse.setFlqOrderNo(existOrderEntity.getOrderNo());
                localLifeCreateOrderResponse.setCashierUrl(cashierUrl + existOrderEntity.getOrderNo());
                Map<String, Object> prePayResponse = localLifeGateWay.addLocalLifeCommonParams(localLifeCreateOrderResponse);
                localLifeCreateOrderResponse = JSON.parseObject(JSON.toJSONString(prePayResponse), LocalLifeCreateOrderResponse.class);
                return ApiResponse.success(localLifeCreateOrderResponse);
            }
            String externalPayResultUrl = decodeBase64URLSafe(requestDTO.getDetailUrl());

            CustomerEntity customer = customerService.getCustomerById(Long.valueOf(requestDTO.getUserId()));
            if (customer == null) {
                log.error("未找到会员ID为{}的会员信息", requestDTO.getUserId());
                return ApiResponse.fail(700, "用户不存在");
            }

            //获取本地生活订单url
            DistAccessRequest localLifeRedirectUrlDTO = new DistAccessRequest();
            BeanUtils.copyProperties(requestDTO, localLifeRedirectUrlDTO);
            localLifeRedirectUrlDTO.setUserId(requestDTO.getUserId());
            localLifeRedirectUrlDTO.setSceneValue(requestDTO.getOrderNo());
            localLifeRedirectUrlDTO.setSceneId(LocalLifeGoodsEnum.ORDER_LIST.getCode());
            String orderDetailUrl = null;
            try {
                RegionEntity region = regionRepository.getCurrentRegion(customer.getId());
                localLifeRedirectUrlDTO.setLatitude(region.getLat());
                localLifeRedirectUrlDTO.setLongitude(region.getLng());
                localLifeRedirectUrlDTO.setChannelType(DistChannelType.LOCALLIFE);
                DistAccessResponse distAccessResponse = unifiedDistributionApi.access(localLifeRedirectUrlDTO);
                orderDetailUrl = distAccessResponse.getRedirectUrl();
            } catch (Exception e) {
                log.error("未获取到用户定位信息 customer={}", customer.getId());
            }

            CreateOrderDTO createOrderDTO = new CreateOrderDTO();
            createOrderDTO.setCustomerId(customer.getId());
            createOrderDTO.setCustomerMobile(customer.getPhone());
            createOrderDTO.setExternalOrderNo(outOrderSn);
            createOrderDTO.setProductSpuId(22L);
            createOrderDTO.setProductType(ProductTypeEnum.APP.getType());
            createOrderDTO.setExternalAppProductPrice(tradeAmount);
            createOrderDTO.setExternalPayResultUrl(externalPayResultUrl);
            createOrderDTO.setOrderDetailUrl(orderDetailUrl);
            OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO, localLifeOrderInfoBuilder);
            localLifeCreateOrderResponse = LocalLifeCreateOrderResponse
                    .builder()
                    .flqOrderNo(orderInfoEntity.getOrderNo())
                    .cashierUrl(cashierUrl + orderInfoEntity.getOrderNo())
                    .build();
            Map<String, Object> prePayResponse = localLifeGateWay.addLocalLifeCommonParams(localLifeCreateOrderResponse);
            localLifeCreateOrderResponse = JSON.parseObject(JSON.toJSONString(prePayResponse), LocalLifeCreateOrderResponse.class);
            log.info("本地生活下单返回数据{}", JSONUtil.toJsonStr(localLifeCreateOrderResponse));
            return ApiResponse.success(localLifeCreateOrderResponse, "OK");
        } catch (Exception e) {
            log.error("处理本地生活订单创建通知异常，订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
            return ApiResponse.fail("系统异常：" + e.getMessage());
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public ApiResponse<LocalLifeOrderRefundResponse> refund(LocalLifeRefundOrderDTO requestDTO) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(requestDTO.getRefundOrderNo())
                || StringUtils.isEmpty(requestDTO.getOrderNo())
                || requestDTO.getOrderType() == null
                || StringUtils.isEmpty(requestDTO.getSignature())
                || StringUtils.isEmpty(requestDTO.getTimestamp())
                || StringUtils.isEmpty(requestDTO.getRefundAmount())
        ) {
            return ApiResponse.fail("参数不全");
        }
        // 校验金额是否异常
        BigDecimal refundAmount = new BigDecimal(requestDTO.getRefundAmount());
        if (refundAmount.compareTo(BigDecimal.ZERO) < 0) {
            return ApiResponse.fail("订单金额非法");
        }
        // 验签
        Map<String, Object> map = JSONObject.parseObject(JSONUtil.toJsonStr(requestDTO), Map.class);
        if (!localLifeGateWay.checkLocalLifeSign(map)) {
            log.error("local_life/refund_sign: {}", JSONUtil.toJsonStr(map));
            return ApiResponse.fail("签名验证失败");
        }
        String outOrderSn = requestDTO.getOrderNo();
        String lockKey = LOCALLIFE_ORDER_REFUND_LOCK_PREFIX + outOrderSn;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取本地生活订单退款通知处理锁失败，订单号：{}", outOrderSn);
                return ApiResponse.fail("系统繁忙，请稍后重试");
            }

            OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(requestDTO.getOrderNo(), null);
            if (order == null) {
                return ApiResponse.fail(605, "订单不存在");
            }

            AfterSaleEntity afterSaleEntity = new AfterSaleEntity();
            Integer paymentStatus = order.getPaymentStatus();
            if (Objects.equals(paymentStatus, PaymentStatusEnum.UNPAID.getCode())) {
                return ApiResponse.fail(609, "订单状态不可退");
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.PAID.getCode())) {
                AutoRefundRequestDTO autoRefundRequestDTO = new AutoRefundRequestDTO();
                autoRefundRequestDTO.setOrderNo(order.getOrderNo());
                autoRefundRequestDTO.setExternalRefundNo(requestDTO.getRefundOrderNo());
                autoRefundRequestDTO.setExternalRefundAmount(refundAmount);
                BigDecimal orderAmount = order.getTotalSellAmount();
                // 整单退
                if (orderAmount.compareTo(refundAmount) == 0) {
                    // 整单退场景需要退手续费
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode());
                    autoRefundRequestDTO.setApplyRefundAmount(order.getOrderAmount());
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else if (orderAmount.compareTo(refundAmount) > 0) {
                    // 部分退
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.PARTIAL_REFUND.getCode());
                    autoRefundRequestDTO = afterSaleCaseService.calDistributionOrderRefundAmount(autoRefundRequestDTO);
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else {
                    return ApiResponse.fail(607, "退款金额大于订单金额");
                }
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDED.getCode())) {
                return ApiResponse.fail(608, "已整单退款,不可再退");
            }
            LocalLifeOrderRefundResponse localLifeOrderRefundResponse = LocalLifeOrderRefundResponse
                    .builder()
                    .flqRefundNo(afterSaleEntity.getRefundNo())
                    .build();
            Map<String, Object> refundResponse = localLifeGateWay.addLocalLifeCommonParams(localLifeOrderRefundResponse);
            localLifeOrderRefundResponse = JSON.parseObject(JSON.toJSONString(refundResponse), LocalLifeOrderRefundResponse.class);
//        Map<String, Object> refundResponse = localLifeGateWay.buildLocalLifeRefundResponse(afterSaleEntity);
//        LocalLifeOrderRefundResponse localLifeOrderRefundResponse = JSON.parseObject(JSON.toJSONString(refundResponse), LocalLifeOrderRefundResponse.class);
            log.info("本地生活退款返回数据{}", JSONUtil.toJsonStr(localLifeOrderRefundResponse));
            return ApiResponse.success(localLifeOrderRefundResponse, "OK");
        } catch (Exception e) {
            log.error("处理本地生活订单退款通知异常，订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
            return ApiResponse.fail("系统异常：" + e.getMessage());
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public ApiResponse<LocalLifeOrderPayQueryResponse> payQuery(LocalLifePayQueryRequest request) {
//        Map<String, Object> stringObjectMap = localLifeGateWay.addLocalLifeCommonParams(request);
//        request = JSON.parseObject(JSON.toJSONString(stringObjectMap), LocalLifePayQueryRequest.class);
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getOrderNo())
                || StringUtils.isEmpty(request.getSignature())
                || StringUtils.isEmpty(request.getTimestamp())
        ) {
            return ApiResponse.fail("参数不全");
        }
        // 验签
        Map<String, Object> map = JSONObject.parseObject(JSONUtil.toJsonStr(request), Map.class);
        if (!localLifeGateWay.checkLocalLifeSign(map)) {
            log.error("local_life/payQuery_sign: {}", JSONUtil.toJsonStr(map));
            return ApiResponse.fail("签名验证失败");
        }
        LocalLifeOrderPayQueryResponse response = new LocalLifeOrderPayQueryResponse();
        String orderNo = request.getOrderNo();
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(orderNo, null);
        if (order != null) {
            response.setOrderNo(orderNo);
            response.setFlqOrderNo(order.getOrderNo());
            Integer payStatus = order.getPaymentStatus();
            PaymentStatusEnum paymentStatusEnum = PaymentStatusEnum.getByCode(payStatus);
            if (Objects.requireNonNull(paymentStatusEnum) == PaymentStatusEnum.PAID) {
                response.setTradeStatus("00");
                response.setTradeTime(String.valueOf(order.getPaymentTime()
                        .atZone(ZoneId.systemDefault())
                        .toInstant()
                        .getEpochSecond()));
            } else {
                response.setTradeStatus("01");
                response.setTradeTime("0");
            }
            Map<String, Object> localLifeOrderPayQueryResponseMap = localLifeGateWay.addLocalLifeCommonParams(response);
            response = JSON.parseObject(JSONUtil.toJsonStr(localLifeOrderPayQueryResponseMap), LocalLifeOrderPayQueryResponse.class);
            return ApiResponse.success(response, "OK");
        } else {
            return ApiResponse.fail(605, "订单不存在");
        }
    }

    @Override
    public ApiResponse<LocalLifeOrderRefundQueryResponse> refundQuery(LocalLifeRefundQueryRequest request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getRefundNo())
                || StringUtils.isEmpty(request.getSignature())
                || StringUtils.isEmpty(request.getTimestamp())
        ) {
            return ApiResponse.fail("参数不全");
        }
        // 验签
        Map<String, Object> map = JSONObject.parseObject(JSONUtil.toJsonStr(request), Map.class);
        if (!localLifeGateWay.checkLocalLifeSign(map)) {
            log.error("local_life/refundQuery_sign: {}", JSONUtil.toJsonStr(map));
            return ApiResponse.fail("签名验证失败");
        }
        AfterSaleEntity afterSaleEntity = afterSaleRepository.findByExternalRefundNo(request.getRefundNo());
        if (afterSaleEntity == null) {
            return ApiResponse.fail("退款单不存在");
        }
        OrderInfoEntity orderInfo = orderRepository.findByOrderNo(afterSaleEntity.getOrderNo());
        LocalLifeOrderRefundQueryResponse response = new LocalLifeOrderRefundQueryResponse();
        response.setOrderNo(orderInfo.getExternalOrderNo());
        response.setFlqRefundNo(afterSaleEntity.getExternalRefundNo());
        RefundStatusEnum refundStatusEnum = RefundStatusEnum.getByCode(afterSaleEntity.getRefundStatus());
        if (Objects.requireNonNull(refundStatusEnum) == RefundStatusEnum.REFUND_SUCCESS) {
            response.setRefundStatus("10");
            response.setRefundTime(String.valueOf(afterSaleEntity.getRefundTime()
                    .atZone(ZoneId.systemDefault())
                    .toInstant()
                    .getEpochSecond()));
            response.setRefundAmount(String.valueOf(afterSaleEntity.getRefundAmount()));
        } else {
            response.setRefundStatus("11");
            response.setRefundTime("0");
            response.setRefundAmount("0.00");
        }
        Map<String, Object> localLifeOrderRefundQueryResponseMap = localLifeGateWay.addLocalLifeCommonParams(response);
        response = JSON.parseObject(JSONUtil.toJsonStr(localLifeOrderRefundQueryResponseMap), LocalLifeOrderRefundQueryResponse.class);
        return ApiResponse.success(response, "OK");
    }

    /**
     * 简单验证
     *
     * @param requestDTO
     * @return
     */
    private boolean checkAuth(NotifyLocalLifeDTO requestDTO) {
        String netpayId = requestDTO.getNetpayId();
        String requestTime = requestDTO.getRequestTime();
        String requestRand = requestDTO.getRequestRand();
        String sign = requestDTO.getSign();
        String calculatedSign = DigestUtils.sha1Hex("yeeboknetpay888" + netpayId + requestTime + requestRand);
        return calculatedSign.equals(sign);
    }

    /**
     * 解码Base64URL安全格式的字符串
     *
     * @param base64URLSafe Base64URL安全格式的字符串
     * @return 解码后的字节数组
     */
    public String decodeBase64URLSafe(String base64URLSafe) {
        // 将Base64URL安全字符替换为标准Base64字符
        String base64 = base64URLSafe.replace('-', '+').replace('_', '/');

        // 添加填充字符'='以确保长度是4的倍数
        switch (base64.length() % 4) {
            case 2:
                base64 += "==";
                break;
            case 3:
                base64 += "=";
                break;
            default:
                // 长度已经是4的倍数，不需要填充
                break;
        }
        return new String(Base64.getDecoder().decode(base64));
    }
}
