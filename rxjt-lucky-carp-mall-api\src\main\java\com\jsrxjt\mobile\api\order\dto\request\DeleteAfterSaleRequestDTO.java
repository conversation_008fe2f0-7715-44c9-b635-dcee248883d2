package com.jsrxjt.mobile.api.order.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 删除售后单请求DTO
 * 
 * <AUTHOR>
 * @since 2025-12-11
 */
@Data
@Schema(description = "删除售后单请求")
public class DeleteAfterSaleRequestDTO {

    /**
     * 售后单号
     */
    @NotBlank(message = "售后单号不能为空")
    @Schema(description = "售后单号")
    private String afterSaleNo;
}
