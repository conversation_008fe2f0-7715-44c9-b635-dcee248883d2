package com.jsrxjt.mobile.domain.product.repository;

/**
 * 核销页弹框
 * <AUTHOR>
 * @date 2025/09/12
 */
public interface ProductDialogRepository {

    /**
     * 获取核销页弹框
     * @param productSpuId
     * @param productType(1卡券 2套餐)
     * @param dialogType(1:提交订单弹框 2:核销页弹框)
     * @return {@link String}
     */
    String getDialog(Long productSpuId, Integer productType, Integer dialogType);

    /**
     * 获取核销页弹框包括删除
     * @param productSpuId
     * @param productType
     * @param dialogType
     * @return {@link String}
     */
    String getDialogWithDel(Long productSpuId, Integer productType, Integer dialogType);
}
