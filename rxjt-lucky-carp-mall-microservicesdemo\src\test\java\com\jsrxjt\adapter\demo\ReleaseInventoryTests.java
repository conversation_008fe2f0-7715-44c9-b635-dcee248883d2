package com.jsrxjt.adapter.demo;

import com.alibaba.fastjson2.JSON;
import com.jsrxjt.mobile.api.inventory.types.ReleasePlanOperationType;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.inventory.service.ReleaseInventoryCaseService;
import com.jsrxjt.mobile.domain.inventory.types.SkuReleaseMessage;
import com.jsrxjt.mobile.domain.order.service.CalculateAmountService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @description
 * @since 2025/3/11
 **/
@SpringBootTest
public class ReleaseInventoryTests {

    @Autowired
    private ReleaseInventoryCaseService releaseInventoryCaseService;

    @Autowired
    private CalculateAmountService calculateAmountService;

    @Test
    public void testExecuteNextDayReleaseTask() {
        releaseInventoryCaseService.executeNextDayReleaseTask(ProductTypeEnum.COUPON.getType());
    }

    @Test
    public void testAutoUpdateReleaseInventoryStatus() {
        releaseInventoryCaseService.autoUpdateReleaseInventoryStatus(ProductTypeEnum.COUPON.getType());
    }

    @Test
    public void testHandleReleasePlanChange() {
        SkuReleaseMessage skuReleaseMessage = new SkuReleaseMessage();
        skuReleaseMessage.setOperationType(ReleasePlanOperationType.ADD);
        List<Long> planIds = new ArrayList<>();
        List<Long> extraPlanIds = new ArrayList<>();
        planIds.add(35L);
        planIds.add(36L);
        extraPlanIds.add(22L);
        extraPlanIds.add(23L);
        skuReleaseMessage.setPlanIds(planIds);
        skuReleaseMessage.setExtraPlanIds(extraPlanIds);
        System.out.println(JSON.toJSONString(skuReleaseMessage));

        releaseInventoryCaseService.handleReleasePlanChange(skuReleaseMessage);
    }

    @Test
    public void testHandleReleasePlanChangeEdit() {
        // 测试编辑操作，验证当天放量起止时间是否正确更新
        SkuReleaseMessage skuReleaseMessage = new SkuReleaseMessage();
        skuReleaseMessage.setOperationType(ReleasePlanOperationType.EDIT);
        List<Long> planIds = new ArrayList<>();
        List<Long> extraPlanIds = new ArrayList<>();
        planIds.add(35L);
        extraPlanIds.add(22L);
        skuReleaseMessage.setPlanIds(planIds);
        skuReleaseMessage.setExtraPlanIds(extraPlanIds);
        System.out.println("测试编辑操作: " + JSON.toJSONString(skuReleaseMessage));

        boolean result = releaseInventoryCaseService.handleReleasePlanChange(skuReleaseMessage);
        System.out.println("编辑操作结果: " + result);
    }

    @Test
    public void testCalculateServiceFeeAmount() {
        // ProductItem productItem = new ProductItem();
        // productItem.setProductType(1);
        // productItem.setSkuId(43L);
        // productItem.setSpuId(139L);
        // productItem.setPlatformPrice(new BigDecimal("99.00"));
        // productItem.setDefaultServiceFeePercent(new BigDecimal("0.10"));
        // Integer buyNum = 2; // 购买数量
        // ServiceFeeCalculationResult serviceFeeResult =
        // calculateAmountService.calculateServiceFee(productItem, buyNum, 1534L);
        // System.out.println("手续费结果：" + JSON.toJSONString(serviceFeeResult));
        //
        // ProductItem appItem = new ProductItem();
        // appItem.setProductType(3);
        // appItem.setSkuId(0L);
        // appItem.setSpuId(3L);
        // appItem.setPlatformPrice(new BigDecimal("100.00"));
        //
        // Long regionId = 109099L;
        // Integer appBuyNum = 1; // 购买数量
        // ServiceFeeCalculationResult appServiceFeeResult =
        // calculateAmountService.calculateServiceFee(appItem, appBuyNum, regionId);
        // System.out.println("应用手续费结果：" + JSON.toJSONString(appServiceFeeResult));

    }

}
