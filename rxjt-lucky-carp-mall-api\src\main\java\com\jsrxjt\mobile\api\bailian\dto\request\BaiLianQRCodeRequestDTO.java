package com.jsrxjt.mobile.api.bailian.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BaiLianQRCodeRequestDTO {

    @Schema(description = "分销应用类型")
    @NotBlank(message = "应用类型不能为空")
    private String distributionType;

    @Schema(description = "渠道来源类型 1：h5 2:小程序 3:app （不传则默认1）")
    private String source;
}
