package com.jsrxjt.common.adapter.config;

import com.jsrxjt.common.adapter.filter.HttpServletRequestWrapFilter;
import com.jsrxjt.common.adapter.interceptor.PostSignInterceptor;
import com.jsrxjt.common.core.config.RedisTemplateConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.Filter;

/**
 *  WebMvcConfig
 * 
 * <AUTHOR> Fengping
 * 2023/3/14 11:57
 * 
 **/
@AutoConfiguration
@AutoConfigureAfter(RedisTemplateConfiguration.class)
public class WebMvcConfig implements WebMvcConfigurer {
    @Bean
    public PostSignInterceptor postSignInterceptor() {
        return new PostSignInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
      registry.addInterceptor(postSignInterceptor()).addPathPatterns("/**")
              .excludePathPatterns("/static/**","resource/**",
                      "/swagger-ui.html","/swagger-resources/**","/v3/api-docs","/webjars/**"
                      ,"/error","/error/**");
    }


    @Bean
    public FilterRegistrationBean<Filter> filterRegistrationBean() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new HttpServletRequestWrapFilter());
        registrationBean.setName("interceptor filter body params");
        registrationBean.addUrlPatterns("/*");
        return registrationBean;
    }

}
