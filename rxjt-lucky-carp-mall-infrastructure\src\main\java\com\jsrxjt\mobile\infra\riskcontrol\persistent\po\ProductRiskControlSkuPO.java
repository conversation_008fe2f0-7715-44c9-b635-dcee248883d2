package com.jsrxjt.mobile.infra.riskcontrol.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 产品风控策略sku表
 * @Author: ywt
 * @Date: 2025-09-18 11:00
 * @Version: 1.0
 */
@Data
@TableName("product_risk_control_sku")
public class ProductRiskControlSkuPO {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 风控策略id
     */
    private Long riskId;

    /**
     * 风控类型 0风控策略 1禁用策略
     */
    private Integer riskType;

    /**
     * 产品spuid
     */
    private Long productSpuId;

    /**
     * 产品skuid，risk_type为1时无效
     */
    private Long productSkuId;

    /**
     * 产品类型 1:卡券 2套餐 3:应用 4支付宝红包
     */
    private Integer productType;

    /**
     * 风控产品类型：1卡券 2套餐 3应用，4支付宝红包 5扫码付(展码付和首页扫码付)
     */
    private Integer riskProductType;

    /**
     * 手续费百分比
     */
    private BigDecimal commissionFee;

    /**
     * 每人每日限数量，仅product_type为1/2/4有效
     */
    private Integer limitNumPerDay;

    /**
     * 每人每月限数量，仅product_type为1/2/4有效
     */
    private Integer limitNumPerMonth;

    /**
     * spu每人每月限额金额，仅product_type为1/2/4有效
     */
    private BigDecimal userMonthlyLimitAmount;

    /**
     * 修改人
     */
    private Long modId;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 删除标记
     */
    private Integer delFlag;
}
