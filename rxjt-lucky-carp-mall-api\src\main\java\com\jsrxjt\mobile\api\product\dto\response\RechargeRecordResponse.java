package com.jsrxjt.mobile.api.product.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 产品充值记录响应
 * @Author: ywt
 * @Date: 2025-08-08 15:54
 * @Version: 1.0
 */
@Data
public class RechargeRecordResponse {
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "spuId")
    private Long productSpuId;

    @Schema(description = "skuId")
    private Long productSkuId;

    @Schema(description = "产品名称")
    private String productName;
    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "SKU面值")
    private BigDecimal faceAmount;

    @Schema(description = "logo图片地址")
    private String productLogo;

    @Schema(description = "充值账户")
    private String rechargeAccount;

    @Schema(description = "订单状态: 0-待付款 10-进行中 20-交易成功 30-交易关闭 40-超时取消 41-手动取消")
    private int orderStatus;

    @Schema(description = "发货状态: 0-未发货/未充值  1-发货中/充值中 2-已发货/已充值 3-发货失败/充值失败")
    private int deliveryStatus;

    @Schema(description = "售后状态: 1-待审核 20-审核通过 30-审核驳回 32-退款驳回 33-售后撤销 34-售后完成")
    private Integer afterSaleStatus;

    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;

    @Schema(description = "下单时间")
    private LocalDateTime createTime;
}
