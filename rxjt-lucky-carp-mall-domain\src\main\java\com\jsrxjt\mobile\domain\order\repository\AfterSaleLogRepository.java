package com.jsrxjt.mobile.domain.order.repository;

import com.jsrxjt.mobile.domain.order.entity.AfterSaleLogEntity;

import java.util.List;

/**
 * 售后日志仓储接口
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface AfterSaleLogRepository {
    
    /**
     * 保存售后日志
     * 
     * @param afterSaleLog 售后日志
     */
    void save(AfterSaleLogEntity afterSaleLog);
    
    /**
     * 根据售后单号查询日志列表
     * 
     * @param afterSaleNo 售后单号
     * @return 日志列表
     */
    List<AfterSaleLogEntity> findByAfterSaleNo(String afterSaleNo);
}
