package com.jsrxjt.mobile.api.payment.dto.request;

import com.jsrxjt.mobile.api.enums.PaymentChannelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 支付回调请求DTO
 * 
 * <AUTHOR>
 * @since 2025/6/17
 */
@Data
@Schema(description = "支付回调请求")
public class PaymentCallbackRequestDTO {

    @Schema(description = "支付渠道")
    @NotNull
    private PaymentChannelEnum paymentChannel;

    @Schema(description = "支付渠道原始请求")
    @NotNull
    private HttpServletRequest httpServletRequest;

    @Schema(description = "支付渠道原始请求body，针对application/json")
    private Map<String, Object> requestBody;

}