package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 支付结果通知
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "前轮轮询订单请求体")
public class PollPickChannelOrderRequestDTO {

    @Schema(description = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    @Schema(description = "卡号")
    @NotBlank(message = "卡号不能为空")
    private String code;

}
