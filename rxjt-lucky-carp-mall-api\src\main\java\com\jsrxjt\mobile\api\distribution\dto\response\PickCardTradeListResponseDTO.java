package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 卡消费记录
 */
@Data
public class PickCardTradeListResponseDTO {
    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "业务订单号")
    private String orderNo;

    @Schema(description = "外部业务单号")
    private String outOrderNo;

    @Schema(description = "交易号")
    private String tradeNo;

    @Schema(description = "子-交易单号 微信交易单号")
    private String tradeInfoNo;

    @Schema(description = "子-预付卡交易单号")
    private String outTradeInfoNo;

    @Schema(description = "子-交易退款单号")
    private String tradeRefundInfoNo;

    @Schema(description = "子-外部交易退款单号")
    private String outTradeRefundInfoNo;

    @Schema(description = "变动类型  TRADE 交易  REFUND 退款 CANCEL 冲正 RECHARGE 充值")
    private String type;

    @Schema(description = "卡号")
    private String cardNo;

    @Schema(description = "交易金额")
    private Long tradePrice;

    @Schema(description = "交易时间")
    private Date tradeTime;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;

    @Schema(description = "删除时间")
    private Integer deletedAt;
}