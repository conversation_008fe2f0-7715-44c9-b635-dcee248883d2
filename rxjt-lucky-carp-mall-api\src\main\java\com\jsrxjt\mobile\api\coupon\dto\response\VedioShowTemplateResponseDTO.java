package com.jsrxjt.mobile.api.coupon.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 直充卡券显示样式模版响应
 * @Author: ywt
 * @Date: 2025-08-11 09:20
 * @Version: 1.0
 */
@Data
@Schema(description = "直充卡券显示样式模版响应")
public class VedioShowTemplateResponseDTO {
    @Schema(description = "卡券的spuid")
    private Long couponSpuId;
    @Schema(description = "1-腾讯视频 2-爱奇艺 3-优酷视频 4-芒果TV 5-中石化权益包 6-便利蜂 7-咪咕视频 8-喜马拉雅 9-搜狐视频，前端可用来判断详情页使用哪种模版样式显示，仅用于直充类卡券")
    private Integer showTemplate;
}