package com.jsrxjt.mobile.api.enums;

/**
 * 应用类型
 */
public enum AppTypeEnum {

    ORDINARY(1, "普通应用"),

    ALIPAY_VOUCHER(2,"支付宝红包"),

    PHONE_BILL(3,"苏西话费"),
    SCAN_PICK(4,"扫码提货"),
    BIANLF_RECHARGE(7,"便利蜂直充");

    private final Integer channel;

    private final String channelName;

    AppTypeEnum(Integer channel, String channelName) {
        this.channel = channel;
        this.channelName = channelName;
    }

    public Integer getChannel() {
        return channel;
    }

    public String getChannelName() {
        return channelName;
    }

    public static String getNameByType(Integer type) {
        for (AppTypeEnum appType : AppTypeEnum.values()) {
            if (appType.getChannel().equals(type)) {
                return appType.getChannelName();
            }
        }
        return "";
    }
}
