package com.jsrxjt.mobile.api.ticket.types;

/**
 * 券状态枚举
 * <AUTHOR>
 * @Date 2025/9/15
 */
public enum TicketStatusEnum {

    UNUSED((byte) 1, "未核销"),

    USED((byte) 2, "已核销");

    private final Byte status;

    private final String name;

    TicketStatusEnum(Byte status, String name) {
        this.status = status;
        this.name = name;
    }

    public Byte getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }
}
