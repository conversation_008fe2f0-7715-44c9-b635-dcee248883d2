package com.jsrxjt.mobile.domain.pickplatform.response;

import lombok.Data;

@Data
public class PickPlatformPayInfoResponse {

    //支付明细订单号(存在则是消费)
    private String payOrderSn;
    //退款订单号(存在则是退款)
    private String refundOrderSn;
    //消费门店ID
    private String shopId;
    //消费门店
    private String shopName;
    //消费金额（正数为退款，负数为消费）
    private String price;
    //交易时间
    private String createdAt;

}
