package com.jsrxjt.mobile.api.enums;

/**
 * @Description: 资讯评价的状态
 * @Author: ywt
 * @Date: 2025-06-11 09:59
 * @Version: 1.0
 */
public enum EvaluateStatusEnum {
    AUDITING(0, "待审核"),
    AUDIT_SUCESS(1, "审核通过"),
    AUDIT_FAILD(2, "审核驳回");
    private final Integer code;
    private final String value;

    EvaluateStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
