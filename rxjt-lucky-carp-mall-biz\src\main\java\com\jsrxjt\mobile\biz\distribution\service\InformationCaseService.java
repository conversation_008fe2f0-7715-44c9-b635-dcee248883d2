package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.InformationAggregationResponseDto;
import com.jsrxjt.mobile.api.distribution.dto.response.InformationCatResponseDto;
import com.jsrxjt.mobile.api.distribution.dto.response.InformationEvaResponseDto;
import com.jsrxjt.mobile.api.distribution.dto.response.InformationResponseDto;

import java.util.List;

/**
 * @Description: 资讯服务接口
 * @Author: ywt
 * @Date: 2025-06-10 18:56
 * @Version: 1.0
 */
public interface InformationCaseService {
    InformationAggregationResponseDto infoAggregation(InformationAggregationRequestDto requestDto);
    List<InformationCatResponseDto> catList();
    List<InformationResponseDto> topInformationList(InformationTopListRequestDto requestDto);
    PageDTO<InformationResponseDto> informationList(InformationListRequestDto requestDto);
    List<InformationResponseDto> searchInformations(InformationSearchRequestDto requestDto);
    List<InformationResponseDto> suggestInfoList(InformationAggregationRequestDto requestDto);
    boolean clickInformation(InformationRequestDto requestDTO);
    InformationResponseDto infoDetail(InformationRequestDto requestDTO);
    boolean forwardInformation(InformationRequestDto requestDTO);
    PageDTO<InformationEvaResponseDto> evaList(InformationEvaListRequestDto requestDTO);
    boolean submitEvaluate(InformationEvaAddRequestDto requestDTO);
    boolean likeEvaluate(InformationEvaRequestDto requestDTO);
}
