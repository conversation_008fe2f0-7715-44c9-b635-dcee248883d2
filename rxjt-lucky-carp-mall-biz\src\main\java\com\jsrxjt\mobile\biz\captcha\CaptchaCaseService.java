package com.jsrxjt.mobile.biz.captcha;

import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaGenerateDTO;
import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaVerifyRequestDTO;
import com.jsrxjt.mobile.api.captcha.dto.response.CaptchaResponseDTO;
import com.jsrxjt.mobile.api.captcha.dto.response.CaptchaVerifyResponseDTO;

/**
 * 验证码业务服务接口
 * 
 * <AUTHOR>
 * @since 2025-12-04
 */
public interface CaptchaCaseService {

    /**
     * 获取验证码
     * 
     * @param request 获取验证码请求
     * @return 验证码响应
     */
    CaptchaResponseDTO getCaptcha(CaptchaGenerateDTO request);

    /**
     * 校验验证码
     * 
     * @param request 校验验证码请求
     * @return 校验结果响应
     */
    CaptchaVerifyResponseDTO verifyCaptcha(CaptchaVerifyRequestDTO request);

}
