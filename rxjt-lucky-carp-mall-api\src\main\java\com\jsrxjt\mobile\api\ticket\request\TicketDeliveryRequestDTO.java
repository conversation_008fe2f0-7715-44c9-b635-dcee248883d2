package com.jsrxjt.mobile.api.ticket.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 优惠券订单实体类
 *
 */
@Data
public class TicketDeliveryRequestDTO  {
    @Schema(description = "当前页")
    @NotNull(message = "当前页不能为空")
    private Long page;
    @Schema(description = "每页条数")
    @NotNull(message = "每页条数不能为空")
    private Long size;

}
