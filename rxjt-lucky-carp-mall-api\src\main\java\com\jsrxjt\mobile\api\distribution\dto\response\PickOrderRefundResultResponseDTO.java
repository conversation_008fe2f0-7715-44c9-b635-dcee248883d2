package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 支付结果通知
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "支付结果通知")
public class PickOrderRefundResultResponseDTO {

    @Schema(description = "平台退款流水号")
    private String refundNo;

    @Schema(description = "平台订单流水号")
    private String orderNo;

    @Schema(description = "支付交易流水号")
    private String tradeNo;

}
