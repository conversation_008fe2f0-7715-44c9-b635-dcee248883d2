package com.jsrxjt.mobile.domain.ticket.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 下单赠送券订单实体
 * 
 * <AUTHOR>
 * @date 2025/09/22
 */
@Data
public class GiftTicketOrderEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 赠券券订单编号
     */
    private String ticketOrderNo;

    /**
     * 关联的订单id
     */
    private Long orderId;

    /**
     * 关联订单编号
     */
    private String orderNo;

    /**
     * 外部订单号
     */
    private String externalOrderNo;

    /**
     * 营销中台优惠券发放编号
     */
    private String centerTicketCouponNumber;

    /**
     * 优惠券类型：1全球购线上商城优惠券 2商家自发优惠券  3瑞祥代发优惠券 4门店优惠券
     */
    private Integer ticketType;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 推送发券状态 0 未推送 1 推送中 2 推送成功 3 有异常
     */
    private Integer pushStatus;

    /**
     * 卡管的券id或全球购的活动id
     */
    private String centerCouponId;

    /**
     * 优惠券id
     */
    private Long ticketId;

    /**
     * 优惠券名称
     */
    private String ticketName;

    /**
     * 优惠券规格图片
     */
    private String specPicUrl;

    /**
     * 优惠券品牌id
     */
    private Long ticketBrandId;

    /**
     * 应赠券数量
     */
    private Integer shouldNum;

    /**
     * 实际发放数量
     */
    private Integer actualNum;

    /**
     * 推送异常信息
     */
    private String pushErrorMsg;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 修改时间
     */
    private LocalDateTime modTime;

    /**
     * 修改人
     */
    private Long modId;

    /**
     * 删除标记 1:已删除状态 0:正常状态
     */
    private Integer delFlag;
}
