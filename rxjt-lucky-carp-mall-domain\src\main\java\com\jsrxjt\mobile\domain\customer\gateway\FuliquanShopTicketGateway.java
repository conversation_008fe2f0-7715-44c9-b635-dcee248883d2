package com.jsrxjt.mobile.domain.customer.gateway;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.customer.gateway.request.HisCouponQueryRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.request.SendTicketRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.request.ShopTicketListRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.HisCouponListResponseDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.SendTicketResponseDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.ShopTicketListResponseDTO;

import java.util.List;

/**
 * @Description: 福鲤圈门店优惠券接口
 * @Author: zy
 */
public interface FuliquanShopTicketGateway {

    /**
     * 发券(生日券)
     * @param requestDTO
     * @return
     */
    SendTicketResponseDTO sendCoupon(SendTicketRequestDTO requestDTO);

    /**
     * 获取门店优惠券列表
     * @param requestDTO
     * @return
     */
    PageDTO<ShopTicketListResponseDTO> getShopTicketList(ShopTicketListRequestDTO requestDTO);

    /**
     * 获取历史券包
     * @param requestDTO 请求参数
     * @return 券列表
     */
    List<HisCouponListResponseDTO> getHisCouponList(HisCouponQueryRequestDTO requestDTO);

    /**
     * 获取历史订单Url
     * @param requestDTO 请求参数
     * @return 历史订单Url
     */
    String getHisOrderListUrl(HisCouponQueryRequestDTO requestDTO);
}
