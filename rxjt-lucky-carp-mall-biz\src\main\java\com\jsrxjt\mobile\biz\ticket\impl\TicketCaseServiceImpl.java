package com.jsrxjt.mobile.biz.ticket.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.coupon.dto.request.TicketConsumeStatusNotifyRequestDTO;
import com.jsrxjt.mobile.api.coupon.dto.request.TicketStatusNotifyRequestDTO;
import com.jsrxjt.mobile.biz.ticket.TicketCaseService;
import com.jsrxjt.mobile.domain.ticket.entity.TicketCallBackEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketConsumeCallBackEntity;
import com.jsrxjt.mobile.domain.ticket.service.TicketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description: 优惠券服务
 * @Author: ywt
 * @Date: 2025-10-15 15:17
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TicketCaseServiceImpl implements TicketCaseService {
    private final TicketService ticketService;

    @Override
    public BaseResponse callback(TicketStatusNotifyRequestDTO requestDTO) {
        TicketCallBackEntity entity = new TicketCallBackEntity();
        BeanUtil.copyProperties(requestDTO, entity);
        return ticketService.callback(entity);
    }

    @Override
    public BaseResponse consumeCallback(TicketConsumeStatusNotifyRequestDTO requestDTO) {
        TicketConsumeCallBackEntity entity = new TicketConsumeCallBackEntity();
        BeanUtil.copyProperties(requestDTO, entity);
        return ticketService.consumeCallback(entity);
    }
}
