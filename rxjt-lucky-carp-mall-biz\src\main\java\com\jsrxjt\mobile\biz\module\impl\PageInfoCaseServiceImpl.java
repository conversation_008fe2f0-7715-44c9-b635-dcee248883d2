package com.jsrxjt.mobile.biz.module.impl;

import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.biz.module.ModuleCaseService;
import com.jsrxjt.mobile.biz.module.PageInfoCaseService;
import com.jsrxjt.mobile.domain.module.entity.PageInfoEntity;
import com.jsrxjt.mobile.domain.module.repository.PageInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 首页服务
 * <AUTHOR>
 * @date 2025/06/20
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PageInfoCaseServiceImpl implements PageInfoCaseService {

    private final PageInfoRepository pageInfoRepository;

    private final RedisUtil redisUtil;

    private final ModuleCaseService moduleService;

    @Override
    @Transactional
    public void pageInfoRelease() {
        // 查找所有定时发布且时间已到的页面
        List<PageInfoEntity> scheduledPages = pageInfoRepository.findScheduledPages();
        if (!scheduledPages.isEmpty()) {
            PageInfoEntity pageToPublish = scheduledPages.get(0); // 最早的页面
            pageInfoRepository.publishScheduledPage(pageToPublish);
            //生成当前首页缓存
            redisUtil.set(RedisKeyConstants.CURRENT_HOME_PAGE, String.valueOf(pageToPublish.getPageId()), true);
            moduleService.handleReleasePage(pageToPublish.getPageId(), pageToPublish.getActivityId());
        }
    }
}
