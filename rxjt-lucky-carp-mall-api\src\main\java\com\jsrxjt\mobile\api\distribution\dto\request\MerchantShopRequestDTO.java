package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * @Description: 扫码提货附近门店请求参数
 * @Author: ywt
 * @Date: 2025-05-27 13:39
 * @Version: 1.0
 */
@Data
@Schema(description = "商户门店请求参数")
public class MerchantShopRequestDTO {
    /*@Schema(description = "第三方商户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "第三方商户id不能为空")
    private String thirdId;//第三方商户id*/
    @Schema(description = "扫码付应用的ID")
    @NotNull(message = "扫码付应用id不能为空")
    private Long appId;
    @Schema(description = "地址id")
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
    /*@Schema(description = "城市编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "城市编码不能为空")
    private String cityCode;//城市编码*/
    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "页码不能为空")
    private Integer page;
    @Schema(description = "每页数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "每页数量不能为空")
    private Integer pageSize;
    /*@Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "纬度不能为空")
    private String latitude;
    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "经度不能为空")
    private String longitude;*/
}
