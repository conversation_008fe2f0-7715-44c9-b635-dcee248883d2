package com.jsrxjt.mobile.api.ticket.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 卡券发放通知请求
 *
 * <AUTHOR>
 * @since 2025-11-13
 */
@Data
public class TicketGiveOutNotifyRequest {

  /**
   * appid
   */
  @NotBlank(message = "appid不能为空")
  private String appid;

  /**
   * 时间戳
   */
  @NotNull(message = "时间戳不能为空")
  private Long timestamp;

  /**
   * 随机字符串
   */
  @NotBlank(message = "随机字符串不能为空")
  private String nounce;

  /**
   * 签名密钥
   */
  @NotBlank(message = "签名密钥不能为空")
  private String signature;

  /**
   * 卡券列表（JSON格式字符串）
   */
  @NotBlank(message = "卡券列表不能为空")
  private String cardList;

  /**
   * 卡券号
   */
  @NotBlank(message = "卡券号不能为空")
  private String couponNumber;

  /**
   * 卡券信息内部类
   * 用于解析cardList JSON字符串
   */
  @Data
  public static class CardInfo {

    /**
     * 编号
     */
    private String id;

    /**
     * 卡号
     */
    private String code;

    /**
     * 卡密
     */
    private String pass;

    /**
     * crc校验码
     */
    private String crc;

    /**
     * 短链接
     */
    private String url;

    /**
     * 短链接密码
     */
    @JSONField(name = "url_pass")
    private String urlPass;

    /**
     * 卡券类型
     * 101: 平台自发券
     * 201: 卡密+卡号
     * 202: 卡号或卡密
     * 203: 卡号+卡密+校验码
     * 301: 链接类
     * 302: 链接+验证码
     * 303: 链接+卡号+验证码
     */
    private Integer type;

    /**
     * 批次号
     */
    @JSONField(name = "batch_num")
    private String batchNum;

    /**
     * 过期时间（YYYY-MM-DD格式，空表示永久有效）
     */
    @JSONField(name = "valid_time")
    private String validTime;

    /**
     * 用户卡券ID
     */
    @JSONField(name = "coupon_member_id")
    private String couponMemberId;

    /**
     * 卡管平台核销页地址
     */
    @JSONField(name = "h5_url")
    private String h5Url;
  }
}
