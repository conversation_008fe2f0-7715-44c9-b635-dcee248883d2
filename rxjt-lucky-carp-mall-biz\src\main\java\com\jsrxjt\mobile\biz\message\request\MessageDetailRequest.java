package com.jsrxjt.mobile.biz.message.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(name="MessageDetailRequest", description = "瑞祥通知消息条件")
public class MessageDetailRequest {
    @Schema(description = "当前页")
    @NotNull(message = "当前页不能为空")
    private Long page;
    @Schema(description = "每页条数")
    @NotNull(message = "每页条数不能为空")
    private Long size;
}
