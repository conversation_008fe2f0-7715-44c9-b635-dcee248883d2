package com.jsrxjt.mobile.api.distribution.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 支付结果通知
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "支付状态查询")
public class PickChannelOrderResultResponseDTO {

    @Schema(description = "福鲤圈订单号")
    @JSONField(name = "out_order_sn")
    private String out_order_sn;

    @Schema(description = "提货券中台平台订单流水号")
    @JSONField(name = "order_no")
    private String order_no;

    @Schema(description = "提货券中台平台交易流水号(对应订单表中dist_trade_no)")
    @JSONField(name = "trade_no")
    private String trade_no;

    @Schema(description = "状态 00支付成功 01待支付 02支付失败(含取消支付)")
    @JSONField(name = "trade_status")
    private String trade_status;

    @Schema(description = "交易时间 非成功状态统一留空")
    @JSONField(name = "trade_time")
    private String trade_time;

}
