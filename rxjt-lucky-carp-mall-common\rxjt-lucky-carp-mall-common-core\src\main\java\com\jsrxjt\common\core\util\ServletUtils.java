package com.jsrxjt.common.core.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR> Cheng
 * @Date: Feb 9, 2012 2:03:44 AM
 * @Version
 */
public class ServletUtils {

    /**
     * @return
     * @return String
     * @Description: 获取根目录
     * <AUTHOR>
     * @Date: Feb 9, 2012 2:07:30 AM
     */
    public static String serverRootDirectory() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        return attr.getRequest().getRealPath(File.separator);
    }

    /**
     * @return
     * @return String
     * @Description: 获取请求地址
     * <AUTHOR> Cheng
     * @Date: Feb 9, 2012 2:08:15 AM
     */
    public static String serverUrl() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = attr.getRequest();
        String temp = request.getServerPort() == 80 ? "" : ":" + request.getServerPort();
        return request.getScheme() + "://" + request.getServerName() + temp + request.getContextPath() + "/";
    }

    public static String serverUrl1() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = attr.getRequest();
        String temp = request.getServerPort() == 80 ? "" : ":" + request.getServerPort();
        return request.getScheme() + "://" + request.getServerName() + temp + request.getContextPath() + "/";
    }

    /**
     * 获取请求地址 不要项目名称
     *
     * @return
     */
    public static String getServerUrl() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = attr.getRequest();
        String temp = request.getServerPort() == 80 ? "" : ":" + request.getServerPort();
        return request.getScheme() + "://" + request.getServerName() + temp;
    }

    /**
     * @param request
     * @return
     * @return String
     * @Description: 获取当前请求的URL地址域参数
     * <AUTHOR> Cheng
     * @Date: Feb 9, 2012 2:10:02 AM
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getPrams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<String, Object>();
        Map requestParams = request.getParameterMap();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }
        return params;
    }

    public static String getRemortIp() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = attr.getRequest();
        if (request.getHeader("x-forwarded-for") == null) {
            return request.getRemoteAddr();
        }
        String ip = request.getHeader("x-forwarded-for");
        if (StringUtils.isNotBlank(ip)) {
            String[] ips = ip.split(",");
            ip = ips[0].trim();
        }
        return ip;
    }

    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        String localIP = "127.0.0.1";
        if ((ip == null) || (ip.length() == 0) || (ip.equalsIgnoreCase(localIP)) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if ((ip == null) || (ip.length() == 0) || (ip.equalsIgnoreCase(localIP)) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if ((ip == null) || (ip.length() == 0) || (ip.equalsIgnoreCase(localIP)) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if (ip.equals(localIP) || ip.equals("0:0:0:0:0:0:0:1")) {
                // 根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                }
                ip = inet.getHostAddress();
            }
        }
        if (StringUtils.isNotBlank(ip)) {
            String[] ips = ip.split(",");
            ip = ips[0].trim();
        }
        return ip;
    }

    public static String getUserAgent(HttpServletRequest request) {

        String ua = request.getHeader("user-agent");

        return ua == null ? "" : ua;

    }

    /**
     * 提取参数
     * @param httpServletRequest 请求
     * @return 参数
     */
    public static Map<String, String> extractParamsFromRequest(HttpServletRequest httpServletRequest) throws IOException {
        Map<String, String> params = new HashMap<>();

        // 方法1：先尝试获取标准参数
        httpServletRequest.getParameterMap().forEach((key, values) -> {
            if (values.length > 0) {
                params.put(key, values[0]);
            }
        });
        // 方法2：如果标准参数为空，读取原始请求体
        if (params.isEmpty()) {
            String body = httpServletRequest.getReader().lines().collect(Collectors.joining());
            if (!body.trim().isEmpty()) {
                String[] pairs = body.split("&");
                for (String pair : pairs) {
                    int eqIndex = pair.indexOf('=');
                    if (eqIndex > 0) {
                        String key = pair.substring(0, eqIndex);
                        String value = pair.substring(eqIndex + 1);
                        params.put(key, URLDecoder.decode(value, "UTF-8"));
                    }
                }
            }
        }
        return params;
    }
}
