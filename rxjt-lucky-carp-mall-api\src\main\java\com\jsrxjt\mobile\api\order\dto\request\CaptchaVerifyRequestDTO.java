package com.jsrxjt.mobile.api.order.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 图形验证码验证请求DTO
 * 
 * <AUTHOR>
 * @since 2025/11/25
 */
@Data
@Schema(description = "图形验证码验证请求DTO")
public class CaptchaVerifyRequestDTO {

    /**
     * 验证码唯一标识
     */
    @NotBlank(message = "验证码ID不能为空")
    @Schema(description = "验证码唯一标识", example = "captcha_123456789", required = true)
    private String captchaId;

    /**
     * 用户输入的验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Schema(description = "用户输入的验证码", example = "ABCD", required = true)
    private String code;
}
