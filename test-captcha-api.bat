@echo off
echo 测试图形验证码接口
echo.

echo 1. 编译项目...
call mvn clean compile -f rxjt-lucky-carp-mall-order/pom.xml -q

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 2. 运行单元测试...
call mvn test -Dtest=CaptchaCaseServiceImplTest -f rxjt-lucky-carp-mall-biz/pom.xml

echo.
echo 测试完成！
echo.
echo 接口信息：
echo - 接口路径：POST /v1/order/captcha
echo - 功能：生成图形验证码
echo - 请求参数：width, height, codeCount, lineCount（可选）
echo - 响应：captchaId, captchaImage（Base64）, expireTime
echo.
pause
