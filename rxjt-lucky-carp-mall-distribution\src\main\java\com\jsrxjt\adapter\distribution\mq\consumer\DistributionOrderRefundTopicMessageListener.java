package com.jsrxjt.adapter.distribution.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.jsrxjt.mobile.biz.distribution.service.DistributionAfterSalesService;
import com.jsrxjt.mobile.domain.distribution.types.DistributionOrderRefundMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.MessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 分销应用退款通知事件消息监听器
 * <AUTHOR>
 * @date 2025-10-13
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class DistributionOrderRefundTopicMessageListener implements MessageListener {

    private final DistributionAfterSalesService distributionAfterSalesService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        try {
            String msgBody  = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
            log.info("distribution_order_refund_consumer_group received message {}",messageView);
            DistributionOrderRefundMessage event = JSON.parseObject(msgBody, DistributionOrderRefundMessage.class);
            distributionAfterSalesService.handleDistributionRefundCallBack(event);
            log.info("distribution_order_refund_consumer_group 消费完成 message {}",messageView);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("处理应用退款通知消息异常", e);
            return ConsumeResult.FAILURE;
        }
    }
}
