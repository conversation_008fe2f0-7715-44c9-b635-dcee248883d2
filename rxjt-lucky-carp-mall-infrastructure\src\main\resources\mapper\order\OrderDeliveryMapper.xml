<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.order.persistent.mapper.OrderDeliveryMapper">

  <resultMap id="BaseResultMap" type="com.jsrxjt.mobile.infra.order.persistent.po.OrderDeliveryPO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="order_id" jdbcType="BIGINT" property="orderId"/>
    <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId"/>
    <result column="mini_sku_id" jdbcType="BIGINT" property="miniSkuId"/>
    <result column="delivery_type" jdbcType="TINYINT" property="deliveryType"/>
    <result column="delivery_status" jdbcType="TINYINT" property="deliveryStatus"/>
    <result column="flat_product_type" jdbcType="INTEGER" property="flatProductType"/>
    <result column="coupon_verification_type" jdbcType="INTEGER" property="couponVerificationType"/>
    <result column="coupon_code" jdbcType="VARCHAR" property="couponCode"/>
    <result column="coupon_pin" jdbcType="VARCHAR" property="couponPin"/>
    <result column="coupon_crc" jdbcType="VARCHAR" property="couponCrc"/>
    <result column="coupon_url" jdbcType="VARCHAR" property="couponUrl"/>
    <result column="coupon_url_pass" jdbcType="VARCHAR" property="couponUrlPass"/>
    <result column="coupon_batch_no" jdbcType="VARCHAR" property="couponBatchNo"/>
    <result column="brand_id" jdbcType="BIGINT" property="brandId"/>
    <result column="valid_date" jdbcType="DATE" property="validDate"/>
    <result column="delivery_content" jdbcType="CHAR" property="deliveryContent"/>
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
    <result column="recharge_account" jdbcType="VARCHAR" property="rechargeAccount"/>
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName"/>
    <result column="receiver_mobile" jdbcType="VARCHAR" property="receiverMobile"/>
    <result column="receiver_address" jdbcType="VARCHAR" property="receiverAddress"/>
    <result column="express_company" jdbcType="VARCHAR" property="expressCompany"/>
    <result column="express_no" jdbcType="VARCHAR" property="expressNo"/>
    <result column="delivery_error_code" jdbcType="VARCHAR" property="deliveryErrorCode"/>
    <result column="delivery_error_msg" jdbcType="VARCHAR" property="deliveryErrorMsg"/>
    <result column="delivery_remark" jdbcType="VARCHAR" property="deliveryRemark"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="mod_time" jdbcType="TIMESTAMP" property="modTime"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, order_no, order_item_id, mini_sku_id, delivery_type, delivery_status, 
	flat_product_type, coupon_verification_type, coupon_code, coupon_pin, coupon_crc, coupon_url, coupon_url_pass, 
	coupon_batch_no, brand_id, valid_date, delivery_content, delivery_time, recharge_account, receiver_name, 
	receiver_mobile, receiver_address, express_company, express_no, delivery_error_code, delivery_error_msg, delivery_remark, 
	create_time, mod_time, del_flag
  </sql>
  <update id="updateDelFlag">
    update t_order_delivery
    set del_flag = #{delFlag}
    where id in
    <foreach item="item" collection="idList" separator="," open="(" close=")">
      #{item}
    </foreach>
    <if test="originDelFlag != null">
        and del_flag = #{originDelFlag}
    </if>
  </update>


  <select id="selectCustomerCouponPackageBrandList"
          parameterType="com.jsrxjt.mobile.domain.order.query.CouponPackageListQuery"
          resultType="com.jsrxjt.mobile.infra.order.persistent.po.CouponPackageBrandPO">
    SELECT
      pb.brand_name as brandName,
      COUNT(DISTINCT tod.id) AS couponNum,
      pb.brand_logo as brandLogo,
      pb.id as brandId, MAX(toi.create_time) AS latest_create_time
      FROM
      t_order_delivery tod
      inner join
      t_order_item toi on tod.order_item_id  = toi.id
      left JOIN
      package_sub_sku pss ON pss.id = tod.mini_sku_id and toi.product_type = 2
      left join
      package_spu ps on pss.package_spu_id = ps.id and toi.product_type = 2
      left JOIN
      coupon_goods_sku cgs ON cgs.coupon_sku_id  = tod.mini_sku_id and toi.product_type = 1
      left join
      coupon_goods cg on cgs.coupon_spu_id = cg.coupon_spu_id and toi.product_type = 1
      left JOIN
      product_brand pb on cg.brand_id = pb.id or ps.brand_id = pb.id
    WHERE
      toi.customer_id = #{param.customerId}
      and tod.delivery_status = 2
      and tod.delivery_type = 1
      <if test="param.delFlag != null">
        AND tod.del_flag = #{param.delFlag}
      </if>
      <if test="param.delFlag == null">
        AND tod.del_flag = 0
      </if>
      <if test="param.queryName != null">
        AND (
        (toi.product_type = 1 AND CONCAT(pb.brand_name, cgs.amount_name) LIKE CONCAT('%', #{param.queryName}, '%')) OR
        (toi.product_type = 2 AND CONCAT(pb.brand_name, pss.amount_name) LIKE CONCAT('%', #{param.queryName}, '%'))
        )
      </if>
    GROUP BY
      pb.id, pb.brand_name
    ORDER BY
      latest_create_time desc
  </select>

  <select id="selectCustomerCouponPackageList"
          parameterType="com.jsrxjt.mobile.domain.order.query.CouponPackageListQuery"
          resultType="com.jsrxjt.mobile.infra.order.persistent.po.CouponPackageListPO">
    SELECT
    tod.id as couponPackageId,
    CASE
    WHEN toi.product_type = 1 THEN cgs.amount
    WHEN toi.product_type= 2 THEN pss.amount
    END AS amount,
    CASE
    WHEN toi.product_type = 1 THEN cgs.amount_name
    WHEN toi.product_type= 2 THEN pss.amount_name
    END AS amountName,
    CASE
    WHEN toi.product_type = 1 THEN cg.flq_type
    WHEN toi.product_type= 2 THEN pss.flq_type
    END AS flqType,
    toi.create_time as createTime,
    pb.id as brandId,
    tod.coupon_code,
    tod.coupon_pin,
    tod.coupon_crc,
    tod.coupon_url,
    tod.coupon_url_pass,
    tod.coupon_verification_type
    FROM
    t_order_delivery tod
    inner join
    t_order_item toi on tod.order_item_id  = toi.id
    left JOIN
    package_sub_sku pss ON pss.id = tod.mini_sku_id and toi.product_type = 2
    left join
    package_spu ps on pss.package_spu_id = ps.id and toi.product_type = 2
    left JOIN
    coupon_goods_sku cgs ON cgs.coupon_sku_id  = tod.mini_sku_id and toi.product_type = 1
    left join
    coupon_goods cg on cgs.coupon_spu_id = cg.coupon_spu_id and toi.product_type = 1
    left JOIN
    product_brand pb on cg.brand_id = pb.id or ps.brand_id = pb.id
    WHERE
    toi.customer_id = #{param.customerId}
    and tod.delivery_status = 2
    and tod.delivery_type = 1
    <if test="param.brandIdList != null">
      AND pb.id IN
      <foreach collection="param.brandIdList" item="brandId" separator="," open="(" close=")">
        #{brandId}
      </foreach>
    </if>
    <if test="param.queryName != null">
      AND (
      (toi.product_type = 1 AND CONCAT(pb.brand_name, cgs.amount_name) LIKE CONCAT('%', #{param.queryName}, '%')) OR
      (toi.product_type = 2 AND CONCAT(pb.brand_name, pss.amount_name) LIKE CONCAT('%', #{param.queryName}, '%'))
      )
    </if>
    <if test="param.delFlag != null">
      AND tod.del_flag = #{param.delFlag}
    </if>
    <if test="param.delFlag == null">
      AND tod.del_flag = 0
    </if>
  </select>

    <select id="selectCouponPackageListByPickProductId"
            resultType="com.jsrxjt.mobile.infra.order.persistent.po.CouponPackageDetailPO">
      select
        tod.coupon_url as couponUrl,
        to2.product_name as productName,
        to2.delivery_time as orderTime,
        cg.flq_type  as flqType
      from
        rxflq_db.t_order_delivery tod
          join rxflq_db.t_order to2  on to2.id  = tod.order_id
          join rxflq_db.coupon_goods cg on to2.product_spu_id = cg.coupon_spu_id
      where
        to2.customer_id = #{customerId}
        and cg.pick_product_id = #{pickProductId}
        and to2.delivery_status =2
        and to2.payment_status =1
        and tod.delivery_type =1
        and tod.delivery_status =2
        and tod.del_flag =0
        and cg.flq_type =9
        and cg.is_self_coupon =1

    </select>

  <select id="selectCustomerCouponPackageNum" resultType="int">
    SELECT
      count(tod.id)
    FROM t_order_delivery tod
    INNER JOIN t_order o ON o.id = tod.order_id
    WHERE o.customer_id = #{customerId}
    and tod.delivery_status = 2
    and tod.delivery_type = 1
    <if test="delFlag != null">
      AND tod.del_flag = #{delFlag}
    </if>
    <if test="delFlag == null">
      AND tod.del_flag = 0
    </if>
  </select>

  <select id="selectOrderDeliveryByCustomerId" resultMap="BaseResultMap">
    select tod.* from t_order_delivery tod
                        inner join t_order_item toi on tod.order_item_id = toi.id
    where toi.customer_id = #{customerId} and tod.id = #{id}
  </select>
</mapper>
