server:
  port: 18080
  shutdown: graceful
spring:
  application:
    name: mobile-web-coupon
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:localhost:8848}
        namespace: @profiles.active@
        group: FLQ_GROUP
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: @profiles.active@
        group: FLQ_GROUP
      username: ${NACOS_NAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
  config:
    import:
      - optional:nacos:flq-datasource-config.yml
      - optional:nacos:flq-mybatis-config.yml
      - optional:nacos:flq-redis-config.yml
      - optional:nacos:flq-rocketmq-config.yml
      - optional:nacos:flq-oss-config.yml
      - optional:nacos:flq-feign-config.yml
      - optional:nacos:flq-sms-config.yml
      - optional:nacos:flq-api-sentinel-config.yml
      - optional:nacos:flq-common-config.properties
      - optional:nacos:flq-es-config.yml
      - optional:nacos:flq-sa-token-config.yml
      - optional:nacos:flq-id-config.yml
      - optional:nacos:flq-tencent-map-config.yml
      - optional:nacos:flq-thirdcard-config.yml
      - optional:nacos:flq-distribution-config.yml
#  datasource:
#    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
#    url: jdbc:shardingsphere:classpath:sharding/<EMAIL>@.yaml
logging:
  config: classpath:logback/<EMAIL>@.xml
  level:
    org:
      apache:
        ibatis=DEBUG: DEBUG
management:
  health:
    solr:
      enabled: false
  endpoints:
    web:
      exposure:
        include:
          - health
          - info
          - prometheus
  metrics:
    tags:
      application: ${spring.application.name}