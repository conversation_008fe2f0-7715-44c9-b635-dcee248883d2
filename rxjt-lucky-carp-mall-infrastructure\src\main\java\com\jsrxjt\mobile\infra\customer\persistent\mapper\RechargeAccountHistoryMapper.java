package com.jsrxjt.mobile.infra.customer.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.customer.persistent.po.RechargeAccountHistoryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户历史充值账号Mapper
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/8/8
 */
@Mapper
public interface RechargeAccountHistoryMapper extends CommonBaseMapper<RechargeAccountHistoryPO> {
    
    /**
     * 根据客户ID和产品信息查询历史充值账号
     * 
     * @param customerId 客户ID
     * @param productSpuId 产品SPU ID
     * @param productType 产品类型
     * @param accountType 账户类型
     * @return 历史充值账号列表
     */
    List<RechargeAccountHistoryPO> selectByCustomerAndProduct(@Param("customerId") Long customerId,
                                                              @Param("productSpuId") Long productSpuId,
                                                              @Param("productType") Integer productType,
                                                              @Param("accountType") Integer accountType);
    
    /**
     * 根据客户ID查询历史充值账号
     * 
     * @param customerId 客户ID
     * @return 历史充值账号列表
     */
    List<RechargeAccountHistoryPO> selectByCustomerId(@Param("customerId") Long customerId);
    
    /**
     * 条件查询历史充值账号
     * 
     * @param query 查询条件
     * @return 历史充值账号列表
     */
    List<RechargeAccountHistoryPO> selectByCriteria(@Param("query") RechargeAccountHistoryPO query);
}
