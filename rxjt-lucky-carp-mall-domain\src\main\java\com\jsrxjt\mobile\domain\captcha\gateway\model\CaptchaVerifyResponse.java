package com.jsrxjt.mobile.domain.captcha.gateway.model;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 验证码验证响应结构
 */
@AllArgsConstructor
@Data
public class CaptchaVerifyResponse {
    private boolean success;

    private String message;

    private String code;

    public static CaptchaVerifyResponse success() {

        return new CaptchaVerifyResponse(true,"验证成功","0000");
    }

    public static CaptchaVerifyResponse failure(String message, String errorCode) {
        return new CaptchaVerifyResponse(false, message, errorCode);
    }
}
