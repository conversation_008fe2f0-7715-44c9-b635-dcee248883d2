package com.jsrxjt.mobile.infra.product.persistent.po;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 核销页-核销须知表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Getter
@Setter
@TableName("product_offset_page")
@Schema(name = "product_offset_page", description = "核销页-核销须知表")
public class ProductOffsetPagePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "核销页核销须知id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "产品spuid")
    @TableField("product_spu_id")
    private Long productSpuId;

    @Schema(description = "产品类型 1 卡券 2 套餐")
    @TableField("product_type")
    private Byte productType;

    @Schema(description = "适用场景")
    @TableField("scenarios")
    private String scenarios;


    @Schema(description = "背景色")
    @TableField("background_color")
    private String backgroundColor;

    @Schema(description = "核销页有效期")
    @TableField("validity_date")
    private String validityDate;

    @Schema(description = "备注")
    @TableField("remark")
    private String remark;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "创建人id")
    @TableField("create_id")
    private Long createId;

    @Schema(description = "删除标志 0:否 1:删除")
    @TableField("del_flag")
    private Byte delFlag;

    @Schema(description = "修改人id")
    @TableField("mod_id")
    private Long modId;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modTime;
}
