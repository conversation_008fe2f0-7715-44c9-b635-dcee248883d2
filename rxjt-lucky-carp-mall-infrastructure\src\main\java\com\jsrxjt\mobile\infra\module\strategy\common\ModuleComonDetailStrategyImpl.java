package com.jsrxjt.mobile.infra.module.strategy.common;

import com.jsrxjt.mobile.api.module.annotation.ModuleDetailTypeHandler;
import com.jsrxjt.mobile.api.module.types.ModuleDetailType;
import com.jsrxjt.mobile.domain.module.entity.ModuleDetailEntity;
import com.jsrxjt.mobile.domain.module.service.ModuleDetailStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 通用策略实现
 * <AUTHOR>
 * @date 2025/10/13
 */
@ModuleDetailTypeHandler({ModuleDetailType.COMMON})
@Component
@RequiredArgsConstructor
@Slf4j
public class ModuleComonDetailStrategyImpl extends ModuleDetailStrategy {
    @Override
    public ModuleDetailEntity updateModuleDetailEntityInfo(ModuleDetailEntity moduleDetailEntity) {
        return moduleDetailEntity;
    }
}
