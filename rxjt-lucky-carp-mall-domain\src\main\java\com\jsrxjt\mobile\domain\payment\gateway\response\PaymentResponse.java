package com.jsrxjt.mobile.domain.payment.gateway.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 发起支付响应
 * <AUTHOR>
 * @since 2025/8/15
 */
@Data
public class PaymentResponse {
    
    /**
     * 业务订单号
     */
    @JSONField(name = "order_no")
    private String orderNo;
    
    /**
     * 交易单号
     */
    @JSONField(name = "trade_no")
    private String tradeNo;
    
    /**
     * 是否存在其他支付方式 Y 是存在 N 不存在
     */
    @JSONField(name = "is_other")
    private Boolean isOther;
    
    /**
     * 其他支付通道 不存在其他支付渠道的时候为空
     */
    @JSONField(name = "other_channel")
    private String otherChannel;
    
    /**
     * app支付参数
     */
    @JSONField(name = "app_pay_params")
    private String appPayParams;
    
    /**
     * 小程序支付参数
     */
    @JSONField(name = "mini_pay_params")
    private String miniPayParams;
    
    /**
     * 支付状态 WAIT 待交易 IN 交易中 FAIL 交易失败 SUCCESS 交易成功 CLOSE 交易关闭
     */
    @JSONField(name = "pay_status")
    private String payStatus;
}