package com.jsrxjt.mobile.api.scanPay.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 线下扫码展示码请求dto
 * @Author: ZY
 * @Date: 20250530
 */
@Data
@Schema(description = "线下扫码展示码请求dto")
public class OfflineScanCodeRequestDTO {
    @Schema(description = "应用appId")
    @NotNull(message = "应用appId不能为空")
    private Long appId;

    @Schema(description = "来源码随机数")
    @NotBlank(message = "来源码不能为空")
    private String sourceCode;

//    @Schema(description = "区域id")
//    @NotBlank(message = "区域id不能为空")
//    private Integer regionId;

}
