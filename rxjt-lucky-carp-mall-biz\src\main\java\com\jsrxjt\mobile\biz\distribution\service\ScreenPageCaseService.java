package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.mobile.api.distribution.dto.request.ScreenPageClickRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.ScreenPageRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.ScreenPageResponseDTO;
import java.util.List;

/**
 * @Description: 启动页接口
 * @Author: ywt
 * @Date: 2025-06-09 15:10
 * @Version: 1.0
 */
public interface ScreenPageCaseService {
    List<ScreenPageResponseDTO> getScreenList(ScreenPageRequestDTO requestDTO);
    boolean clickScreenPage(ScreenPageClickRequestDTO requestDTO);
}
