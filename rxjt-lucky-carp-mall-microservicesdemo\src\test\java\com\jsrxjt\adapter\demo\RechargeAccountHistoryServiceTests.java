package com.jsrxjt.adapter.demo;

import com.jsrxjt.mobile.domain.customer.dp.RechargeAccount;
import com.jsrxjt.mobile.domain.customer.entity.RechargeAccountHistoryEntity;
import com.jsrxjt.mobile.domain.customer.service.RechargeAccountHistoryService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 用户充值账号历史服务测试类
 * 
 * <AUTHOR>
 * @description 测试RechargeAccountHistoryService的save方法
 * @since 2025/8/5
 */
@SpringBootTest
public class RechargeAccountHistoryServiceTests {

    @Autowired
    private RechargeAccountHistoryService rechargeAccountHistoryService;

    @Test
    public void testSave() {
        System.out.println("=== 测试保存充值账号历史 ===");

        // 创建充值账号对象 - 使用全参数构造函数
        RechargeAccount account = new RechargeAccount(1115965283836637203L, 280L, 1, 1, "***********");

        // 调用保存方法
        RechargeAccountHistoryEntity result = rechargeAccountHistoryService.save(account);

        System.out.println("保存成功，ID: " + result.getId());
        System.out.println("客户ID: " + result.getCustomerId());
        System.out.println("产品SPU ID: " + result.getProductSpuId());
        System.out.println("产品类型: " + result.getProductType());
        System.out.println("账户类型: " + result.getAccountType());
        System.out.println("账户: " + result.getAccount());
        System.out.println("=== 测试完成 ===");
    }

    @Test
    public void testSaveQQAccount() {
        System.out.println("=== 测试保存QQ账号充值历史 ===");

        // 创建QQ充值账号对象 - 使用全参数构造函数
        RechargeAccount account = new RechargeAccount(1002L, 2002L, 402, 2, "*********");

        // 调用保存方法
        RechargeAccountHistoryEntity result = rechargeAccountHistoryService.save(account);

        // 验证结果
        assert result != null : "保存结果不能为空";
        assert result.getId() != null : "保存后应该有ID";
        assert result.getCustomerId().equals(1002L) : "客户ID应该匹配";
        assert result.getProductType().equals(402) : "产品类型应该匹配";
        assert result.getAccountType().equals(2) : "账户类型应该匹配";
        assert result.getAccount().equals("*********") : "QQ账户应该匹配";

        System.out.println("QQ账号保存成功，ID: " + result.getId());
        System.out.println("=== QQ账号测试完成 ===");
    }

    @Test
    public void testSaveMultipleAccounts() {
        System.out.println("=== 测试保存多个充值账号历史 ===");

        // 测试同一客户的多个账号
        Long customerId = 1003L;

        // 第一个账号 - 使用全参数构造函数
        RechargeAccount account1 = new RechargeAccount(customerId, 2003L, 102, 1, "***********");
        RechargeAccountHistoryEntity result1 = rechargeAccountHistoryService.save(account1);

        // 第二个账号 - 使用全参数构造函数
        RechargeAccount account2 = new RechargeAccount(customerId, 2004L, 402, 2, "*********");
        RechargeAccountHistoryEntity result2 = rechargeAccountHistoryService.save(account2);

        // 验证结果
        assert result1 != null && result2 != null : "两个保存结果都不能为空";
        assert !result1.getId().equals(result2.getId()) : "两个记录的ID应该不同";
        assert result1.getCustomerId().equals(result2.getCustomerId()) : "客户ID应该相同";

        System.out.println("第一个账号ID: " + result1.getId() + ", 账户: " + result1.getAccount());
        System.out.println("第二个账号ID: " + result2.getId() + ", 账户: " + result2.getAccount());
        System.out.println("=== 多账号测试完成 ===");
    }
}


