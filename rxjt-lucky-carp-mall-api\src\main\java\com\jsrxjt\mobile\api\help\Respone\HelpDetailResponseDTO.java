package com.jsrxjt.mobile.api.help.Respone;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class HelpDetailResponseDTO {

    @Schema(description = "帮助id")
    private Integer helpId;

    @Schema(description = "分类id")
    private Integer catId;

    @Schema(description = "问题")
    private String helpQuestion;

    @Schema(description = "帮助类型(1:图文详情 2:单链接)")
    private Byte type;

    @Schema(description = "帮助回答(类型1对应图文富文本,类型2对应链接)")
    private String helpAnswer;

    private Long clickNum;

}
