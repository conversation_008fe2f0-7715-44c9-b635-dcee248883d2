package com.jsrxjt.mobile.domain.bailian.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 百联获取付款二维码返回参数
 * <AUTHOR>
 * @Date 2025/10/28
 **/
@Data
@Schema(description = "百联获取付款二维码返回参数")
public class BaiLianAccessResponse {

    @Schema(description = "百联第三方号")
    @JSONField(name = "thd_user_id")
    private String thdUserId;

    @Schema(description = "安付宝卡包ID")
    @JSONField(name = "wallet_id")
    private String walletId;

    @Schema(description = "付款二维码")
    @JSONField(name = "pay_qr_code")
    private String payQRCode;

    @Schema(description = "有效时间:单位-秒")
    @JSONField(name = "valid_time")
    private String validTime;
}
