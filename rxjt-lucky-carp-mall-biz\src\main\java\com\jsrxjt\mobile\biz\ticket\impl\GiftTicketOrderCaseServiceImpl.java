package com.jsrxjt.mobile.biz.ticket.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponNotifyResponseDTO;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.api.order.dto.request.CouponOrderCreatedNotifyDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketGiveOutNotifyRequest;
import com.jsrxjt.mobile.api.ticket.response.TicketNotifyResponseDTO;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.ticket.GiftTicketOrderCaseService;
import com.jsrxjt.mobile.domain.coupon.entity.CouponCardEntity;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformFactory;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketOrderEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketBrandEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketDeliveryEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.gateway.TicketPlatformGateway;
import com.jsrxjt.mobile.domain.ticket.repository.GiftTicketOrderRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketBrandRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketDeliveryRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class GiftTicketOrderCaseServiceImpl implements GiftTicketOrderCaseService {

    private final GiftTicketOrderRepository giftTicketOrderRepository;
    private final TicketRepository ticketRepository;
    private final TicketBrandRepository ticketBrandRepository;
    private final TicketDeliveryRepository ticketDeliveryRepository;
    private final DistributedLock distributedLock;
    private final TicketPlatformGateway ticketPlatformGateway;

    private static final String GIFT_TICKET_ORDER_LOCK_PREFIX = "gift_ticket_order_notify_lock:";

    @Override
    public CouponNotifyResponseDTO processCouponOrderCreatedNotify(CouponOrderCreatedNotifyDTO notifyDTO) {
        log.info("[入口参数]处理下单赠券的券订单已被创建的通知，入参：{}", notifyDTO.getRequestBody());
        String outOrderSn = notifyDTO.getOutOrderSn();
        String lockKey = GIFT_TICKET_ORDER_LOCK_PREFIX + outOrderSn;

        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取下单赠券订单通知处理锁失败，券订单号：{}", outOrderSn);
                return CouponNotifyResponseDTO.fail("系统繁忙，请稍后重试");
            }

            // 处理下单赠券订单创建流程
            return processGiftTicketOrderCreated(notifyDTO, outOrderSn);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取分布式锁被中断，券订单号：{}", outOrderSn, e);
            return CouponNotifyResponseDTO.fail("系统繁忙，请稍后重试");
        } catch (BizException e) {
            log.error("处理下单赠券订单通知业务异常，券订单号：{}，错误信息：{}", outOrderSn, e.getMsg(), e);
            return CouponNotifyResponseDTO.fail(e.getMsg());
        } catch (Exception e) {
            log.error("处理下单赠券订单通知异常，券订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
            return CouponNotifyResponseDTO.fail("系统异常：" + e.getMessage());
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    /**
     * 处理下单赠券订单创建流程
     */
    private CouponNotifyResponseDTO processGiftTicketOrderCreated(CouponOrderCreatedNotifyDTO notifyDTO,
            String outOrderSn) {
        log.info("[业务逻辑]开始处理下单赠券订单创建，券订单号：{}", outOrderSn);

        // 1. 根据outOrderSn查询giftTicketOrder对象
        GiftTicketOrderEntity giftTicketOrder = giftTicketOrderRepository.findByTicketOrderNo(outOrderSn);
        if (giftTicketOrder == null) {
            log.error("赠券订单不存在，券订单号：{}", outOrderSn);
            throw new BizException("赠券订单不存在");
        }

        // 2. 校验外部订单号是否和orderSn一致
        if (!notifyDTO.getOrderSn().equals(giftTicketOrder.getExternalOrderNo())) {
            log.error("外部订单号不一致，券订单号：{}，通知中的orderSn：{}，赠券订单中的externalOrderNo：{}",
                    outOrderSn, notifyDTO.getOrderSn(), giftTicketOrder.getExternalOrderNo());
            throw new BizException("订单信息不匹配");
        }

        // 3. 根据ticketId查询ticket表得到ticket信息
        TicketEntity ticket = ticketRepository.getTicketById(giftTicketOrder.getTicketId());
        if (ticket == null) {
            log.error("优惠券不存在，券订单号：{}，ticketId：{}", outOrderSn, giftTicketOrder.getTicketId());
            throw new BizException("优惠券不存在");
        }

        // 4. 根据brandId查询ticket_brand表得到brandName
        TicketBrandEntity ticketBrand = ticketBrandRepository.getTicketBrandById(ticket.getBrandId());
        if (ticketBrand == null) {
            log.error("优惠券品牌不存在，券订单号：{}，brandId：{}", outOrderSn, ticket.getBrandId());
            throw new BizException("优惠券品牌不存在");
        }

        // 5. 这里都是普通卡券，调用获卡接口
        List<CouponCardEntity> couponCards = obtainCouponCards(giftTicketOrder, ticket);

        // 6. 拿到卡信息之后结合前面的ticket和ticket_brand,
        // gift_ticket_order等信息，批量插入ticket_delivery表中
        batchInsertTicketDelivery(couponCards, giftTicketOrder, ticket, ticketBrand);

        log.info("下单赠券订单处理成功，券订单号：{}", outOrderSn);
        return CouponNotifyResponseDTO.success();
    }

    /**
     * 获取卡券信息
     */
    private List<CouponCardEntity> obtainCouponCards(GiftTicketOrderEntity giftTicketOrder, TicketEntity ticket) {
        log.info("[第三方服务调用]开始调用获卡接口发卡，券订单号：{}，券类型：{}",
                giftTicketOrder.getTicketOrderNo(), ticket.getTicketType());

        // 根据券类型获取对应的卡券平台策略
        CouponTypeEnum couponType = getCouponTypeByTicketType(ticket.getTicketType());
        if (couponType == null) {
            log.error("无法识别的券类型，券订单号：{}，券类型：{}", giftTicketOrder.getTicketOrderNo(), ticket.getTicketType());
            throw new BizException("无法识别的券类型");
        }

        log.info("[第三方服务调用]获取卡券平台策略，券类型：{}", couponType.getText());
        CouponPlatformStrategy couponPlatform = CouponPlatformFactory.getCouponPlatform(couponType);

        // 调用获卡接口获取卡券信息
        List<CouponCardEntity> couponCards = couponPlatform.getCouponCardInfo(giftTicketOrder.getExternalOrderNo());

        if (couponCards == null || couponCards.isEmpty()) {
            log.error("获卡失败，未获取到卡券信息，券订单号：{}", giftTicketOrder.getTicketOrderNo());
            throw new BizException("获卡失败");
        }

        return couponCards;
    }

    /**
     * 批量插入ticket_delivery表
     */
    private void batchInsertTicketDelivery(List<CouponCardEntity> couponCards, GiftTicketOrderEntity giftTicketOrder,
            TicketEntity ticket, TicketBrandEntity ticketBrand) {
        List<TicketDeliveryEntity> ticketDeliveries = new ArrayList<>();
        Date now = new Date();

        for (CouponCardEntity couponCard : couponCards) {
            TicketDeliveryEntity ticketDelivery = new TicketDeliveryEntity();
            ticketDelivery.setOrderId(giftTicketOrder.getOrderId());
            ticketDelivery.setTicketOrderNo(giftTicketOrder.getTicketOrderNo());
            ticketDelivery.setOrderNo(giftTicketOrder.getOrderNo());
            ticketDelivery.setExternalOrderNo(giftTicketOrder.getExternalOrderNo());
            ticketDelivery
                    .setTicketType(giftTicketOrder.getTicketType() != null ? ticket.getTicketType().byteValue() : null);
            ticketDelivery.setCustomerId(giftTicketOrder.getCustomerId());
            ticketDelivery.setStatus((byte) 1); // 1-未核销

            // 券信息
            ticketDelivery.setCenterCouponId(giftTicketOrder.getCenterCouponId());
            ticketDelivery.setTicketCode(couponCard.getCardCode());
            ticketDelivery.setTicketPin(couponCard.getCardPass());
            ticketDelivery.setTicketId(ticket.getTicketId());
            ticketDelivery.setTicketName(ticket.getTicketName());
            ticketDelivery.setTicketValidDate(couponCard.getValidTime() != null
                    ? Timestamp.valueOf(couponCard.getValidTime().atStartOfDay())
                    : null);
            ticketDelivery.setOffsetPageType(
                    ticket.getOffsetPageType() != null ? ticket.getOffsetPageType().byteValue() : null);

            // 品牌信息
            ticketDelivery.setBrandName(ticketBrand.getBrandName());
            ticketDelivery.setOffsetLogo(ticketBrand.getOffsetLogo());

            ticketDelivery.setCreateTime(now);
            ticketDelivery.setModTime(now);

            ticketDeliveries.add(ticketDelivery);
        }

        // 批量保存
        ticketDeliveryRepository.batchSave(ticketDeliveries);
        log.info("批量插入ticket_delivery成功，券订单号：{}，插入条数：{}",
                giftTicketOrder.getTicketOrderNo(), ticketDeliveries.size());
    }

    /**
     * 根据券类型获取卡管券类型枚举
     */
    private CouponTypeEnum getCouponTypeByTicketType(Integer ticketType) {
        if (ticketType == 0 || ticketType == 2) {
            return CouponTypeEnum.REGULAR; // 默认使用普通卡券策略
        }
        return null;
    }

    @Override
    public TicketNotifyResponseDTO processTicketGiveOutNotify(TicketGiveOutNotifyRequest request) {
        log.info("[入口参数]处理赠券卡券发放通知，入参：{}", JSON.toJSONString(request));

        String couponNumber = request.getCouponNumber();
        String lockKey = GIFT_TICKET_ORDER_LOCK_PREFIX + couponNumber;

        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey);
            if (!lockAcquired) {
                log.error("获取卡券发放通知处理锁失败，券号：{}", couponNumber);
                return TicketNotifyResponseDTO.builder()
                        .code(-1)
                        .message("系统繁忙，请稍后重试")
                        .build();
            }

            // 处理卡券发放通知流程
            return processTicketGiveOut(request, couponNumber);
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    /**
     * 处理卡券发放通知核心逻辑
     */
    private TicketNotifyResponseDTO processTicketGiveOut(TicketGiveOutNotifyRequest request, String couponNumber) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("[步骤1]开始验签，券号：{}", couponNumber);
            }
            // 1. 验签
            Map<String, Object> requestMap = JSON.parseObject(JSON.toJSONString(request), Map.class);
            if (!ticketPlatformGateway.verifySign(requestMap)) {
                log.error("卡券发放通知验签失败，券号：{}", couponNumber);
                return TicketNotifyResponseDTO.builder()
                        .code(-1)
                        .message("验签失败")
                        .build();
            }
            if (log.isDebugEnabled()) {
                log.debug("[步骤1]验签成功，券号：{}", couponNumber);
            }

            if (log.isDebugEnabled()) {
                log.debug("[步骤2]开始查询赠券订单，券号：{}", couponNumber);
            }
            // 2. 外部订单号记录的是券编号的第一个，根据couponNumber查询giftTicketOrder,卡券类型的赠券订单，券编号只有1个
            // 则两者相等
            GiftTicketOrderEntity giftTicketOrder = giftTicketOrderRepository
                    .findByExternalOrderNo(couponNumber);
            if (giftTicketOrder == null) {
                log.error("根据券号未找到赠券订单，券号：{}", couponNumber);
                return TicketNotifyResponseDTO.builder()
                        .code(1)
                        .message("未找到对应的赠券订单")
                        .build();
            }
            if (log.isDebugEnabled()) {
                log.debug("[步骤2]查询赠券订单成功，券号：{}，订单号：{}，当前pushStatus：{}",
                        couponNumber, giftTicketOrder.getTicketOrderNo(), giftTicketOrder.getPushStatus());
            }

            if (log.isDebugEnabled()) {
                log.debug("[步骤3]开始校验pushStatus，券号：{}", couponNumber);
            }
            // 3. 校验pushStatus是否是2，如果是2表示已经发成功了，直接忽略
            if (giftTicketOrder.getPushStatus() != null && giftTicketOrder.getPushStatus() == 2) {
                log.info("赠券订单已经发放成功，直接返回成功，券订单号：{}", giftTicketOrder.getTicketOrderNo());
                return TicketNotifyResponseDTO.builder()
                        .code(200)
                        .message("success")
                        .build();
            }
            if (log.isDebugEnabled()) {
                log.debug("[步骤3]pushStatus校验通过，可以继续处理，券号：{}", couponNumber);
            }

            if (log.isDebugEnabled()) {
                log.debug("[步骤4]开始查询ticket信息，券号：{}，ticketId：{}", couponNumber, giftTicketOrder.getTicketId());
            }
            // 4. 根据ticketId查询ticket表得到ticket信息
            TicketEntity ticket = ticketRepository.getTicketById(giftTicketOrder.getTicketId());
            if (ticket == null) {
                log.error("根据ticketId未找到票券信息，ticketId：{}", giftTicketOrder.getTicketId());
                return TicketNotifyResponseDTO.builder()
                        .code(2)
                        .message("未找到对应的券信息")
                        .build();
            }
            if (log.isDebugEnabled()) {
                log.debug("[步骤4]查询ticket信息成功，券号：{}，ticketName：{}，brandId：{}",
                        couponNumber, ticket.getTicketName(), ticket.getBrandId());
            }


            if (log.isDebugEnabled()) {
                log.debug("[步骤5]开始解析cardList，券号：{}，cardList长度：{}", couponNumber,
                        request.getCardList() != null ? request.getCardList().length() : 0);
            }
            // 6. 解析cardListJson，得到couponCards
            List<TicketGiveOutNotifyRequest.CardInfo> couponCards = JSON.parseArray(request.getCardList(),
                    TicketGiveOutNotifyRequest.CardInfo.class);
            if (CollectionUtils.isEmpty(couponCards)) {
                log.error("cardListJson为空，券号：{}", couponNumber);
                return TicketNotifyResponseDTO.builder()
                        .code(4)
                        .message("cardList为空")
                        .build();
            }
            if (log.isDebugEnabled()) {
                log.debug("[步骤5]解析cardList成功，券号：{}，卡券数量：{}", couponNumber, couponCards.size());
            }

            if (log.isDebugEnabled()) {
                log.debug("[步骤6]开始批量插入ticket_delivery，券号：{}，卡券数量：{}", couponNumber, couponCards.size());
            }
            // 7. 根据cardList信息结合前面的ticket和ticket_brand,
            // gift_ticket_order等信息，批量插入ticket_delivery表中
            batchInsertTicketDeliveryFromCardList(couponCards, giftTicketOrder, ticket);
            if (log.isDebugEnabled()) {
                log.debug("[步骤7]批量插入ticket_delivery成功，券号：{}", couponNumber);
            }

            if (log.isDebugEnabled()) {
                log.debug("[步骤8]开始更新订单状态，券号：{}，实际数量：{}", couponNumber, couponCards.size());
            }
            // 8. 更新giftTicketOrder的pushStatus为2，actualNum为卡券数量
            updateGiftTicketOrderStatus(giftTicketOrder, couponCards.size());
            if (log.isDebugEnabled()) {
                log.debug("[步骤8]更新订单状态成功，券号：{}", couponNumber);
            }

            log.info("卡券发放通知处理成功，券订单号：{}", giftTicketOrder.getTicketOrderNo());
            return TicketNotifyResponseDTO.builder()
                    .code(200)
                    .message("success")
                    .build();

        } catch (Exception e) {
            log.error("处理卡券发放通知异常，券号：{}", couponNumber, e);
            return TicketNotifyResponseDTO.builder()
                    .code(-1)
                    .message("处理异常：" + e.getMessage())
                    .build();
        }
    }

    /**
     * 根据cardList信息批量插入ticket_delivery表
     */
    private void batchInsertTicketDeliveryFromCardList(List<TicketGiveOutNotifyRequest.CardInfo> cardInfoList,
            GiftTicketOrderEntity giftTicketOrder,
            TicketEntity ticket) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("[批量插入]开始构建ticket_delivery数据，订单号：{}，卡券数量：{}",
                        giftTicketOrder.getTicketOrderNo(), cardInfoList.size());
            }

            List<TicketDeliveryEntity> ticketDeliveries = new ArrayList<>();
            Date now = new Date();

            for (TicketGiveOutNotifyRequest.CardInfo couponCard : cardInfoList) {
                if (log.isDebugEnabled()) {
                    log.debug("[批量插入]处理卡券信息，订单号：{}，卡券ID：{}，卡号：{}",
                            giftTicketOrder.getTicketOrderNo(), couponCard.getId(), couponCard.getCode());
                }
                TicketDeliveryEntity ticketDelivery = new TicketDeliveryEntity();
                ticketDelivery.setOrderId(giftTicketOrder.getOrderId());
                ticketDelivery.setTicketOrderNo(giftTicketOrder.getTicketOrderNo());
                ticketDelivery.setOrderNo(giftTicketOrder.getOrderNo());
                ticketDelivery.setExternalOrderNo(giftTicketOrder.getExternalOrderNo());
                ticketDelivery
                        .setTicketType(
                                giftTicketOrder.getTicketType() != null ? ticket.getTicketType().byteValue() : null);
                ticketDelivery.setCustomerId(giftTicketOrder.getCustomerId());
                ticketDelivery.setStatus((byte) 1); // 1-未核销

                // 券信息
                ticketDelivery.setCenterCouponId(giftTicketOrder.getCenterCouponId());
                ticketDelivery.setTicketCode(couponCard.getCode());
                ticketDelivery.setTicketPin(couponCard.getPass());
                ticketDelivery.setUrl(couponCard.getUrl());
                ticketDelivery.setUrlPass(couponCard.getUrlPass());
                ticketDelivery.setCrc(couponCard.getCrc());
                ticketDelivery.setBatchNum(couponCard.getBatchNum());
                ticketDelivery.setH5Url(couponCard.getH5Url());
                ticketDelivery.setCheckType(couponCard.getType());
                ticketDelivery.setTicketId(ticket.getTicketId());
                ticketDelivery.setTicketName(ticket.getTicketName());
                ticketDelivery.setTicketValidDate(StringUtils.isNotBlank(couponCard.getValidTime())
                        ? DateUtil.parse(couponCard.getValidTime(), "yyyy-MM-dd")
                        : null);
                ticketDelivery.setOffsetPageType(
                        ticket.getOffsetPageType() != null ? ticket.getOffsetPageType().byteValue() : null);

                // 品牌信息
                ticketDelivery.setBrandName(ticket.getBrandName());
                ticketDelivery.setOffsetLogo(ticket.getOffsetLogo());

                ticketDelivery.setIsBirthdayTicket(0);

                ticketDelivery.setCenterTicketCouponNumber(giftTicketOrder.getCenterTicketCouponNumber());
                ticketDelivery.setCouponNumberId(couponCard.getCouponMemberId());

                ticketDelivery.setTicketCatCode(ticket.getTicketCatCode());
                ticketDelivery.setDiscountAmount(ticket.getDiscountAmount());
                ticketDelivery.setThresholdAmount(ticket.getThresholdAmount());

                ticketDelivery.setCreateTime(now);
                ticketDelivery.setModTime(now);

                ticketDeliveries.add(ticketDelivery);

            }

            if (log.isDebugEnabled()) {
                log.debug("[批量插入]开始执行数据库批量保存，订单号：{}，待插入条数：{}",
                        giftTicketOrder.getTicketOrderNo(), ticketDeliveries.size());
            }
            // 批量保存
            ticketDeliveryRepository.batchSave(ticketDeliveries);
            log.info("根据cardList批量插入ticket_delivery成功，券订单号：{}，插入条数：{}",
                    giftTicketOrder.getTicketOrderNo(), ticketDeliveries.size());

        } catch (Exception e) {
            log.error("解析cardList并批量插入ticket_delivery失败，券订单号：{}", giftTicketOrder.getTicketOrderNo(), e);
            throw new BizException("处理卡券信息失败");
        }
    }

    /**
     * 更新赠券订单状态
     */
    private void updateGiftTicketOrderStatus(GiftTicketOrderEntity giftTicketOrder, int actualNum) {
        if (log.isDebugEnabled()) {
            log.debug("[状态更新]开始更新赠券订单状态，订单号：{}，原pushStatus：{}，新actualNum：{}",
                    giftTicketOrder.getTicketOrderNo(), giftTicketOrder.getPushStatus(), actualNum);
        }

        giftTicketOrder.setPushStatus(2); // 2-推送成功
        giftTicketOrder.setActualNum(actualNum);
        giftTicketOrder.setModTime(LocalDateTime.now());

        giftTicketOrderRepository.save(giftTicketOrder);
        log.info("更新赠券订单状态成功，券订单号：{}，实际数量：{}", giftTicketOrder.getTicketOrderNo(), actualNum);
    }
}
