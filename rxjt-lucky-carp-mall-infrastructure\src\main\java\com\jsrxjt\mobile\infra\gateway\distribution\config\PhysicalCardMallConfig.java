package com.jsrxjt.mobile.infra.gateway.distribution.config;

import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 实体卡商城配置
 * @Author: ywt
 * @Date: 2025-10-23 10:55
 * @Version: 1.0
 */
@Data
@Configuration
public class PhysicalCardMallConfig extends DistributionConfig.ChannelConfig {
    private String indexUrl;//首页
//    private String payCallBackUrl;//支付回调
//    private String refundCallBackUrl;//退款回调

    private String aesSecretKey;

    private String aesIv;
}
