package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * 卡包列表请求参数
 * <AUTHOR>
 * @date 2025/07/22
 */
@Data
public class CouponPackageDelRequestDTO extends BaseParam{

    @Schema(description = "ID")
    @NotEmpty(message = "ID不能为空")
    private List<Long> couponPackageIdList;

}
