package com.jsrxjt.mobile.domain.payment.gateway.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 发起支付请求
 * <AUTHOR>
 * @since 2025/8/15
 */
@Data
public class PaymentRequest {
    
    /**
     * 业务订单号
     */
    @JSONField(name = "order_no")
    private String orderNo;
    
    /**
     * 预支付订单号
     */
    @JSONField(name = "pre_order_no")
    private String preOrderNo;

    /**
     * 支付密码
     */
    @JSONField(name = "pass")
    private String pass;

    /**
     * is_with_pass 是否使用密码 true 跳过密码 false 不跳过密码 默认false
     */
    @JSONField(name = "is_with_pass")
    private boolean withPass;
}