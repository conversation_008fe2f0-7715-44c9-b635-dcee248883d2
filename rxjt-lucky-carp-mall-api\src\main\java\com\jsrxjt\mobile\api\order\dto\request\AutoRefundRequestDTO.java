package com.jsrxjt.mobile.api.order.dto.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 自动售后退款请求DTO
 * <AUTHOR>
 * @since 2025/9/26
 **/
@Data
public class AutoRefundRequestDTO {
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 外部退款单号
     */
    private String externalRefundNo;

    /**
     * 申请退款金额 单位元 保留两位小数
     */
    private BigDecimal applyRefundAmount;

    /**
     * 售后类型 1-部分退款 2-全额退款
     */
    private Integer afterSaleType;

    /**
     * 申请售后数量
     */
    private Integer afterSaleQuantity;

    /**
     * 外部应用申请退款金额 单位元 保留两位小数
     */
    private BigDecimal externalRefundAmount;
}
