package com.jsrxjt.mobile.api.locallife.dto.request;

import com.jsrxjt.mobile.api.locallife.dto.LocalLifeBaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/6/19 18:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "本地生活退款请求参数")
public class LocalLifeRefundOrderDTO extends LocalLifeBaseDTO {
    @Schema(description = "本地生活侧订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "本地生活退款请求订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String refundOrderNo;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private String refundAmount;

    /**
     * 1：线上订单 2：门店买单
     */
    private String orderType;
}
