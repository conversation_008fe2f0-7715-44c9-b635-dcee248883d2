# AJ-Captcha验证码配置
aj:
  captcha:
    # 滑动验证，底图路径，不配置将使用默认图片
    # 支持全路径
    # 支持项目路径,以classpath:开头,取resource目录下路径
   # jigsaw: classpath:captcha/images/jigsaw
    # 滑动验证，底图路径，不配置将使用默认图片
    # 支持全路径
    # 支持项目路径,以classpath:开头,取resource目录下路径
   # pic-click: classpath:captcha/images/pic-click
    # 缓存类型 local/redis
    cache-type: redis
    # 验证码类型 default两种都实例化
    type: default
    # 右下角水印文字(我的水印)
    water-mark: 瑞祥
    # 右下角水印字体(不配置时，默认使用文泉驿正黑)
    water-font: WenQuanZhengHei.ttf
    # 点选文字验证码的文字字体(文泉驿正黑)
    font-type: WenQuanZhengHei.ttf
    # 校验滑动拼图允许误差偏移量(默认5像素)
    slip-offset: 5
    # aes加密坐标开启或者禁用(true|false)
    aes-status: true
    # 滑动干扰项(0/1/2)
    interference-options: 2
    # 点选字体样式 默认Font.BOLD
    font-style: 1
    # 点选字体字体大小
    font-size: 25
    # 历史数据清除开关
    history-data-clear-enable: false
    # 接口请求次数一分钟限制是否开启 true|false
    req-frequency-limit-enable: false
    # 验证失败5次，get接口锁定
    req-get-lock-limit: 5
    # 验证失败后，锁定时间间隔,s
    req-get-lock-seconds: 360
    # get接口一分钟内请求数限制
    req-get-minute-limit: 30
    # check接口一分钟内请求数限制
    req-check-minute-limit: 60
    # verify接口一分钟内请求数限制
    req-verify-minute-limit: 60

# 自定义验证码配置
jsrxjt:
  captcha:
    # 是否启用验证码功能
    enabled: true
