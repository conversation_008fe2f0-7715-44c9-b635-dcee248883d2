package com.jsrxjt.mobile.biz.homeScanPay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.*;
import com.jsrxjt.mobile.api.enums.*;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.scanPay.response.HomeScanCodeResponseDTO;
import com.jsrxjt.mobile.api.scanPay.types.WssPushTypeEnum;
import com.jsrxjt.mobile.biz.homeScanPay.service.PickScanCaseService;
import com.jsrxjt.mobile.biz.homeScanPay.service.ScanPayWssService;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.mq.QueueSenderService;
import com.jsrxjt.mobile.biz.mq.constants.DestinationConstants;
import com.jsrxjt.mobile.biz.order.AfterSaleCaseService;
import com.jsrxjt.mobile.biz.order.AutoRefundCaseService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.TradeCardBalanceChangeEntity;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.repository.TradeCardBalanceChangeRepository;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.ThirdPickOrderInfoBuilder;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformChannelGateway;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayCodeRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayRefundRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayResultRequest;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayCodeResponse;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayResultResponse;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayStatusResultResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.jsrxjt.common.core.constant.RedisKeyConstants.*;

/**
 * @Description: 扫码提货领域层服务
 * @Author: ywt
 * @Date: 2025-05-19 10:24
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PickScanCaseServiceImpl implements PickScanCaseService {
    private final PickPlatformChannelGateway pickPlatformChannelGateway;
    private final RedisUtil redisUtil;
    private final AppGoodsRepository appGoodsRepository;
    private final OrderCaseService orderCaseService;
    private final ThirdPickOrderInfoBuilder thirdPickOrderInfoBuilder;
    private final CustomerRepository customerRepository;
    private final TradeCardBalanceChangeRepository tradeCardBalanceChangeRepository;
    private final OrderRepository orderRepository;
    private final ScanPayWssService scanPayWssService;
    private final AutoRefundCaseService autoRefundCaseService;
    private final QueueSenderService queueSenderService;
    private final AfterSaleRepository afterSaleRepository;
    private final DistributedLock distributedLock;
    private final AfterSaleCaseService afterSaleCaseService;

    protected static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private final String PICK_ORDER_REFUND_LOCK_PREFIX = "pick:order:refund:lock:";

    private static final DateTimeFormatter DEFAULT_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final int orderExpireTime = 1 * 60;
    @Override
    public PickPaymentCodeResponseDTO getPaymentCode(PickPaymentCodeRequestDTO requestDTO) {
        PickPaymentCodeResponseDTO responseDTO = new PickPaymentCodeResponseDTO();
        PickPlatformPayCodeRequest request = new PickPlatformPayCodeRequest();
        request.setNonce(requestDTO.getNonce());
        request.setTimestamp(requestDTO.getTimestamp());
        request.setThirdId(requestDTO.getThirdId());
        request.setUserCode(String.valueOf(requestDTO.getUserId()));
        PickPlatformPayCodeResponse payCodeResponse = pickPlatformChannelGateway.getPaymentCode(request);
        if (Objects.nonNull(payCodeResponse)) {
            responseDTO.setPaymentCode(payCodeResponse.getPayment_code());
        }
        return responseDTO;
    }

    //创建订单(外部系统调用)
    @Override
    public PickBaseResponse<PickChannelOrderResponseDTO> createChannelOrder(PickChannelOrderRequestDTO requestDTO) {
        log.info("提货中台回调--创建订单--请求参数：{}", JSON.toJSONString(requestDTO));
        try {
            String code = requestDTO.getPayment_code();
            String offScanCode = String.format(OFF_SCAN_CODE,code);
            String dtoStr = redisUtil.get(offScanCode);
            if (StrUtil.isBlank(dtoStr)) {
                log.error("接口：getThirdVipOrder,支付码已过期,请重新获取:{}", code);
                throw new BizException("支付码已过期");
            }
            HomeScanCodeResponseDTO dto = JSONUtil.toBean(dtoStr, HomeScanCodeResponseDTO.class);
            Long customerId = dto.getCustomerId();
            CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
            if (customerEntity == null) {
                log.error("接口：getThirdVipOrder,未找到会员ID为{}的会员信息", customerId);
            }
            // 保存到redis（幂等）key 用户+卡号
            String key = String.format(OFF_PRE_ORDER, customerId, requestDTO.getOrder_no());
            String s = redisUtil.get(key);
            if (StrUtil.isNotBlank(s)) {
                log.error("接口：getThirdVipOrder,pos订单已存在,请勿重复提交:{}", code);
                throw new BizException("pos订单已存在");
            }
            //获取应用详情信息
            Long appSpuId = dto.getAppId();
            AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(appSpuId);
            if(appGoodsEntity == null){
                log.info("接口：createChannelOrder,未找到应用信息");
                throw new BizException("未找到应用信息");
            }
            CreateOrderDTO createOrderDTO = saveOrderInfo(requestDTO, customerEntity, appGoodsEntity);
            String expiredTime = requestDTO.getExpired_time();
            DateTime parse = DateUtil.parse(expiredTime, DatePattern.NORM_DATETIME_FORMATTER);
            createOrderDTO.setPayExpireTimestamp(parse.getTime()/1000);
            JSONObject jsonObject = new JSONObject().fluentPut("type", requestDTO.getType());
            createOrderDTO.setExtraInfo(jsonObject);
            OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO, thirdPickOrderInfoBuilder);
            //转换字段保存redis
            String jsonStr = JSONUtil.toJsonStr(orderInfoEntity);
            log.info("提货中台展码付key：{},订单信息：{}",key,jsonStr);
            long between = DateUtil.between(new Date(), parse.toJdkDate(), DateUnit.SECOND);
            if(between <= 0){
                log.info("接口：createChannelOrder,订单已过期");
                throw new BizException("订单已过期");
            }
            //保存redis 默认1分钟过期
            redisUtil.set(key,jsonStr,orderExpireTime);
            PickChannelOrderResponseDTO responseDTO = new PickChannelOrderResponseDTO(String.valueOf(orderInfoEntity.getOrderNo()));
            try {
                PickChannelWssRequestDTO pickChannelWssRequestDTO = new PickChannelWssRequestDTO();
                pickChannelWssRequestDTO.setCustomerId(customerId);
                pickChannelWssRequestDTO.setCode(code);
                ScanPayWssPushDTO scanPayWssPushDTO = new ScanPayWssPushDTO();
                scanPayWssPushDTO.setFlqOrderNo(orderInfoEntity.getOrderNo());
                scanPayWssPushDTO.setType(WssPushTypeEnum.PICK_PRE_PAY.getType());
                pickChannelWssRequestDTO.setBody(JSONObject.from(scanPayWssPushDTO));
                scanPayWssService.pushWssInfo(pickChannelWssRequestDTO);
            }catch (BizException e){
                log.error("websocket推送展码付订单信息失败,code={},orderNo={}, BizException={}", code, orderInfoEntity.getOrderNo(), e.getMessage());
                throw e;
            }catch (Exception e){
                log.error("websocket推送展码付订单信息失败,code={},orderNo={}, e={}", code, orderInfoEntity.getOrderNo(), e.getStackTrace());
                throw e;
            }
            return PickBaseResponse.succeed(responseDTO);
        }catch(BizException e){
            return PickBaseResponse.fail(e.getMessage());
        } catch (Exception e){
            return PickBaseResponse.fail("创单失败");
        }
    }

    /**
     * 订单变更
     *
     * @param requestDTO
     */
    @Override
    public BaseResponse updateChannelOrder(PickChannelOrderUpdateRequestDTO requestDTO) {
        return null;
    }

    /**
     * 支付状态查询 (外部系统调用)
     *
     * @param requestDTO
     */
    @Override
    public PickPlatformPayStatusResultResponse getChannelPayStatus(PickPaymentRefundRequestDTO requestDTO) {
        return null;
    }

    private CreateOrderDTO saveOrderInfo(PickChannelOrderRequestDTO requestDTO,
                                         CustomerEntity customerEntity,
                                         AppGoodsEntity appGoodsEntity
    ) {
        CreateOrderDTO request = new CreateOrderDTO();
        request.setCustomerId(customerEntity.getId());
        request.setCustomerMobile(customerEntity.getPhone());
        request.setProductType(ProductTypeEnum.APP.getType());
        request.setProductSpuId(appGoodsEntity.getAppId());
        request.setProductSkuId(appGoodsEntity.getAppId());
        request.setExternalOrderNo(requestDTO.getOrder_no());
        request.setThirdId(appGoodsEntity.getThirdId());
        if (!Objects.equals(requestDTO.getShop_id(), "0")){
            request.setExternalShopId(requestDTO.getShop_id());
        }else {
            if (appGoodsEntity.getShopId() != null){
                request.setExternalShopId(String.valueOf(appGoodsEntity.getShopId()));
            }else {
                throw new BizException("未获取到对应的shopId");
            }
        }
        request.setExternalShopUserId(requestDTO.getShop_user_id());
        request.setDistTradeNo(requestDTO.getTrade_no());
        request.setExternalAppProductPrice(new BigDecimal(requestDTO.getAmount()).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
        return request;
    }

    /**
     * 订单退款(外部系统调用)
     *
     * @param requestDTO
     */
    @Override
    public PickBaseResponse<PickChannelOrderRefundResponseDTO> refundChannelOrder(PickChannelOrderRefundRequestDTO requestDTO) {
        log.info("提货券中台订单退款, externalOrderNo={}, refundNo={}", requestDTO.getOrder_no(), requestDTO.getRefund_no());
        String externalOrderNo = requestDTO.getOrder_no();
        String tradeNo = requestDTO.getTrade_no();
        String refundNo = requestDTO.getRefund_no();
        BigDecimal refundAmount = new BigDecimal(requestDTO.getAmount()).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        PickBaseResponse<OrderInfoEntity> orderResponse = this.findByExternalOrderNoAndDistTradeNo(externalOrderNo, tradeNo);
        if (orderResponse.isFail()){
            return PickBaseResponse.fail(orderResponse.getMsg());
        }
        PickChannelOrderRefundResponseDTO dto = new PickChannelOrderRefundResponseDTO();
        OrderInfoEntity existOrderEntity = orderResponse.getData();
        Integer paymentStatus = existOrderEntity.getPaymentStatus();
        String lockKey = PICK_ORDER_REFUND_LOCK_PREFIX + refundNo;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取提货券中台订单退款通知处理锁失败，订单号：{}", existOrderEntity.getOrderNo());
                return PickBaseResponse.fail("系统繁忙，请稍后重试");
            }
            if (Objects.equals(paymentStatus, PaymentStatusEnum.UNPAID.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_PAID.getCode())) {
                return PickBaseResponse.fail("订单状态不可退");
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.PAID.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDING.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDED.getCode())) {
                AutoRefundRequestDTO autoRefundRequestDTO = new AutoRefundRequestDTO();
                autoRefundRequestDTO.setOrderNo(existOrderEntity.getOrderNo());
                autoRefundRequestDTO.setExternalRefundNo(refundNo);
                autoRefundRequestDTO.setExternalRefundAmount(refundAmount);
                // 下单时第三方订单金额
                BigDecimal orderAmount = existOrderEntity.getTotalSellAmount();
                // 整单退
                PickPlatformPayRefundRequest pickPlatformPayRefundRequest = new PickPlatformPayRefundRequest();
                pickPlatformPayRefundRequest.setOrderNo(externalOrderNo);
                pickPlatformPayRefundRequest.setTradeNo(tradeNo);
                pickPlatformPayRefundRequest.setThirdId(requestDTO.getThird_id());
                pickPlatformPayRefundRequest.setType(requestDTO.getType());
                pickPlatformPayRefundRequest.setTradeTime(existOrderEntity.getPaymentTime().format(DEFAULT_FORMATTER));
                pickPlatformPayRefundRequest.setRefundNo(refundNo);
                if (orderAmount.compareTo(refundAmount) == 0) {
                    // 整单退场景需要退手续费
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode());
                    autoRefundRequestDTO.setApplyRefundAmount(existOrderEntity.getOrderAmount());
                    AfterSaleEntity afterSale = this.createAfterSaleForRecharge(existOrderEntity,existOrderEntity.getOrderAmount(), requestDTO.getRefund_no(), AfterSaleTypeEnum.FULL_REFUND);
                    dto.setOut_refund_sn(afterSale.getRefundNo());
                    queueSenderService.send(DestinationConstants.DestinationName.PICK_SCAN_BACK_TOPIC, "refund_back", JSONObject.toJSONString(pickPlatformPayRefundRequest), 5*1000);
                } else if (orderAmount.compareTo(refundAmount) > 0) {
                    // 部分退
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.PARTIAL_REFUND.getCode());
                    autoRefundRequestDTO = afterSaleCaseService.calDistributionOrderRefundAmount(autoRefundRequestDTO);
                    AfterSaleEntity afterSale = this.createAfterSaleForRecharge(existOrderEntity, autoRefundRequestDTO.getApplyRefundAmount(), requestDTO.getRefund_no(), AfterSaleTypeEnum.PARTIAL_REFUND);
                    dto.setOut_refund_sn(afterSale.getRefundNo());
                    queueSenderService.send(DestinationConstants.DestinationName.PICK_SCAN_BACK_TOPIC, "refund_back", JSONObject.toJSONString(pickPlatformPayRefundRequest), 5*1000);
                } else {
                    return PickBaseResponse.fail("退款金额大于订单金额");
                }
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDED.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDING.getCode())) {
                return PickBaseResponse.fail("已整单退款,不可再退");
            }
        } catch (Exception e) {
            log.error("提货券中台订单通知异常，订单号：{}，错误信息：{}", existOrderEntity.getOrderNo(), e.getMessage(), e);
            return PickBaseResponse.fail("系统异常,请稍后再试!");
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
        log.info("提货券中台订单退款返回:{}", dto);
        return PickBaseResponse.succeed(dto);
    }


    /**
     * 退款状态查询(外部系统调用)
     *
     * @param requestDTO
     */
    @Override
    public PickBaseResponse<PickOrderRefundStatusResponseDTO> getRefundStatus(PickOrderRefundStatusRequestDTO requestDTO) {
        String externalRefundNo = requestDTO.getRefund_no();
        AfterSaleEntity afterSale = afterSaleRepository.findByExternalRefundNo(externalRefundNo);
        if (afterSale == null){
            return PickBaseResponse.fail(Status.AS_REFUND_NULL.getMessage());
        }
        String tradeStatus = "11";  //默认退款中
        RefundStatusEnum refundStatusEnum = RefundStatusEnum.getByCode(afterSale.getRefundStatus());
        switch (refundStatusEnum){
            case REFUNDING:
            case NOT_REFUNDED:
                tradeStatus = "11";
                break;
            case REFUND_SUCCESS:
                tradeStatus = "10";
                break;
            case REFUND_FAILED:
            case REFUND_REJECTED:
                tradeStatus = "12";
                break;
        }
        PickOrderRefundStatusResponseDTO dto = new PickOrderRefundStatusResponseDTO();
        dto.setTrade_status(tradeStatus);
        dto.setOrder_no(requestDTO.getOrder_no());
        dto.setTrade_no(requestDTO.getTrade_no());
        dto.setOut_refund_sn(afterSale.getRefundNo());
        return PickBaseResponse.succeed(dto);
    }

    /**
     * 订单退款结果回告[幂等性]
     *
     * @param request
     */
    @Override
    public PickOrderRefundResultResponseDTO getPaymentOrderRefund(PickPlatformPayRefundRequest request) {
        PickOrderRefundResultResponseDTO responseDTO = new PickOrderRefundResultResponseDTO();
        try {
            PickPlatformPayResultResponse paymentOrderRefund = pickPlatformChannelGateway.getPaymentOrderRefund(request);
            if (Objects.nonNull(paymentOrderRefund)) {
                responseDTO.setRefundNo(paymentOrderRefund.getRefund_no());
                responseDTO.setTradeNo(paymentOrderRefund.getTrade_no());
                responseDTO.setOrderNo(paymentOrderRefund.getOrder_no());
            }
        } catch (Exception e) {
            log.info("接口：getPaymentOrderRefund,订单退款结果回告失败:{}",JSONUtil.toJsonStr(request));
            throw new RuntimeException(e);
        }
        return responseDTO;
    }

    /**
     * 订单支付结果回告
     *
     * @param requestDTO
     */
    @Override
    public PickPlatformPayResultResponse getPaymentOrderResult(PickPlatformPayResultRequestDTO requestDTO) {
        PickPlatformPayResultRequest request = BeanUtil.toBean(requestDTO, PickPlatformPayResultRequest.class);
        request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
        request.setNonce(IdUtil.simpleUUID());
        PickPlatformPayResultResponse paymentOrderResult = pickPlatformChannelGateway.getPaymentOrderResult(request);
        return paymentOrderResult;
    }


    @Override
    public BaseResponse<List<PickCardTradeListResponseDTO>> cardTradeList(String cardNo) {
        List<TradeCardBalanceChangeEntity> list = tradeCardBalanceChangeRepository.findAllByCardNo(cardNo);
        return BaseResponse.succeed(BeanUtil.copyToList(list, PickCardTradeListResponseDTO.class));
    }

    @Override
    public PickBaseResponse orderConfirm(PickChannelOrderConfirmRequestDTO requestDTO) {
        log.info("提货券中台确认订单状态, externalOrderNo={}, tradeStatus={}", requestDTO.getOrder_no(), requestDTO.getTrade_status());
        String externalOrderNo = requestDTO.getOrder_no();
        String tradeNo = requestDTO.getTrade_no();
        String tradeStatus = requestDTO.getTrade_status();
        PickBaseResponse<OrderInfoEntity> response = this.findByExternalOrderNoAndDistTradeNo(externalOrderNo, tradeNo);
        if (response.isFail()){
            return response;
        }
        OrderInfoEntity existOrderEntity = response.getData();
        OrderInfoEntity updateParam = new OrderInfoEntity();
        // 设置查询条件
        updateParam.setId(existOrderEntity.getId());
        updateParam.setCustomerId(existOrderEntity.getCustomerId());
        if (!tradeStatus.equals("00")){
            //已经支付订单生成售后单，退款
            if (existOrderEntity.getPaymentStatus().equals(PaymentStatusEnum.PAID.getCode())){
                updateOrderToRechargeFailed(existOrderEntity);
                createAfterSaleForRecharge(existOrderEntity, existOrderEntity.getPaymentAmount(),null, AfterSaleTypeEnum.FULL_REFUND);
            }
        }
        return PickBaseResponse.succeed();
    }

    @Override
    public PickBaseResponse<PickChannelOrderResultResponseDTO> getChannelOrderPayStatus(PickChannelOrderStatusRequestDTO requestDTO) {
        String externalOrderNo = requestDTO.getOrder_no();
        String tradeNo = requestDTO.getTrade_no();
        PickBaseResponse<OrderInfoEntity> response = this.findByExternalOrderNoAndDistTradeNo(externalOrderNo, tradeNo);
        if (response.isFail()){
            return PickBaseResponse.fail();
        }
        OrderInfoEntity orderInfoEntity = response.getData();
        Integer payStatus = orderInfoEntity.getPaymentStatus();

        PickChannelOrderResultResponseDTO pickChannelOrderResultResponseDTO = new PickChannelOrderResultResponseDTO();
        pickChannelOrderResultResponseDTO.setOut_order_sn(orderInfoEntity.getOrderNo());
        pickChannelOrderResultResponseDTO.setOrder_no(orderInfoEntity.getExternalOrderNo());
        pickChannelOrderResultResponseDTO.setTrade_no(orderInfoEntity.getDistTradeNo());
        if (orderInfoEntity.getPaymentTime() != null){
            pickChannelOrderResultResponseDTO.setTrade_time(orderInfoEntity.getPaymentTime().format(DEFAULT_FORMATTER));
        }
        String trade_status = "01";  //默认未支付
        if (payStatus.equals(PaymentStatusEnum.PAID)){
            trade_status = "00";
        }
        pickChannelOrderResultResponseDTO.setTrade_status(trade_status);
        log.info("提货券中台订单支付状态返回内容 {}", pickChannelOrderResultResponseDTO);
        return PickBaseResponse.succeed(pickChannelOrderResultResponseDTO);
    }

    @Override
    public void sendBackMessage(String tag, JSONObject parseObject) {
        switch (tag){
            case "refund_back":
                log.info("提现订单支付状态结果回告:{}", parseObject);
                PickPlatformPayRefundRequest pickPlatformPayRefundRequest = parseObject.to(PickPlatformPayRefundRequest.class);
                this.getPaymentOrderRefund(pickPlatformPayRefundRequest);
                break;
        }
    }

    private PickBaseResponse<OrderInfoEntity> findByExternalOrderNoAndDistTradeNo(String externalOrderNo, String tradeNo){
        OrderInfoEntity orderInfoEntity = orderRepository.findByExternalOrderNoAndDistTradeNo(externalOrderNo, tradeNo);
        if (orderInfoEntity == null){
            return PickBaseResponse.fail(Status.NULL_ORDER.getMessage());
        }
        return PickBaseResponse.succeed(orderInfoEntity);
    }

    /**
     *
     * @param requestDTO 外部订单
     * @param localUserRequestDTO 当前用户信息
     */
    private OrderInfoEntity saveOrderInfo(PickChannelOrderRequestDTO requestDTO,
                               PickLocalUserRequestDTO localUserRequestDTO,
                               AppGoodsEntity appGoodEntity

    ){
        OrderInfoEntity entity = new OrderInfoEntity();
        //订单编号
        entity.setOrderNo("");
        entity.setCustomerId(Long.parseLong(requestDTO.getUser_code()));
        entity.setThirdId(requestDTO.getThird_id());
        entity.setExternalShopId(requestDTO.getShop_id());
        entity.setExternalShopUserId(requestDTO.getShop_user_id());
        entity.setExternalOrderNo(requestDTO.getOrder_no());
        entity.setTradeNo(requestDTO.getTrade_no());
        String type = requestDTO.getType();
        PickTypeToChannelEnum byType = PickTypeToChannelEnum.getByType(type);
        if(byType !=null){
            entity.setOrderChannel(byType.getChannel());
        }
        //默认保留2位
        BigDecimal amount = new BigDecimal(requestDTO.getAmount());
        entity.setPaymentAmount(amount);
        //商品原售价总价
        entity.setTotalSellAmount(amount);
        //优惠总金额
        entity.setTotalDiscountAmount(BigDecimal.ZERO);
        //订单金额
        entity.setOrderAmount(amount);

        entity.setCreateTime(DateUtil.parseLocalDateTime(requestDTO.getCreated_time()));
        entity.setModTime(LocalDateTime.now());
        int seconds = DateUtil.parse(requestDTO.getExpired_time(), DatePattern.NORM_DATETIME_FORMATTER).getSeconds();
        entity.setPayExpireTimestamp((long)seconds);

        entity.setCustomerMobile(localUserRequestDTO.getPhone());
        entity.setLongitude(new BigDecimal(localUserRequestDTO.getLongitude()));
        entity.setLatitude(new BigDecimal(localUserRequestDTO.getLatitude()));
        //应用id
        entity.setProductSkuId(appGoodEntity.getAppId());
        entity.setProductName(appGoodEntity.getAppName());
        entity.setFlatProductType(FlatProductTypeEnum.SCAN_PICKUP_APP.getType());
        //订单类型 订单类型: 1-卡券 2-普通应用 3-红包/充值 4扫码付应用'
        entity.setOrderType(OrderTypeEnum.SCAN_APP.getType());
        entity.setOrderStatus(OrderStatusEnum.PENDING_PAYMENT.getCode());
        entity.setOrderChannel(OrderChannelEnum.DISTRIBUTION_CENTER.getCode());
        entity.setChannelId(appGoodEntity.getChannelId());
        entity.setBrandId(appGoodEntity.getBrandId());
        //外部订单状态
       // entity.setExternalStatus();
        //支付状态
        entity.setPaymentStatus(PaymentStatusEnum.UNPAID.getCode());
        //todo 保存
        return null;
    }

    //保存订单支付信息
    private Long saveOrderPaymentInfo (PickChannelOrderRequestDTO requestDTO,
                           OrderInfoEntity orderInfoEntity

    ){
        return null;
    }

    private PickChannelOrderDetailResponseDTO setDetailOrder(PickChannelOrderRequestDTO requestDTO,long orderId){
        PickChannelOrderDetailResponseDTO dto = new PickChannelOrderDetailResponseDTO();
        dto.setCustomerId(Long.parseLong(requestDTO.getUser_code()));
        dto.setOrderId(orderId);
        dto.setCode(requestDTO.getPayment_code());
        dto.setThirdId(requestDTO.getThird_id());
        dto.setExternalShopId(requestDTO.getShop_id());
        dto.setExternalShopUserId(requestDTO.getShop_user_id());
        dto.setExternalOrderNo(requestDTO.getOrder_no());
        dto.setTradeNo(requestDTO.getTrade_no());
        dto.setRate(requestDTO.getRate());
        String type = requestDTO.getType();
        PickTypeToChannelEnum byType = PickTypeToChannelEnum.getByType(type);
        if(byType !=null){
            dto.setOrderChannel(byType.getChannel());
        }
        //默认保留2位
        BigDecimal amount = new BigDecimal(requestDTO.getAmount());
        dto.setPaymentAmount(amount);
        dto.setOrderCreateTime(DateUtil.parse(requestDTO.getCreated_time(), DatePattern.NORM_DATETIME_FORMATTER));
        dto.setPayExpireTime(DateUtil.parse(requestDTO.getExpired_time(), DatePattern.NORM_DATETIME_FORMATTER));
        //提货券支付（无用可去掉）
       //  dto.setPayType("thpay");
        return dto;
    }

    /**
     * 更新订单状态为发货失败
     */
    private void updateOrderToRechargeFailed(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);
        log.info("订单发货状态更新为发货失败，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERY_FAILED.getDescription());
    }

    /**
     * 自动创建售后单并退款
     *
     * @param orderInfo 订单信息
     */
    private AfterSaleEntity createAfterSaleForRecharge(OrderInfoEntity orderInfo, BigDecimal refundAmount, String externalRefundNo, AfterSaleTypeEnum afterSaleTypeEnum) {
        log.info("开始为展码付发放失败订单创建售后单并自动退款，订单号：{}", orderInfo.getOrderNo());
        AutoRefundRequestDTO autoRefundRequestDTO = new AutoRefundRequestDTO();
        autoRefundRequestDTO.setOrderNo(orderInfo.getOrderNo());
        autoRefundRequestDTO.setApplyRefundAmount(refundAmount);
        autoRefundRequestDTO.setAfterSaleType(afterSaleTypeEnum.getCode());
        if (StrUtil.isNotEmpty(externalRefundNo)){
            autoRefundRequestDTO.setExternalRefundNo(externalRefundNo);
        }
        AfterSaleEntity afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
        log.info ("展码付售后单退款成功，订单号：{}，售后单号：{}", orderInfo.getOrderNo(), afterSaleEntity.getAfterSaleNo());
        return afterSaleEntity;
    }

}
