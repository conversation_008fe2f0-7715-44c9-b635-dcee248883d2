package com.jsrxjt.mobile.biz.captcha.impl;

import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaGenerateDTO;
import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaVerifyRequestDTO;
import com.jsrxjt.mobile.api.captcha.dto.response.CaptchaResponseDTO;
import com.jsrxjt.mobile.api.captcha.dto.response.CaptchaVerifyResponseDTO;
import com.jsrxjt.mobile.biz.captcha.CaptchaCaseService;
import com.jsrxjt.mobile.domain.captcha.gateway.CaptchaGateway;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaRequest;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaResponse;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaVerifyRequest;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaVerifyResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 验证码业务服务实现类
 * 
 * <AUTHOR>
 * @since 2025-12-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaptchaCaseServiceImpl implements CaptchaCaseService {

    private final CaptchaGateway captchaGateway;

    @Override
    public CaptchaResponseDTO getCaptcha(CaptchaGenerateDTO request) {
        log.info("获取验证码请求：类型={}, 浏览器信息={}", request.getCaptchaType(), request.getBrowserInfo());
        
        // 转换为领域模型
        CaptchaRequest domainRequest = new CaptchaRequest(
            request.getCaptchaType(),
            request.getClientUid(),
            request.getBrowserInfo(),
            System.currentTimeMillis()
        );
        
        // 调用领域网关
        CaptchaResponse domainResponse = captchaGateway.generateCaptcha(domainRequest);
        
        // 转换为API响应
        CaptchaResponseDTO response = new CaptchaResponseDTO();
        BeanUtils.copyProperties(domainResponse, response);
        
        log.info("获取验证码成功：token={}", response.getToken());
        return response;
    }

    @Override
    public CaptchaVerifyResponseDTO verifyCaptcha(CaptchaVerifyRequestDTO request) {
        log.info("校验验证码请求：类型={}, token={}", request.getCaptchaType(), request.getToken());
        
        // 转换为领域模型
        CaptchaVerifyRequest domainRequest = new CaptchaVerifyRequest();
        BeanUtils.copyProperties(request, domainRequest);
        
        // 调用领域网关
        CaptchaVerifyResponse domainResponse = captchaGateway.verifyCaptcha(domainRequest);
        
        // 转换为API响应
        CaptchaVerifyResponseDTO response = new CaptchaVerifyResponseDTO();
        BeanUtils.copyProperties(domainResponse, response);
        
        log.info("校验验证码结果：{}", domainResponse.isSuccess() ? "成功" : "失败");
        return response;
    }

}
