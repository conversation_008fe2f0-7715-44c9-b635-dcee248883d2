package com.jsrxjt.mobile.api.scanPay.types;


/**
 * 扫码付pos推送类型
 * <AUTHOR>
 * @date 2025/10/30
 */
public enum WssPushTypeEnum {

    /**
     * 提货券分销平台预下单
     */
    PICK_PRE_PAY("PICK_PRE_PAY"),

    /**
     * 预下单
     */
    PRE_PAY("PRE_PAY"),

    /**
     * 支付错误
     */
    ERROR("ERROR"),

    /**
     * 支付成功
     */
    SUCCESS("SUCCESS"),

    /**
     * 微信支付
     */
    WXPAY("WXPAY");

    private String type;

    WssPushTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static WssPushTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (WssPushTypeEnum posV2PushType : values()) {
            if (posV2PushType.getType().equals(type)) {
                return posV2PushType;
            }
        }
        return null;
    }
}
