package com.jsrxjt.mobile.infra.shop.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商户门店实体类
 * 对应表：shop_agent
 */
@Data
@TableName(value = "shop_agent")
public class ShopAgentPO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 城市编码
     */
    private Long cityCode;

    /**
     * 第三方ID (1 瑞祥)
     */
    private Integer thirdId;

    /**
     * 商户品牌id
     */
    private Integer brandId;

    /**
     * 商户品牌
     */
    private String brandName;

    /**
     * 商户分组id
     */
    private Integer groupId;

    /**
     * 商户分组
     */
    private String groupName;

    /**
     * 门店类型id
     */
    private Integer typeId;

    /**
     * 门店类型
     */
    private String typeName;

    /**
     * 商户分类id
     */
    private Integer cateId;

    /**
     * 商户分类
     */
    private String cateName;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 区id
     */
    private Integer areaId;

    /**
     * 门店编号
     */
    private String no;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 地址
     */
    private String address;

    /**
     * 电话
     */
    private String tel;

    /**
     * 特约商户LOGO
     */
    private String specialLogo;

    /**
     * 联盟商户类别
     */
    private String specialType;

    /**
     * 是否显示在特约商户
     */
    private String showSpecial;

    /**
     * 是否显示在扫码提货
     */
    private String showPick;

    /**
     * 是否提货凭证商户
     */
    private String isThShop;

    /**
     * 是否开启提货凭证支付
     */
    private String isThOpen;

    /**
     * 开票类型 专票 SP 普票 GP 收据 RCPT
     */
    private String invoiceType;

    /**
     * 开票税率
     */
    private String invoiceTax;

    /**
     * 商户标签 持凭证提货 CERT_PICK 展码提货 CODE_PICK 持卡支付 CERT_PAY 展码支付 CODE_PAY
     */
    private String tag;

    /**
     * 门店LOGO角标
     */
    private String icon;

    /**
     * 角标有效期
     */
    private String iconValidAt;

    /**
     * 门店标签
     */
    private String label;

    /**
     * 门店标签颜色
     */
    private String labelColor;

    /**
     * 门店标签背景色
     */
    private String labelBgColor;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 删除时间
     */
    private LocalDateTime deletedAt;
    @TableField(exist = false)
    private Double distance;
}
