package com.jsrxjt.mobile.api.distribution.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import com.jsrxjt.common.core.vo.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class PickCodeRequestDTO extends BaseRequest {
    @Schema(description = "动态码")
    @NotNull(message = "动态码不能为空")
    private String code;

}
