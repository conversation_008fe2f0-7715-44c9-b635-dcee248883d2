<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsrxjt.mobile.infra.order.persistent.mapper.AfterSaleMapper">

    <!-- 售后单关联订单项结果映射 -->
    <resultMap id="AfterSaleWithItemResultMap" type="com.jsrxjt.mobile.infra.order.persistent.po.AfterSaleWithItemPO">
        <result property="afterSaleId" column="after_sale_id"/>
        <result property="afterSaleNo" column="after_sale_no"/>
        <result property="orderNo" column="order_no"/>
        <result property="afterSaleType" column="after_sale_type"/>
        <result property="afterSaleStatus" column="after_sale_status"/>
        <result property="refundStatus" column="refund_status"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="afterSaleQuantity" column="after_sale_quantity"/>
        <result property="createTime" column="create_time"/>
        <result property="productName" column="product_name"/>
        <result property="flatProductType" column="flat_product_type"/>
        <result property="brandName" column="brand_name"/>
        <result property="productLogo" column="product_logo"/>
        <result property="sellPrice" column="sell_price"/>
        <result property="faceAmount" column="face_amount"/>
        <result property="quantity" column="quantity"/>
    </resultMap>

    <!-- 分页查询售后单及订单项 -->
    <select id="selectAfterSaleListWithItemsPage" resultMap="AfterSaleWithItemResultMap">
        SELECT
            a.id as after_sale_id,
            a.after_sale_no,
            a.order_no,
            a.after_sale_type,
            a.after_sale_status,
            a.refund_status,
            a.refund_amount,
            a.after_sale_quantity,
            a.create_time,
            oi.product_name,
            oi.flat_product_type,
            oi.brand_name,
            oi.product_logo,
            oi.sell_price,
            oi.face_amount,
            oi.quantity
        FROM t_after_sale a
        LEFT JOIN t_order_item oi ON a.order_no = oi.order_no
        ${ew.customSqlSegment}
    </select>

</mapper>