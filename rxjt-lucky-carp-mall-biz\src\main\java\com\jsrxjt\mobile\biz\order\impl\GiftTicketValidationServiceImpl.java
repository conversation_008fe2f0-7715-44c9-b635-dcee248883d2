package com.jsrxjt.mobile.biz.order.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.order.dto.request.GiftTicketRequestDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.order.GiftTicketValidationService;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketInfo;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.service.TicketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 赠送券校验服务实现
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/1/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GiftTicketValidationServiceImpl implements GiftTicketValidationService {

    private final TicketService ticketService;

    @Override
    public List<GiftTicketInfo> validateAndProcessGiftTickets(ProductItemId productItemId,
                                                              List<GiftTicketRequestDTO> giftTickets,
                                                              Integer quantity) {
        // 如果没有赠送券，直接返回空列表
        if (CollectionUtil.isEmpty(giftTickets)) {
            return new ArrayList<>();
        }
        
        // 只有卡券和套餐产品才支持赠送券
        if (!isSupportGiftTicket(productItemId.getProductType())) {
            throw new BizException("当前产品类型不支持赠送券");
        }
        
        // 校验券数量总和与购买数量一致
        validateGiftTicketQuantity(giftTickets, quantity);
        
        // 获取券ID列表
        List<Long> ticketIds = giftTickets.stream()
                .map(GiftTicketRequestDTO::getTicketId)
                .collect(Collectors.toList());
        
        // 调用TicketService获取券信息
        List<TicketEntity> ticketEntities = ticketService.getTicketsBySkuIdsAndTicketIds(
                productItemId.getSkuId(),
                productItemId.getProductType(),
                ticketIds);
        
        if (CollectionUtil.isEmpty(ticketEntities)) {
            throw new BizException("选择的赠送券不存在或不可用");
        }
        
        // 校验券ID是否都存在
        validateTicketExists(giftTickets, ticketEntities);
        
        // 转换为响应DTO
        return convertToGiftTicketInfo(giftTickets, ticketEntities);
    }

    /**
     * 判断产品类型是否支持赠送券
     */
    private boolean isSupportGiftTicket(Integer productType) {
        return Objects.equals(productType, ProductTypeEnum.COUPON.getType()) ||
               Objects.equals(productType, ProductTypeEnum.PACKAGE.getType());
    }

    /**
     * 校验赠送券数量
     */
    private void validateGiftTicketQuantity(List<GiftTicketRequestDTO> giftTickets, Integer quantity) {
        int totalTicketNum = giftTickets.stream()
                .mapToInt(GiftTicketRequestDTO::getTicketNum)
                .sum();
        
        if (totalTicketNum != quantity) {
            throw new BizException(String.format("赠送券总数量(%d)与购买数量(%d)不一致", totalTicketNum, quantity));
        }
    }

    /**
     * 校验券ID是否都存在
     */
    private void validateTicketExists(List<GiftTicketRequestDTO> giftTickets, List<TicketEntity> ticketEntities) {
        Set<Long> existTicketIds = ticketEntities.stream()
                .map(TicketEntity::getTicketId)
                .collect(Collectors.toSet());
        
        List<Long> notExistTicketIds = giftTickets.stream()
                .map(GiftTicketRequestDTO::getTicketId)
                .filter(ticketId -> !existTicketIds.contains(ticketId))
                .collect(Collectors.toList());
        
        if (!notExistTicketIds.isEmpty()) {
            log.error("产品包含的赠券ID不存在: {}", notExistTicketIds);
            throw new BizException("选了不包含在可选范围内的权益");
        }
    }

    /**
     * 转换为赠送券响应DTO
     */
    private List<GiftTicketInfo> convertToGiftTicketInfo(List<GiftTicketRequestDTO> giftTickets,
                                                                   List<TicketEntity> ticketEntities) {
        Map<Long, TicketEntity> ticketMap = ticketEntities.stream()
                .collect(Collectors.toMap(TicketEntity::getTicketId, Function.identity()));
        
        return giftTickets.stream()
                .map(giftTicket -> {
                    TicketEntity ticketEntity = ticketMap.get(giftTicket.getTicketId());
                    
                    GiftTicketInfo response = new GiftTicketInfo();
                    BeanUtils.copyProperties(ticketEntity, response);
                    response.setTicketNum(giftTicket.getTicketNum());
                    return response;
                })
                .collect(Collectors.toList());
    }
}