package com.jsrxjt.adapter.order.controller;

import cn.hutool.json.JSONUtil;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionOrderStatusNotifyDTO;
import com.jsrxjt.mobile.api.locallife.dto.request.LocalLifePayQueryRequest;
import com.jsrxjt.mobile.api.locallife.dto.request.LocalLifeRefundQueryRequest;
import com.jsrxjt.mobile.api.locallife.dto.response.LocalLifeOrderPayQueryResponse;
import com.jsrxjt.mobile.api.locallife.dto.response.LocalLifeOrderRefundQueryResponse;
import com.jsrxjt.mobile.api.physicalCardMall.dto.request.PhysicalCardCreateOrderRequestDTO;
import com.jsrxjt.mobile.api.physicalCardMall.dto.request.PhysicalCardRefundRequestDTO;
import com.jsrxjt.mobile.api.physicalCardMall.dto.response.PhysicalCardCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.physicalCardMall.dto.response.PhysicalCardRefundResponseDTO;
import com.jsrxjt.mobile.biz.distribution.service.PhysicalCardMallCaseService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 实体卡商城控制器
 * @Author: ywt
 * @Date: 2025-10-23 09:21
 * @Version: 1.0
 */
@RestController
@RequestMapping("/v1/physical-card")
@RequiredArgsConstructor
@Slf4j
public class PhysicalCardMallController {
    private final PhysicalCardMallCaseService physicalCardMallCaseService;

    @PostMapping("/prepay")
    @Operation(summary = "实体卡商城下单")
    public ApiResponse<PhysicalCardCreateOrderResponseDTO> prepay(PhysicalCardCreateOrderRequestDTO requestDTO) {
        log.info("实体卡商城-下单接口-请求参数：{}", JSONUtil.toJsonStr(requestDTO));
        return physicalCardMallCaseService.prePay(requestDTO);
    }

    @PostMapping("/refund")
    @Operation(summary = "实体卡商城退款")
    public ApiResponse<PhysicalCardRefundResponseDTO> refund(PhysicalCardRefundRequestDTO requestDTO) {
        log.info("实体卡商城-退款接口-请求参数：{}", JSONUtil.toJsonStr(requestDTO));
        return physicalCardMallCaseService.refund(requestDTO);
    }

    /**
     * 订单支付状态查询
     *  备注：这个方法没有实际意义，因为实体卡商城不处理这个接口返回的数据，但如果不提供这个接口实体卡商城会反复调用11次本接口
     * @param request
     * @return 直接返回成功
     */
    @PostMapping("/payQuery")
    public ApiResponse<LocalLifeOrderPayQueryResponse> payQuery(LocalLifePayQueryRequest request) {
        return ApiResponse.success();
    }

    /**
     * 订单退款状态查询
     *  备注：这个方法没有实际意义，因为实体卡商城不处理这个接口返回的数据，但如果不提供这个接口实体卡商城会反复调用11次本接口
     * @param request
     * @return  直接返回成功
     */
    @PostMapping("/refundQuery")
    public ApiResponse<LocalLifeOrderRefundQueryResponse> payQuery(LocalLifeRefundQueryRequest request) {
        return ApiResponse.success();
    }

    /**
     * 确认收货通知
     *  备注：福鲤圈不处理应用的确认收货消息
     * @param request
     * @return
     */

    @PostMapping("/confirmReceipt")
    public ApiResponse<Object> confirmReceipt(DistributionOrderStatusNotifyDTO request) {
        log.info("实体卡商城-确认收货通知-请求参数：{}", JSONUtil.toJsonStr(request));
        return ApiResponse.success();
    }

    /*@PostMapping("/refund")
    @Operation(summary = "本地生活订单退款")
    public ApiResponse<LocalLifeOrderRefundResponse> refund(LocalLifeRefundOrderDTO requestDTO) {
        log.info("localLifeOrderRefundNotify(request={})", JSONUtil.toJsonStr(requestDTO));
        return localLifeCaseService.refund(requestDTO);
    }

    *//**
     * 订单支付状态查询
     *
     * @param request
     * @return
     *//*
    @PostMapping("/payQuery")
    public ApiResponse<LocalLifeOrderPayQueryResponse> payQuery(LocalLifePayQueryRequest request) {
        return localLifeCaseService.payQuery(request);
    }

    @PostMapping("/refundQuery")
    public ApiResponse<LocalLifeOrderRefundQueryResponse> payQuery(LocalLifeRefundQueryRequest request) {
        return localLifeCaseService.refundQuery(request);
    }*/
}
