package com.jsrxjt.mobile.api.ticket.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 券核销返回结果
 * <AUTHOR>
 * @Date 2025/9/13
 */
@Data
public class TicketVerifyDetailResponseDTO {

    @Schema(description = "门店名称")
    private String name;

    @Schema(description = "门店编号")
    private String no;

    @Schema(description = "门店id")
    private Long shopId;

    @Schema(description = "核销状态 0成功 1失败")
    private int status;

}
