# 售后进度详情重构说明

## 重构背景

原有的 `buildProgressDetails` 方法依赖于 `AfterSaleLogEntity` 日志表来构建进度详情，但根据业务需求，需要改为直接根据 `AfterSaleEntity` 和 `OrderInfoEntity` 的状态来构建进度详情，提供更准确和一致的用户体验。

## 重构内容

### 1. 方法签名变更

**重构前**：
```java
private List<AfterSaleDetailResponseDTO.AfterSaleLogDTO> buildProgressDetails(List<AfterSaleLogEntity> logs)
```

**重构后**：
```java
private List<AfterSaleDetailResponseDTO.AfterSaleLogDTO> buildProgressDetails(AfterSaleEntity afterSale, OrderInfoEntity order)
```

### 2. 业务逻辑重构

根据伪代码要求，按照 `after_sale_status` 的不同状态构建不同数量的进度详情：

#### 状态1：待审核
- **返回**：1个进度详情
- **内容**：
  - operationDesc = "提交申请"
  - operationDetail = "您的申请已提交，请耐心等待客服审核"
  - operationTime = apply_time

#### 状态20/30/33：审核通过/驳回/撤销
- **返回**：2个进度详情
- **第一个**：提交申请（同上）
- **第二个**：客服审核
  - 状态20：operationDetail = "客服审核通过，请等待退款"，operationTime = audit_time
  - 状态30：operationDetail = "审核已驳回，请联系平台处理"，operationTime = audit_time
  - 状态33：operationDetail = "售后已撤销，请联系平台处理"，operationTime = cancel_time

#### 状态32：退款驳回
- **返回**：4个进度详情
- **前2个**：提交申请和客服审核（同状态20）
- **第三个**：平台退款
  - operationDesc = "平台退款"
  - operationDetail = "退款处理中"
  - operationTime = apply_time
- **第四个**：退款失败
  - operationDesc = "退款失败"
  - operationDetail = "退款已驳回，请联系客服"
  - operationTime = refund_reject_time

#### 状态34：售后完成
- **返回**：4个进度详情
- **前3个**：提交申请、客服审核、平台退款（同状态32）
- **第四个**：退款成功
  - 全额退款（after_sale_type == 2 || payment_status == 6）：operationDesc = "退款成功"
  - 部分退款：operationDesc = "部分退款成功"
  - operationDetail = "退款已完成，请查看到款信息"
  - operationTime = refund_success_time

### 3. 代码优化

新增了 `addSubmitAndAuditDetails` 辅助方法来复用前两个通用的进度详情构建逻辑，遵循DRY原则。

## 技术实现

### 核心方法

```java
private List<AfterSaleDetailResponseDTO.AfterSaleLogDTO> buildProgressDetails(
    AfterSaleEntity afterSale, OrderInfoEntity order) {
    
    List<AfterSaleDetailResponseDTO.AfterSaleLogDTO> details = new ArrayList<>();
    Integer afterSaleStatus = afterSale.getAfterSaleStatus();
    Integer afterSaleType = afterSale.getAfterSaleType();
    Integer paymentStatus = order.getPaymentStatus();
    
    // 根据不同状态构建对应的进度详情
    if (afterSaleStatus.equals(1)) {
        // 待审核逻辑
    } else if (afterSaleStatus.equals(20) || afterSaleStatus.equals(30) || afterSaleStatus.equals(33)) {
        // 审核通过/驳回/撤销逻辑
    } else if (afterSaleStatus.equals(32)) {
        // 退款驳回逻辑
    } else if (afterSaleStatus.equals(34)) {
        // 售后完成逻辑
    }
    
    return details;
}
```

### 辅助方法

```java
private void addSubmitAndAuditDetails(List<AfterSaleDetailResponseDTO.AfterSaleLogDTO> details, 
    AfterSaleEntity afterSale) {
    // 添加提交申请和客服审核的通用详情
}
```

## 测试验证

创建了完整的单元测试 `AfterSaleCaseServiceImplProgressTest`，覆盖所有状态场景：

1. **状态1测试**：验证待审核状态返回1个进度详情
2. **状态20测试**：验证审核通过状态返回2个进度详情
3. **状态30测试**：验证审核驳回状态返回2个进度详情
4. **状态33测试**：验证售后撤销状态返回2个进度详情
5. **状态32测试**：验证退款驳回状态返回4个进度详情
6. **状态34测试**：验证售后完成状态返回4个进度详情（全额/部分退款）

## 重构优势

### 1. 业务逻辑清晰
- 直接根据售后状态构建进度，逻辑更加直观
- 避免了依赖日志表可能存在的数据不一致问题

### 2. 性能提升
- 减少了对日志表的查询依赖
- 直接使用已有的实体数据，提高响应速度

### 3. 维护性提升
- 业务逻辑集中在一个方法中，便于维护
- 状态判断清晰，易于理解和修改

### 4. 数据一致性
- 基于实体状态构建，确保进度详情与实际状态一致
- 避免了日志记录不完整或错误导致的显示问题

## 影响范围

### 修改的文件
- `AfterSaleCaseServiceImpl.java`：重构 `buildProgressDetails` 方法
- 新增测试文件：`AfterSaleCaseServiceImplProgressTest.java`

### 依赖变更
- 移除了对 `AfterSaleLogEntity` 的依赖
- 新增了对 `OrderInfoEntity` 的依赖

### 接口兼容性
- 对外接口保持不变
- 返回的数据结构保持一致
- 只是数据来源和构建逻辑发生变化

## 后续建议

1. **数据验证**：在生产环境部署前，建议对比新旧逻辑的输出结果
2. **监控告警**：增加对售后进度显示的监控，及时发现异常
3. **文档更新**：更新相关的接口文档和业务流程文档
4. **性能测试**：验证重构后的性能表现是否符合预期

## 总结

这次重构成功地将售后进度详情的构建逻辑从依赖日志表改为直接基于实体状态，提高了数据的一致性和系统的性能，同时保持了接口的兼容性。通过完整的单元测试确保了重构的正确性和可靠性。
