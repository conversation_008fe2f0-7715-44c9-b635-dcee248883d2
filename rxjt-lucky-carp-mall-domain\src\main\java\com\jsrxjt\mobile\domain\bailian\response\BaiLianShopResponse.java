package com.jsrxjt.mobile.domain.bailian.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 百联门店信息
 * <AUTHOR>
 * @Date 2025/10/28
 **/
@Data
@Schema(description = "百联门店信息")
public class BaiLianShopResponse {

    @JSONField(name = "id")
    private Long id;

    @JSONField(name = "brand_id")
    private Long brandId;

    @JSONField(name = "shop_name")
    private String shopName;

    @JSONField(name = "address")
    private String address;

    @JSONField(name = "sort")
    private Integer sort;

    @JSONField(name = "is_enable")
    private Integer isEnable;

    @JSONField(name = "is_delete")
    private Integer isDelete;

    @JSONField(name = "create_time")
    private Date createTime;

    @JSONField(name = "update_time")
    private Date updateTime;

    @JSONField(name = "brand_name")
    private String brandName;

    @JSONField(name = "image_id")
    private Long imageId;

    @JSONField(name = "image")
    private BaiLianImageResponse imageResponse;
}
