package com.jsrxjt.mobile.api.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "支付宝红包应用SKU信息响应")
public class AlipayAppSkuResponse {

    @Schema(description = "SKU ID")
    private Long appSkuId;

    @Schema(description = "应用SPU ID")
    private Long appSpuId;

    @Schema(description = "外部平台ID")
    private String outPlatformId;

    @Schema(description = "面值名称")
    private String amountName;

    @Schema(description = "面值")
    private BigDecimal amount;

    @Schema(description = "建议售价")
    private BigDecimal platformPrice;

    @Schema(description = "手续费百分比")
    private BigDecimal commissionFee;

    @Schema(description = "外部平台状态")
    private String outPlatformStatus;

    @Schema(description = "支付宝预算余额")
    private BigDecimal budgetBalance;

    @Schema(description = "有效期")
    private String validPeriod;

    @Schema(description = "面值备注")
    private String remark;

    @Schema(description = "是否选中 0否 1是")
    private Byte isSkuSelect = 0;

}
