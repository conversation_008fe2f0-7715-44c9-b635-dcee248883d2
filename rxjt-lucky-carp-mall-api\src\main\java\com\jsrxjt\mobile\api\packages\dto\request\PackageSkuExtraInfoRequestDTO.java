package com.jsrxjt.mobile.api.packages.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 套餐sku其他信息请求参数
 * @Author: ywt
 * @Date: 2025-07-16 09:30
 * @Version: 1.0
 */
@Data
public class PackageSkuExtraInfoRequestDTO {
    @Schema(description = "套餐的spuid")
    @NotNull(message = "套餐spuid为空错误")
    private Long packageSpuId;
    @Schema(description = "套餐的skuid")
    private Long packageSkuId;
    @Schema(description = "三级地址id")
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
}
