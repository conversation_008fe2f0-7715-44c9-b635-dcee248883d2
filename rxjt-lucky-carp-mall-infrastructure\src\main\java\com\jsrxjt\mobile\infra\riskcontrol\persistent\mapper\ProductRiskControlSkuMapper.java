package com.jsrxjt.mobile.infra.riskcontrol.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.riskcontrol.persistent.po.ProductRiskControlSkuPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 产品风控策略sku的mapper
 * @Author: ywt
 * @Date: 2025-09-18 10:59
 * @Version: 1.0
 */
@Mapper
public interface ProductRiskControlSkuMapper extends CommonBaseMapper<ProductRiskControlSkuPO> {
    List<ProductRiskControlSkuPO> getRiskDisableProducts(@Param("accountName") String accountName,
                                                         @Param("companyId") Long companyId,
                                                         @Param("productSpuId") Long productSpuId,
                                                         @Param("productType") Integer productType);
}
