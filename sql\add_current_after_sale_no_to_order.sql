-- 为订单表添加当前售后单号字段
-- 执行时间：2025-11-03
-- 作者：系统
-- 描述：在同步售后单状态到订单时，同步当前售后单号到订单

-- 添加字段到t_order表
ALTER TABLE t_order 
ADD COLUMN current_after_sale_no VARCHAR(64) COMMENT '当前售后单号（最新的售后单号）' AFTER after_sale_status;

-- 创建索引以提高查询性能
CREATE INDEX idx_order_current_after_sale_no ON t_order (current_after_sale_no);

-- 添加注释
ALTER TABLE t_order MODIFY COLUMN current_after_sale_no VARCHAR(64) COMMENT '当前售后单号（最新的售后单号）';

-- 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() 
--   AND TABLE_NAME = 't_order' 
--   AND COLUMN_NAME = 'current_after_sale_no';
