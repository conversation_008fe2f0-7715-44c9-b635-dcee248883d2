package com.jsrxjt.mobile.domain.merchant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.common.core.util.geo.GeoUtil;
import com.jsrxjt.mobile.domain.merchant.entity.MerchantShopDataEntity;
import com.jsrxjt.mobile.domain.merchant.repository.MerchantRepository;
import com.jsrxjt.mobile.domain.merchant.request.MerchantShopRequest;
import com.jsrxjt.mobile.domain.merchant.service.MerchantService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 商户领域服务
 * @Author: ywt
 * @Date: 2025-08-25 10:34
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class MerchantServiceImpl implements MerchantService {
    private final MerchantRepository merchantRepository;

    @Override
    public List<MerchantShopDataEntity.MerchantShopEntity> getRxShopList(MerchantShopRequest request) {
        List<MerchantShopDataEntity.MerchantShopEntity> list = merchantRepository.getRxShopList();
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        list.forEach(item -> {
            if (StringUtils.isNotEmpty(request.getLatitude())
                    && StringUtils.isNotEmpty(request.getLongitude())
                    && StringUtils.isNotEmpty(item.getLat())
                    && StringUtils.isNotEmpty(item.getLng())) {
                item.setDistance(GeoUtil.getDistance(Double.parseDouble(request.getLatitude()), Double.parseDouble(request.getLongitude()),
                        Double.parseDouble(item.getLat()), Double.parseDouble(item.getLng())));
            } else {
                //缺失经纬度，默认使用南京到北极圈的距离
                item.setDistance((double) 3837270);
            }

        });
        List<MerchantShopDataEntity.MerchantShopEntity> responseList = list.stream().sorted(Comparator.comparingDouble(MerchantShopDataEntity.MerchantShopEntity::getDistance)).collect(Collectors.toList());
        return responseList;
    }
}
