package com.jsrxjt.mobile.api.order.types;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后状态枚举
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Getter
@AllArgsConstructor
public enum AfterSaleStatusEnum {

    /**
     * 待审核
     */
    PENDING_AUDIT(1, "待审核"),

    /**
     * 审核通过
     */
    AUDIT_PASSED(20, "审核通过"),

    /**
     * 审核驳回
     */
    AUDIT_REJECTED(30, "审核驳回"),

    /**
     * 退款驳回
     */
    REFUND_REJECTED(32, "退款驳回"),

    /**
     * 售后撤销
     */
    AFTER_SALE_CANCELLED(33, "售后撤销"),

    /**
     * 售后完成
     */
    AFTER_SALE_COMPLETED(34, "售后完成");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static AfterSaleStatusEnum getByCode(Integer code) {
        for (AfterSaleStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}