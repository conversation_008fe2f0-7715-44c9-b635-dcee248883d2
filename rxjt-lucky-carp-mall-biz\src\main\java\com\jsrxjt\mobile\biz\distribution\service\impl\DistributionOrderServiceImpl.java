package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.*;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.api.payment.response.PaymentResponseDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.distribution.service.DistributionOrderService;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.order.AfterSaleCaseService;
import com.jsrxjt.mobile.biz.order.AutoRefundCaseService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistVerifySignRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistConfigResponse;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.CommonDistributionOrderInfoBuilder;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2025/7/24 17:58
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DistributionOrderServiceImpl implements DistributionOrderService {

    private final OrderCaseService orderCaseService;

    private final OrderRepository orderRepository;

    private final AutoRefundCaseService autoRefundCaseService;

    private final AppGoodsRepository appGoodsRepository;

    private final CustomerRepository customerRepository;

    private final UnifiedDistributionApi unifiedDistributionApi;

    private final CommonDistributionOrderInfoBuilder commonDistributionOrderInfoBuilder;

    private final AfterSaleRepository afterSaleRepository;

    private final AfterSaleCaseService afterSaleCaseService;

    private final DistributedLock distributedLock;

    private final RegionRepository regionRepository;

    private static final String DISTRIBUTION_ORDER_LOCK_PREFIX = "distribution_order_notify_lock:";

    private static final String DISTRIBUTION_ORDER_REFUND_LOCK_PREFIX = "distribution_order_refund_notify_lock:";

    @Value("${flq.cashier.url}")
    private String cashierUrl;


    @Override
    public ApiResponse<DistributionCreateOrderResponseDTO> distributionOrderCreate(Long distributionId, DistributionOrderCreateNotifyDTO request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getOrderNo()) || StringUtils.isEmpty(request.getSignature())) {
            return ApiResponse.fail("参数不全");
        }

        DistributionCreateOrderResponseDTO responseDTO = new DistributionCreateOrderResponseDTO();
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(distributionId);
        if (appGoodsEntity == null) {
            return ApiResponse.fail("当前应用不存在或已下架");
        }
        String appFlag = appGoodsEntity.getAppFlag();
        DistChannelType distChannelType = DistChannelType.valueOf(appFlag);
        DistVerifySignRequest distVerifySignRequest = DistVerifySignRequest
                .builder()
                .channelType(distChannelType)
                .verifySignDTO(request)
                .build();

        if (!unifiedDistributionApi.verifySign(distVerifySignRequest)) {
            return ApiResponse.fail("签名验证失败");
        }
        String orderNo = request.getOrderNo();
        String lockKey = DISTRIBUTION_ORDER_LOCK_PREFIX + orderNo;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取应用订单通知处理锁失败，订单号：{}", orderNo);
                return ApiResponse.fail("系统繁忙，请稍后重试");
            }
            String tradeNo = request.getTradeNo();
            OrderInfoEntity existOrderEntity = orderRepository.findByExternalOrderNoAndAppFlag(orderNo, appFlag);
            // 幂等校验，根据外部单号和应用类型查询订单并记录最新一次的外部交易单号
            if (existOrderEntity != null) {
                log.error("此订单已存在,三方订单号,{},应用标记为:{},三方交易单号为:{}", orderNo, appFlag, tradeNo);
                if (!Objects.equals(existOrderEntity.getDistTradeNo(), tradeNo)) {
                    OrderInfoEntity updateOrder = new OrderInfoEntity();
                    updateOrder.setId(existOrderEntity.getId());
                    updateOrder.setDistTradeNo(tradeNo);
                    updateOrder.setCustomerId(existOrderEntity.getCustomerId());
                    orderRepository.updateOrder(updateOrder);
                }
                responseDTO.setThirdOrderNo(existOrderEntity.getOrderNo());
                responseDTO.setCashierUrl(cashierUrl + existOrderEntity.getOrderNo());
                return ApiResponse.success(responseDTO);
            }
            CustomerEntity customerEntity = null;
            if (StringUtils.isNotBlank(request.getUserId())) {
                Long customerId = Long.valueOf(request.getUserId());
                customerEntity = customerRepository.selectCustomerById(customerId);

            } else if (StringUtils.isNotBlank(request.getUnionId())) {
                customerEntity = customerRepository.selectCustomerByUnionId(request.getUnionId());
            }
            if (customerEntity == null) {
                log.error("未找到会员id或unionid：{}的会员信息", request.getUserId() == null ? request.getUnionId() : request.getUserId());
                return ApiResponse.fail("用户不存在");
            }
            CreateOrderDTO createOrderDTO = new CreateOrderDTO();
            createOrderDTO.setCustomerId(customerEntity.getId());
            createOrderDTO.setCustomerMobile(customerEntity.getPhone());
            createOrderDTO.setExternalOrderNo(request.getOrderNo());
            createOrderDTO.setDistTradeNo(request.getTradeNo());
            createOrderDTO.setProductSpuId(distributionId);
            createOrderDTO.setProductType(ProductTypeEnum.APP.getType());
            // 兼容金额字段不同场景
            BigDecimal externalOrderAmount = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(request.getTotalAmount())) {
                externalOrderAmount = BigDecimal.valueOf(Double.parseDouble(request.getTotalAmount()));
            }
            if (StringUtils.isNotBlank(request.getTradeAmount())) {
                externalOrderAmount = BigDecimal.valueOf(Double.parseDouble(request.getTradeAmount()));
            }
            createOrderDTO.setExternalAppProductPrice(externalOrderAmount);
            // 兼容不同场景订单支付超时时间
            String orderTime = request.getOrderTime();
            String orderExpire = request.getOrderExpire();
            if (StringUtils.isNotBlank(orderTime) && StringUtils.isNotBlank(orderExpire)) {
                if (orderExpire.length() == 10) {
                    createOrderDTO.setExternalOrderExpireTime(orderExpire);
                } else {
                    Instant originalInstant = Instant.ofEpochSecond(Long.parseLong(orderTime));
                    // 增加分钟
                    Instant newInstant = originalInstant.plus(Duration.ofMinutes(Long.parseLong(orderExpire)));
                    // 获取新的时间戳
                    long externalOrderExpireTime = newInstant.getEpochSecond();
                    createOrderDTO.setExternalOrderExpireTime(String.valueOf(externalOrderExpireTime));
                }
            }
            if (StringUtils.isNotBlank(request.getExpireTime())) {
                createOrderDTO.setExternalOrderExpireTime(request.getExpireTime());
            }
            // 兼容订单支付完成跳转页面,需解码
            if (StringUtils.isNotBlank(request.getResultPageUrl())) {
                createOrderDTO.setExternalPayResultUrl(decodeBase64URLSafe(request.getResultPageUrl()));
            }
            if (StringUtils.isNotBlank(request.getResultUrl())) {
                createOrderDTO.setExternalPayResultUrl(request.getResultUrl());
            }
            // 订单详情页跳转页面,需解码detailPageUrl,returnUrl
            if (StringUtils.isNotBlank(request.getDetailPageUrl())) {
                createOrderDTO.setOrderDetailUrl(decodeBase64URLSafe(request.getDetailPageUrl()));
            }
            // 美团,小象
            if (StringUtils.isNotBlank(request.getReturnUrl())) {
                createOrderDTO.setOrderDetailUrl(decodeBase64URLSafe(request.getReturnUrl()));
            }
            // detailUrl,orderDetailUrl不需要解码
            if (StringUtils.isNotBlank(request.getDetailUrl())) {
                DistConfigResponse distributionConfig = unifiedDistributionApi.getDistributionConfig(distChannelType);
                String appId = distributionConfig == null ? "" : distributionConfig.getAppId();
                String detailUrl = request.getDetailUrl() + "?app_id=" + appId + "&user_code=" + request.getUserId();
                createOrderDTO.setExternalPayResultUrl(detailUrl);
            }
            if (StringUtils.isNotBlank(request.getOrderDetailUrl())) {
                createOrderDTO.setOrderDetailUrl(request.getOrderDetailUrl());
            }
            // 部分分销应用需要特殊处理下单请求数据
            extraHandleOrderCreateDTO(createOrderDTO,distChannelType);

            log.info("处理应用订单通知请求参数为:{}", JSONUtil.toJsonStr(createOrderDTO));
            OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO, commonDistributionOrderInfoBuilder);
            if (Objects.isNull(orderInfoEntity) || StringUtils.isEmpty(orderInfoEntity.getOrderNo())) {
                return ApiResponse.fail("下单失败");
            }
            responseDTO.setThirdOrderNo(orderInfoEntity.getOrderNo());
            // 同步并免密支付返回支付结果
            if (Objects.equals(1, appGoodsEntity.getIsSyncPayment()) && Objects.equals(1, appGoodsEntity.getIsWithPass())) {
                PaymentResponseDTO paymentResponseDTO = orderCaseService.orderAutoPay(orderInfoEntity);
                if (paymentResponseDTO == null || !Objects.equals("SUCCESS", paymentResponseDTO.getPayStatus())) {
                    if (Objects.equals(DistChannelType.BAILIAN.name(), appFlag)) {
                        responseDTO.setPayStatus("40");
                    } else {
                        responseDTO.setPayStatus("0");
                        responseDTO.setPayAmount(String.valueOf(externalOrderAmount));
                    }
                } else {
                    if (Objects.equals(DistChannelType.BAILIAN.name(), appFlag)) {
                        responseDTO.setPayStatus("20");
                        responseDTO.setTradeTime(String.valueOf(System.currentTimeMillis() / 1000));
                    } else {
                        responseDTO.setPayStatus("1");
                        responseDTO.setPayAmount(String.valueOf(externalOrderAmount));
                        responseDTO.setPayTime(LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_FORMATTER));
                    }
                }
            } else {
                // 异步支付返回收银台地址
                responseDTO.setCashierUrl(cashierUrl + orderInfoEntity.getOrderNo());
            }
            log.info("处理应用订单通知成功,结果为:{}", JSONUtil.toJsonStr(responseDTO));
            return ApiResponse.success(responseDTO);
        } catch (Exception e) {
            log.error("处理应用订单通知异常，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            return ApiResponse.fail("系统异常,请稍后再试!");
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public ApiResponse<WalMartOrderCreateResponseDTO> walMartDistributionOrderCreateNotify(Long distributionId, WalMartOrderCreateNotifyDTO request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getUserId())
                || StringUtils.isEmpty(request.getOrderNumber())
                || StringUtils.isEmpty(request.getSignature())
        ) {
            return ApiResponse.fail("参数不全");
        }
        WalMartOrderCreateResponseDTO responseDTO = new WalMartOrderCreateResponseDTO();
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(distributionId);
        if (appGoodsEntity == null) {
            return ApiResponse.fail("当前应用不存在或已下架");
        }
        String appFlag = appGoodsEntity.getAppFlag();
        DistChannelType distChannelType = DistChannelType.valueOf(appFlag);
        DistVerifySignRequest distVerifySignRequest = DistVerifySignRequest
                .builder()
                .channelType(distChannelType)
                .verifySignDTO(request)
                .build();
        if (!unifiedDistributionApi.verifySign(distVerifySignRequest)) {
            return ApiResponse.fail("签名验证失败");
        }
        String outOrderSn = request.getOrderNumber();
        String lockKey = DISTRIBUTION_ORDER_LOCK_PREFIX + outOrderSn;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取沃尔玛应用订单通知处理锁失败，订单号：{}", outOrderSn);
                return ApiResponse.fail("系统繁忙，请稍后重试");
            }
            String orderNo = request.getOrderNumber();
            OrderInfoEntity existOrderEntity = orderRepository.findByExternalOrderNoAndAppFlag(orderNo, appFlag);
            // 幂等校验，根据外部单号和应用类型查询订单
            if (existOrderEntity != null) {
                log.error("此订单已存在,三方订单号,{},应用标记为:{}", orderNo, appFlag);
                responseDTO.setOrderNumber(existOrderEntity.getExternalOrderNo());
                responseDTO.setThirdOrderSn(existOrderEntity.getOrderNo());
                responseDTO.setPaymentLink(cashierUrl + existOrderEntity.getOrderNo());
                responseDTO.setPayMoney(String.valueOf(existOrderEntity.getTotalSellAmount()));
                return ApiResponse.success0(responseDTO);
            }
            CustomerEntity customerEntity = null;
            if (StringUtils.isNotBlank(request.getUserId())) {
                Long customerId = Long.valueOf(request.getUserId());
                customerEntity = customerRepository.selectCustomerById(customerId);

            }
            if (customerEntity == null) {
                log.error("未找到会员id：{}的会员信息", request.getUserId());
                return ApiResponse.fail("用户不存在");
            }
            // 构建创建订单参数
            CreateOrderDTO createOrderDTO = new CreateOrderDTO();
            createOrderDTO.setCustomerId(customerEntity.getId());
            createOrderDTO.setCustomerMobile(customerEntity.getPhone());
            createOrderDTO.setExternalOrderNo(request.getOrderNumber());
            createOrderDTO.setProductSpuId(distributionId);
            createOrderDTO.setProductType(ProductTypeEnum.APP.getType());
            BigDecimal externalOrderAmount = BigDecimal.valueOf(Double.parseDouble(request.getPayMoney()));
            createOrderDTO.setExternalAppProductPrice(externalOrderAmount);
            if (StringUtils.isNotBlank(request.getExpireTime())) {
                LocalDateTime localDateTime = LocalDateTime.parse(request.getExpireTime(), DatePattern.NORM_DATETIME_FORMATTER);
                createOrderDTO.setExternalOrderExpireTime(String.valueOf(localDateTime.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond()));
            }
            if (StringUtils.isNotBlank(request.getResultPageUrl())) {
                createOrderDTO.setExternalPayResultUrl(request.getResultPageUrl());
            }
            // 若无订单详情页，跳转至首页
//            if (StringUtils.isEmpty(createOrderDTO.getOrderDetailUrl())){
//                try {
//                    RegionEntity region = regionRepository.getCurrentRegion(customerEntity.getId());
//                    DistAccessRequest redirectUrlDTO = DistAccessRequest.builder()
//                            .channelType(distChannelType)
//                            .userId(String.valueOf(customerEntity.getId()))
//                            .mobile(customerEntity.getPhone())
//                            .latitude(region.getLat())
//                            .longitude(region.getLng())
//                            .build();
//                    DistAccessResponse distAccessResponse = unifiedDistributionApi.access(redirectUrlDTO);
//                    createOrderDTO.setOrderDetailUrl(distAccessResponse.getRedirectUrl());
//                } catch (Exception e) {
//                    log.error("未获取到用户定位信息 customer={}", customerEntity.getId());
//                }
//
//            }

            log.info("处理沃尔玛订单通知请求参数为:{}", JSONUtil.toJsonStr(createOrderDTO));
            OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO, commonDistributionOrderInfoBuilder);
            if (Objects.isNull(orderInfoEntity) || StringUtils.isEmpty(orderInfoEntity.getOrderNo())) {
                return ApiResponse.fail("下单失败");
            }
            responseDTO.setOrderNumber(orderInfoEntity.getExternalOrderNo());
            responseDTO.setThirdOrderSn(orderInfoEntity.getOrderNo());
            responseDTO.setPaymentLink(cashierUrl + orderInfoEntity.getOrderNo());
            responseDTO.setPayMoney(request.getPayMoney());
            log.info("处理沃尔玛应用订单通知成功,结果为:{}", JSONUtil.toJsonStr(responseDTO));
            return ApiResponse.success0(responseDTO);
        } catch (Exception e) {
            log.error("处理沃尔玛订单通知异常，订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
            return ApiResponse.fail("系统异常,请稍后再试!");
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }


    }

    @Override
    public ApiResponse<ELeMeOrderCreateResponseDTO> eLeMeDistributionOrderCreateNotify(Long distributionId, ELeMeOrderCreateNotifyDTO request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getUserCode()) || StringUtils.isEmpty(request.getTransactionId()) || StringUtils.isEmpty(request.getPayAmount()) ) {
            return ApiResponse.failForCode("参数不全");
        }
        ELeMeOrderCreateResponseDTO responseDTO = new ELeMeOrderCreateResponseDTO();
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(distributionId);
        if (appGoodsEntity == null) {
            return ApiResponse.failForCode("当前应用不存在或已下架");
        }
        String appFlag = appGoodsEntity.getAppFlag();
        DistChannelType distChannelType = DistChannelType.valueOf(appFlag);
        DistVerifySignRequest distVerifySignRequest = DistVerifySignRequest
                .builder()
                .channelType(distChannelType)
                .verifySignDTO(request)
                .build();
        if (!unifiedDistributionApi.verifySign(distVerifySignRequest)) {
            return ApiResponse.failForCode("签名验证失败");
        }
        String outOrderSn = request.getTransactionId();
        String lockKey = DISTRIBUTION_ORDER_LOCK_PREFIX + outOrderSn;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取饿了么应用订单通知处理锁失败，订单号：{}", outOrderSn);
                return ApiResponse.failForCode("系统繁忙，请稍后重试");
            }
            OrderInfoEntity existOrderEntity = orderRepository.findByExternalOrderNoAndAppFlag(outOrderSn, appFlag);
            // 幂等校验，根据外部单号和应用类型查询订单
            if (existOrderEntity != null) {
                log.error("此订单已存在,三方订单号,{},应用标记为:{}", outOrderSn, appFlag);
                responseDTO.setUrl(cashierUrl + existOrderEntity.getOrderNo());
                responseDTO.setOutTradeNo(existOrderEntity.getOrderNo());
                return ApiResponse.successForCode(responseDTO);
            }
            CustomerEntity customerEntity = null;
            if (StringUtils.isNotBlank(request.getUserCode())) {
                Long customerId = Long.valueOf(request.getUserCode());
                customerEntity = customerRepository.selectCustomerById(customerId);
            }
            if (customerEntity == null) {
                log.error("未找到会员id：{}的会员信息", request.getUserCode());
                return ApiResponse.failForCode("用户不存在");
            }
            // 构建创建订单参数
            CreateOrderDTO createOrderDTO = new CreateOrderDTO();
            createOrderDTO.setCustomerId(customerEntity.getId());
            createOrderDTO.setCustomerMobile(customerEntity.getPhone());
            createOrderDTO.setExternalOrderNo(request.getTransactionId());
            createOrderDTO.setProductSpuId(distributionId);
            createOrderDTO.setProductType(ProductTypeEnum.APP.getType());
            BigDecimal externalOrderAmount = new BigDecimal(request.getPayAmount()).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            createOrderDTO.setExternalAppProductPrice(externalOrderAmount);
            LocalDateTime localDateTime = LocalDateTime.parse(request.getTimeExpire(), DatePattern.PURE_DATETIME_FORMATTER);
            createOrderDTO.setExternalOrderExpireTime(String.valueOf(localDateTime.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond()));
            createOrderDTO.setExternalPayResultUrl(request.getRedirectUrl());
            // 若无订单详情页，跳转至首页
            if (StringUtils.isEmpty(createOrderDTO.getOrderDetailUrl())){
                try {
                    RegionEntity region = regionRepository.getCurrentRegion(customerEntity.getId());
                    DistAccessRequest redirectUrlDTO = DistAccessRequest.builder()
                            .channelType(distChannelType)
                            .userId(String.valueOf(customerEntity.getId()))
                            .mobile(customerEntity.getPhone())
                            .latitude(region.getLat())
                            .longitude(region.getLng())
                            .build();
                    DistAccessResponse distAccessResponse = unifiedDistributionApi.access(redirectUrlDTO);
                    createOrderDTO.setOrderDetailUrl(distAccessResponse.getRedirectUrl());
                } catch (Exception e) {
                    log.error("未获取到用户定位信息 customer={}", customerEntity.getId());
                }
            }
            log.info("处理饿了么订单通知请求参数为:{}", JSONUtil.toJsonStr(createOrderDTO));
            OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO, commonDistributionOrderInfoBuilder);
            if (Objects.isNull(orderInfoEntity) || StringUtils.isEmpty(orderInfoEntity.getOrderNo())) {
                return ApiResponse.failForCode("下单失败");
            }
            responseDTO.setOutTradeNo(orderInfoEntity.getOrderNo());
            responseDTO.setUrl(cashierUrl + orderInfoEntity.getOrderNo());
            log.info("处理饿了么应用订单通知成功,结果为:{}", JSONUtil.toJsonStr(responseDTO));
            return ApiResponse.successForCode(responseDTO);
        } catch (Exception e) {
            log.error("处理饿了么订单通知异常，订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
            return ApiResponse.failForCode("系统异常,请稍后再试!");
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public ApiResponse<DistributionOrderRefundResponseDTO> distributionOrderRefund(DistributionOrderRefundNotifyDTO request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getRefundOrderNo())
                || StringUtils.isEmpty(request.getOrderNo())
                || StringUtils.isEmpty(request.getRefundAmount())
        ) {
            return ApiResponse.fail("参数不全");
        }
        // 校验金额是否异常
        BigDecimal refundAmount = new BigDecimal(request.getRefundAmount());
        if (refundAmount.compareTo(BigDecimal.ZERO) < 0) {
            return ApiResponse.fail("订单金额非法");
        }
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(request.getOrderNo(), null);
        if (order == null) {
            return ApiResponse.fail("订单不存在");
        }
        DistChannelType distChannelType = DistChannelType.valueOf(order.getAppFlag());
        DistVerifySignRequest distVerifySignRequest = DistVerifySignRequest
                .builder()
                .channelType(distChannelType)
                .verifySignDTO(request)
                .build();

        if (!unifiedDistributionApi.verifySign(distVerifySignRequest)) {
            return ApiResponse.fail("签名验证失败");
        }
        String distributionRefundNo = request.getRefundOrderNo();
        String lockKey = DISTRIBUTION_ORDER_REFUND_LOCK_PREFIX + distributionRefundNo;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取应用订单退款通知处理锁失败，订单号：{}", distributionRefundNo);
                return ApiResponse.fail("系统繁忙，请稍后重试");
            }
            AfterSaleEntity afterSaleEntity = new AfterSaleEntity();
            Integer paymentStatus = order.getPaymentStatus();
            if (Objects.equals(paymentStatus, PaymentStatusEnum.UNPAID.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_PAID.getCode())) {
                return ApiResponse.fail("订单状态不可退");
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.PAID.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDING.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDED.getCode())) {
                AutoRefundRequestDTO autoRefundRequestDTO = new AutoRefundRequestDTO();
                autoRefundRequestDTO.setOrderNo(order.getOrderNo());
                autoRefundRequestDTO.setExternalRefundNo(request.getRefundOrderNo());
                autoRefundRequestDTO.setExternalRefundAmount(refundAmount);
                // 下单时第三方订单金额
                BigDecimal orderAmount = order.getTotalSellAmount();
                // 整单退
                if (orderAmount.compareTo(refundAmount) == 0) {
                    // 整单退场景需要退手续费
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode());
                    autoRefundRequestDTO.setApplyRefundAmount(order.getOrderAmount());
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else if (orderAmount.compareTo(refundAmount) > 0) {
                    // 部分退
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.PARTIAL_REFUND.getCode());
                    autoRefundRequestDTO = afterSaleCaseService.calDistributionOrderRefundAmount(autoRefundRequestDTO);
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else {
                    return ApiResponse.fail("退款金额大于订单金额");
                }
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDED.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDING.getCode())) {
                return ApiResponse.fail("已整单退款,不可再退");
            }
            DistConfigResponse distributionConfig = unifiedDistributionApi.getDistributionConfig(distChannelType);
            // 异步退款通知的应用场景
            if (!Objects.isNull(distributionConfig) && Objects.equals(0, distributionConfig.getSyncRefundFlag())) {
                DistributionOrderRefundResponseDTO response = DistributionOrderRefundResponseDTO
                        .builder()
                        .thirdRefundOrderNo(afterSaleEntity.getRefundNo())
                        .build();
                log.info("分销应用异步退款返回数据{}", JSONUtil.toJsonStr(response));
                return ApiResponse.success(response);
            } else {
                // 同步退款的应用场景
                // 退款成功
                if (Objects.equals(RefundStatusEnum.REFUND_SUCCESS.getCode(), afterSaleEntity.getRefundStatus())) {
                    DistributionOrderRefundResponseDTO response = DistributionOrderRefundResponseDTO
                            .builder()
                            .thirdRefundOrderNo(afterSaleEntity.getRefundNo())
                            .refundStatus("10")
                            .refundAmount(String.valueOf(afterSaleEntity.getApplyRefundAmount()))
                            .refundTime(LocalDateTimeUtil.format(order.getCreateTime(), DatePattern.PURE_DATETIME_FORMATTER))
                            .build();
                    log.info("分销应用同步退款返回数据{}", JSONUtil.toJsonStr(response));
                    return ApiResponse.success(response);
                } else {
                    return ApiResponse.fail("退款失败");
                }
            }
        } catch (Exception e) {
            log.error("处理应用订单退款通知异常，订单号：{}，错误信息：{}", distributionRefundNo, e.getMessage(), e);
            return ApiResponse.fail("系统异常,请稍后再试!");
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public ApiResponse<WalMartOrderRefundResponseDTO> walMartDistributionOrderRefund(WalMartOrderRefundNotifyDTO request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getAfterSaleNumber())
                || StringUtils.isEmpty(request.getThirdOrderSn())
                || StringUtils.isEmpty(request.getRefundMoney())
                || StringUtils.isEmpty(request.getOrderNumber())
        ) {
            return ApiResponse.fail("参数不全");
        }
        // 校验金额是否异常
        BigDecimal refundAmount = new BigDecimal(request.getRefundMoney());
        if (refundAmount.compareTo(BigDecimal.ZERO) < 0) {
            return ApiResponse.fail("订单金额非法");
        }
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(request.getOrderNumber(), null);
        if (order == null) {
            return ApiResponse.fail("订单不存在");
        }
        DistChannelType distChannelType = DistChannelType.valueOf(order.getAppFlag());
        DistVerifySignRequest distVerifySignRequest = DistVerifySignRequest
                .builder()
                .channelType(distChannelType)
                .verifySignDTO(request)
                .build();

        if (!unifiedDistributionApi.verifySign(distVerifySignRequest)) {
            return ApiResponse.fail("签名验证失败");
        }
        String distributionRefundNo = request.getAfterSaleNumber();
        String lockKey = DISTRIBUTION_ORDER_REFUND_LOCK_PREFIX + distributionRefundNo;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取沃尔玛应用订单退款通知处理锁失败，订单号：{}", distributionRefundNo);
                return ApiResponse.fail("系统繁忙，请稍后重试");
            }
            AfterSaleEntity afterSaleEntity = new AfterSaleEntity();
            Integer paymentStatus = order.getPaymentStatus();
            if (Objects.equals(paymentStatus, PaymentStatusEnum.UNPAID.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_PAID.getCode())) {
                return ApiResponse.fail("订单状态不可退");
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.PAID.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDING.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDED.getCode())) {
                AutoRefundRequestDTO autoRefundRequestDTO = new AutoRefundRequestDTO();
                autoRefundRequestDTO.setOrderNo(order.getOrderNo());
                autoRefundRequestDTO.setExternalRefundAmount(refundAmount);
                BigDecimal orderAmount = order.getTotalSellAmount();
                // 整单退
                if (orderAmount.compareTo(refundAmount) == 0) {
                    // 整单退场景需要退手续费
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode());
                    autoRefundRequestDTO.setApplyRefundAmount(order.getOrderAmount());
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else if (orderAmount.compareTo(refundAmount) > 0) {
                    // 部分退
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.PARTIAL_REFUND.getCode());
                    autoRefundRequestDTO = afterSaleCaseService.calDistributionOrderRefundAmount(autoRefundRequestDTO);
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else {
                    return ApiResponse.fail("退款金额大于订单金额");
                }
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDED.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDING.getCode())) {
                return ApiResponse.fail("已整单退款,不可再退");
            }
            // 同步退款的应用场景
            // 退款成功
            if (Objects.equals(RefundStatusEnum.REFUND_SUCCESS.getCode(), afterSaleEntity.getRefundStatus())) {
                WalMartOrderRefundResponseDTO response = WalMartOrderRefundResponseDTO
                        .builder()
                        .orderNumber(request.getOrderNumber())
                        .thirdOrderSn(request.getThirdOrderSn())
                        .afterSaleNumber(distributionRefundNo)
                        .refundStatus("1")
                        .refundTime(LocalDateTimeUtil.format(order.getCreateTime(), DatePattern.PURE_DATETIME_FORMATTER))
                        .build();
                log.info("沃尔玛分销应用同步退款返回数据{}", JSONUtil.toJsonStr(response));
                return ApiResponse.success0(response);
            } else {
                return ApiResponse.fail("退款失败");
            }
        } catch (Exception e) {
            log.error("处理沃尔玛应用订单退款通知异常，订单号：{}，错误信息：{}", distributionRefundNo, e.getMessage(), e);
            return ApiResponse.fail("系统异常,请稍后再试!");
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public ApiResponse<ELeMeOrderRefundResponseDTO> eLeMeDistributionOrderRefund(ELeMeOrderRefundNotifyDTO request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getTransactionId()) || StringUtils.isEmpty(request.getRefundNo()) || StringUtils.isEmpty(request.getRefundAmount())) {
            return ApiResponse.failForCode("参数不全");
        }
        // 校验金额是否异常
        BigDecimal refundAmount = new BigDecimal(request.getRefundAmount()).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        if (refundAmount.compareTo(BigDecimal.ZERO) < 0) {
            return ApiResponse.failForCode("订单金额非法");
        }
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(request.getTransactionId(), null);
        if (order == null) {
            return ApiResponse.failForCode("订单不存在");
        }
        DistChannelType distChannelType = DistChannelType.valueOf(order.getAppFlag());
        DistVerifySignRequest distVerifySignRequest = DistVerifySignRequest
                .builder()
                .channelType(distChannelType)
                .verifySignDTO(request)
                .build();

        if (!unifiedDistributionApi.verifySign(distVerifySignRequest)) {
            return ApiResponse.failForCode("签名验证失败");
        }
        String distributionRefundNo = request.getRefundNo();
        String lockKey = DISTRIBUTION_ORDER_REFUND_LOCK_PREFIX + distributionRefundNo;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取饿了么应用订单退款通知处理锁失败，订单号：{}", distributionRefundNo);
                return ApiResponse.failForCode("系统繁忙，请稍后重试");
            }
            AfterSaleEntity afterSaleEntity = new AfterSaleEntity();
            Integer paymentStatus = order.getPaymentStatus();
            if (Objects.equals(paymentStatus, PaymentStatusEnum.UNPAID.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_PAID.getCode())) {
                return ApiResponse.failForCode("订单状态不可退");
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.PAID.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDING.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDED.getCode())) {
                AutoRefundRequestDTO autoRefundRequestDTO = new AutoRefundRequestDTO();
                autoRefundRequestDTO.setOrderNo(order.getOrderNo());
                autoRefundRequestDTO.setExternalRefundNo(distributionRefundNo);
                autoRefundRequestDTO.setExternalRefundAmount(refundAmount);
                BigDecimal orderAmount = order.getTotalSellAmount();
                // 整单退
                if (orderAmount.compareTo(refundAmount) == 0) {
                    // 整单退场景需要退手续费
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode());
                    autoRefundRequestDTO.setApplyRefundAmount(order.getOrderAmount());
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else if (orderAmount.compareTo(refundAmount) > 0) {
                    // 部分退
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.PARTIAL_REFUND.getCode());
                    autoRefundRequestDTO = afterSaleCaseService.calDistributionOrderRefundAmount(autoRefundRequestDTO);
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else {
                    return ApiResponse.failForCode("退款金额大于订单金额");
                }
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDED.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDING.getCode())) {
                return ApiResponse.failForCode("已整单退款,不可再退");
            }
            // 同步退款的应用场景
            // 退款成功
            if (Objects.equals(RefundStatusEnum.REFUND_SUCCESS.getCode(), afterSaleEntity.getRefundStatus())) {
                ELeMeOrderRefundResponseDTO response = ELeMeOrderRefundResponseDTO
                        .builder()
                        .transactionId(request.getTransactionId())
                        .outTradeNo(order.getOrderNo())
                        .refundNo(request.getRefundNo())
                        .outRefundNo(afterSaleEntity.getRefundNo())
                        .refundAmount(afterSaleEntity.getApplyRefundAmount().multiply(new BigDecimal("100")).intValue())
                        .refundStatus("SUCCESS")
                        .build();
                log.info("饿了么分销应用同步退款返回数据{}", JSONUtil.toJsonStr(response));
                return ApiResponse.successForCode(response);
            } else {
                return ApiResponse.failForCode("退款失败");
            }
        } catch (Exception e) {
            log.error("处理饿了么应用订单退款通知异常，订单号：{}，错误信息：{}", distributionRefundNo, e.getMessage(), e);
            return ApiResponse.failForCode("系统异常,请稍后再试!");
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public ApiResponse<DistributionOrderPayInfoWithAmountResponseDTO> distributionOrderPayInfoWithPayAmount(DistributionOrderPayInfoQueryDTO request) {
        DistributionOrderPayInfoWithAmountResponseDTO dto = new DistributionOrderPayInfoWithAmountResponseDTO();
        String orderNo = request.getOrderNo();
        String tradeNo = request.getTradeNo();
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(orderNo, tradeNo);
        if (order != null) {
            dto.setOrderNo(orderNo);
            dto.setTradeNo(tradeNo);
            dto.setThirdOrderNo(order.getOrderNo());
            Integer payStatus = order.getPaymentStatus();
            PaymentStatusEnum paymentStatusEnum = PaymentStatusEnum.getByCode(payStatus);
            switch (Objects.requireNonNull(paymentStatusEnum)) {
                case UNPAID -> {
                    dto.setTradeStatus("01");
                    dto.setTradeTime(0);
                    dto.setPayAmount("0.00");
                }
                case PAID -> {
                    dto.setTradeStatus("00");
                    dto.setTradeTime((int) order.getPaymentTime()
                            .atZone(ZoneId.systemDefault())
                            .toInstant()
                            .getEpochSecond());
                    dto.setPayAmount(String.valueOf(order.getPaymentAmount()));
                }
//                case FAIL_PAY -> {
//                    dto.setTradeStatus("02");
//                    dto.setTradeTime((int) tradeOrder.getTradeTime()
//                            .atZone(ZoneId.systemDefault())
//                            .toInstant()
//                            .getEpochSecond());
//                    dto.setPayAmount(String.valueOf(tradeOrder.getPayAmount()));
//                }
            }
            log.info("查询订单支付结果为:{}", JSONUtil.toJsonStr(dto));
            return ApiResponse.success(dto);
        } else {
            return ApiResponse.fail("订单不存在");
        }
    }

    @Override
    public ApiResponse<DistributionOrderPayInfoResponseDTO> distributionOrderPayInfo(DistributionOrderPayInfoQueryDTO request) {
        DistributionOrderPayInfoResponseDTO dto = new DistributionOrderPayInfoResponseDTO();
        String orderNo = request.getOrderNo();
        String tradeNo = request.getTradeNo();
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(orderNo, tradeNo);
        if (order != null) {
            dto.setOrderNo(orderNo);
            dto.setTradeNo(tradeNo);
            dto.setThirdOrderNo(order.getOrderNo());
            Integer payStatus = order.getPaymentStatus();
            PaymentStatusEnum paymentStatusEnum = PaymentStatusEnum.getByCode(payStatus);
            switch (Objects.requireNonNull(paymentStatusEnum)) {
                case UNPAID -> {
                    dto.setTradeStatus(0);
                    dto.setTradeTime(0);
                }
                case PAID -> {
                    dto.setTradeStatus(1);
                    dto.setTradeTime((int) order.getPaymentTime()
                            .atZone(ZoneId.systemDefault())
                            .toInstant()
                            .getEpochSecond());
                }
//                case FAIL_PAY -> {
//                    dto.setTradeStatus("2");
//                    dto.setTradeTime((int) tradeOrder.getTradeTime()
//                            .atZone(ZoneId.systemDefault())
//                            .toInstant()
//                            .getEpochSecond());
//                }
            }
            log.info("查询订单支付结果为:{}", JSONUtil.toJsonStr(dto));
            return ApiResponse.success(dto);
        } else {
            return ApiResponse.fail("订单不存在");
        }
    }

    @Override
    public ApiResponse<DistributionOrderRefundInfoResponseDTO> distributionOrderRefundInfo(DistributionOrderRefundQueryDTO request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getRefundNo()) || StringUtils.isEmpty(request.getTradeNo())) {
            return ApiResponse.fail("参数不全");
        }
        AfterSaleEntity afterSaleEntity = afterSaleRepository.findByExternalRefundNo(request.getRefundNo());
        if (afterSaleEntity == null) {
            return ApiResponse.fail("退款单不存在");
        }
        OrderInfoEntity orderInfo = orderRepository.findByOrderNo(afterSaleEntity.getOrderNo());
        DistributionOrderRefundInfoResponseDTO response = new DistributionOrderRefundInfoResponseDTO();
        response.setOrderNo(orderInfo.getExternalOrderNo());
        response.setTradeNo(request.getTradeNo());
        response.setThirdRefundOrderNo(afterSaleEntity.getExternalRefundNo());
        RefundStatusEnum refundStatusEnum = RefundStatusEnum.getByCode(afterSaleEntity.getRefundStatus());
        switch (Objects.requireNonNull(refundStatusEnum)) {
            case REFUND_SUCCESS -> {
                response.setRefundStatus("10");
                response.setRefundTime(String.valueOf(afterSaleEntity.getRefundTime()
                        .atZone(ZoneId.systemDefault())
                        .toInstant()
                        .getEpochSecond()));
                response.setRefundAmount(String.valueOf(afterSaleEntity.getRefundAmount()));
            }
            case REFUNDING -> {
                response.setRefundStatus("11");
                response.setRefundTime("0");
                response.setRefundAmount("0.00");
            }
            default -> {
                response.setRefundStatus("12");
                response.setRefundTime("0");
                response.setRefundAmount("0.00");
            }
        }
        log.info("查询订单退款结果为:{}", JSONUtil.toJsonStr(response));
        return ApiResponse.success(response);
    }

    @Override
    public ApiResponse<WalMartOrderQueryResponseDTO> walMartDistributionOrderPayInfo(WalMartOrderQueryRequestDTO request) {
        WalMartOrderQueryResponseDTO dto = new WalMartOrderQueryResponseDTO();
        String orderNo = request.getOrderNumber();
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(orderNo, null);
        if (order != null) {
            dto.setUserId(String.valueOf(order.getCustomerId()));
            dto.setOrderNumber(order.getExternalOrderNo());
            dto.setThirdOrderSn(order.getOrderNo());
            dto.setPayMoney(String.valueOf(order.getTotalSellAmount()));
            Integer orderStatus = order.getOrderStatus();
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(orderStatus);
            switch (Objects.requireNonNull(orderStatusEnum)) {
                case PENDING_PAYMENT -> dto.setStatus("0");
                case IN_PROGRESS -> {
                    dto.setStatus("1");
                    dto.setPayTime(LocalDateTimeUtil.format(order.getPaymentTime(), DatePattern.NORM_DATETIME_FORMATTER));
                }
                case TRADE_SUCCESS -> {
                    dto.setStatus("2");
                    dto.setPayTime(LocalDateTimeUtil.format(order.getPaymentTime(), DatePattern.NORM_DATETIME_FORMATTER));
                }
                case TRADE_CLOSED -> dto.setStatus("3");
                case TIMEOUT_CANCEL, MANUAL_CANCEL -> dto.setStatus("5");
            }
            log.info("查询沃尔玛订单详情为:{}", JSONUtil.toJsonStr(dto));
            return ApiResponse.success0(dto);
        } else {
            return ApiResponse.fail("订单不存在");
        }

    }

    @Override
    public ApiResponse<ELeMeOrderQueryResponseDTO> eLeMeDistributionOrderPayInfo(ELeMeOrderQueryRequestDTO request) {
        ELeMeOrderQueryResponseDTO dto = new ELeMeOrderQueryResponseDTO();
        String externalOrderNo = request.getTransactionId();
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(externalOrderNo, null);
        if (order != null) {
            dto.setTransactionId(order.getExternalOrderNo());
            dto.setOutTradeNo(order.getOrderNo());
            dto.setPayAmount(String.valueOf(order.getTotalSellAmount().multiply(new BigDecimal("100"))));
            Integer payStatus = order.getPaymentStatus();
            PaymentStatusEnum paymentStatusEnum = PaymentStatusEnum.getByCode(payStatus);
            switch (Objects.requireNonNull(paymentStatusEnum)) {
                case UNPAID -> dto.setPayStatus("NOTPAY");
                case PAID -> dto.setPayStatus("SUCCESS");
            }
            log.info("查询饿了么订单支付结果为:{}", JSONUtil.toJsonStr(dto));
            return ApiResponse.successForCode(dto);
        } else {
            return ApiResponse.failForCode("订单不存在");
        }
    }

    @Override
    public ApiResponse<ELeMeOrderRefundResponseDTO> eLeMeDistributionOrderRefundInfo(ELeMeOrderRefundNotifyDTO request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getRefundNo()) || StringUtils.isEmpty(request.getTransactionId())) {
            return ApiResponse.failForCode("参数不全");
        }
        AfterSaleEntity afterSaleEntity = afterSaleRepository.findByExternalRefundNo(request.getRefundNo());
        if (afterSaleEntity == null) {
            return ApiResponse.failForCode("退款单不存在");
        }
        OrderInfoEntity orderInfo = orderRepository.findByOrderNo(afterSaleEntity.getOrderNo());
        ELeMeOrderRefundResponseDTO response = new ELeMeOrderRefundResponseDTO();
        response.setOutTradeNo(orderInfo.getExternalOrderNo());
        response.setTransactionId(request.getTransactionId());
        response.setRefundNo(request.getRefundNo());
        response.setOutRefundNo(afterSaleEntity.getRefundNo());
        response.setRefundAmount(afterSaleEntity.getApplyRefundAmount().multiply(new BigDecimal("100")).intValue());
        RefundStatusEnum refundStatusEnum = RefundStatusEnum.getByCode(afterSaleEntity.getRefundStatus());
        switch (Objects.requireNonNull(refundStatusEnum)) {
            case REFUND_SUCCESS -> response.setRefundStatus("SUCCESS");
            case REFUNDING -> response.setRefundStatus("PROCESSING");
            default -> response.setRefundStatus("FAIL");
        }
        log.info("查询饿了么订单退款结果为:{}", JSONUtil.toJsonStr(response));
        return ApiResponse.successForCode(response);
    }

    @Override
    public ApiResponse<Object> eLeMeDistributionOrderClose(ELeMeOrderQueryRequestDTO request) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(request.getTransactionId())) {
            return ApiResponse.failForCode("参数不全");
        }
        String externalOrderNo = request.getTransactionId();
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(externalOrderNo, null);
        if (order == null) {
            return ApiResponse.failForCode("订单不存在");
        }
        DistChannelType distChannelType = DistChannelType.valueOf(order.getAppFlag());
        DistVerifySignRequest distVerifySignRequest = DistVerifySignRequest
                .builder()
                .channelType(distChannelType)
                .verifySignDTO(request)
                .build();
        if (!unifiedDistributionApi.verifySign(distVerifySignRequest)) {
            return ApiResponse.failForCode("签名验证失败");
        }
        if (Objects.equals(order.getPaymentStatus(), PaymentStatusEnum.PAID.getCode())
                || Objects.equals(order.getPaymentStatus(), PaymentStatusEnum.PARTIAL_PAID.getCode())){
            return ApiResponse.failForCode("订单已支付，不允许关闭订单");
        }
        if (Objects.equals(order.getPaymentStatus(), PaymentStatusEnum.UNPAID.getCode())){
            // 设置订单为已取消且不显示状态
            OrderInfoEntity updateOrder = new OrderInfoEntity();
            updateOrder.setId(order.getId());
            updateOrder.setCustomerId(order.getCustomerId());
            updateOrder.setHidden(); // 设置为不显示
            updateOrder.setOrderStatus(OrderStatusEnum.MANUAL_CANCEL.getCode()); // 设置为不显示
            updateOrder.setModTime(LocalDateTime.now());
            orderRepository.updateOrder(updateOrder);
            log.info("饿了么订单关闭成功，分销订单号：{}", externalOrderNo);
        }
        return ApiResponse.successForCode();
    }

    @Override
    public ApiResponse<Object> commonDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
        ApiResponse<Object> checkRes = checkOrderAndVerifySign(request.getOrderNo(), request);
        if (!Objects.equals(200,checkRes.getStatus())){
            return checkRes;
        }
        String orderNo = request.getOrderNo();
        String orderStatus = request.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            OrderInfoEntity orderInfo = new OrderInfoEntity();
            orderInfo.setExternalOrderNo(orderNo);
            if (Objects.equals("-1", orderStatus)) {
                orderInfo.setOrderStatus(OrderStatusEnum.MANUAL_CANCEL.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Object> syfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
        ApiResponse<Object> checkRes = checkOrderAndVerifySign(request.getOrderNo(), request);
        if (!Objects.equals(200,checkRes.getStatus())){
            return checkRes;
        }
        String orderNo = request.getOrderNo();
        String orderStatus = request.getChangeStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("3", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.MANUAL_CANCEL.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Object> yhfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
        ApiResponse<Object> checkRes = checkOrderAndVerifySign(request.getOrderNo(), request);
        if (!Objects.equals(200,checkRes.getStatus())){
            return checkRes;
        }
        String orderNo = request.getOrderNo();
        String orderStatus = request.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("6", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.MANUAL_CANCEL.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Object> yhfxDistributionOrderPackageStatusNotify(DistributionOrderStatusNotifyDTO request) {
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Object> bsfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
        ApiResponse<Object> checkRes = checkOrderAndVerifySign(request.getOrderNo(), request);
        if (!Objects.equals(200,checkRes.getStatus())){
            return checkRes;
        }
        String orderNo = request.getOrderNo();
        String orderStatus = request.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("93", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.MANUAL_CANCEL.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    private ApiResponse<Object> checkOrderAndVerifySign(String externalOrderNo, Object request) {
        OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(externalOrderNo, null);
        if (order == null) {
            return ApiResponse.fail("订单不存在");
        }
        DistChannelType distChannelType = DistChannelType.valueOf(order.getAppFlag());
        DistVerifySignRequest distVerifySignRequest = DistVerifySignRequest
                .builder()
                .channelType(distChannelType)
                .verifySignDTO(request)
                .build();

        if (!unifiedDistributionApi.verifySign(distVerifySignRequest)) {
            return ApiResponse.fail("签名验证失败");
        }
        return ApiResponse.success();
    }

    // 分销应用下单请求参数需要特殊处理
    private void extraHandleOrderCreateDTO(CreateOrderDTO createOrderDTO,DistChannelType distChannelType) {
        switch (distChannelType) {
            case RTMARTSCANPAY -> createOrderDTO.setOrderDetailUrl(null);
        }
    }
    /**
     * 解码Base64URL安全格式的字符串
     *
     * @param base64URLSafe Base64URL安全格式的字符串
     * @return 解码后的字节数组
     */
    public String decodeBase64URLSafe(String base64URLSafe) {
        if (StringUtils.isBlank(base64URLSafe)) {
            return null;
        }
        // 将Base64URL安全字符替换为标准Base64字符
        String base64 = base64URLSafe.replace('-', '+').replace('_', '/');

        // 添加填充字符'='以确保长度是4的倍数
        switch (base64.length() % 4) {
            case 2:
                base64 += "==";
                break;
            case 3:
                base64 += "=";
                break;
            default:
                // 长度已经是4的倍数，不需要填充
                break;
        }
        return new String(Base64.getDecoder().decode(base64));
    }

}
