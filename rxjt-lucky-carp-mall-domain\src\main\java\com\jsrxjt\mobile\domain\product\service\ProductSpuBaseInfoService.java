package com.jsrxjt.mobile.domain.product.service;

import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;

import java.util.List;

/**
 * 产品SPU基础信息领域服务
 * 
 * <AUTHOR>
 * @since 2025/8/4
 */
public interface ProductSpuBaseInfoService {
    
    /**
     * 根据区域过滤可售产品
     * 
     * @param regionId 区域ID
     * @param productSpuBaseInfos 产品列表
     * @return 过滤后的可售产品列表
     */
    List<ProductSpuBaseInfo> filterByRegion(Integer regionId, List<ProductSpuBaseInfo> productSpuBaseInfos);
    
    /**
     * 设置产品活动标签信息
     * 
     * @param products 产品列表
     */
    void setPromotionLabels(List<ProductSpuBaseInfo> products);
}