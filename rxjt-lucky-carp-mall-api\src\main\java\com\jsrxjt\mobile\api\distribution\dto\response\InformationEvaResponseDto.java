package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 资讯评价响应
 * @Author: ywt
 * @Date: 2025-06-11 15:14
 * @Version: 1.0
 */
@Data
public class InformationEvaResponseDto {
    @Schema(description = "评价id")
    private Long id;
    @Schema(description = "用户名")
    private String userName;
    @Schema(description = "用户头像")
    private String userUrl;
    /*@Schema(description = "手机号")
    private String phone;*/
    /*@Schema(description = "手机号")
    private Integer informationId;*/
    @Schema(description = "评价内容")
    private String content;
    @Schema(description = "点赞数")
    private Integer praiseNum;
    @Schema(description = "评价时间")
    private LocalDateTime createTime;
}
