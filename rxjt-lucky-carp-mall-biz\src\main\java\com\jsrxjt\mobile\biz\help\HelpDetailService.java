package com.jsrxjt.mobile.biz.help;

import com.jsrxjt.mobile.api.help.Respone.HelpDetailResponseDTO;
import com.jsrxjt.mobile.domain.help.entity.HelpCatEntity;
import com.jsrxjt.mobile.domain.help.entity.HelpDetailEntity;

import java.util.List;

public interface HelpDetailService {
    /**
     * 更具分类id获取已启用的帮助详情列表
     * @param catId
     * @return
     */
    List<HelpDetailResponseDTO> helpDetailList(Long catId);

    /**
     * 根据更新点击次数
     * @param helpId
     * @return
     */
    void updateClickNum(Long helpId);
}
