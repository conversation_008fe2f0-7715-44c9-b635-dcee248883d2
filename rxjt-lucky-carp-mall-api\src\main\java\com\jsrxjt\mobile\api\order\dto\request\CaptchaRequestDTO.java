package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 图形验证码请求DTO
 * 
 * <AUTHOR>
 * @since 2025/11/25
 */
@Data
@Schema(description = "图形验证码请求DTO")
public class CaptchaRequestDTO extends BaseParam  {

    /**
     * 验证码宽度
     */
    @Schema(description = "验证码图片宽度", example = "120")
    @Min(value = 80, message = "验证码宽度不能小于80")
    @Max(value = 300, message = "验证码宽度不能大于300")
    private Integer width = 120;

    /**
     * 验证码高度
     */
    @Schema(description = "验证码图片高度", example = "40")
    @Min(value = 30, message = "验证码高度不能小于30")
    @Max(value = 100, message = "验证码高度不能大于100")
    private Integer height = 40;

    /**
     * 验证码字符数量
     */
    @Schema(description = "验证码字符数量", example = "4")
    @Min(value = 3, message = "验证码字符数量不能小于3")
    @Max(value = 6, message = "验证码字符数量不能大于6")
    private Integer codeCount = 4;

    /**
     * 干扰线数量
     */
    @Schema(description = "干扰线数量", example = "10")
    @Min(value = 0, message = "干扰线数量不能小于0")
    @Max(value = 50, message = "干扰线数量不能大于50")
    private Integer lineCount = 10;
}
