package com.jsrxjt.mobile.domain.ticket.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/9/13
 */
@Data
public class TicketOffsetRecordEntity {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "优惠券id")
    private Long ticketId;

    @Schema(description = "优惠券发放表的id")
    private Long ticketDeliveryId;

    @Schema(description = "优惠券码")
    private String ticketCode;

    @Schema(description = "有效期")
    private Date validDate;

    @Schema(description = "优惠券名称")
    private String ticketName;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "门店id")
    private Long storeId;

    @Schema(description = "领取时间")
    private Date pickTime;

    @Schema(description = "核销时间")
    private Date offsetTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "编辑时间")
    private Date modTime;

    @Schema(description = "是否删除标志(0:否 1:是)")
    private Integer delFlag;
}