package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 资讯响应
 * @Author: ywt
 * @Date: 2025-06-12 18:14
 * @Version: 1.0
 */
@Data
public class InformationResponseDto {
    @Schema(description = "资讯id")
    private Integer informationId;
    @Schema(description = "资讯分类id")
    private Integer catId;
    @Schema(description = "资讯分类名称")
    private String catName;
    @Schema(description = "资讯名称")
    private String informationTitle;
    @Schema(description = "资讯副标题")
    private String subTitle;
    @Schema(description = "资讯标签")
    private String label;
    @Schema(description = "是否置顶(0:否 1:是)")
    private Integer top;
    @Schema(description = "资讯图片url")
    private String imgUrl;
    @Schema(description = "资讯内容(图文富文本)")
    private String content;
    @Schema(description = "已读数量")
    private Integer readNum;
    @Schema(description = "转发数量")
    private Integer forwardNum;
    @Schema(description = "评论数量")
    private Integer commentNum;
    @Schema(description = "资讯有效期：开始时间")
    private Date startTime;
    @Schema(description = "资讯有效期：到期时间")
    private Date endTime;
}
