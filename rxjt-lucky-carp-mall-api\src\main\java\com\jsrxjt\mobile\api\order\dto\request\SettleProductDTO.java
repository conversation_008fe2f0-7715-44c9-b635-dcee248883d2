package com.jsrxjt.mobile.api.order.dto.request;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 结算产品DTO
 * 
 * <AUTHOR>
 * @since 2025/6/16
 **/
@Getter
@Setter
public class SettleProductDTO {
    /**
     * 客户Id
     */
    private Long customerId;


    /**
     * 产品类型
     */
    private Integer productType;
    
    /**
     * 产品spuid
     */
    private Long productSpuId;

    /**
     * 产品skuid,不存在时传0
     */
    private Long productSkuId;

    /**
     * 数量，默认是1
     */
    private Integer quantity = 1;

    private BigDecimal appProductPrice;

    /**
     * 赠送券列表
     */
    private List<GiftTicketRequestDTO> giftTickets;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 易盾的业务Id
     */
    private String ydBusinessId;
    /**
     * 易盾的token
     */
    private String ydToken;


}
