package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 资讯置顶的请求参数
 * @Author: ywt
 * @Date: 2025-06-12 16:36
 * @Version: 1.0
 */
@Data
public class InformationTopListRequestDto {
    @Schema(description = "资讯分类id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "资讯分类id为空错误")
    private Integer catId;

    @Schema(description = "三级地址id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
}
