package com.jsrxjt.mobile.api.customer.types;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户卡类型枚举
 */
public enum CustomerCardTypeEnum {

    BUSINESS_CARD(2, "商联卡/红卡", "RBIZ"),

//    RED_CARD(2, "红卡", "RR"),

    WHITE_CARD(7,"白金账户", "RW"),

    BLACK_CARD(5, "黑金账户", "RB"),

//    DELIVERY_VOUCHER(22, "提货凭证", "VT"),

    TRADE_UNION_VOUCHER(23, "工会凭证", "LT");

    CustomerCardTypeEnum(int cardType, String name, String cardTypeCode) {
        this.cardType = cardType;
        this.name = name;
        this.cardTypeCode = cardTypeCode;
    }

    /**
     * 用户卡类型
     */
    private final int cardType;

    /**
     * 卡名称
     */
    private final String name;

    /**
     * 卡类型编码
     */
    private final String cardTypeCode;

    public int getCardType() {
        return cardType;
    }

    public String getName() {
        return name;
    }

    public String getCardTypeCode() {
        return cardTypeCode;
    }

    public static String getAllCardTypes() {
        return Arrays.stream(CustomerCardTypeEnum.values())
                .mapToInt(CustomerCardTypeEnum::getCardType)
                .distinct()  // Remove duplicates
                .mapToObj(String::valueOf)
                .collect(Collectors.joining(","));
    }

    public static int getCardType(int type) {
        for (CustomerCardTypeEnum cardTypeEnum : values()) {
            if (cardTypeEnum.cardType == type) {
                return cardTypeEnum.cardType;
            }
        }
        throw new IllegalArgumentException("不支持的卡类型: " + type);
    }

    public static String getCardTypeCodeByType(int type) {
        for (CustomerCardTypeEnum cardTypeEnum : values()) {
            if (cardTypeEnum.cardType == type) {
                return cardTypeEnum.cardTypeCode;
            }
        }
        throw new IllegalArgumentException("不支持的卡类型: " + type);
    }

    public static CustomerCardTypeEnum getByCardCodeType(String cardTypeCode) {
        for (CustomerCardTypeEnum value : values()) {
            if (value.getCardTypeCode().equals(cardTypeCode)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据卡类型编码获取卡类型
     *
     * @param cardTypeCodes 卡类型编码列表
     * @return 卡类型
     */
    public static String getCardTypesByCodes(List<String> cardTypeCodes) {
        if (cardTypeCodes == null || cardTypeCodes.isEmpty()) {
            return "";
        }
        List<String> cardTypes = new ArrayList<>();
        for (String cardTypeCode : cardTypeCodes) {
            CustomerCardTypeEnum customerCardTypeEnum = getByCardCodeType(cardTypeCode);
            if (customerCardTypeEnum != null) {
                cardTypes.add(String.valueOf(customerCardTypeEnum.getCardType()));
            }
        }
        return String.join(",", cardTypes);
    }

    /**
     * 判断是否是可绑定的卡类型
     *
     * @param cardTypeCode 卡类型编码
     * @return true/false
     */
    public static boolean isBindableCardType(String cardTypeCode) {
        return cardTypeCode.equals("RBIZ") || cardTypeCode.equals("LT");
    }
}
