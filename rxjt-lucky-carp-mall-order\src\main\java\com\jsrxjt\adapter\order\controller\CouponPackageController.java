package com.jsrxjt.adapter.order.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.*;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackageDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackageListResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackagePageDTO;
import com.jsrxjt.mobile.api.order.dto.response.SelCouponPayDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.request.HisCouponQueryRequest;
import com.jsrxjt.mobile.api.order.dto.response.HisCouponInfoResponse;
import com.jsrxjt.mobile.biz.order.coupon.CouponPackageCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 卡包接口
 * <AUTHOR>
 * @date 2025/07/22
 */
@RestController
@RequestMapping("/v1/couponPackage")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "卡包接口", description = "卡包相关接口")
public class CouponPackageController {

    private final CouponPackageCaseService couponPackageCaseService;

    /**
     * 卡包列表接口
     */
    @PostMapping("/couponPackageList")
    @Operation(summary = "卡包列表", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<CouponPackagePageDTO<CouponPackageListResponseDTO>> couponPackageList(@RequestBody @Valid CouponPackageListRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        request.setDelFlag(0);
        return BaseResponse.succeed(couponPackageCaseService.getCouponPackageList(customerId, request));
    }

    /**
     * 卡包列表接口
     */
    @PostMapping("/recycleBinList")
    @Operation(summary = "回收站列表", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<CouponPackagePageDTO<CouponPackageListResponseDTO>> recycleBinList(@RequestBody @Valid CouponPackageListRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        request.setDelFlag(2);
        return BaseResponse.succeed(couponPackageCaseService.getCouponPackageList(customerId, request));
    }


    @PostMapping("/getCouponPackageDetail")
    @Operation(summary = "卡包详情", description = "卡包详情")
    @VerifySign(hasToken = true)
    public BaseResponse<CouponPackageDetailResponseDTO> getCouponPackageDetail(@RequestBody @Valid CouponPackageDetailRequestDTO request){
        return BaseResponse.succeed(couponPackageCaseService.getCouponPackageDetail(request.getCouponPackageId()));
    }

    @PostMapping("/getCouponPackageIdForOrder")
    @Operation(summary = "订单页卡包id", description = "订单页卡包id接口")
    @VerifySign(hasToken = true)
    public BaseResponse<Map<String, Object>> getCouponPackageIdForOrder(@RequestBody @Valid CouponPackageOrderDetailRequestDTO request){
        Map<String, Object> map = new HashMap<>();
        map.put("couponPackageId", couponPackageCaseService.getCouponPackageIdForOrder(request.getOrderNo()));
        return BaseResponse.succeed(map);
    }

    /**
     * 消费明细
     */
    @PostMapping("/consumeList")
    @Operation(summary = "消费明细", description = "消费明细接口")
    @VerifySign(hasToken = true)
    public BaseResponse<PageDTO<SelCouponPayDetailResponseDTO>> consumeList(@RequestBody @Valid CouponPayDetailPageRequestDTO request){
        return BaseResponse.succeed(couponPackageCaseService.consumeList(request));
    }

    @PostMapping("/putInRecycleBin")
    @Operation(summary = "卡包放入回收站", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<?> putInRecycleBin(@RequestBody @Valid CouponPackageDelRequestDTO request){
        couponPackageCaseService.putInRecycleBin(request);
        return BaseResponse.succeed();
    }

    @PostMapping("/restoreRecycleBin")
    @Operation(summary = "回收站恢复", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<?> restoreRecycleBin(@RequestBody @Valid CouponPackageDelRequestDTO request){
        couponPackageCaseService.restoreRecycleBin(request);
        return BaseResponse.succeed();
    }

    @PostMapping("/deleteCouponPackage")
    @Operation(summary = "回收站删除", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<?> deleteCouponPackage(@RequestBody @Valid CouponPackageDelRequestDTO request){
        couponPackageCaseService.deleteCouponPackage(request);
        return BaseResponse.succeed();
    }


    @PostMapping("/getHisCouponList")
    @Operation(summary = "分页获取历史券包")
    @VerifySign(hasToken = true)
    public BaseResponse<List<HisCouponInfoResponse>> getHisCouponList(@RequestBody @Valid HisCouponQueryRequest request){
        return BaseResponse.succeed(couponPackageCaseService.getHisCouponList(request));
    }
}
