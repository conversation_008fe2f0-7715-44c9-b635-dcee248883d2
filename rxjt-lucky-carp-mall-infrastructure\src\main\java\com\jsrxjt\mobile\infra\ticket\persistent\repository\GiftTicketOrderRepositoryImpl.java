package com.jsrxjt.mobile.infra.ticket.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketOrderEntity;
import com.jsrxjt.mobile.domain.ticket.repository.GiftTicketOrderRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.GiftTicketOrderMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.GiftTicketOrderPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 下单赠送券订单仓储实现
 * 
 * <AUTHOR>
 * @date 2025/09/22
 */
@Repository
@RequiredArgsConstructor
public class GiftTicketOrderRepositoryImpl implements GiftTicketOrderRepository {

    private final GiftTicketOrderMapper giftTicketOrderMapper;

    @Override
    public void batchSave(List<GiftTicketOrderEntity> giftTicketOrders) {
        if (giftTicketOrders == null || giftTicketOrders.isEmpty()) {
            return;
        }

        List<GiftTicketOrderPO> poList = giftTicketOrders.stream()
                .map(this::convertToPO)
                .collect(Collectors.toList());

        // 批量插入
        if (!poList.isEmpty()) {
            giftTicketOrderMapper.batchInsert(poList);
        }
    }

    @Override
    public GiftTicketOrderEntity findByTicketOrderNo(String ticketOrderNo) {
        LambdaQueryWrapper<GiftTicketOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GiftTicketOrderPO::getTicketOrderNo, ticketOrderNo);

        GiftTicketOrderPO po = giftTicketOrderMapper.selectOne(queryWrapper);
        return convertToEntity(po);
    }

    @Override
    public GiftTicketOrderEntity findByCenterTicketCouponNumber(String centerTicketCouponNumber) {
        LambdaQueryWrapper<GiftTicketOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GiftTicketOrderPO::getCenterTicketCouponNumber, centerTicketCouponNumber);

        GiftTicketOrderPO po = giftTicketOrderMapper.selectOne(queryWrapper);
        return convertToEntity(po);
    }

    @Override
    public GiftTicketOrderEntity findByExternalOrderNo(String externalOrderNo) {
        LambdaQueryWrapper<GiftTicketOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GiftTicketOrderPO::getExternalOrderNo, externalOrderNo).last("LIMIT 1");
        GiftTicketOrderPO po = giftTicketOrderMapper.selectOne(queryWrapper);
        return convertToEntity(po);
    }

    @Override
    public void save(GiftTicketOrderEntity giftTicketOrder) {
        if (giftTicketOrder == null) {
            return;
        }

        GiftTicketOrderPO po = convertToPO(giftTicketOrder);
        if (giftTicketOrder.getId() == null) {
            // 新增
            giftTicketOrderMapper.insert(po);
            giftTicketOrder.setId(po.getId());
        } else {
            // 更新
            giftTicketOrderMapper.updateById(po);
        }
    }

    private GiftTicketOrderPO convertToPO(GiftTicketOrderEntity entity) {
        GiftTicketOrderPO po = BeanUtil.copyProperties(entity, GiftTicketOrderPO.class);
        return po;
    }

    private GiftTicketOrderEntity convertToEntity(GiftTicketOrderPO po) {
        if (po == null) {
            return null;
        }
        return BeanUtil.copyProperties(po, GiftTicketOrderEntity.class);
    }
}
