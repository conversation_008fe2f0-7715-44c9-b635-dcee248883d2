package com.jsrxjt.mobile.api.scanPay.response;

import com.jsrxjt.mobile.api.customer.response.CustomerDetailResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "返回给第三方用户信息和订单id")
@Data
public class CustomerPaySortResponseDTO {

    @Schema(description = "新福鲤圈付款循序")
    private String newPaySort;

    @Schema(description = "原福鲤圈付款循序")
    private String oldPaySort;




}
