package com.jsrxjt.mobile.api.advertisement.types;

import lombok.Data;
import lombok.Getter;

/**
 * @Description: 广告所属位置枚举
 * @Author: ywt
 * @Date: 2025-05-08 19:18
 * @Version: 1.0
 */
@Getter
public enum AdvertisementTypeEnum {
    ADV_COUPON(1, "卡券详情页"),
    ADV_USER_CENTER(2, "个人中心"),
    ADV_PAY_SUCESS(3, "支付成功页"),
    ADV_CATEGORY(4, "分类页"),
    ADV_PACKAGE(5, "套餐详情页"),
    ADV_INFORMATION(6, "资讯集合页");
    private final Integer code;
    private final String value;

    AdvertisementTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }
}
