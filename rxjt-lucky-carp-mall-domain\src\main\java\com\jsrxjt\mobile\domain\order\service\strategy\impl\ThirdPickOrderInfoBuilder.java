package com.jsrxjt.mobile.domain.order.service.strategy.impl;

import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description: 展码付推单订单信息构建
 * @Author: shenyue
 * @Date: 2023/9/27
 */
@Component
@Slf4j
public class ThirdPickOrderInfoBuilder extends DefaultOrderInfoBuilder {

    public ThirdPickOrderInfoBuilder(BusinessIdGenerator businessIdGenerator, ConfigRepository configRepository) {
        super(businessIdGenerator, configRepository);
    }
    
    @Override
    public void supplementOrderInfo(OrderInfoEntity orderInfo, CreateOrderDTO request) {
        log.info("补充第三方推单订单信息，订单号：{}", orderInfo.getOrderNo());
        
        // 补充第三方特有信息
        if (request.getExternalOrderNo() != null) {
            orderInfo.setExternalOrderNo(request.getExternalOrderNo());
        }
        
        if (request.getThirdId() != null) {
            orderInfo.setThirdId(request.getThirdId());
        }

        if (request.getDistTradeNo() != null) {
            orderInfo.setDistTradeNo(request.getDistTradeNo());
        }

        if (request.getExternalShopId() != null) {
            orderInfo.setExternalShopId(request.getExternalShopId());
        }
        
        if (request.getExternalShopUserId() != null) {
            orderInfo.setExternalShopUserId(request.getExternalShopUserId());
        }
        
        if (request.getTradeNo() != null) {
            orderInfo.setTradeNo(request.getTradeNo());
        }
        if (request.getExtraInfo() != null){
            orderInfo.getOrderItems().get(0).setExtraInfo(request.getExtraInfo().toJSONString());
        }

        orderInfo.setPayExpireTimestamp(request.getPayExpireTimestamp());
        
        // 设置特定的订单渠道
        orderInfo.setOrderChannel(OrderChannelEnum.SCAN_PAY.getCode());
        
        log.info("第三方推单订单信息补充完成，外部订单号：{}", orderInfo.getExternalOrderNo());
    }
} 