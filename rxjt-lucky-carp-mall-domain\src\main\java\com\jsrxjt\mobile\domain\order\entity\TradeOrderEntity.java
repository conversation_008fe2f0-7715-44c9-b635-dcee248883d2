package com.jsrxjt.mobile.domain.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易订单领域实体
 * <AUTHOR>
 * @since 2025/8/18
 */
@Data
public class TradeOrderEntity {

    /**
     * 交易ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 业务订单号
     */
    private String orderNo;

    /**
     * 外部业务单号
     */
    private String outOrderNo;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 支付类型 ONLINE 线上支付 OFFLINE 线下支付
     */
    private String payType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单来源 APP_ANDROID APP_IOS WX_MINI
     */
    private String orderSource;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单渠道
     */
    private Integer orderChannel;

    /**
     * 支付金额（元）
     */
    private BigDecimal payAmount;

    /**
     * 支付状态 WAIT 待交易 PAYING 交易中 SUCCESS 交易成功 FAIL 支付失败 CLOSE 交易关闭
     */
    private String payStatus;

    /**
     * 退款金额（元）
     */
    private BigDecimal refundAmount;

    /**
     * 退款状态 NONE 无退款 PART 部分退款 FULL 全部退款
     */
    private String refundStatus;

    /**
     * 瑞祥卡支付金额（元）
     */
    private BigDecimal cardPayAmount;

    /**
     * 第三方支付金额（元）
     */
    private BigDecimal thirdPayAmount;

    /**
     * 交易时间
     */
    private LocalDateTime tradeTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 删除时间
     */
    private LocalDateTime deletedAt;

    // 支付类型常量
    public static final String PAY_TYPE_ONLINE = "ONLINE";
    public static final String PAY_TYPE_OFFLINE = "OFFLINE";

    // 订单来源常量
    public static final String ORDER_SOURCE_APP_ANDROID = "APP_ANDROID";
    public static final String ORDER_SOURCE_APP_IOS = "APP_IOS";
    public static final String ORDER_SOURCE_WX_MINI = "WX_MINI";

    // 支付状态常量
    public static final String PAY_STATUS_WAIT = "WAIT";
    public static final String PAY_STATUS_PAYING = "PAYING";
    public static final String PAY_STATUS_SUCCESS = "SUCCESS";
    public static final String PAY_STATUS_FAIL = "FAIL";
    public static final String PAY_STATUS_CLOSE = "CLOSE";

    // 退款状态常量
    public static final String REFUND_STATUS_NONE = "NONE";
    public static final String REFUND_STATUS_PART = "PART";
    public static final String REFUND_STATUS_FULL = "FULL";

    /**
     * 判断是否为线上支付
     */
    public boolean isOnlinePay() {
        return PAY_TYPE_ONLINE.equals(payType);
    }

    /**
     * 判断是否为线下支付
     */
    public boolean isOfflinePay() {
        return PAY_TYPE_OFFLINE.equals(payType);
    }

    /**
     * 判断是否支付成功
     */
    public boolean isPaySuccess() {
        return PAY_STATUS_SUCCESS.equals(payStatus);
    }

    /**
     * 判断是否支付失败
     */
    public boolean isPayFailed() {
        return PAY_STATUS_FAIL.equals(payStatus);
    }

    /**
     * 判断是否交易关闭
     */
    public boolean isTradeClosed() {
        return PAY_STATUS_CLOSE.equals(payStatus);
    }

    /**
     * 判断是否可以退款
     */
    public boolean canRefund() {
        return isPaySuccess() && 
               (REFUND_STATUS_NONE.equals(refundStatus) || 
                REFUND_STATUS_PART.equals(refundStatus));
    }

    /**
     * 获取可退款金额
     */
    public BigDecimal getAvailableRefundAmount() {
        if (!canRefund()) {
            return BigDecimal.ZERO;
        }
        return payAmount.subtract(refundAmount != null ? refundAmount : BigDecimal.ZERO);
    }

    /**
     * 判断是否混合支付
     */
    public boolean isMixedPay() {
        return cardPayAmount != null && cardPayAmount.compareTo(BigDecimal.ZERO) > 0 &&
               thirdPayAmount != null && thirdPayAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断是否纯卡支付
     */
    public boolean isPureCardPay() {
        return cardPayAmount != null && cardPayAmount.compareTo(BigDecimal.ZERO) > 0 &&
               (thirdPayAmount == null || thirdPayAmount.compareTo(BigDecimal.ZERO) == 0);
    }

    /**
     * 判断是否纯第三方支付
     */
    public boolean isPureThirdPay() {
        return thirdPayAmount != null && thirdPayAmount.compareTo(BigDecimal.ZERO) > 0 &&
               (cardPayAmount == null || cardPayAmount.compareTo(BigDecimal.ZERO) == 0);
    }

    /**
     * 更新支付状态
     */
    public void updatePayStatus(String newStatus, LocalDateTime tradeTime) {
        this.payStatus = newStatus;
        this.tradeTime = tradeTime;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 更新退款信息
     */
    public void updateRefundInfo(BigDecimal refundAmount, String refundStatus) {
        this.refundAmount = refundAmount;
        this.refundStatus = refundStatus;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 关闭交易
     */
    public void closeTrade() {
        this.payStatus = PAY_STATUS_CLOSE;
        this.updatedAt = LocalDateTime.now();
    }
}