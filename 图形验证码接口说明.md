# 图形验证码接口说明

## 接口概述

该接口用于生成图形验证码，返回Base64编码的验证码图片，用于订单安全验证等场景。

## 接口信息

- **接口路径**: `POST /v1/order/captcha`
- **接口描述**: 生成图形验证码
- **认证方式**: 需要签名验证 (`@VerifySign`)

## 请求参数

### 请求体 (JSON)

```json
{
    "width": 120,
    "height": 40,
    "codeCount": 4,
    "lineCount": 10
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 描述 | 取值范围 |
|--------|------|------|--------|------|----------|
| width | Integer | 否 | 120 | 验证码图片宽度 | 80-300 |
| height | Integer | 否 | 40 | 验证码图片高度 | 30-100 |
| codeCount | Integer | 否 | 4 | 验证码字符数量 | 3-6 |
| lineCount | Integer | 否 | 10 | 干扰线数量 | 0-50 |

## 响应结果

### 成功响应

```json
{
    "code": "200",
    "message": "操作成功",
    "data": {
        "captchaId": "captcha_a1b2c3d4e5f6",
        "captchaImage": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAoCAYAAAA16j4lAAAFB...",
        "expireTime": 300
    },
    "success": true
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| captchaId | String | 验证码唯一标识，用于后续验证 |
| captchaImage | String | 验证码图片Base64编码，可直接用于img标签显示 |
| expireTime | Integer | 验证码过期时间（秒），默认300秒 |

### 失败响应

```json
{
    "code": "500",
    "message": "生成图形验证码失败",
    "data": null,
    "success": false
}
```

## 业务逻辑

1. **参数处理**: 使用默认值填充未提供的参数
2. **验证码生成**: 使用hutool工具生成线段干扰验证码
3. **缓存存储**: 将验证码存储到Redis，设置过期时间
4. **响应构建**: 返回验证码ID、Base64图片和过期时间

## 验证码特性

- **字符类型**: 数字和字母混合（0-9, A-Z）
- **干扰元素**: 线段干扰，增加识别难度
- **图片格式**: PNG格式，Base64编码
- **缓存策略**: Redis存储，自动过期
- **安全性**: 验证成功后自动删除，防止重复使用

## 使用示例

### JavaScript 前端调用

```javascript
// 生成验证码
fetch('/v1/order/captcha', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        width: 150,
        height: 50,
        codeCount: 5,
        lineCount: 15
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        // 显示验证码图片
        document.getElementById('captchaImg').src = data.data.captchaImage;
        // 保存验证码ID用于后续验证
        document.getElementById('captchaId').value = data.data.captchaId;
    }
});
```

### HTML 显示验证码

```html
<div class="captcha-container">
    <img id="captchaImg" src="" alt="验证码" onclick="refreshCaptcha()" />
    <input type="hidden" id="captchaId" />
    <input type="text" id="captchaCode" placeholder="请输入验证码" maxlength="6" />
    <button onclick="refreshCaptcha()">刷新验证码</button>
</div>

<script>
function refreshCaptcha() {
    // 调用生成验证码接口
    generateCaptcha();
}
</script>
```

### cURL 请求示例

```bash
curl -X POST "http://localhost:8080/v1/order/captcha" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "width": 120,
    "height": 40,
    "codeCount": 4,
    "lineCount": 10
  }'
```

## 验证码验证接口

### 接口信息

- **接口路径**: `POST /v1/order/captcha/verify`
- **接口描述**: 验证用户输入的图形验证码
- **认证方式**: 需要签名验证 (`@VerifySign`)

### 请求参数

```json
{
    "captchaId": "captcha_a1b2c3d4e5f6",
    "code": "ABCD"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| captchaId | String | 是 | 验证码唯一标识 |
| code | String | 是 | 用户输入的验证码 |

### 响应结果

```json
{
    "code": "200",
    "message": "操作成功",
    "data": true,
    "success": true
}
```

### 编程方式验证

也可以在业务代码中直接调用服务方法：

```java
// 验证验证码
boolean isValid = captchaCaseService.verifyCaptcha(captchaId, userInputCode);
if (isValid) {
    // 验证成功，继续业务逻辑
} else {
    // 验证失败，提示用户重新输入
}
```

## 注意事项

1. **过期时间**: 验证码有效期为5分钟，过期后需要重新生成
2. **一次性使用**: 验证成功后验证码自动失效，无法重复使用
3. **大小写不敏感**: 验证时忽略大小写
4. **参数限制**: 请求参数有范围限制，超出范围会返回参数错误
5. **性能考虑**: 建议合理设置图片尺寸，避免生成过大的图片影响性能

## 错误处理

| 错误场景 | 错误码 | 处理建议 |
|----------|--------|----------|
| 参数超出范围 | 400 | 检查参数是否在允许范围内 |
| 生成失败 | 500 | 重试或联系技术支持 |
| Redis连接失败 | 500 | 检查Redis服务状态 |

## 相关接口

- 验证码验证功能需要在具体业务接口中实现
- 可以结合订单提交、用户登录等场景使用
- 建议在敏感操作前进行验证码校验
