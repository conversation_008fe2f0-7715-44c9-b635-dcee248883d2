package com.jsrxjt.mobile.api.coupon.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 自发券适用门店响应
 * @Author: ywt
 * @Date: 2025-05-07 13:39
 * @Version: 1.0
 */
@Data
public class CouponShopResponseDTO {
    @Schema(description = "门店id")
    private Integer id;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "地址")
    private String address;
    @Schema(description = "电话")
    private String tel;
    @Schema(description = "特约商户LOGO")
    private String logo;
    @Schema(description = "距离")
    private String distance;
}
