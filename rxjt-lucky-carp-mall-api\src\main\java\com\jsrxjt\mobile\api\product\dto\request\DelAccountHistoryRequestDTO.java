package com.jsrxjt.mobile.api.product.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 删除历史充值账户记录
 * @Author: ywt
 * @Date: 2025-11-13 09:03
 * @Version: 1.0
 */
@Data
@Schema(description = "删除历史充值账户记录请求参数")
public class DelAccountHistoryRequestDTO {
    @Schema(description = "历史充值账户记录的主键id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "id为空错误")
    private Long id;
}
