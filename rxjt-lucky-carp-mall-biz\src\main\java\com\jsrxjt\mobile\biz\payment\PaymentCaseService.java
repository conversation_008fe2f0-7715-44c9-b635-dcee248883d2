package com.jsrxjt.mobile.biz.payment;

import com.jsrxjt.mobile.api.payment.request.PrePayRequestDTO;
import com.jsrxjt.mobile.api.payment.response.PrePayResponseDTO;
import com.jsrxjt.mobile.api.payment.request.PaymentRequestDTO;
import com.jsrxjt.mobile.api.payment.response.PaymentResponseDTO;

/**
 * 支付业务服务接口
 * <AUTHOR>
 * @since 2025/8/8
 */
public interface PaymentCaseService {
    
    /**
     * 预支付处理
     * 包括查询订单、校验订单状态、发起预支付
     * 
     * @param request 预支付请求
     * @return 预支付响应
     */
    PrePayResponseDTO prePay(PrePayRequestDTO request);
    
    /**
     * 发起支付处理
     * 根据预支付订单号发起支付
     * 
     * @param request 发起支付请求
     * @return 发起支付响应
     */
    PaymentResponseDTO pay(PaymentRequestDTO request);
}
