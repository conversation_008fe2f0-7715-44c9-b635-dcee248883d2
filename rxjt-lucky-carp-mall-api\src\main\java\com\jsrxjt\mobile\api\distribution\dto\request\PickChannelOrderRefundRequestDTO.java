package com.jsrxjt.mobile.api.distribution.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jsrxjt.common.core.vo.SignRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 提货券中台订单退款请求
 * <AUTHOR>
 * @date 2025/11/12
 */
@Data
@Schema(description = "提货券中台订单退款请求")
public class PickChannelOrderRefundRequestDTO extends SignRequest {

    @Schema(description = "第三方商户ID，默认值：1-瑞祥商户")
    @JSONField(name = "third_id")
    private String third_id;

    @Schema(description = "类型描述，例如：place-分销订单 yike-逸刻 watsons-屈臣氏 ")
    @JSONField(name = "type")
    private String type;

    @Schema(description = "平台订单流水号")
    @JSONField(name = "order_no")
    private String order_no;

    @Schema(description = "平台退款流水号")
    @JSONField(name = "refund_no")
    private String refund_no;

    @Schema(description = "平台交易流水号")
    @JSONField(name = "trade_no")
    private String trade_no;

    @Schema(description = "订单金额")
    @JSONField(name = "amount")
    private String amount;

}
