package com.jsrxjt.mobile.api.customer.types;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 用户卡充值配置枚举
 */
public enum CardRechargeConfigEnum {

    BLACK_CARD(
            CustomerCardTypeEnum.BLACK_CARD.getCardTypeCode()
    ),
    WHITE_CARD(
            CustomerCardTypeEnum.WHITE_CARD.getCardTypeCode(),
            CustomerCardTypeEnum.BUSINESS_CARD.getCardTypeCode()
    );

    private static final String PAY_TYPE_WX = "WX";
    private final String cardType;
    private final Set<String> supportedPayTypes;

    CardRechargeConfigEnum(String cardType, String... payTypes) {
        this.cardType = cardType;
        this.supportedPayTypes = Stream.concat(
                Stream.of(cardType),
                Stream.concat(
                        Arrays.stream(payTypes),
                        Stream.of(PAY_TYPE_WX)
                )
        ).collect(Collectors.toUnmodifiableSet());
    }

    public static CardRechargeConfigEnum getByCardType(String cardType) {
        return Arrays.stream(values())
                .filter(config -> config.cardType.equals(cardType))
                .findFirst()
                .orElse(null);
    }

    public boolean supportsPayType(String payType) {
        return supportedPayTypes.contains(payType);
    }
}


