package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 支付成功页的广告列表请求参数
 * @Author: ywt
 * @Date: 2025-10-28 15:13
 * @Version: 1.0
 */
@Data
public class AdvertisementListRequestDto {
    @Schema(description = "页面类型，固定传3")
    @NotNull(message = "页面类型为空错误")
    Integer pageType;

    @Schema(description = "三级地址id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
}
