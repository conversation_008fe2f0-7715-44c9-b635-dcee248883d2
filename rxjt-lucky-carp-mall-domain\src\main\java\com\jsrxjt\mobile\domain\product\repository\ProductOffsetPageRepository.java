package com.jsrxjt.mobile.domain.product.repository;

import com.jsrxjt.mobile.domain.product.entity.ProductExplainEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductOffsetPageEntity;

import java.util.List;

/**
 * 核销产品说明Repository接口
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface ProductOffsetPageRepository {

    /**
     * 根据SPUID查询说明信息
     * 
     * @param spuId spuId
     * @return 品牌实体
     */
    List<ProductOffsetPageEntity> findBySpuId(Long spuId, Byte productType);

    /**
     * 根据SPUID查询说明信息
     *
     * @param spuId spuId
     * @return 品牌实体
     */
    List<ProductOffsetPageEntity> findBySpuIdWithDel(Long spuId, Byte productType);
}