package com.jsrxjt.adapter.order.controller;

import cn.hutool.json.JSONUtil;
import com.jsrxjt.common.core.util.ServletUtils;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.*;
import com.jsrxjt.mobile.biz.distribution.service.DistributionOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/7/17 16:38
 * 分销应用下单通知接口
 */
@RestController
@RequestMapping("/v1/distribution-order-notify")
@RequiredArgsConstructor
@Slf4j
public class DistributionOrderNotifyController {

    private final DistributionOrderService distributionOrderService;

    /**
     * 通用分销应用创建订单
     *
     * @param distributionId 应用id
     * @param request        创建订单请求参数
     * @return 订单创建结果
     */
    @PostMapping("/common/order/create/{distributionId}")
    public ApiResponse<DistributionCreateOrderResponseDTO> commonDistributionOrderCreateNotify(
            @PathVariable("distributionId") Long distributionId, DistributionOrderCreateNotifyDTO request) {
        log.info("distributionOrderCreateNotify(distributionId = {},request={})", distributionId, JSONUtil.toJsonStr(request));
        return distributionOrderService.distributionOrderCreate(distributionId, request);

    }

    /**
     * 大润发扫码付分销应用创建订单 JSON头+表单数据
     *
     * @param distributionId 应用id
     * @param httpServletRequest 创建订单请求参数
     * @return 订单创建结果
     */
    @PostMapping("/rtMartScanPay/order/create/{distributionId}")
    public ApiResponse<DistributionCreateOrderResponseDTO> rtMartScanPayDistributionOrderCreateNotify(@PathVariable("distributionId") Long distributionId, HttpServletRequest httpServletRequest) {
        Map<String, String> params = null;
        try {
            params = ServletUtils.extractParamsFromRequest(httpServletRequest);
        } catch (IOException e) {
            throw new RuntimeException("提取参数失败", e);
        }
        DistributionOrderCreateNotifyDTO request = new DistributionOrderCreateNotifyDTO();
        convertMapToDTO(params, request);
        log.info("rtMartScanPayDistributionOrderCreateNotify(distributionId = {},request={})",distributionId, JSONUtil.toJsonStr(request));
        return distributionOrderService.distributionOrderCreate(distributionId, request);
    }
    private void convertMapToDTO(Map<String, String> params,  Object request) {
        BeanWrapper beanWrapper = new BeanWrapperImpl(request);
        params.forEach((key, value) -> {
            try {
                if (beanWrapper.isWritableProperty(key)) {
                    beanWrapper.setPropertyValue(key, value);
                } else {
                    log.debug("DTO中没有字段: {}", key);
                }
            } catch (Exception e) {
                log.warn("设置字段失败: key={}, value={}, error={}", key, value, e.getMessage());
            }
        });
    }

    /**
     * 沃尔玛分销应用创建订单
     *
     * @param distributionId 应用id
     * @param request        创建订单请求参数
     * @return 订单创建结果
     */
    @PostMapping("/walMart/order/create/{distributionId}")
    public ApiResponse<WalMartOrderCreateResponseDTO> walMartDistributionOrderCreateNotify(
            @PathVariable("distributionId") Long distributionId, @RequestBody WalMartOrderCreateNotifyDTO request) {
        log.info("walMartDistributionOrderCreateNotify(distributionId = {},request={})", distributionId, JSONUtil.toJsonStr(request));
        return distributionOrderService.walMartDistributionOrderCreateNotify(distributionId, request);
    }

    /**
     * 饿了么分销应用创建订单
     *
     * @param distributionId 应用id
     * @param request        创建订单请求参数
     * @return 订单创建结果
     */
    @PostMapping("/eLeMe/order/create/{distributionId}")
    public ApiResponse<ELeMeOrderCreateResponseDTO> eLeMeDistributionOrderCreateNotify(@PathVariable("distributionId") Long distributionId, @RequestBody ELeMeOrderCreateNotifyDTO request) {
        log.info("eLeMeDistributionOrderCreateNotify(distributionId = {},request={})", distributionId, JSONUtil.toJsonStr(request));
        return distributionOrderService.eLeMeDistributionOrderCreateNotify(distributionId, request);
    }


    /**
     * 通用分销应用订单退款通知
     *
     * @param request 订单退款通知请求参数
     * @return 订单退款结果
     */
    @PostMapping("/common/order/refund")
    public ApiResponse<DistributionOrderRefundResponseDTO> commonDistributionOrderRefundNotify(
            DistributionOrderRefundNotifyDTO request) {
        log.info("commonDistributionOrderRefundNotify(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.distributionOrderRefund(request);
    }

    /**
     * 大润发扫码付分分销应用订单退款通知 JSON头+表单数据
     *
     * @param httpServletRequest 订单退款通知请求参数
     * @return 订单退款结果
     */
    @PostMapping("/rtMartScanPay/order/refund")
    public ApiResponse<DistributionOrderRefundResponseDTO> rtMartScanPayDistributionOrderRefundNotify(HttpServletRequest httpServletRequest) {
        Map<String, String> params = null;
        try {
            params = ServletUtils.extractParamsFromRequest(httpServletRequest);
        } catch (IOException e) {
            throw new RuntimeException("提取参数失败", e);
        }
        DistributionOrderRefundNotifyDTO request = new DistributionOrderRefundNotifyDTO();
        convertMapToDTO(params, request);
        log.info("rtMartScanPayDistributionOrderRefundNotify(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.distributionOrderRefund(request);
    }
    /**
     * 沃尔玛分销应用订单退款通知
     *
     * @param request 订单退款通知请求参数
     * @return 订单退款结果
     */
    @PostMapping("/walMart/order/refund")
    public ApiResponse<WalMartOrderRefundResponseDTO> walMartDistributionOrderRefundNotify(
            @RequestBody WalMartOrderRefundNotifyDTO request) {
        log.info("walMartDistributionOrderRefundNotify(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.walMartDistributionOrderRefund(request);
    }

    /**
     * 饿了么分销应用订单退款通知
     *
     * @param request 订单退款通知请求参数
     * @return 订单退款结果
     */
    @PostMapping("/eLeMe/order/refund")
    public ApiResponse<ELeMeOrderRefundResponseDTO> eLeMeDistributionOrderRefundNotify(@RequestBody ELeMeOrderRefundNotifyDTO request) {
        log.info("eLeMeDistributionOrderRefundNotify(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.eLeMeDistributionOrderRefund(request);
    }

    /**
     * 饿了么关闭订单接口
     *
     * @param request
     * @return
     */
    @PostMapping("/eLeMe/order/close")
    public ApiResponse<Object> eLeMeDistributionOrderClose(@RequestBody ELeMeOrderQueryRequestDTO request) {
        log.info("eLeMeDistributionOrderClose(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.eLeMeDistributionOrderClose(request);
    }

    /**
     * 通用分销应用订单状态变更 (叮咚买菜,大润发,食行生鲜,卫岗,清美,话费,屈臣氏,百联)
     *
     * @param request
     * @return
     */

    @PostMapping("/common/order/status-change")
    public ApiResponse<Object> commonDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
        log.info("distributionOrderStatusNotify(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.commonDistributionOrderStatusNotify(request);
    }

    /**
     * 大润发扫码付分销应用订单状态变更 JSON头+表单数据
     *
     * @param httpServletRequest 订单退款通知请求参数
     * @return 返回结果
     */
    @PostMapping("/rtMartScanPay/order/status-change")
    public ApiResponse<Object> rtMartScanPayDistributionOrderStatusNotify(HttpServletRequest httpServletRequest) {
        Map<String, String> params = null;
        try {
            params = ServletUtils.extractParamsFromRequest(httpServletRequest);
        } catch (IOException e) {
            throw new RuntimeException("提取参数失败", e);
        }
        DistributionOrderStatusNotifyDTO request = new DistributionOrderStatusNotifyDTO();
        convertMapToDTO(params, request);
        log.info("rtMartScanPayDistributionOrderStatusNotify(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.commonDistributionOrderStatusNotify(request);
    }

    /**
     * 视听分销应用订单状态变更
     *
     * @param request
     * @return
     */
//    @PostMapping("/stfx/order/status-change")
//    public ApiResponse<Object> stfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
//        log.info("stfxDistributionOrderStatusNotify(request={})", JSONUtil.toJsonStr(request));
//        String orderNo = request.getOrderNo();
//        String orderStatus = request.getChangeStatus();
//        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
//            if (Objects.equals("5", orderStatus)) {
//                OrderInfoEntity orderInfo = new OrderInfoEntity();
//                orderInfo.setExternalOrderNo(orderNo);
//                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
//                orderRepository.updateOrderByExternalOrderNo(orderInfo);
//            }
//        }
//        return ApiResponse.success();
//    }

    /**
     * 水韵分销应用订单状态变更
     *
     * @param request
     * @return
     */
    @PostMapping("/syfx/order/status-change")
    public ApiResponse<Object> syfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
        log.info("syfxDistributionOrderStatusNotify(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.syfxDistributionOrderStatusNotify(request);
    }

    /**
     * 同程分销应用订单状态变更
     *
     * @param request
     * @return
     */
//    @PostMapping("/tcfx/order/status-change")
//    public ApiResponse<Object> tcfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
//        String orderNo = request.getOrderNo();
//        String orderStatus = request.getOrderStatus();
//        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
//            if (Objects.equals("3", orderStatus)) {
//                OrderInfoEntity orderInfo = new OrderInfoEntity();
//                orderInfo.setExternalOrderNo(orderNo);
//                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
//                orderRepository.updateOrderByExternalOrderNo(orderInfo);
//            }
//        }
//        return ApiResponse.success();
//    }

    /**
     * 团油分销应用订单状态变更
     *
     * @param request
     * @return
     */
//    @PostMapping("/tyfx/order/status-change")
//    public ApiResponse<Object> tyfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
//        String orderNo = request.getOrderNo();
//        String orderStatus = request.getChangeStatus();
//        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
//            if (Objects.equals("1", orderStatus)) {
//                OrderInfoEntity orderInfo = new OrderInfoEntity();
//                orderInfo.setExternalOrderNo(orderNo);
//                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
//                orderRepository.updateOrderByExternalOrderNo(orderInfo);
//            }
//        }
//        return ApiResponse.success();
//    }

    /**
     * 西橙分销应用订单状态变更
     *
     * @param request
     * @return
     */
//    @PostMapping("/mvfx/order/status-change")
//    public ApiResponse<Object> mvfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
//        String orderNo = request.getOrderNo();
//        String orderStatus = request.getChangeStatus();
//        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
//            if (Objects.equals("1", orderStatus)) {
//                OrderInfoEntity orderInfo = new OrderInfoEntity();
//                orderInfo.setExternalOrderNo(orderNo);
//                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
//                orderRepository.updateOrderByExternalOrderNo(orderInfo);
//            }
//        }
//        return ApiResponse.success();
//    }

    /**
     * 永辉分销应用订单状态变更
     *
     * @param request
     * @return
     */
    @PostMapping("/yhfx/order/status-change")
    public ApiResponse<Object> yhfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
        log.info("yhfxDistributionOrderStatusNotify(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.yhfxDistributionOrderStatusNotify(request);
    }

    /**
     * 永辉分销应用订单包裹变更
     * @param request
     * @return
     */
    @PostMapping("/yhfx/order/package-change")
    public ApiResponse<Object> yhfxDistributionOrderPackageStatusNotify(DistributionOrderStatusNotifyDTO request) {
        return distributionOrderService.yhfxDistributionOrderPackageStatusNotify(request);
    }

    @PostMapping("/bsfx/order/status-change")
    public ApiResponse<Object> bsfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
        log.info("bsfxDistributionOrderStatusNotify(request={})", JSONUtil.toJsonStr(request));
        return distributionOrderService.bsfxDistributionOrderStatusNotify(request);
    }

    /**
     * 中免日上分销应用订单状态变更
     *
     * @param request
     * @return
     */
//    @PostMapping("/zmrsfx/order/status-change")
//    public ApiResponse<Object> zmrsfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request) {
//        String orderNo = request.getOrderNo();
//        String orderStatus = request.getOrderStatus();
////        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
////            if (Objects.equals("1", orderStatus)) {
////                OrderInfoEntity orderInfo = new OrderInfoEntity();
////                orderInfo.setOrderNo(orderNo);
////                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
////                orderRepository.updateOrderByOrderNo(orderInfo);
////            }
////        }
//        return ApiResponse.success();
//    }


}
