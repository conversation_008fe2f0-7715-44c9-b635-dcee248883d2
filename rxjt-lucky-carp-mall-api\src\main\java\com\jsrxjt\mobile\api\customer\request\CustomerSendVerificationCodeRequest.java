package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@Schema(description = "用户获取验证码请求参数")
public class CustomerSendVerificationCodeRequest extends BaseParam {

    @Schema(description = "验证码类型 1注册绑定 2验证码登录 3设置修改密码 4修改手机号码 5注销用户")
    @NotNull(message = "验证码类型不能为空")
    private Integer codeType;

    @Schema(description = "用户输入的手机号码 codeType=1、2、4时必传")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String phone;

    @Schema(description = "用户id codeType=3、4、5时必传")
    private Long customerId;

}
