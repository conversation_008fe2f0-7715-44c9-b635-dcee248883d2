package com.jsrxjt.mobile.domain.order.service.strategy.aftersale.impl;

import com.jsrxjt.mobile.api.order.dto.response.AfterSaleDetailResponseDTO;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.service.strategy.aftersale.AfterSaleProgressBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 空进度构建器
 */
@Slf4j
public class AfterSaleEmptyProgressBuilder implements AfterSaleProgressBuilder {
    @Override
    public boolean supports(Integer status) {
        return false;
    }

    @Override
    public List<AfterSaleDetailResponseDTO.ProgressDetailDTO> build(AfterSaleEntity afterSale, OrderInfoEntity order) {
        log.warn("未匹配到售后状态，用空进度构建器构建售后进度");
        return List.of();
    }
}
