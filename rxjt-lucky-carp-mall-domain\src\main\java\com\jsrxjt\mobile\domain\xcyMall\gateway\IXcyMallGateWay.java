package com.jsrxjt.mobile.domain.xcyMall.gateway;

import com.jsrxjt.mobile.api.customer.response.CustomerDetailResponse;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCancelResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallQueryUserResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallRefundResponseDTO;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;

import java.util.Map;

/**
 * Created by jeffery.yang on 2025/10/21 17:10
 *
 * @description:
 * @author: jeffery.yang
 * @date: 2025/10/21 17:10
 * @version: 1.0
 */
public interface IXcyMallGateWay {

	/**
	 * 验签
	 * @param requestMap
	 * @param appFlag
	 * @return
	 */
	Boolean verifySign(Map<String, Object> requestMap,String appFlag);

	/**
	 * 构建查询会员信息响应
	 * @param customerDetail
	 * @param appGoods
	 * @return
	 */
	XcyMallQueryUserResponseDTO buildGetVipInfoResponse(CustomerDetailResponse customerDetail,AppGoodsEntity appGoods);

	/**
	 * 构建创建订单响应
	 * @param orderInfoEntity
	 * @param appFlag
	 * @return
	 */
	XcyMallCreateOrderResponseDTO buildCreateOrderResponse(OrderInfoEntity orderInfoEntity,String appFlag);

	/**
	 * 构建退款订单响应
	 * @param afterSaleEntity
	 * @param appFlag
	 * @return
	 */
	XcyMallRefundResponseDTO buildRefundOrderResponse(AfterSaleEntity afterSaleEntity,String appFlag);

	/**
	 * 构建取消订单响应
	 * @param appGoods
	 * @param appFlag
	 * @return
	 */
	XcyMallCancelResponseDTO buildCancelOrderResponse(AppGoodsEntity appGoods,String appFlag);

}
