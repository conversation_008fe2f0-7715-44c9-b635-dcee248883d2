package com.jsrxjt.mobile.api.distribution.dto.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/23 18:04
 */
@Data
public class DistributionCreateOrderResponseDTO {
    /**
     * 订单号
     */
    private String thirdOrderNo;
    /**
     * 收银台地址
     */
    private String cashierUrl;

    /**
     * 支付状态 0=支付失败 1=支付成功
     */
    private String payStatus;

    /**
     * 支付金额
     */
    private String payAmount;

    /**
     * 支付状态等于1的时候必传 支付完成时间，格式yyyyMMddHHmmss
     */
    private String payTime;

    /**
     * 支付成功时间 10位时间戳
     */
    private String tradeTime;

}
