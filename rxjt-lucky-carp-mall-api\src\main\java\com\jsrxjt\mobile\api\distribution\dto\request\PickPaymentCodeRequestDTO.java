package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 扫码提货的付款码请求参数
 * @Author: ywt
 * @Date: 2025-05-19 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "扫码提货的付款码请求参数")
public class PickPaymentCodeRequestDTO {
    @Schema(description = "第三方商户id，提货券分销平台提供", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "第三方商户id不能为空")
    private String thirdId;
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户id不能为空")
    private Long userId;
    @Schema(description = "32位随机字符串", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "nonce不能为空")
    private String nonce;
    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "时间戳不能为空")
    private String timestamp;
}
