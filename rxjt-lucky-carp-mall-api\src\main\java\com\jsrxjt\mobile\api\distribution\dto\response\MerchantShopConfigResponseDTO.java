package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 商户大全的门店响应
 * @Author: ywt
 * @Date: 2025-05-28 09:48
 * @Version: 1.0
 */
@Data
@Schema(description = "商户大全门店详情config响应")
public class MerchantShopConfigResponseDTO {
    @Schema(description = "门店ID")
    private String shop_agent_id;

    @Schema(description = "商户分组ID")
    private String shop_group_id;

    @Schema(description = "瑞祥门店号")
    private String store_id;

    @Schema(description = "瑞祥终端号")
    private String term_id;

    @Schema(description = "福鲤圈动态码支付开关")
    private String pay_code;

    @Schema(description = "福鲤圈动态码提货凭证开关")
    private String dynamic_union_switch;

    @Schema(description = "福鲤圈动态码提货券开关")
    private String dynamic_voucher_switch;

    @Schema(description = "福鲤圈静态码支付开关")
    private String static_code;

    @Schema(description = "福鲤圈付款码入账类型")
    private String order_type;

    @Schema(description = "单卡核销入账开关 商联卡:1 compay 2 thpay 瑞祥红卡:3 compay 4 thpay 白金卡:5 compay 6 thpay 黑金卡:7 compay 8 thpay 提货券:9 compay 10 thpay")
    private String card_select;

    @Schema(description = "是否生日享商户")
    private String is_birth;

    @Schema(description = "生日享品牌ID")
    private String birth_brand_id;

    @Schema(description = "是否首页推荐")
    private String birth_is_index;

    @Schema(description = "商户宝周结起始点 1 周一  2 周二 3 周三 4 周四 5 周五 6 周六 7 周日")
    private String shb_week_tj_start;

    @Schema(description = "商户宝功能权限 1 快速收银 2 固定收款码 3 营销活动核销 4 卡券核销 5 商城卡管理 6 余额查询 7 收款查询 8 打印日结单 9 打印周结单 10 打印月结单 11 提货码核销")
    private String shb_auth;

    @Schema(description = "核销模块显示 1 本地生活券核销 2 工会消费券核销 3 券码核销 4 好生日核销")
    private String verification_module;

    @Schema(description = "商户宝收银支持类型 1 瑞祥卡入口 2 提货券/扫码入口")
    private String cashier_type;

    @Schema(description = "商户宝日周月结显示：黑金卡 tj_show_hj ，商联卡 tj_show_sl，提货凭证 tj_show_th，黑金主卡 tj_show_main，就餐卡 tj_show_jc，微信 tj_show_wx，支付宝 tj_show_ali，云闪付 tj_show_ysf，银行卡 tj_show_card，白金主卡 tj_show_bjmain，白金卡 tj_show_bjcard")
    private String tj_show;

    @Schema(description = "商户主密钥")
    private String token;

    @Schema(description = "ERP-门店编号")
    private String erp_agent_no;

    @Schema(description = "汇付聚合SN码")
    private String huifu_sn;

    @Schema(description = "易联云密钥")
    private String yly_key;

    @Schema(description = "易联云终端号")
    private String yly_mch_id;

    @Schema(description = "聚合门店号")
    private String jh_md;

    @Schema(description = "终端每日退款限额")
    private String refund_pre_day_price_limit;

    @Schema(description = "终端退款天数限制")
    private String refund_day_limit;

    @Schema(description = "全球购门店显示")
    private String show_shop_switch;

    @Schema(description = "福鲤圈支付方式（兼容）映射flq_pay_type_relation表type")
    private String flq_pay_type;

    @Schema(description = "福鲤圈拖拽式选择支付方式 1 黑金卡(已废弃黑金卡包) 2 商联卡 3 微信 4 工会凭证 5 黑金主卡 6 就餐卡 7 白金主卡 8 白金卡 9 汽车服务包(白金卡体系) 10 好生日 11 好生日黑金主卡 12 凭证主卡(礼品之家卡) 20 支付宝 21 瑞祥红卡 22 提货凭证")
    private String pay_type_select;

    @Schema(description = "支付类型")
    private String pay_mode;

    @Schema(description = "微信支付开关")
    private String wechat_pay;
}
