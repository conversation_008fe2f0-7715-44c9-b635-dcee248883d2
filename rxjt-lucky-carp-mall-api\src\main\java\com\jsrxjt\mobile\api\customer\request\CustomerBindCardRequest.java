package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "用户绑卡请求参数")
public class CustomerBindCardRequest extends BaseParam {

    @Schema(description = "用户Id")
    @NotNull(message = "用户Id不能为空")
    private Long customerId;

    @Schema(description = "卡号")
    @NotBlank(message = "卡号不能为空")
    private String cardNo;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "cvv")
    @NotBlank(message = "cvv不能为空")
    private String cvv;

    @Schema(description = "卡类型 RBIZ（商联卡/红卡）、LT（工会凭证）")
    @NotBlank(message = "卡类型不能为空")
    private String cardTypeCode;

    @Schema(description = "用户登录ip", hidden = true)
    private String loginIp;
    @Schema(description = "易盾的业务Id")
    private String ydBusinessId;
    @Schema(description = "易盾的token")
    private String ydToken;

}
