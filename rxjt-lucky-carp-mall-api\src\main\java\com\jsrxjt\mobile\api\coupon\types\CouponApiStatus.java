package com.jsrxjt.mobile.api.coupon.types;

import lombok.Getter;

/**
 * 卡管v2接口API状态码
 */
@Getter
public enum CouponApiStatus {
    SUCCESS(0, "成功状态码"),
    ERROR(1, "错误状态码"),
    SIGN_ERROR(10, "签名异常"),
    TOKEN_ERROR(20, "凭证异常"),
    LOGIN_EXPIRED(21, "登录过期"),
    NO_PERMISSION(30, "无权限"),
    NOT_FOUND(404, "接口不存在"),
    BALANCE_ERROR(60001, "余额异常"),
    STOCK_ERROR(60002, "库存异常"),
    MERCHANT_ERROR(60003, "经销商异常"),
    COUPON_ERROR(60004, "卡券异常"),
    BRAND_ERROR(60005, "品牌异常");

    private final int code;
    private final String desc;

    CouponApiStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static CouponApiStatus fromCode(int code) {
        for (CouponApiStatus e : values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return null;
    }
}
