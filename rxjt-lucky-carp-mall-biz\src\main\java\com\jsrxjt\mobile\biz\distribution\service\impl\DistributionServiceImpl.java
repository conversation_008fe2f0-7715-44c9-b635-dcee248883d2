package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionRedirectBaseDTO;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.distribution.service.DistributionService;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025-10-22
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DistributionServiceImpl implements DistributionService {
    private final UnifiedDistributionApi unifiedDistributionApi;

    private final CustomerService customerService;

    private final RegionRepository regionRepository;

    private final AppGoodsRepository appGoodsRepository;

    @Override
    public BaseResponse<String> getRedirectUrl(DistributionRedirectBaseDTO requestDTO) {
        Long userId = StpUtil.getLoginIdAsLong();
        CustomerEntity customerEntity = customerService.getCustomerById(userId);
        if (customerEntity == null) {
            throw new BizException(Status.USER_NOT_EXIST);
        }
        DistChannelType distChannelType = DistChannelType.getByCode(requestDTO.getDistributionType());
        if (distChannelType == null) {
            return BaseResponse.fail(Status.NOT_SUPPORT_REDIRECT);
        }
        AppGoodsEntity appGoodsByAppFlag = appGoodsRepository.findAppGoodsByAppFlag(requestDTO.getDistributionType());
        if (appGoodsByAppFlag == null) {
            return BaseResponse.fail(Status.NOT_SUPPORT_REDIRECT);
        }
        RegionEntity region = regionRepository.getCurrentRegion(customerEntity.getId());
        DistAccessRequest request = DistAccessRequest.builder()
                .channelType(distChannelType)
                .userId(String.valueOf(userId))
                .mobile(customerEntity.getPhone())
                .sceneValue(requestDTO.getLocalLifeRedirectParamValue())
                .sceneId(requestDTO.getLocalLifeRedirectType())
                .latitude(region.getLat())
                .longitude(region.getLng())
                .build();

        if(isXcyMallApp(requestDTO.getDistributionType())){
            String onceToken = customerService.getOnceToken(userId);
            request.setOnceToken(onceToken);
        }
        DistAccessResponse distAccessResponse = unifiedDistributionApi.access(request);
        if (distAccessResponse.isSuccess()){
            return BaseResponse.succeed(distAccessResponse.getRedirectUrl());
        }
        return BaseResponse.fail(Status.NOT_SUPPORT_REDIRECT);
    }

    @Override
    public Boolean isXcyMallApp(String distributionType) {
        return distributionType.startsWith("XCY");
    }

}
