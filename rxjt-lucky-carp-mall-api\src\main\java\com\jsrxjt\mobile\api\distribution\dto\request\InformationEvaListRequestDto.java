package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Description: 评价列表请求参数
 * @Author: ywt
 * @Date: 2025-08-19 16:58
 * @Version: 1.0
 */
@Data
public class InformationEvaListRequestDto {
    @Schema(description = "资讯id")
    @NotNull(message = "资讯id为空错误")
    private Integer informationId;
    @Schema(description = "分页参数，每页条数，默认10条")
    @Min(value = 1,message = "每页最少1条数据")
    @Max(value = 20,message = "每页最多20条数据")
    @NotNull(message = "每页数量为空错误")
    private Integer pageSize = 10;
    @Schema(description = "分页参数，从第几页开始 默认从第一页开始")
    @Min(value = 1, message = "分页参数错误")
    @NotNull(message = "页码为空错误")
    private Integer pageNum = 1;
}
