package com.jsrxjt.mobile.api.coupon.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 直充卡券的显示模版样式请求参数
 * @Author: ywt
 * @Date: 2025-08-11 09:18
 * @Version: 1.0
 */
@Data
@Schema(description = "直充卡券的显示模版样式请求参数")
public class VedioShowTemplateRequestDTO {
    @Schema(description = "卡券的spuid", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "卡券spuid为空错误")
    private Long couponSpuId;
}
