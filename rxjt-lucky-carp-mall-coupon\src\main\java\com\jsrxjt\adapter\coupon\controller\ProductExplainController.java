
package com.jsrxjt.adapter.coupon.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.product.dto.request.DefaultSearchKeywordRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.ProductSearchRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.ProductSuggestionRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.RechargeUsageInstructionsRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.ProductSuggestionResponseDTO;
import com.jsrxjt.mobile.api.product.dto.response.SearchKeywordResponseDTO;
import com.jsrxjt.mobile.biz.product.service.ProductExplainCaseService;
import com.jsrxjt.mobile.biz.product.service.ProductRechargeCaseService;
import com.jsrxjt.mobile.biz.product.service.ProductSearchCaseService;
import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/product-explain")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "产品说明接口", description = "产品说明接口")
public class ProductExplainController {

    private final ProductExplainCaseService productExplainCaseService;


    @PostMapping("/rechargeUsageInstructions")
    @Operation(summary = "获取指定产品的充值使用说明")
    @VerifySign
    public BaseResponse<String> rechargeUsageInstructions(@RequestBody @Valid RechargeUsageInstructionsRequestDTO requestDTO) {
        return BaseResponse.succeed(productExplainCaseService.getRechargeUsageInstructions(requestDTO));
    }
}
