package com.jsrxjt.mobile.api.birth.request;

import com.jsrxjt.common.core.vo.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description:生日券参数
 */
@Data
public class BirthCouponRequestDTO extends BaseRequest {

    @Schema(description = "优惠券ID")
    @NotNull(message = "优惠券ID不能为空")
    private Long ticketId;

    @Schema(description = "领取数量， 默认为1")
    private Integer number = 1;
}
