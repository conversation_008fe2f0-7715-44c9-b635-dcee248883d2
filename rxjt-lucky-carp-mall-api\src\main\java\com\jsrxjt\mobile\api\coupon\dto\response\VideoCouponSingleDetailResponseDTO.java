package com.jsrxjt.mobile.api.coupon.dto.response;

import com.jsrxjt.mobile.api.product.dto.ProductExplainResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 独立视频卡券详情
 * @Author: ywt
 * @Date: 2025-07-23 15:47
 * @Version: 1.0
 */
@Data
public class VideoCouponSingleDetailResponseDTO {
    @Schema(description = "视频直充卡券id")
    private Long couponSpuId;
    @Schema(description = "卡券名称")
    private String couponSpuName;
    @Schema(description = "logo图片url")
    private String logoUrl;
    @Schema( description = "卡券图片url")
    private String imgUrl;
    @Schema(description = "品牌名")
    private String brandName;
    @Schema( description = "1-腾讯视频 2-爱奇艺 3-优酷视频 4-芒果TV 5-中石化权益包 6-便利蜂 7-咪咕视频 8-喜马拉雅 9-搜狐视频，前端可用来判断详情页使用哪种模版样式显示，仅用于直充类卡券")
    private Integer showTemplate;
    @Schema(description = "使用须知，仅isSelected为1有效")
    List<ProductExplainResponseDTO> userDetailList;
    @Schema(description = "温馨提示，仅isSelected为1有效")
    List<ProductExplainResponseDTO> warmToastList;
    @Schema( description = "单SPU的所有sku信息，仅isShowPolymerize为0有效")
    private List<CouponSkuInfoResponseDTO> skuList;
}
