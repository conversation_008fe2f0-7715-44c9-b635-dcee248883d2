package com.jsrxjt.mobile.api.enums;

//发货状态: 0-未发货/未充值  1-发货中/充值中 2-已发货/已充值 3-发货失败/充值失败
public enum DeliveryStatusEnum {
    UNDELIVERED((byte)0, "未发货/为充值"),
    DELIVERING((byte)1, "发货中/充值中"),
    DELIVERED((byte)2, "已发货/已充值"),
    DELIVERY_FAILED((byte)3, "发货失败/充值失败");
    private final Byte code;
    private final String description;
    DeliveryStatusEnum(Byte code, String description) {
        this.code = code;
        this.description = description;
    }
    public Byte getCode() {
        return code;
    }
    public String getDescription() {
        return description;
    }
    public static String getDescriptionByCode(Byte code) {
        for (DeliveryStatusEnum value : DeliveryStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDescription();
            }
        }
        return null;
    }
}
