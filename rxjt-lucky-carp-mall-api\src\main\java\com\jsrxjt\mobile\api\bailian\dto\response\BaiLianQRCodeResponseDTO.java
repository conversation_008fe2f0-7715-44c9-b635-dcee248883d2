package com.jsrxjt.mobile.api.bailian.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/10/28
 */
@Data
@Schema(description = "百联获取付款二维码返回参数")
public class BaiLianQRCodeResponseDTO {

    @Schema(description = "百联第三方号")
    private String thdUserId;

    @Schema(description = "安付宝卡包ID")
    private String walletId;

    @Schema(description = "付款二维码")
    private String payQRCode;

    @Schema(description = "有效时间:单位-秒")
    private String validTime;
}
