package com.jsrxjt.mobile.api.promotion.types;


/**
 * 卡券活动-卡券类型枚举类
 */
public enum PromotionActivityCouponTypeEnum {

    COUPON(1, "普通卡券"),

    PACKAGE_COUPON(2, "组合套餐卡券");

    private final Integer type;

    private final String typeName;

    PromotionActivityCouponTypeEnum(Integer type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public Integer getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }

}
