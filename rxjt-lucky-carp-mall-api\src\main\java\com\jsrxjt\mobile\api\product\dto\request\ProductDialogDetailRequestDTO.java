package com.jsrxjt.mobile.api.product.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 产品弹框信息请求参数
 * @Author: ywt
 * @Date: 2025-11-06 09:23
 * @Version: 1.0
 */
@Data
@Schema(description = "产品弹框信息请求参数")
public class ProductDialogDetailRequestDTO {
    @Schema(description = "产品spuid", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "产品spuid不能为空")
    private Long productSpuId;
    @Schema(description = "产品类型, 1 卡券 2 套餐", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "产品类型不能为空")
    private Integer productType;
    @Schema(description = "弹框的页面, 1:提交订单弹框 2:核销页弹框", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "弹框的页面不能为空")
    private Integer dialogType;
}
