package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 启动页数据响应
 * @Author: ywt
 * @Date: 2025-06-09 14:46
 * @Version: 1.0
 */
@Data
@Schema(name = "ScreenPageResponseDTO", description = "启动页")
public class ScreenPageResponseDTO {
    @Schema(description = "开屏页id")
    private Long screenId;
    @Schema(description = "图片url")
    private String imgUrl;
    @Schema(description = "停留倒计时")
    private Integer countdown;
    @Schema(description = "IOS跳转url")
    private String iosUrl;
    @Schema(description = "安卓跳转url")
    private String androidUrl;
    @Schema(description = "小程序appid")
    private String wechatMiniAppid;
    @Schema(description = "小程序跳转url")
    private String wechatMiniUrl;
}
