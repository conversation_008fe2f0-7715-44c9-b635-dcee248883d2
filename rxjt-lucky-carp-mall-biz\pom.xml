<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jsrxjt</groupId>
        <artifactId>rxjt-lucky-carp-mall</artifactId>
        <version>1.0.0</version>
    </parent>
    <packaging>jar</packaging>
    <artifactId>rxjt-lucky-carp-mall-biz</artifactId>
    <name>rxjt-lucky-carp-mall-mobile-biz</name>
    <description>rxjt-lucky-carp-mall-mobile 业务层</description>

    <dependencies>
        <dependency>
            <groupId>com.jsrxjt</groupId>
            <artifactId>rxjt-lucky-carp-mall-domain</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>

        <!-- skywalking 整合 logback -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

    </dependencies>
</project>
