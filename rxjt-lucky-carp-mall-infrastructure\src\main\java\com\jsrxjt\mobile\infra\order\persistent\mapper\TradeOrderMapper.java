package com.jsrxjt.mobile.infra.order.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.TradeOrderPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交易订单表Mapper接口
 * <AUTHOR>
 * @since 2025/8/18
 */
@Mapper
public interface TradeOrderMapper extends CommonBaseMapper<TradeOrderPO> {

    /**
     * 根据订单号和交易号查询交易订单信息
     * 
     * @param orderNo 订单号
     * @param tradeNo 交易号
     * @return 交易订单信息
     */
    TradeOrderPO selectByOrderNoAndTradeNo(@Param("orderNo") String orderNo, @Param("tradeNo") String tradeNo);

    /**
     * 根据订单号查询交易订单信息列表
     * 
     * @param orderNo 订单号
     * @return 交易订单信息列表
     */
    List<TradeOrderPO> selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据交易号查询交易订单信息
     * 
     * @param tradeNo 交易号
     * @return 交易订单信息
     */
    TradeOrderPO selectByTradeNo(@Param("tradeNo") String tradeNo);
}