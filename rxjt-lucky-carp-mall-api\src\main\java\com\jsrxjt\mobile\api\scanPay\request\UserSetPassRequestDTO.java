package com.jsrxjt.mobile.api.scanPay.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 卡号dto
 * @Author: ZY
 * @Date: 20250530
 */
@Data
@Schema(description = "用户密码验证")
public class UserSetPassRequestDTO {
    @Schema(description = "手机号")
    @NotBlank(message = "会员手机号不能为空")
    private String mobile;

    @Schema(description = "密码 MD5加密")
    @NotBlank(message = "原支付密码不能为空")
    private String originPayPassword;

    @Schema(description = "密码 MD5加密")
     @NotBlank(message = "现支付密码不能为空")
    private String nowPayPassword;

}
