package com.jsrxjt.mobile.api.coupon.dto.response;

import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.api.ticket.response.BrandTicketResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 卡券SKU信息响应
 * @Author: ywt
 * @Date: 2025-04-29 13:50
 * @Version: 1.0
 */
@Data
@Schema(description = "卡券sku信息响应")
public class CouponSkuInfoResponseDTO {
    @Schema(description = "卡券skuId")
    private Long couponSkuId;
    @Schema(description = "卡券spuid")
    private Long couponSpuId;//卡券spuid(coupon_goods主键)
    @Schema(description = "卡管平台卡券id")
    private String couponPlatformId;//卡管平台卡券id
    @Schema(description = "面值名称")
    private String amountName;
    @Schema(description = "面值")
    private BigDecimal amount;//面值
    @Schema(description = "手续费百分比")
    private BigDecimal commissionFee;//手续费百分比
    @Schema(description = "福鲤圈的售价")
    private BigDecimal platformPrice;
    @Schema(description = "每月每人单卡限购数量")
    private Integer limitNumPerMonth;//每月每人单卡限购数量
    @Schema(description = "已售数量")
    private Integer soldNum;//已售数量
    @Schema(description = "卡券状态 0:下架 1:出售中")
    private Integer status;//卡券状态 0:下架 1:出售中
    @Schema(description = "卡券库存，仅普通卡券有效")
    private Integer inventory;//卡券库存
    @Schema(description = "规格值id，多个规格值用逗号分隔")
    private String specsValue;//规格值id，多个规格值用逗号分隔
    @Schema(description = "备注文本。注意：若是视听直充卡券，前端要对本字段按英文;进行分隔并换行展示")
    private String remark;//备注文本
    @Schema(description = "起售数量")
    private Integer onSaleNum;//起售数量
    @Schema(description = "单次限售数量")
    private Integer rationSaleNum;//单次限售数量

    @Schema(description = "充值类型,0其他 1手机号 2QQ号 3中石化账户")
    private Integer accountType;

    @Schema(description = "卡券核销类型 101:平台自发券 201:卡号+卡密 202:卡号或卡密 203:卡号+卡密+校验码 301:链接 302:链接+验证码 303:链接+卡号+验证码 304:卡号+短链接")
    private Integer type;
    @Schema(description = "是否选中， 0--未选中 1--选中")
    private Integer isSelected;
    @Schema(description = "sku的促销活动信息")
    private PromotionSkuInfo promotionInfo;
    @Schema(description = "优惠券")
    private List<BrandTicketResponseDTO> brandTicketList;
}
