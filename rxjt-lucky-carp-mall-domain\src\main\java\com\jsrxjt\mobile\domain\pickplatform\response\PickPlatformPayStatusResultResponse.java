package com.jsrxjt.mobile.domain.pickplatform.response;

import lombok.Data;

/**
 * @Description: 支付状态查询
 * @Author: zy
 * @Date: 2025/6/04
 */
@Data
public class PickPlatformPayStatusResultResponse {
    //交易时间 非成功状态统一留空
    private String trade_time;
    //状态 00支付成功 01待支付 02支付失败(含取消支付)
    private String trade_status;
    //外部订单号
    private String out_order_sn;
    //平台交易流水号
    private String trade_no;
    //平台订单流水号
    private String order_no;
}
