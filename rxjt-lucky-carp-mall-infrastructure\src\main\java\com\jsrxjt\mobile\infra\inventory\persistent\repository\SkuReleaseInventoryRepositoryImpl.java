package com.jsrxjt.mobile.infra.inventory.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.domain.inventory.entity.SkuReleaseInventoryEntity;
import com.jsrxjt.mobile.domain.inventory.repository.SkuReleaseInventoryRepository;
import com.jsrxjt.mobile.infra.inventory.persistent.mapper.SkuReleaseInventoryMapper;
import com.jsrxjt.mobile.infra.inventory.persistent.po.SkuReleaseInventoryPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 放量库存记录存储实现类
 * 
 * <AUTHOR>
 * @since 2025/3/11
 **/
@Repository
@RequiredArgsConstructor
public class SkuReleaseInventoryRepositoryImpl implements SkuReleaseInventoryRepository {

    private final SkuReleaseInventoryMapper skuReleaseInventoryMapper;

    @Override
    public int batchSaveReleaseInventories(List<SkuReleaseInventoryEntity> releaseInventoryRecords) {
        if (releaseInventoryRecords == null || releaseInventoryRecords.isEmpty()) {
            return 0;
        }

        // 将实体转换为PO对象
        List<SkuReleaseInventoryPO> poList = releaseInventoryRecords.stream()
                .map(entity -> {
                    SkuReleaseInventoryPO po = new SkuReleaseInventoryPO();
                    BeanUtil.copyProperties(entity, po);
                    return po;
                })
                .toList();

        // 批量保存
        return skuReleaseInventoryMapper.batchInsert(poList);
    }

    @Override
    public int updateStartedInventoryStatus(Integer productType, LocalDateTime currentTime) {
        return skuReleaseInventoryMapper.updateStartedInventoryStatus(productType, currentTime);
    }

    @Override
    public int updateEndedInventoryStatus(Integer productType, LocalDateTime currentTime) {
        return skuReleaseInventoryMapper.updateEndedInventoryStatus(productType, currentTime);
    }

    @Override
    public List<SkuReleaseInventoryEntity> findUnfinishedByPlanId(Long planId, Long policyId) {
        if (planId == null || policyId == null) {
            return List.of();
        }

        // 查询PO列表
        List<SkuReleaseInventoryPO> poList = skuReleaseInventoryMapper.selectListUnfinishedByPlanId(planId, policyId);
        if (poList == null || poList.isEmpty()) {
            return List.of();
        }

        // 将PO转换为实体对象
        return poList.stream()
                .map(po -> {
                    SkuReleaseInventoryEntity entity = new SkuReleaseInventoryEntity();
                    BeanUtil.copyProperties(po, entity);
                    return entity;
                })
                .toList();
    }

    @Override
    public int batchUpdateReleaseInventories(List<SkuReleaseInventoryEntity> releaseInventoryRecords) {
        if (releaseInventoryRecords == null || releaseInventoryRecords.isEmpty()) {
            return 0;
        }

        // 将实体转换为PO对象
        List<SkuReleaseInventoryPO> poList = releaseInventoryRecords.stream()
                .map(entity -> {
                    SkuReleaseInventoryPO po = new SkuReleaseInventoryPO();
                    BeanUtil.copyProperties(entity, po);
                    return po;
                })
                .toList();

        // 批量更新
        return skuReleaseInventoryMapper.batchUpdateReleaseInventories(poList);
    }

    @Override
    public int deleteUnfinishedByPlanId(Long planId, LocalDateTime modTime) {
        if (planId == null || modTime == null) {
            return 0;
        }
        return skuReleaseInventoryMapper.updateUnfinishedAsDeleted(planId, modTime);
    }

    @Override
    public boolean existsReleaseInventoryByPlanIdAndDate(Long planId, LocalDate releaseDate) {
        if (planId == null || releaseDate == null) {
            return false;
        }
        // 设置日期范围为当天，因为放量库存是按照日期生成的
        int count = skuReleaseInventoryMapper.countByPlanIdAndDateRange(planId, releaseDate, releaseDate);
        return count > 0;
    }

    @Override
    public boolean existsReleaseInventoryByPlanIdAndPolicyIdAndDate(Long planId, Long policyId, LocalDate releaseDate) {
        if (planId == null || policyId == null || releaseDate == null) {
            return false;
        }
        // 设置日期范围为当天，因为放量库存是按照日期生成的
        int count = skuReleaseInventoryMapper.countByPlanIdAndPolicyIdAndDateRange(planId, policyId, releaseDate,
                releaseDate);
        return count > 0;
    }

    @Override
    public List<SkuReleaseInventoryEntity> findValidByProductTypeAndSkuIdAndPolicyId(Integer productType, Long skuId,
            long policyId) {
        if (productType == null || skuId == null) {
            return List.of();
        }

        // 查询PO列表
        List<SkuReleaseInventoryPO> poList = skuReleaseInventoryMapper
                .selectValidByProductTypeAndSkuIdAndPolicyId(productType, skuId, policyId);
        if (poList == null || poList.isEmpty()) {
            return List.of();
        }

        // 将PO转换为实体对象
        return poList.stream()
                .map(po -> {
                    SkuReleaseInventoryEntity entity = new SkuReleaseInventoryEntity();
                    BeanUtil.copyProperties(po, entity);
                    return entity;
                })
                .toList();
    }

    @Override
    public int reduceInventoryById(Long id, Integer quantity) {
        if (id == null || quantity == null || quantity <= 0) {
            return 0;
        }
        return skuReleaseInventoryMapper.reduceInventoryById(id, quantity);
    }

    @Override
    public int batchReduceInventoryById(List<InventoryReduction> reductionList) {
        if (reductionList == null || reductionList.isEmpty()) {
            return 0;
        }

        // 转换为Map格式供MyBatis使用
        List<Map<String, Object>> mapList = reductionList.stream()
                .map(reduction -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", reduction.getId());
                    map.put("quantity", reduction.getQuantity());
                    return map;
                })
                .toList();

        return skuReleaseInventoryMapper.batchReduceInventoryById(mapList);
    }

    @Override
    public List<InventoryReduction> findReductionsByOrderNo(String orderNo) {
        // 这个方法需要缓存中获取，此处返回空列表，实际实现在Service中通过缓存获取
        return List.of();
    }

    @Override
    public int recoverInventoryById(Long id, Integer quantity) {
        if (id == null || quantity == null || quantity <= 0) {
            return 0;
        }
        return skuReleaseInventoryMapper.recoverInventoryById(id, quantity);
    }

    @Override
    public int batchRecoverInventoryById(List<InventoryReduction> reductionList) {
        if (reductionList == null || reductionList.isEmpty()) {
            return 0;
        }

        // 转换为Map格式
        List<Map<String, Object>> mapList = reductionList.stream()
                .map(reduction -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", reduction.getId());
                    map.put("quantity", reduction.getQuantity());
                    return map;
                })
                .toList();

        return skuReleaseInventoryMapper.batchRecoverInventoryById(mapList);
    }

    @Override
    public List<SkuReleaseInventoryEntity> findTodayReleaseInventoryBySkuAndPolicy(Long productSkuId,
            Integer productType, Long policyId, LocalDate releaseDate) {
        if (productSkuId == null || productType == null || policyId == null || releaseDate == null) {
            return List.of();
        }

        // 查询PO列表
        List<SkuReleaseInventoryPO> poList = skuReleaseInventoryMapper
                .selectTodayReleaseInventoryBySkuAndPolicy(productSkuId, productType, policyId, releaseDate);
        if (poList == null || poList.isEmpty()) {
            return List.of();
        }

        // 将PO转换为实体对象
        return poList.stream()
                .map(po -> {
                    SkuReleaseInventoryEntity entity = new SkuReleaseInventoryEntity();
                    BeanUtil.copyProperties(po, entity);
                    return entity;
                })
                .toList();
    }
}