package com.jsrxjt.mobile.api.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 分类详情DTO
 * 
 * <AUTHOR>
 * @since 2025/5/12
 */
@Data
@Schema(description = "分类产品DTO")
public class CategoryProductDTO {

    @Schema(description = "二级分类ID")
    private Long categoryId;

    @Schema(description = "二级分类名称")
    private String categoryName;


    @Schema(description = "分类对应的产品列表")
    private List<ProductBaseInfoDTO> products;
}