package com.jsrxjt.mobile.domain.order.types;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交易支付状态枚举
 * <AUTHOR>
 * @since 2025/8/15
 */
@Getter
@AllArgsConstructor
public enum TradePayStatusEnum {
    
    WAIT_PAY("WAIT_PAY", "待支付"),
    SUCCESS_PAY("SUCCESS_PAY", "支付成功"),
    FAIL_PAY("FAIL_PAY", "支付失败");
    
    private final String code;
    private final String desc;

    public static TradePayStatusEnum fromCode(String payStatus) {
        for (TradePayStatusEnum value : values()) {
            if (value.code.equals(payStatus)) {
                return value;
            }
        }
        return null;
    }
}