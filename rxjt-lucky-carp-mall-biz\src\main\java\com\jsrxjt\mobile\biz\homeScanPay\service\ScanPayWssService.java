package com.jsrxjt.mobile.biz.homeScanPay.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.PickChannelWssRequestDTO;

/**
 * 扫码付websocket服务
 * <AUTHOR>
 * @date 2025/09/04
 */
public interface ScanPayWssService {

    /**
     * 发送消息给用户
     * @param customerId
     * @param payCode
     * @param message
     */
    void sendMessageToCustomer(Long customerId, String payCode, String message);

    /**
     * 判断用户是否已连接
     * @param customerId
     * @param payCode
     * @return boolean
     */
    boolean isConnected(Long customerId, String payCode);


    /**
     * webscoket 推送
     * @param requestDTO
     * @return {@link BaseResponse}
     */
    BaseResponse pushWssInfo(PickChannelWssRequestDTO requestDTO);
}
