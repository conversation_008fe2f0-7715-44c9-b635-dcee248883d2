package com.jsrxjt.mobile.domain.rxmember.service.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.riskcontrol.types.RiskBusinessTypeEnum;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.rxmember.entity.RxMemberRiskEntity;
import com.jsrxjt.mobile.domain.rxmember.gateway.RxMemberGateway;
import com.jsrxjt.mobile.domain.rxmember.request.RxMemberRiskRequest;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberResponse;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberRiskResponse;
import com.jsrxjt.mobile.domain.rxmember.service.RxMemberRiskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * @Description: 会员风控领域服务
 * @Author: ywt
 * @Date: 2025-09-25 16:10
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RxMemberRiskServiceImpl implements RxMemberRiskService {
    private final RxMemberGateway rxMemberGateway;
    private final CustomerRepository customerRepository;

    @Override
    public Integer getUserRiskLevel(RxMemberRiskRequest request) {
        if (Objects.isNull(request.getToken()) || Objects.isNull(request.getBusinessId())) {
            log.error("获取用户的易盾风险失败-token或业务id为空");
            //出现异常默认用户没有命中风控
            return 1;
        }
        RxMemberRiskEntity entity = new RxMemberRiskEntity();
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        entity.setPhone(customerEntity.getPhone());
        entity.setUnionid(customerEntity.getUnionid());
        entity.setVipId(customerEntity.getVipId());
        entity.setBusinessType(request.getTypeEnum().getName());
        entity.setToken(request.getToken());
        entity.setBusinessId(request.getBusinessId());
        entity.setIp(request.getIP());
        if (request.getTypeEnum().getStatus() == RiskBusinessTypeEnum.REGISTER_TYPE.getStatus()
                || request.getTypeEnum().getStatus() == RiskBusinessTypeEnum.LOGIN_TYPE.getStatus()) {
            RxMemberRiskEntity.RegisterOrLogData registerOrLogData = new RxMemberRiskEntity.RegisterOrLogData();
            BeanUtils.copyProperties(request.getRegisterOrLogData(), registerOrLogData);
            entity.setRegisterOrLogData(registerOrLogData);
        } else if (request.getTypeEnum().getStatus() == RiskBusinessTypeEnum.BIND_CARD_TYPE.getStatus()) {
            RxMemberRiskEntity.BindCardData bindCardData = new RxMemberRiskEntity.BindCardData();
            BeanUtils.copyProperties(request.getBindCardData(), bindCardData);
            entity.setBindCardData(bindCardData);
        } else if (request.getTypeEnum().getStatus() == RiskBusinessTypeEnum.PLACEORDER_TYPE.getStatus()) {
            RxMemberRiskEntity.PlaceOrderData placeOrderData = new RxMemberRiskEntity.PlaceOrderData();
            BeanUtils.copyProperties(request.getPlaceOrderData(), placeOrderData);
            // 四舍五入转为整数（结果为 BigDecimal 类型）
            BigDecimal integerValue = request.getPlaceOrderData().getConsumption().setScale(0, RoundingMode.HALF_UP);
            placeOrderData.setConsumption(integerValue.intValue());
            entity.setPlaceOrderData(placeOrderData);
        }
        RxMemberResponse response = rxMemberGateway.getUserRiskLevel(entity);
        if (!response.isSuccess()) {
            //出现异常默认用户没有命中风控
            return 1;
        }
        RxMemberRiskResponse riskResponse = (RxMemberRiskResponse) response.getData();
        return riskResponse.getRiskLevel();
    }
}
