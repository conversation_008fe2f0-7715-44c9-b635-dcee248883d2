package com.jsrxjt.mobile.infra.airRecharge.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.airRecharge.persistent.po.BatchVipPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 空充信息mapper
 * @Author: ywt
 * @Date: 2025-08-26 15:34
 * @Version: 1.0
 */
@Mapper
public interface AirRechargeMapper extends CommonBaseMapper<BatchVipPO> {
    /**
     * 只返回黑金主卡和白金主卡的空充记录
     */
    List<BatchVipPO> getAirRechargeInfo(String mobile);
}
