package com.jsrxjt.mobile.domain.order.service.strategy.aftersale.impl;

import com.jsrxjt.mobile.domain.order.service.strategy.aftersale.AfterSaleProgressBuilder;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 售后进度详情构建工厂
 * <AUTHOR>
 * @since 2025-12-02
 */
@Component
public class AfterSaleProgressBuilderFactory {

    private final List<AfterSaleProgressBuilder> builders;

    public AfterSaleProgressBuilderFactory(List<AfterSaleProgressBuilder> builders) {
        this.builders = builders;
    }

    public AfterSaleProgressBuilder getBuilder(Integer status) {
        return builders.stream()
                .filter(builder -> builder.supports(status))
                .findFirst()
                .orElse(new AfterSaleEmptyProgressBuilder());
    }
}
