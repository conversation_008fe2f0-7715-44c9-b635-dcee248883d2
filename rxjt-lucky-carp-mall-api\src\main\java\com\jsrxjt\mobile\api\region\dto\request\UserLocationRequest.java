package com.jsrxjt.mobile.api.region.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "用户定位")
public class UserLocationRequest extends BaseParam {

    @NotNull(message = "是否开启定位不能为空")
    @Schema(description = "是否开启定位 0:否 1:是")
    private Integer enablePosition;

    @NotNull(message = "纬度不能为空")
    @Schema(description = "纬度")
    private String lat;

    @NotNull(message = "经度不能为空")
    @Schema(description = "经度")
    private String lng;
}
