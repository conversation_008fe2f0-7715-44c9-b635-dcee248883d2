package com.jsrxjt.mobile.domain.riskcontrol.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.riskcontrol.dto.response.ProductRiskControlResponse;
import com.jsrxjt.mobile.api.riskcontrol.types.RiskControlStrategyStatus;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.riskcontrol.entity.ProductRiskControlAccountEntity;
import com.jsrxjt.mobile.domain.riskcontrol.entity.SkuRiskControlEntity;
import com.jsrxjt.mobile.domain.riskcontrol.entity.SpuRiskFilterEntity;
import com.jsrxjt.mobile.domain.riskcontrol.repository.ProductRiskControlAccountRepository;
import com.jsrxjt.mobile.domain.riskcontrol.repository.ProductRiskControlRepository;
import com.jsrxjt.mobile.domain.riskcontrol.repository.ProductRiskControlSkuRepository;
import com.jsrxjt.mobile.domain.riskcontrol.service.ProductRiskControlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/6/25 15:13
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProductRiskControlServiceImpl implements ProductRiskControlService {

    private final ProductRiskControlAccountRepository productRiskControlAccountRepository;

    private final ProductRiskControlRepository productRiskControlRepository;

    private final CustomerRepository customerRepository;

    private final ProductRiskControlSkuRepository productRiskControlSkuRepository;

    @Override
    public ProductRiskControlResponse getProductRiskControl(ProductItemId productItemId, Long customerId,
            Integer customerRiskLevel) {
        if (log.isDebugEnabled()) {
            log.debug("开始获取产品风控策略, productItemId: {}, customerId: {}, customerRiskLevel: {}",
                    productItemId, customerId, customerRiskLevel);
        }

        ProductRiskControlResponse response = new ProductRiskControlResponse();
        response.setCustomerId(customerId);
        response.setHitStrategyStatus(RiskControlStrategyStatus.NOT_IN_STRATEGY.getStatus());

        // 直接查询所有风控策略（不区分策略类型，已按手续费费率降序排序）
        List<SkuRiskControlEntity> allRiskControls = productRiskControlRepository
                .listAllRiskControlByProduct(productItemId);

        if (log.isDebugEnabled()) {
            log.debug("查询到风控策略总数量: {}",
                    allRiskControls != null ? allRiskControls.size() : 0);
        }

        // 当前商品没有风控策略
        if (CollectionUtils.isEmpty(allRiskControls)) {
            if (log.isDebugEnabled()) {
                log.debug("当前商品没有风控策略，返回NOT_IN_STRATEGY");
            }
            return response;
        }

        response.setHitStrategyStatus(RiskControlStrategyStatus.NOT_HIT_STRATEGY.getStatus());

        if (log.isDebugEnabled()) {
            log.debug("风控策略已按手续费费率降序排序，策略数量: {}", allRiskControls.size());
            if (!allRiskControls.isEmpty()) {
                log.debug("最高手续费费率策略: strategyType={}, commissionFee={}",
                        allRiskControls.get(0).getStrategyType(), allRiskControls.get(0).getCommissionFee());
            }
        }
        CustomerEntity user = customerRepository.selectCustomerById(customerId);
        String accountName = user.getPhone();
        Long companyId = user.getCompanyId();

        if (log.isDebugEnabled()) {
            log.debug("开始匹配用户账户, accountName: {}, companyId: {}", accountName, companyId);
        }

        // 遍历所有风控策略（已按手续费费率降序排序），找到第一个匹配的用户账户
        for (SkuRiskControlEntity riskControl : allRiskControls) {
            if (log.isDebugEnabled()) {
                log.debug("检查风控策略, riskId: {}, strategyType: {}, commissionFee: {}",
                        riskControl.getId(), riskControl.getStrategyType(), riskControl.getCommissionFee());
            }

            ProductRiskControlAccountEntity queryAccount = new ProductRiskControlAccountEntity();
            queryAccount.setRiskId(riskControl.getId());
            queryAccount.setAccountType(0);
            queryAccount.setAccountName(accountName);
            List<ProductRiskControlAccountEntity> accounts = productRiskControlAccountRepository
                    .listRiskControlAccountsByCondition(queryAccount);

            if (accounts == null && companyId != null) {
                // 手机账户类型未匹配上，继续查公司账户是否匹配
                if (log.isDebugEnabled()) {
                    log.debug("手机账户未匹配，尝试匹配公司账户, companyId: {}", companyId);
                }

                queryAccount.setAccountType(1);
                queryAccount.setCompanyId(companyId);
                accounts = productRiskControlAccountRepository.listRiskControlAccountsByCondition(queryAccount);
            }

            // 手机账户和企业账户都未匹配上
            if (accounts == null) {
                if (log.isDebugEnabled()) {
                    log.debug("风控策略未匹配到用户账户, riskId: {}", riskControl.getId());
                }
                continue;
            }

            ProductRiskControlAccountEntity riskControlAccount = accounts.get(0);
            if (riskControlAccount != null) {
                // 匹配到了用户，使用当前策略（已是加点最高的）
                if (log.isDebugEnabled()) {
                    log.debug("匹配到风控策略, riskId: {}, strategyType: {}, commissionFee: {}, accountType: {}",
                            riskControl.getId(), riskControl.getStrategyType(), riskControl.getCommissionFee(),
                            riskControlAccount.getAccountType());
                }

                BeanUtil.copyProperties(riskControl, response);
                response.setPayType(riskControlAccount.getPayType());
                response.setRiskAccountId(riskControlAccount.getId());
                response.setHitStrategyStatus(RiskControlStrategyStatus.HIT_STRATEGY.getStatus());

                log.info("风控策略命中成功, customerId: {}, riskId: {}, strategyType: {}, commissionFee: {}, " +
                                "accountType: {}, payType: {}",
                        customerId, riskControl.getId(), riskControl.getStrategyType(),
                        riskControl.getCommissionFee(), riskControlAccount.getAccountType(), response.getPayType());
                break;
            }
        }
        if (!isRiskLevelLimit(customerRiskLevel)) {
            // 非易盾风险账户，不用判断关联易盾的策略，直接返回
            if (log.isDebugEnabled()) {
                log.debug("非易盾风险账户，直接返回普通策略结果, customerRiskLevel: {}", customerRiskLevel);
            }
            return response;
        }

        if (log.isDebugEnabled()) {
            log.debug("处理易盾风险账户策略, customerRiskLevel: {}", customerRiskLevel);
        }

        // 处理易盾的策略
        ProductRiskControlResponse shieldResponse = new ProductRiskControlResponse();
        shieldResponse.setHitStrategyStatus(RiskControlStrategyStatus.NOT_HIT_STRATEGY.getStatus());
        shieldResponse.setCustomerId(customerId);

        // 遍历所有风控策略（已按手续费费率降序排序），找到第一个匹配的易盾账户
        for (SkuRiskControlEntity riskControl : allRiskControls) {
            if (log.isDebugEnabled()) {
                log.debug("检查易盾账户策略, riskId: {}, strategyType: {}, commissionFee: {}",
                        riskControl.getId(), riskControl.getStrategyType(), riskControl.getCommissionFee());
            }

            // 筛易盾账户类型
            ProductRiskControlAccountEntity queryAccount = new ProductRiskControlAccountEntity();
            queryAccount.setRiskId(riskControl.getId());
            queryAccount.setAccountType(2);
            List<ProductRiskControlAccountEntity> shieldAccounts = productRiskControlAccountRepository
                    .listRiskControlAccountsByCondition(queryAccount);

            if (!CollectionUtils.isEmpty(shieldAccounts)) {
                ProductRiskControlAccountEntity shieldAccount = shieldAccounts.get(0);

                if (log.isDebugEnabled()) {
                    log.debug("匹配到易盾账户策略, riskId: {}, strategyType: {}, commissionFee: {},payType: {}",
                            riskControl.getId(), riskControl.getStrategyType(), riskControl.getCommissionFee(),
                           shieldAccount.getPayType() );
                }

                BeanUtil.copyProperties(riskControl, shieldResponse);
                shieldResponse.setPayType(shieldAccount.getPayType());
                shieldResponse.setRiskAccountId(shieldAccount.getId());
                shieldResponse.setHitStrategyStatus(RiskControlStrategyStatus.HIT_STRATEGY.getStatus());

                log.info("易盾风控策略命中成功, customerId: {}, riskId: {}, strategyType: {}, commissionFee: {}",
                        customerId, riskControl.getId(), riskControl.getStrategyType(), riskControl.getCommissionFee());
                break;
            }
        }

        // 没有易盾类型的账户策略 则直接返回普通策略
        if (!shieldResponse.isHitStrategy()) {
            if (log.isDebugEnabled()) {
                log.debug("未匹配到易盾账户策略，返回普通策略结果");
            }
            return response;
        }

        // 普通策略没命中，则返回易盾策略
        if (!response.isHitStrategy()) {
            if (log.isDebugEnabled()) {
                log.debug("普通策略未命中，返回易盾策略结果");
            }
            return shieldResponse;
        }

        // 两种策略都命中，比较手续费费率，返回加点更高的策略
        if (shieldResponse.getCommissionFee().compareTo(response.getCommissionFee()) > 0) {
            if (log.isDebugEnabled()) {
                log.debug("易盾策略手续费更高，返回易盾策略, 易盾: {}, 普通: {}",
                        shieldResponse.getCommissionFee(), response.getCommissionFee());
            }

            log.info("选择易盾策略（手续费更高）, customerId: {}, 易盾手续费: {}, 普通手续费: {}",
                    customerId, shieldResponse.getCommissionFee(), response.getCommissionFee());
            return shieldResponse;
        }

        if (log.isDebugEnabled()) {
            log.debug("普通策略手续费更高或相等，返回普通策略, 普通: {}, 易盾: {}",
                    response.getCommissionFee(), shieldResponse.getCommissionFee());
        }

        log.info("选择普通策略（手续费更高或相等）, customerId: {}, 普通手续费: {}, 易盾手续费: {}",
                customerId, response.getCommissionFee(), shieldResponse.getCommissionFee());
        return response;
    }

    @Override
    public List<SpuRiskFilterEntity> getRiskDisableProducts(Long customerId) {
        if (Objects.isNull(customerId)) {
            log.error("用户id为空错误");
            return Collections.emptyList();
        }
        CustomerEntity user = customerRepository.selectCustomerById(customerId);
        String accountName = user.getPhone();
        Long companyId = user.getCompanyId();
        if (StringUtils.isEmpty(accountName) && Objects.isNull(companyId)) {
            log.error("用户：" + customerId + "不存在手机号和企业id");
            return Collections.emptyList();
        }
        return productRiskControlSkuRepository.getRiskDisableProducts(accountName, companyId);
    }

    @Override
    public boolean isRiskDisableProduct(CustomerEntity customer, Long spuId, Integer productType) {
        String accountName = customer.getPhone();
        Long companyId = customer.getCompanyId();
        if (StringUtils.isEmpty(accountName) && Objects.isNull(companyId)) {
            log.error("用户：" + customer.getId() + "不存在手机号和企业id");
            return false;
        }
        SpuRiskFilterEntity spuRiskFilterEntity = productRiskControlSkuRepository.getRiskDisableProduct(accountName,
                companyId, spuId, productType);
        if (Objects.nonNull(spuRiskFilterEntity)) {
            return true;
        }
        return false;
    }

    /**
     * 等级：0-正常 1-低风险 2-中风险 3-高风险 4-可信源 5-黑名单 3和5是受限制的
     * 
     * @param customerRiskLevel
     * @return
     */
    private boolean isRiskLevelLimit(Integer customerRiskLevel) {
        return customerRiskLevel == 3 || customerRiskLevel == 5;
    }
}
