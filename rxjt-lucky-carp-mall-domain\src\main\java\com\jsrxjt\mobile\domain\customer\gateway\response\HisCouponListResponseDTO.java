package com.jsrxjt.mobile.domain.customer.gateway.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 福鲤圈历史券包响应结果
 * <AUTHOR>
 * @Date 2025/9/10
 */
@Data
public class HisCouponListResponseDTO {

    /**
     * 券名称
     */
    @JSONField(name = "fullname")
    private String fullName;

    /**
     * 券图片
     */
    @JSONField(name = "pic")
    private String pic;

    /**
     * 券核销页链接
     */
    @JSONField(name = "coupon_url")
    private String couponUrl;

    /**
     * 券类型 1电子卡 2提货券 3套餐
     */
    @JSONField(name = "label")
    private Integer label;

    /**
     * 兑换时间
     */
    @JSONField(name = "create_time")
    private Long createTime;

}
