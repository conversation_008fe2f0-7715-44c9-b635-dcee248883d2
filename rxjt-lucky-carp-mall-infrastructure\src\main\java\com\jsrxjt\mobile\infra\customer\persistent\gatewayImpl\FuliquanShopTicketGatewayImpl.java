package com.jsrxjt.mobile.infra.customer.persistent.gatewayImpl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.customer.gateway.FuliquanShopTicketGateway;
import com.jsrxjt.mobile.domain.customer.gateway.request.HisCouponQueryRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.request.SendTicketRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.request.ShopTicketListRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.HisCouponListResponseDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.SendTicketResponseDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.ShopTicketListResponseDTO;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.pickplatform.util.RxMemberSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Description: 福鲤圈门店优惠券接口
 * @Author: zy
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FuliquanShopTicketGatewayImpl implements FuliquanShopTicketGateway {

    protected final HttpClientGateway httpClientGateway;

    @Value("${flq.shop.ticket.key}")
    private String key;

    @Value("${flq.shop.ticket.url:https://minirx.tongkask.com}")
    private String url;

    @Value("${flq.shop.ticket.sendCoupon:/FuliquanV2/Vip/sendCoupon}")
    private String sendCoupon;

    @Value("${flq.shop.ticket.getCouponList:/FuliquanV2/Vip/getCouponList}")
    private String getCouponList;

    @Value("${flq.shop.order.getMyCoupon:/FuliquanV2/VoucherCenter/getMyCoupon}")
    private String getMyCoupon;

    @Value("${flq.shop.order.orderList:/FuliquanV2/Vip/orderList}")
    private String orderList;

    private int connectTimeout = 3000;

    private int readTimeout = 5000;

    private static final Integer SUCCESS_CODE = 0;

    private static final Integer SEND_LIMIT = 444;

    /**
     * 发券(生日券)
     *
     * @param requestDTO
     * @return
     */
    @Override
    public SendTicketResponseDTO sendCoupon(SendTicketRequestDTO requestDTO) {
        SendTicketResponseDTO dataResponse = null;
        Map<String, Object> requestMap = new HashMap<>();

        requestMap.put("nonce", requestDTO.getNonce());
        requestMap.put("timestamp", requestDTO.getTimestamp());
        requestMap.put("vipid", requestDTO.getVipId());

        String requestSign = RxMemberSignUtil.getSign(requestMap, key);
        requestMap.put("sign", requestSign);
        try {
            String resultStr = httpClientGateway.doPost(url + sendCoupon, requestMap, connectTimeout, readTimeout);
            JSONObject result = JSON.parseObject(resultStr);
            if (Objects.equals(result.getInteger("code"), SUCCESS_CODE) && Objects.nonNull(result.get("data"))) {
                dataResponse = JSON.parseObject(JSON.toJSONString(result.get("data")), SendTicketResponseDTO.class);
            }
            if (Objects.equals(result.getInteger("code"), SEND_LIMIT)){
                throw new BizException(Status.ONLINE_PAY_PRE_INVOKE_ERROR);
            }
        }catch (BizException e) {
            log.error("生日券领取失败，错误码：{} 错误信息：{}", e.getCode(),e.getMsg());
            throw e;
        } catch (Exception e) {
            log.error("福鲤圈领取生日券接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponse;
    }

    /**
     * 获取门店优惠券列表
     *
     * @param requestDTO
     * @return
     */
    @Override
    public PageDTO<ShopTicketListResponseDTO> getShopTicketList(ShopTicketListRequestDTO requestDTO) {
        List<ShopTicketListResponseDTO> dataResponseList = null;
        Long total = 0L;
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("vipid", requestDTO.getVipid());
        requestMap.put("page", requestDTO.getPage());
        requestMap.put("size", requestDTO.getSize());
        requestMap.put("timestamp", requestDTO.getTimestamp());
        requestMap.put("nonce", requestDTO.getNonce());
        String requestSign = RxMemberSignUtil.getSign(requestMap, key);
        requestMap.put("sign", requestSign);
        try {
            String resultStr = httpClientGateway.doPost(url + getCouponList, requestMap, connectTimeout, readTimeout);
            JSONObject result = JSON.parseObject(resultStr);
            if (Objects.equals(result.getInteger("code"), SUCCESS_CODE) && Objects.nonNull(result.get("data"))) {
                String jsonString = JSON.toJSONString(result.get("data"));
                JSONObject jsonObject = JSONObject.parseObject(jsonString);
                total = Long.valueOf(jsonObject.get("total").toString());
                Object object = jsonObject.get("list");
                //转成list
                dataResponseList = JSONArray.parseArray(object.toString(), ShopTicketListResponseDTO.class);

            }
        } catch (Exception e) {
            log.error("门店券接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return PageDTO.build(dataResponseList, total, requestDTO.getSize(), requestDTO.getPage());
    }

    @Override
    public List<HisCouponListResponseDTO> getHisCouponList(HisCouponQueryRequestDTO requestDTO) {
        List<HisCouponListResponseDTO> dataResponseList = new ArrayList<>();
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("vipid", requestDTO.getVipId());
        if (requestDTO.getLabel() != null){
            requestMap.put("label", requestDTO.getLabel());
        }
        requestMap.put("page", requestDTO.getPage());
        requestMap.put("timestamp", requestDTO.getTimestamp());
        requestMap.put("nonce", requestDTO.getNonce());
        String requestSign = RxMemberSignUtil.getSign(requestMap, key);
        requestMap.put("sign", requestSign);
        log.info("获取历史券包接口请求参数：{}", requestMap);
        try {
            String resultStr = httpClientGateway.doPost(url + getMyCoupon, requestMap, connectTimeout, readTimeout);
            JSONObject result = JSON.parseObject(resultStr);
            if (Objects.equals(result.getInteger("code"), SUCCESS_CODE) && Objects.nonNull(result.get("data"))) {
                String jsonString = JSON.toJSONString(result.get("data"));
                JSONObject jsonObject = JSONObject.parseObject(jsonString);
                Object object = jsonObject.get("list");
                dataResponseList = JSONArray.parseArray(object.toString(), HisCouponListResponseDTO.class);
            }
        } catch (Exception e) {
            log.error("获取历史券包接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponseList;
    }

    @Override
    public String getHisOrderListUrl(HisCouponQueryRequestDTO requestDTO) {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("vipid", requestDTO.getVipId());
        requestMap.put("timestamp", requestDTO.getTimestamp());
        requestMap.put("nonce", requestDTO.getNonce());
        String requestSign = RxMemberSignUtil.getSign(requestMap, key);
        requestMap.put("sign", requestSign);
        log.info("获取历史订单Url接口请求参数：{}", requestMap);
        try {
            String resultStr = httpClientGateway.doPost(url + orderList, requestMap, connectTimeout, readTimeout);
            JSONObject result = JSON.parseObject(resultStr);
            if (Objects.equals(result.getInteger("code"), SUCCESS_CODE) && Objects.nonNull(result.get("data"))) {
                return result.getString("data");
            }
        } catch (Exception e) {
            log.error("获取历史订单Url接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

}
