package com.jsrxjt.mobile.domain.product.repository;

import com.jsrxjt.mobile.api.product.types.ProductExplainTypeEnum;
import com.jsrxjt.mobile.domain.product.entity.ProductBrandEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductExplainEntity;

import java.util.List;

/**
 * 产品说明Repository接口
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface ProductExplainRepository {

    /**
     * 根据SPUID查询说明信息
     * 
     * @param spuId spuId
     * @return 品牌实体
     */
    List<ProductExplainEntity> findBySpuId(Long spuId, Byte productType);

    /**
     * 根据SPUID、说明类型查询说明信息
     *
     * @param spuId spuId
     * @param explainType 说明类型
     * @return 说明信息
     */
    List<ProductExplainEntity> findBySpuIdAndExplainType(Long spuId, ProductExplainTypeEnum explainType);

}