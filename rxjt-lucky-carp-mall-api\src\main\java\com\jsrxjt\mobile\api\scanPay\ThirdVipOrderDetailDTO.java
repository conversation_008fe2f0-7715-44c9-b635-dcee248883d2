package com.jsrxjt.mobile.api.scanPay;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Schema(description = "收银台订单详情信息")
@Data
public class ThirdVipOrderDetailDTO {

    @JSONField(name = "bj_total")
    private Integer bj_total;//白金卡支付总额
    @JSONField(name = "bjmain_total")
    private Integer bjmain_total;//白金主卡支付总额
    @JSONField(name = "gift_total")
    private Integer gift_total;//凭证主卡支付总额
    @JSONField(name = "hj_total")
    private Integer hj_total;//黑金卡支付总额
    @JSONField(name = "is_card")
    private Integer is_card;//是否有卡类支付
    @JSONField(name = "is_code")
    private Integer is_code;//是否需要支付密码？
    @JSONField(name = "is_pass")
    private Integer is_pass;//是否有付款密码
    @JSONField(name = "is_show")
    private Integer is_show;// 是否展示在列表
    @JSONField(name = "jc_total")
    private Integer jc_total;//就餐卡支付总额
    @JSONField(name = "main_total")
    private Integer main_total;//主卡支付总额
    @JSONField(name = "pay_channel")
    private String payChannel;//支付类型
    @JSONField(name = "pay_version")
    private String pay_version;//支付版本
    @JSONField(name = "payorder")
    private String payorder;//付款顺序
    @JSONField(name = "price_total")
    private Integer price_total;//支付总金额
    @JSONField(name = "shop_id")
    private Integer shop_id;
    @JSONField(name = "shop_name")
    private String shop_name;
    @JSONField(name = "shop_user_id")
    private Integer shop_user_id;
    @JSONField(name = "sl_total")
    private Integer sl_total;//商联卡支付总额
    @JSONField(name = "th_total")
    private Integer th_total;//提货凭证支付总额
    @JSONField(name = "thq_total")
    private Integer thq_total;//提货券支付总额
    @JSONField(name = "trade_id")
    private Integer trade_id;
    @JSONField(name = "trade_no")
    private String trade_no;
    @JSONField(name = "wx_total")
    private Integer wx_total;//微信卡支付总额
    //卡支付顺序信息
    ThirdVipOrderCardDTO list;
}
