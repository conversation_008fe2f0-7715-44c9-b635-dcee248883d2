package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "用户登录请求参数")
public class CustomerLoginRequest extends BaseParam {

    @Schema(description = "登录类型 1小程序登录 2app登录 3手机号验证码登录")
    @NotNull(message = "登录类型不能为空")
    private Integer loginType;

    @Schema(description = "微信code(loginType=1或loginType=2时必填)")
    private String wxCode;

    @Schema(description = "手机号码(loginType=3时必填)")
    private String phoneCode;

    @Schema(description = "短信验证码(loginType=3时必填)")
    private String verificationCode;

    @Schema(description = "用户登录ip")
    private String loginIp;

    @Schema(description = "易盾的业务Id")
    private String ydBusinessId;
    @Schema(description = "易盾的token")
    private String ydToken;
}
