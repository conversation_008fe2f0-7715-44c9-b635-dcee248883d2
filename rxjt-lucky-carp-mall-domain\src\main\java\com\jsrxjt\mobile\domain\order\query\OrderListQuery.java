package com.jsrxjt.mobile.domain.order.query;

import lombok.Builder;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 订单列表查询条件
 * 
 * <AUTHOR>
 * @since 2025/7/17
 */
@Data
@Builder
public class OrderListQuery {
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 订单状态
     */
    private Integer orderStatus;
    
    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;
    
    /**
     * 商品分类ID
     */
    private Long firstCategoryId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 是否显示
     */
    private Boolean isShow;
}
