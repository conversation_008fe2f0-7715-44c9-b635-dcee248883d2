package com.jsrxjt.mobile.api.coupon.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 卡券sku响应
 * @Author: ywt
 * @Date: 2025-06-17 17:39
 * @Version: 1.0
 */
@Data
public class CouponSkusResponseDTO {
    @Schema(description = "规格显示样式：1直接显示面值 2显示规格名+面值")
    private Integer specType;
    @Schema(description = "规格和sku信息")
    private List<SkusSpecResponseDTO> skusSpecList;

    @Data
    public static class SkusSpecResponseDTO {
        @Schema(description = "规格名")
        private String specValueName;
        @Schema(description = "是否选中， 0--未选中 1--选中")
        private Integer isSelected;
        @Schema(description = "sku列表")
        private List<CouponSkuInfoResponseDTO> skuList;
    }
}
