package com.jsrxjt.mobile.api.physicalCardMall.dto.response;

import lombok.Builder;
import lombok.Data;

/**
 * @Description: 实体卡创建订单响应
 * @Author: ywt
 * @Date: 2025-10-23 09:59
 * @Version: 1.0
 */
@Data
@Builder
public class PhysicalCardCreateOrderResponseDTO extends PhysicalCardBaseResponse{
    /**
     * 福鲤圈订单号
     */
    private String flqOrderNo;

    /**
     * 收银台地址
     */
    private String cashierUrl;
}
