package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 饿了么创建订单通知参数
 * <AUTHOR>
 * @since 2025/12/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ELeMeOrderCreateNotifyDTO extends DistributionNotifyCommonDTO {

    @Schema(description = "商品的标题 /交易标题 /订单标题 /订单关键字等")
    private String subject;

    @Schema(description = "商品描述")
    private String body;

    @Schema(description = "支付网关的订单号")
    private String transactionId;

    @Schema(description = "支付金额，单位分")
    private String payAmount;

    @Schema(description = "订单失效时间 格式为yyyyMMddHHmmss")
    private String timeExpire;

    @Schema(description = "支付成功后跳转页面")
    private String redirectUrl;

    @Schema(description = "放弃支付后跳转页面")
    private String backUrl;

    @Schema(description = "支付异步通知回调地址")
    private String notifyUrl;

    @Schema(description = "用户标识")
    private String userCode;

}
