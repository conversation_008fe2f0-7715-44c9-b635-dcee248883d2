package com.jsrxjt.mobile.api.scanPay.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 普通线下支付支付请求参数对象
 * 付款时使用
 */
@Data
public class CompayRequestDTO {
    private BigDecimal payPrice; //付款金额
    private String payCode;//卡号
    private Long shopId; //门店id
    private Long shopUserId;//用户id
    private Integer source;//
    private Integer payType;//支付类型
    private String deviceId;//设备id
    private String syOrderId;//订单id

}