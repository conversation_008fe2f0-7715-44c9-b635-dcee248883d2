package com.jsrxjt.mobile.api.promotion.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PromotionSkuInfo {

    @Schema(description = "活动商品关联id")
    private Long id;

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "卡券折扣")
    private Integer discount;

    @Schema(description = "卡券活动价")
    private BigDecimal activityPrice;

    @Schema(description = "限购数量")
    private Integer quotaQty;

    @Schema(description = "促销标签文案")
    private String promotionLabelCopy;

    @Schema(description = "促销活动id")
    private Long activityId;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动类型：1打折 2直降")
    private Integer activityType;

    @Schema(description = "活动开始时间")
    private Date startTime;

    @Schema(description = "活动结束时间")
    private Date endTime;

    @Schema(description = "活动状态 1 未开始 2 进行中 3已关闭 4已结束")
    private Integer activityStatus;

    @Schema(description = "卡券类型：1普通卡券 2组合套餐卡券")
    private Integer activityCouponType;

    @Schema(description = "活动标签文案")
    private String labelCopy;

    @Schema(description = "活动标签文案背景图")
    private String labelCopyBackgroundImg;

    @Schema(description = "活动角标图片地址")
    private String subscriptImgUrl;

    @Schema(description = "活动提前透出时间")
    private Date advanceReleaseTime;

}
