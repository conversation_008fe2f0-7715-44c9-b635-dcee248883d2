package com.jsrxjt.mobile.biz.order.coupon;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageDelRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPayDetailPageRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackageDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackageListResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackagePageDTO;
import com.jsrxjt.mobile.api.order.dto.response.SelCouponPayDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.request.HisCouponQueryRequest;
import com.jsrxjt.mobile.api.order.dto.response.HisCouponInfoResponse;

import java.util.List;

/**
 * 卡包服务
 * <AUTHOR>
 * @date 2025/07/22
 */
public interface CouponPackageCaseService {
    /**
     * 获取卡包列表
     * @param customerId
     * @param request
     * @return {@link List}<{@link CouponPackageListResponseDTO}>
     */
    CouponPackagePageDTO<CouponPackageListResponseDTO> getCouponPackageList(Long customerId, CouponPackageListRequestDTO request);

    /**
     *  卡包详情
     * @param couponPackageId
     * @return
     */
    CouponPackageDetailResponseDTO getCouponPackageDetail(Long couponPackageId);

    /**
     * 消费明细
     */
    PageDTO<SelCouponPayDetailResponseDTO> consumeList(CouponPayDetailPageRequestDTO request);


    /**
     * 放入回收站
     */
    void putInRecycleBin(CouponPackageDelRequestDTO request);

   /**
     * 回收站恢复
     */
    void restoreRecycleBin(CouponPackageDelRequestDTO request);

    /**
     * 删除卡包
     */
    void deleteCouponPackage(CouponPackageDelRequestDTO request);

    /**
     * 订单页卡包id
     * @param orderNo
     * @return
     */
    Long getCouponPackageIdForOrder(String orderNo);

    /**
     * 分页获取全球购优惠券列表
     * @param request 请求参数
     * @return 当前页返回结果
     */
    List<HisCouponInfoResponse> getHisCouponList(HisCouponQueryRequest request);
}
