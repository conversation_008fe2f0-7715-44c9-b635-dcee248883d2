package com.jsrxjt.mobile.biz.payment.strategy.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.biz.homeScanPay.service.PickScanCaseService;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.domain.app.repository.AppCouponGoodsRepository;
import com.jsrxjt.mobile.domain.bianlifeng.gateway.BianLiFengGateway;
import com.jsrxjt.mobile.domain.customer.service.RechargeAccountHistoryService;
import com.jsrxjt.mobile.domain.order.entity.*;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleLogRepository;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.AfterSaleLogService;
import com.jsrxjt.mobile.domain.order.service.AfterSaleService;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformChannelGateway;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayResultRequest;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayResultResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


/**
 * 提货分销平台支付成功策略
 * <AUTHOR>
 * @date 2025/11/10
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PickPlatformPaymentSuccessStrategy implements PaymentSuccessStrategy {

    private final PickPlatformChannelGateway pickPlatformChannelGateway;

    private final OrderRepository orderRepository;

    private static final DateTimeFormatter DEFAULT_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public boolean supports(Integer flatProductType) {
        // 仅支持 flatProductType == 304
        return flatProductType != null && flatProductType == 304;
    }

    @Override
    public void handle(OrderInfoEntity order) {
        log.info("开始处理提货分销平台订单支付成功，订单号：{}，扁平化产品类型：{}",
                order.getOrderNo(), order.getFlatProductType());
        String thirdId = order.getThirdId();
        String externalOrderNo = order.getExternalOrderNo();
        String tradeNo = order.getDistTradeNo();
        OrderItemEntity item = order.getOrderItems().get(0);
        String extraInfo = item.getExtraInfo();
        String type = JSONObject.parseObject(extraInfo).getString("type");
        PickPlatformPayResultRequest request = new PickPlatformPayResultRequest();
        request.setThirdId(thirdId);
        request.setTradeNo(tradeNo);
        request.setOrderNo(externalOrderNo);
        request.setType(type);
        request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
        request.setNonce(IdUtil.simpleUUID());
        request.setTradeTime(order.getPaymentTime().format(DEFAULT_FORMATTER));
        PickPlatformPayResultResponse pickPlatformPayResultResponse = pickPlatformChannelGateway.getPaymentOrderResult(request);
        //更改订单状态为已完成
        if (pickPlatformPayResultResponse != null){
            this.updateOrderToDeliverEd(order);
        }
        log.info("提货分销平台订单支付成功处理完成，订单号：{}，外部订单号：{}",
                order.getOrderNo(), externalOrderNo);
    }

    /**
     * 更新订单状态
     */
    private void updateOrderToDeliverEd(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        updateOrder.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);

        log.info("提货券分销中台订单发货状态更新成功，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERED.getDescription());
    }
}
