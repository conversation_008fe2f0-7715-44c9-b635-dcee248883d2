package com.jsrxjt.mobile.api.distribution.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/7/29 14:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DistributionOrderRefundNotifyDTO extends DistributionNotifyCommonDTO {

    /**
     * 分销中心退款订单号，标识一次退款请求
     */
    private String refundOrderNo;

    /**
     * 分销中心业务订单号，标识退款所属的业务订单
     */
    private String orderNo;

    /**
     * 分销中心业务交易号，标识退款所属的交易号
     */
    private String tradeNo;

    /**
     * 本次退款金额，单位元，2位小数，订单可多次进行退款
     */
    private String refundAmount;

    /**
     * 用户发起退款的UTC时间戳，10位
     */
    private String tradeTime;

    /**
     * 物美-支付流水号
     */
    private String paymentNo;
}
