package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "用户设置支付顺序请求参数")
public class CustomerSetPaySortRequest extends BaseParam {

    @Schema(description = "用户Id")
    @NotNull(message = "用户Id不能为空")
    private Long customerId;

    @Schema(description = "是否使用默认扣款顺序 1是 0否")
    @NotNull(message = "是否使用默认扣款顺序不能为空")
    private Boolean useDefault;

    @Schema(description = "支付卡顺序列表")
    private List<String> paySortList;

}
