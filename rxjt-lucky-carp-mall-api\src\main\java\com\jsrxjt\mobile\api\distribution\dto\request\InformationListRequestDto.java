package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-06-12 14:58
 * @Version: 1.0
 */
@Data
public class InformationListRequestDto {
    @Schema(description = "资讯分类id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "资讯分类id为空错误")
    private Integer catId;

    @Schema(description = "三级地址id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;

    @Schema(description = "每页显示条数")
    @Min(value = 1,message = "每页最少1条数据")
    @Max(value = 20,message = "每页最多20条数据")
    private Integer pageSize = 10;

    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "页码为空错误")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;
}
