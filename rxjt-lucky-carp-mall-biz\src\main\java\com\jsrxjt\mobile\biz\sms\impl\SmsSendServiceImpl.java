package com.jsrxjt.mobile.biz.sms.impl;

import com.jsrxjt.mobile.biz.sms.SmsSendService;
import com.jsrxjt.mobile.domain.gateway.sms.SmsGateway;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 *  SmsSendServiceImpl
 *  发送短信服务实现类
 * <AUTHOR>
 * 2023/3/28 14:26
 * 
 **/
@Service
@RequiredArgsConstructor
public class SmsSendServiceImpl implements SmsSendService {

    private final SmsGateway smsGateway;

    @Override
    public boolean sendVerifyCode(String phoneNumber, String code) {
        Map<String,String> templateParamMap = new HashMap<>();
        templateParamMap.put("code",code);
        return smsGateway.sendSms(phoneNumber,templateParamMap);
    }

}
