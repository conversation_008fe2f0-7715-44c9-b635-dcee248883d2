package com.jsrxjt.common.core.util;

import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.security.MessageDigest;
import java.util.Base64;

@Slf4j
public class SimpleUniqueMacIdGenerator {
    /**
     * 获取机器标识（IP + MAC）
     */
    public static String getMachineIdentifier() {
        try {
            // 获取本地IP
            String ip = InetAddress.getLocalHost().getHostAddress();
            log.info("本机ip:{}", ip);

            // 获取MAC地址
            NetworkInterface network = NetworkInterface.getByInetAddress(InetAddress.getLocalHost());
            byte[] mac = network.getHardwareAddress();

            StringBuilder macAddress = new StringBuilder();
            if (mac != null) {
                for (int i = 0; i < mac.length; i++) {
                    macAddress.append(String.format("%02X", mac[i]));
                }
            }
            log.info("本机mac:{}", macAddress.toString());
            return ip + ":" + macAddress.toString();

        } catch (Exception e) {
            e.printStackTrace();
            return "unknown-machine";
        }
    }

    /**
     * 生成SHA-256哈希
     */
    public static String generateSHA256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes());

            // 使用Base64编码，更短且可读性更好
            return Base64.getEncoder().encodeToString(hash)
                    .replace("/", "_")
                    .replace("+", "-")
                    .replace("=", "")
                    .substring(0, 16); // 取前16个字符

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成唯一机器码
     */
    public static String generateMachineCode() {
        String machineId = getMachineIdentifier();
        return generateSHA256(machineId);
    }

    public static void main(String[] args) {
        String machineCode = generateMachineCode();
        System.out.println("机器标识: " + getMachineIdentifier());
        System.out.println("机器码: " + machineCode);
    }
}
