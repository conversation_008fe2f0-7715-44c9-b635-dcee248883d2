package com.jsrxjt.mobile.biz.order;

import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;

/**
 * 自动售后退款业务服务接口
 * <AUTHOR>
 * @since 2025/9/26
 */
public interface AutoRefundCaseService {
    /**
     * 自动售后退款
     * @param request
     * @return
     */
    AfterSaleEntity autoRefund(AutoRefundRequestDTO request);

    /**
     * 仅构建退款成功售后单
     * @param request
     * @return {@link AfterSaleEntity}
     */
    AfterSaleEntity onlyBuildSuccessAfterSale(AutoRefundRequestDTO request);
}
