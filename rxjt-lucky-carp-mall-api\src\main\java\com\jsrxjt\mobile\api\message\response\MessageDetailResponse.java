package com.jsrxjt.mobile.api.message.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="MessageCountResponse", description = "消息")
public class MessageDetailResponse {
    private Long messageId;
    @Schema(description = "标题")
    private String title;
    @Schema(description = "副标题")
    private String subTitle;
    @Schema(description = "分类id")
    private Integer catId;

    @Schema(description = "分类名称")
    private String catName;

    @Schema(description = "缩略图")
    private String thumbUrl;

    @Schema(description = "资讯类型(1:图文详情 2:单链接)")
    private Byte type;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "消息时间")
    private String msgTime;

    @Schema(description = "是否已读 0未读 1已读")
    private Integer isRead = 0;

}
