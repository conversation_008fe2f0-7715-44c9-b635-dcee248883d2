package com.jsrxjt.mobile.infra.ticket.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.ticket.entity.TicketRegionEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketRegionRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketRegionMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketRegionPO;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description: 优惠券与区域关联的服务
 * @Author: ywt
 * @Date: 2025-08-18 13:56
 * @Version: 1.0
 */
@Repository
@RequiredArgsConstructor
public class TicketRegionRepositoryImpl implements TicketRegionRepository {
    private final TicketRegionMapper ticketRegionMapper;

    @Override
    @Cacheable(cacheNames = "ticket:region", key = "#ticketId", unless = "#result == null")
    public List<TicketRegionEntity> getRegionById(Long ticketId) {
        List<TicketRegionPO> list = ticketRegionMapper.selectList(new LambdaQueryWrapper<TicketRegionPO>()
                .eq(TicketRegionPO::getTicketId, ticketId));
        return BeanUtil.copyToList(list, TicketRegionEntity.class);
    }
}
