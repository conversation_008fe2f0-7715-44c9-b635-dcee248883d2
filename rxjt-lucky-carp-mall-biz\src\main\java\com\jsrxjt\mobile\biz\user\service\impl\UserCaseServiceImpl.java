package com.jsrxjt.mobile.biz.user.service.impl;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.user.dto.request.UserRequestDTO;
import com.jsrxjt.mobile.biz.user.service.UserCaseService;
import com.jsrxjt.mobile.domain.user.entity.UserEntity;
import com.jsrxjt.mobile.domain.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/10
 **/
@Service
@RequiredArgsConstructor
public class UserCaseServiceImpl implements UserCaseService {
    private final UserRepository userRepository;

    @Override
    public UserEntity getUser(Long userId) {
        return userRepository.getUserById(userId);
    }

    @Override
    public List<UserEntity> listUser(UserRequestDTO userRequestDTO) {
        return userRepository.listUser(userRequestDTO);
    }

    @Override
    public PageDTO<UserEntity> pageUser(UserRequestDTO userRequestDTO) {
        return userRepository.pageUser(userRequestDTO);
    }


}
