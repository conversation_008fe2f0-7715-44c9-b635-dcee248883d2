<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jsrxjt</groupId>
        <artifactId>rxjt-lucky-carp-mall</artifactId>
        <version>1.0.0</version>
    </parent>
    <packaging>jar</packaging>
    <artifactId>rxjt-lucky-carp-mall-api</artifactId>
    <name>rxjt-lucky-carp-mall-mobile-api</name>
    <description>rxjt-lucky-carp-mall-mobile-api</description>
    <dependencies>
        <dependency>
            <groupId>com.jsrxjt</groupId>
            <artifactId>rxjt-lucky-carp-mall-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-webmvc-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger.core.v3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>2.2.20</version>
        </dependency>

    </dependencies>

</project>
