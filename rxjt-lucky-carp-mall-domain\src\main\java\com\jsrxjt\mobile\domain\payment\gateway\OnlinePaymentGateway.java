package com.jsrxjt.mobile.domain.payment.gateway;

import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.payment.gateway.request.*;
import com.jsrxjt.mobile.domain.payment.gateway.response.*;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * 线上支付gateway接口
 * @author: Cheng
 * @since 2025/8/8
 */
public interface OnlinePaymentGateway {

    /**
     * 预支付接口
     * 根据订单号和来源获取预支付信息
     * 
     * @param request 预支付请求参数
     * @return 预支付响应结果
     */
    PrePayResponse prePayment(PrePayRequest request);
    
    /**
     * 发起支付接口
     * 根据订单号和预支付订单号发起支付
     * 
     * @param request 发起支付请求参数
     * @return 发起支付响应结果
     */
    PaymentResponse pay(PaymentRequest request);

    /**
     * 多卡查询卡余额接口
     *
     * @param request 查询卡余额请求参数
     * @return 查询卡余额响应结果
     */
    List<FindCardBalanceResponse> findBalance(FindCardBalanceRequest request);

    /**
     * 卡号+卡密查询卡余额接口
     *
     * @param request 查询卡余额请求参数
     * @return 查询卡余额响应结果
     */
    CheckFindCardBalanceResponse findBalanceByCardNoAndPassword(CheckFindCardBalanceRequest request);

    /**
     * 退款接口
     *
     * @param afterSale 售后信息
     * @return 退款响应结果
     */
    RefundResponse refund(AfterSaleEntity afterSale);

    /**
     * 会员卡充值接口
     * @param request 充值请求参数
     * @return 充值响应结果
     */
    CardRechargeResponse cardRecharge(CardRechargeRequest request);


    ResponseEntity<String> syncOrder(String tradeNo);
}
