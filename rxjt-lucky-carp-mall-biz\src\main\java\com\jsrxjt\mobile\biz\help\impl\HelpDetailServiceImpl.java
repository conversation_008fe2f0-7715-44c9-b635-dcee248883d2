package com.jsrxjt.mobile.biz.help.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.api.help.Respone.HelpDetailResponseDTO;
import com.jsrxjt.mobile.biz.help.HelpDetailService;
import com.jsrxjt.mobile.domain.help.entity.HelpDetailEntity;
import com.jsrxjt.mobile.domain.help.repository.HelpCatRepository;
import com.jsrxjt.mobile.domain.help.repository.HelpDetailRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class HelpDetailServiceImpl implements HelpDetailService {
    private final HelpDetailRepository helpDetailRepository;

    /**
     * @param catId
     * @return
     */
    @Override
    public List<HelpDetailResponseDTO> helpDetailList(Long catId) {
        List<HelpDetailEntity> allByCatIdAndStatus = helpDetailRepository.findAllByCatId(catId);
        return BeanUtil.copyToList(allByCatIdAndStatus, HelpDetailResponseDTO.class);
    }

    /**
     * 根据更新点击次数
     *
     * @param helpId
     * @return
     */
    @Override
    public void updateClickNum(Long helpId) {
        helpDetailRepository.updateClickNum(helpId);
    }
}
