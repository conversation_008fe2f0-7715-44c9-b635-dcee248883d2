package com.jsrxjt.mobile.api.order.dto.request;

import lombok.Data;

import java.util.Map;

/**
 * 卡券订单创建成功通知请求
 * <AUTHOR> Feng<PERSON>
 * @since 2025/7/8
 **/
@Data
public class CouponOrderCreatedNotifyDTO {
    /**
     * 卡管平台订单号
     */
    private String orderSn;

    /**
     * 福鲤圈订单号（针对卡管是外部订单号）
     */
    private String outOrderSn;

    /**
     * 用户id（针对卡管是外部用户编号）
     */
    private String outUserSn;

    /**
     * 总售价
     */
    private String totalPrice;

    /**
     * 总结算价
     */
    private String totalSettlePrice;

    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * 充值状态 状态(0-待付款 1-交易关闭 2-付款成功 3-待充值 4-充值中 5-充值成功 6-充值失败 7-退款成功 8 充值系统异常)
     */
    private Integer rechargeStatus;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 请求体Map
     */
    private Map<String, Object> requestBody;



}
