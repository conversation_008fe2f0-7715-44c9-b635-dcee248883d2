package com.jsrxjt.mobile.domain.promotion.service.validator;

import com.jsrxjt.mobile.domain.product.entity.ProductItem;

/**
 * 促销活动校验服务
 * 
 * <AUTHOR>
 * @since 2025/10/24
 */
public interface PromotionActivityValidationService {

    /**
     * 校验活动限购数量
     * 
     * @param productItem 商品信息
     * @param customerId 客户ID
     * @param quantity 购买数量
     */
    void validateActivityQuota(ProductItem productItem, Long customerId, Integer quantity);
}
