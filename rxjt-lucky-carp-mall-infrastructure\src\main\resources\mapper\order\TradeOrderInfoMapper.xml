<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsrxjt.mobile.infra.order.persistent.mapper.TradeOrderInfoMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jsrxjt.mobile.infra.order.persistent.po.TradeOrderInfoPO">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_no" property="orderNo"/>
        <result column="out_order_no" property="outOrderNo"/>
        <result column="trade_no" property="tradeNo"/>
        <result column="pay_channel" property="payChannel"/>
        <result column="trade_info_no" property="tradeInfoNo"/>
        <result column="out_trade_info_no" property="outTradeInfoNo"/>
        <result column="exchange_pay_id" property="exchangePayId"/>
        <result column="wx_mch_id" property="wxMchId"/>
        <result column="wx_open_id" property="wxOpenId"/>
        <result column="wx_transaction_id" property="wxTransactionId"/>
        <result column="wx_exchange_recharge_order" property="wxExchangeRechargeOrder"/>
        <result column="wx_pay_params" property="wxPayParams"/>
        <result column="wx_refund_id" property="wxRefundId"/>
        <result column="chan_id" property="chanId"/>
        <result column="mch_id" property="mchId"/>
        <result column="store_id" property="storeId"/>
        <result column="term_id" property="termId"/>
        <result column="card_no" property="cardNo"/>
        <result column="card_trade_type" property="cardTradeType"/>
        <result column="card_business_type" property="cardBusinessType"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_status" property="payStatus"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_status" property="refundStatus"/>
        <result column="trade_time" property="tradeTime"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted_at" property="deletedAt"/>
    </resultMap>

    <!-- 根据订单号查询交易信息列表 -->
    <select id="selectByTradeNoAndOrderNo" resultMap="BaseResultMap">
        SELECT * FROM trade_order_info 
        WHERE trade_no = #{tradeNo} AND order_no = #{orderNo} AND deleted_at = 0
    </select>


</mapper>