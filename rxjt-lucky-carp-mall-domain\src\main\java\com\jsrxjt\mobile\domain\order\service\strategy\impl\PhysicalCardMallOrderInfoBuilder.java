package com.jsrxjt.mobile.domain.order.service.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-10-24 09:32
 * @Version: 1.0
 */
@Component
@Slf4j
public class PhysicalCardMallOrderInfoBuilder extends DefaultOrderInfoBuilder {
    public PhysicalCardMallOrderInfoBuilder(BusinessIdGenerator businessIdGenerator, ConfigRepository configRepository) {
        super(businessIdGenerator, configRepository);
    }

    @Override
    public void supplementOrderInfo(OrderInfoEntity orderInfo, CreateOrderDTO request) {
        log.info("补充实体卡商城推单订单信息，订单号：{}", orderInfo.getOrderNo());
        // 补充第三方特有信息
        if (StrUtil.isNotBlank(request.getExternalOrderNo())) {
            orderInfo.setExternalOrderNo(request.getExternalOrderNo());
        }
        if(StrUtil.isNotBlank(request.getOrderDetailUrl())) {
            orderInfo.setOrderDetailUrl(request.getOrderDetailUrl());
        }
        // 设置特定的订单渠道
        orderInfo.setOrderChannel(OrderChannelEnum.PHYSICAL_CARD_MALL.getCode());
        log.info("实体卡商城推单订单信息补充完成，外部订单号：{}", orderInfo.getExternalOrderNo());
    }
}
