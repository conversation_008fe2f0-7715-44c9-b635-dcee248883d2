package com.jsrxjt.mobile.biz.mq.constants;

/**
 * The Class DestinationConstants.
 */
public class DestinationConstants {

    /**
     * The Enum Result.
     */
    public enum Result {

        /** The sucess. */
        SUCESS("sucess", "发送成功"),

        /** The failed. */
        FAILED("failed", "发送失败"),

        /** The unfindinconstants. */
        UNFINDINCONSTANTS("unFindInConstants", "未绑发送地点定到常量");

        /** The value. */
        private final String value;

        /** The desc. */
        private final String desc;

        /**
         * Instantiates a new result.
         *
         * @param value the value
         * @param desc the desc
         */
        Result(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        /**
         * Gets the value.
         *
         * @return the value
         */
        public String getValue() {
            return value;
        }

        /**
         * Gets the desc.
         *
         * @return the desc
         */
        public String getDesc() {
            return desc;
        }

    }

    /**
     * The Enum DestinationName.
     */
    public enum DestinationName {

        PAYMENT_TOPIC("payment_topic", "支付topic"),

        TEST_USER("test_user","测试任务队列"),

        SCAN_PAY_WSS_TOPIC("scan_pay_wss_topic", "扫码支付WSS"),

        PICK_SCAN_BACK_TOPIC("pick_scan_back_topic", "展码付回调");

        /** The name. */
        private String name;

        /** The desc. */
        private String desc;

        /**
         * Instantiates a new destination name.
         *
         * @param name the name
         * @param desc the desc
         */
        DestinationName(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }

        /**
         * Gets the name.
         *
         * @return the name
         */
        public String getName() {
            return name;
        }

        /**
         * Sets the name.
         *
         * @param name the new name
         */
        public void setName(String name) {
            this.name = name;
        }

        /**
         * Gets the desc.
         *
         * @return the desc
         */
        public String getDesc() {
            return desc;
        }

        /**
         * Sets the desc.
         *
         * @param desc the new desc
         */
        public void setDesc(String desc) {
            this.desc = desc;
        }

    }

    /**
     * 校验队列名称是否在这里面进行配置了
     * 
     * @param name
     * @return
     */
    public static DestinationName getDestinationName(String name) {
        for (DestinationName destination : DestinationName.values()) {
            if (destination.getName().equals(name)) {
                return destination;
            }
        }
        return null;
    }

}
