package com.jsrxjt.mobile.api.bailian.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "获取百联门店信息")
public class BaiLianShopResponseDTO {

    @Schema(description = "门店id")
    private Long id;

    @Schema(description = "门店名称")
    private String shopName;

    @Schema(description = "门店地址")
    private String address;

    @Schema(description = "门店图片")
    private String image;
}
