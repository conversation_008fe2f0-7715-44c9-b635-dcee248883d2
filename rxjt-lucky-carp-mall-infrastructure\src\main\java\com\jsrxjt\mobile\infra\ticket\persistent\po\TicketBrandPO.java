package com.jsrxjt.mobile.infra.ticket.persistent.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 优惠券品牌表
 * @Author: ywt
 * @Date: 2025-08-20 17:56
 * @Version: 1.0
 */
@Data
@TableName("ticket_brand")
public class TicketBrandPO {
    @Schema(description = "优惠券品牌id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "优惠券品牌名")
    @TableField("brand_name")
    private String brandName;

    @Schema(description = "优惠券类型：1全球购线上商城优惠券 2商家自发优惠券  3瑞祥代发优惠券 4门店优惠券")
    @TableField("ticket_type")
    private Byte ticketType;

    @Schema(description = "启动状态：0禁用 1启用")
    @TableField("status")
    private Byte status;

    @Schema(description = "核销logo")
    @TableField("offset_logo")
    private String offsetLogo;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT )
    private Date createTime;

    @Schema(description = "创建人id")
    @TableField("create_id")
    private Long createId;
    private String createName;

    @Schema(description = "编辑人id")
    @TableField(fill = FieldFill.INSERT_UPDATE )
    private Long modId;
    private String modName;

    @Schema(description = "编辑时间")
    @TableField("mod_time")
    private Date modTime;

    @Schema(description = "是否删除标志(0:否 1:是)")
    @TableField("del_flag")
    private Byte delFlag;

    @Schema(description = "删除人id")
    @TableField("del_id")
    private Long delId;

    @Schema(description = "删除时间")
    @TableField("del_time")
    private Date delTime;
}
