package com.jsrxjt.mobile.infra.order.repository;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleLogEntity;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleLogRepository;
import com.jsrxjt.mobile.infra.order.persistent.mapper.AfterSaleLogMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.AfterSaleLogPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后日志仓储实现类
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AfterSaleLogRepositoryImpl implements AfterSaleLogRepository {

    private final AfterSaleLogMapper afterSaleLogMapper;
    private final BusinessIdGenerator businessIdGenerator;

    @Override
    public void save(AfterSaleLogEntity afterSaleLog) {
        AfterSaleLogPO afterSaleLogPO = new AfterSaleLogPO();
        BeanUtils.copyProperties(afterSaleLog, afterSaleLogPO);
        
        // 如果ID为空，则生成ID
        if (afterSaleLogPO.getId() == null) {
            afterSaleLogPO.setId(businessIdGenerator.generateId());
            afterSaleLog.setId(afterSaleLogPO.getId()); // 回填ID
        }
        
        int insert = afterSaleLogMapper.insert(afterSaleLogPO);
        if (insert <= 0) {
            throw new BizException("售后日志保存失败");
        }
        
        log.info("售后日志保存成功，售后单号：{}", afterSaleLog.getAfterSaleNo());
    }

    @Override
    public List<AfterSaleLogEntity> findByAfterSaleNo(String afterSaleNo) {
        LambdaQueryWrapper<AfterSaleLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AfterSaleLogPO::getAfterSaleNo, afterSaleNo)
                .orderByAsc(AfterSaleLogPO::getOperationTime);
        
        List<AfterSaleLogPO> logPOs = afterSaleLogMapper.selectList(queryWrapper);
        
        if (logPOs == null || logPOs.isEmpty()) {
            return Collections.emptyList();
        }
        
        return logPOs.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    /**
     * PO转换为Entity
     */
    private AfterSaleLogEntity convertToEntity(AfterSaleLogPO logPO) {
        AfterSaleLogEntity entity = new AfterSaleLogEntity();
        BeanUtils.copyProperties(logPO, entity);
        return entity;
    }
}
