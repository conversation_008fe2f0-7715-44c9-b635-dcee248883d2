package com.jsrxjt.mobile.domain.global.response;

import lombok.Data;

import java.util.List;

/**
 * 全球购券发放响应
 * 
 * <AUTHOR>
 * @date 2025/09/22
 */
@Data
public class GlobalTicketSendResponse {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private List<SendResult> response;

    /**
     * 发放结果
     */
    @Data
    public static class SendResult {
        /**
         * 用户unionId
         */
        private String unionId;

        /**
         * 活动Id
         */
        private String activityId;

        /**
         * 实际券数量
         */
        private Integer actualCouponNum;

        /**
         * 消息
         */
        private String msg;
    }
}
