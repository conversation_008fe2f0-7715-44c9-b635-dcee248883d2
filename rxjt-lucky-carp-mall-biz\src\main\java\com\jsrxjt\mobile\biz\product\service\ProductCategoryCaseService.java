package com.jsrxjt.mobile.biz.product.service;

import com.jsrxjt.mobile.api.product.dto.ProductBaseInfoDTO;
import com.jsrxjt.mobile.api.product.dto.ProductCategoryDTO;
import com.jsrxjt.mobile.api.product.dto.request.CategoryDetailRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.MultiCategoryRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.CategorySearchResponseDTO;

import java.util.List;

/**
 * 产品分类用例服务
 * 
 * <AUTHOR>
 * @since 2025/5/12
 */
public interface ProductCategoryCaseService {

    /**
     * 查询一级分类列表
     *
     * @return 一级分类列表
     */
    List<ProductCategoryDTO> getFirstLevelCategories();

    /**
     * 根据一级分类ID获取二级分类详情，包括二级分类信息、广告位和产品列表
     *
     * @param requestDTO 请求参数，包含一级分类ID
     * @return 二级分类详情列表
     */
    CategorySearchResponseDTO getSecondLevelCategoryDetails(CategoryDetailRequestDTO requestDTO);

    /**
     * 根据多个分类ID获取产品列表
     *
     * @param requestDTO 请求参数，包含多个分类ID
     * @return 产品列表
     */
    List<ProductBaseInfoDTO> findProductsByCategoryIds(MultiCategoryRequestDTO requestDTO);
}