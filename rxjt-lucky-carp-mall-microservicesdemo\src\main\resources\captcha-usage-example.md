# 验证码使用示例

## API接口调用示例

### 1. 获取滑动拼图验证码

```bash
curl -X GET "http://localhost:8080/captcha/blockPuzzle?clientUid=test-123"
```

或者使用POST方式：

```bash
curl -X POST "http://localhost:8080/captcha/get" \
  -H "Content-Type: application/json" \
  -d '{
    "captchaType": "blockPuzzle",
    "clientUid": "test-123"
  }'
```

### 2. 获取点选文字验证码

```bash
curl -X GET "http://localhost:8080/captcha/clickWord?clientUid=test-456"
```

### 3. 校验验证码

```bash
curl -X POST "http://localhost:8080/captcha/check" \
  -H "Content-Type: application/json" \
  -d '{
    "captchaType": "blockPuzzle",
    "token": "验证码token",
    "pointJson": "用户操作的坐标信息"
  }'
```

### 4. 二次校验（登录场景）

```bash
curl -X POST "http://localhost:8080/captcha/verify" \
  -H "Content-Type: application/json" \
  -d '{
    "captchaVerification": "验证码验证参数"
  }'
```

## 编程式调用示例

### 在业务代码中使用验证码

```java
@RestController
public class LoginController {
    
    @Autowired
    private CaptchaService captchaService;
    
    @PostMapping("/login")
    public ResponseModel login(@RequestBody LoginRequest request) {
        // 验证码二次校验
        boolean captchaValid = CaptchaUtil.verifyForBusiness(
            request.getCaptchaVerification(), 
            captchaService
        );
        
        if (!captchaValid) {
            return ResponseModel.errorMsg("验证码校验失败");
        }
        
        // 继续登录逻辑
        // ...
        
        return ResponseModel.success();
    }
}
```

### 获取验证码示例

```java
@Service
public class CaptchaBusinessService {
    
    @Autowired
    private CaptchaService captchaService;
    
    public ResponseModel getCaptchaForLogin() {
        // 创建滑动拼图验证码
        CaptchaVO captchaVO = CaptchaUtil.createBlockPuzzleCaptcha("login-" + System.currentTimeMillis());
        
        // 获取验证码
        ResponseModel response = captchaService.get(captchaVO);
        
        if (CaptchaUtil.isSuccess(response)) {
            log.info("获取登录验证码成功");
            return response;
        } else {
            log.error("获取登录验证码失败：{}", CaptchaUtil.getErrorMsg(response));
            return ResponseModel.errorMsg("获取验证码失败，请重试");
        }
    }
}
```

## 前端集成示例

### JavaScript调用示例

```javascript
// 获取验证码
async function getCaptcha() {
    try {
        const response = await fetch('/captcha/blockPuzzle?clientUid=' + Date.now());
        const result = await response.json();
        
        if (result.repCode === '0000') {
            // 显示验证码
            showCaptcha(result.repData);
        } else {
            console.error('获取验证码失败：', result.repMsg);
        }
    } catch (error) {
        console.error('获取验证码异常：', error);
    }
}

// 校验验证码
async function checkCaptcha(captchaType, token, pointJson) {
    try {
        const response = await fetch('/captcha/check', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                captchaType: captchaType,
                token: token,
                pointJson: pointJson
            })
        });
        
        const result = await response.json();
        return result.repCode === '0000';
    } catch (error) {
        console.error('校验验证码异常：', error);
        return false;
    }
}
```

## 依赖说明

验证码功能已经通过基础设施层（`rxjt-lucky-carp-mall-infrastructure`）自动引入，无需额外添加依赖。

基础设施层已包含：
```xml
<dependency>
    <groupId>com.jsrxjt</groupId>
    <artifactId>rxjt-lucky-carp-mall-common-captcha</artifactId>
</dependency>
```

## 配置示例

### application.yml配置

```yaml
jsrxjt:
  captcha:
    enabled: true
    type: blockPuzzle
    cache-type: redis

aj:
  captcha:
    jigsaw: classpath:captcha/images/jigsaw
    pic-click: classpath:captcha/images/pic-click
    water-mark: 我的验证码
    slip-offset: 5
    aes-status: true
    req-frequency-limit-enable: true
    req-get-lock-limit: 5
    req-get-lock-seconds: 300
```

## 响应格式说明

### 成功响应
```json
{
    "repCode": "0000",
    "repMsg": "success",
    "success": true,
    "repData": {
        "captchaType": "blockPuzzle",
        "token": "验证码token",
        "jigsawImageBase64": "拼图图片base64",
        "originalImageBase64": "原图base64"
    }
}
```

### 失败响应
```json
{
    "repCode": "6111",
    "repMsg": "验证码已失效，请重新获取",
    "success": false
}
```
