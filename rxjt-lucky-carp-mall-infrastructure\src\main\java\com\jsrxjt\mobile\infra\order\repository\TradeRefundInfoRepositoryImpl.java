package com.jsrxjt.mobile.infra.order.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.order.entity.TradeRefundInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.TradeRefundInfoRepository;
import com.jsrxjt.mobile.infra.order.persistent.mapper.TradeRefundInfoMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.TradeRefundInfoPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 交易退款信息仓储实现类
 * <AUTHOR>
 * @since 2025/9/26
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TradeRefundInfoRepositoryImpl implements TradeRefundInfoRepository {

    private final TradeRefundInfoMapper tradeRefundInfoMapper;

    @Override
    public List<TradeRefundInfoEntity> findByOutRefundNo(String outRefundNo) {
        LambdaQueryWrapper<TradeRefundInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TradeRefundInfoPO::getOutRefundNo, outRefundNo)
                .isNull(TradeRefundInfoPO::getDeletedAt);

        List<TradeRefundInfoPO> tradeRefundInfoPOs = tradeRefundInfoMapper.selectList(queryWrapper);

        if (tradeRefundInfoPOs == null || tradeRefundInfoPOs.isEmpty()) {
            return Collections.emptyList();
        }

        return tradeRefundInfoPOs.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    /**
     * PO转换为Entity
     */
    private TradeRefundInfoEntity convertToEntity(TradeRefundInfoPO tradeRefundInfoPO) {
        TradeRefundInfoEntity entity = new TradeRefundInfoEntity();
        BeanUtils.copyProperties(tradeRefundInfoPO, entity);
        return entity;
    }
}
