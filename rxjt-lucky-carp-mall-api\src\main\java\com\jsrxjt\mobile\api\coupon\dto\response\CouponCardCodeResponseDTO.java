package com.jsrxjt.mobile.api.coupon.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 自发券的动态码响应
 * @Author: ywt
 * @Date: 2025-05-19 19:51
 * @Version: 1.0
 */
@Data
@Schema(description = "自发券的动态码响应")
public class CouponCardCodeResponseDTO {
    @Schema(description = "自发券的动态码")
    private String code;
    @Schema(description = "有效时长")
    private String validTime;
    @Schema(description = "卡余额 单位分")
    private String balance;
}
