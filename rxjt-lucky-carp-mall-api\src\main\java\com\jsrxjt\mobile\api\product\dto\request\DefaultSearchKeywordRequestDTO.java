package com.jsrxjt.mobile.api.product.dto.request;


import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 默认搜索词请求DTO
 * 
 * <AUTHOR>
 * @since 2025/5/14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "默认搜索词请求参数")
public class DefaultSearchKeywordRequestDTO extends BaseParam {

    @Schema(description = "三级地址ID")
    private Long districtId;

    @Schema(description = "类型 1 搜索框搜索词 2 搜索发现")
    @NotNull(message = "类型不能为空")
    private Integer type;
}