package com.jsrxjt.mobile.infra.product.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.api.product.types.ProductExplainTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.domain.product.entity.ProductBrandEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductExplainEntity;
import com.jsrxjt.mobile.domain.product.repository.ProductBrandRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductExplainRepository;
import com.jsrxjt.mobile.infra.product.persistent.mapper.ProductBrandMapper;
import com.jsrxjt.mobile.infra.product.persistent.mapper.ProductExplainMapper;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductBrandPO;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductExplainPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 产品品牌Repository实现类
 * 
 * <AUTHOR> Fengping
 * @since 2025-06-17
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ProductExplainRepositoryImpl implements ProductExplainRepository {

    private final ProductExplainMapper productExplainMapper;

    /**
     * 根据SPUID查询说明信息
     *
     * @param spuId spuId
     * @return 品牌实体
     */
    @Override
    public List<ProductExplainEntity> findBySpuId(Long spuId,Byte productType) {
        LambdaQueryWrapper<ProductExplainPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductExplainPO::getProductSpuId, spuId);
        queryWrapper.eq(ProductExplainPO::getProductType, productType.byteValue());
        List<ProductExplainPO> productExplainPOs = productExplainMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(productExplainPOs)) {
           return BeanUtil.copyToList(productExplainPOs, ProductExplainEntity.class);
        }
        return CollectionUtil.newArrayList();
    }

    @Override
    public List<ProductExplainEntity> findBySpuIdAndExplainType(Long spuId, ProductExplainTypeEnum explainType) {
        LambdaQueryWrapper<ProductExplainPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductExplainPO::getProductSpuId, spuId);
        queryWrapper.eq(ProductExplainPO::getExplainType, explainType.getType());
        List<ProductExplainPO> productExplainPOs = productExplainMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(productExplainPOs)) {
            return BeanUtil.copyToList(productExplainPOs, ProductExplainEntity.class);
        }
        return CollectionUtil.newArrayList();
    }
}