package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 赠送券响应DTO
 * 
 * <AUTHOR>
 * @since 2025/1/20
 */
@Data
@Schema(description = "赠送券响应")
public class GiftTicketResponseDTO {

    /**
     * 券ID
     */
    @Schema(description = "券ID")
    private Long ticketId;

    /**
     * 券名称
     */
    @Schema(description = "券名称")
    private String ticketName;

    /**
     * 规格图片URL
     */
    @Schema(description = "规格图片URL")
    private String specPicUrl;

    /**
     * 券数量
     */
    @Schema(description = "券数量")
    private Integer ticketNum;
}