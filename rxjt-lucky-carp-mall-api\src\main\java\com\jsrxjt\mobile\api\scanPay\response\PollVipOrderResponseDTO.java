package com.jsrxjt.mobile.api.scanPay.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "收银台订单轮询信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PollVipOrderResponseDTO {

    @Schema(description = "客户id")
    private Long customerId;

    @Schema(description = "是否有支付密码 0否 1是 若无密码，需要先添加密码在支付")
    private Integer isPayPass;

    @Schema(description = "客户是否开启免密支付 0否 1是 开启后不需要密码，小金额直接支付")
    private Integer isOpenPay;

    @Schema(description = "客户是否支付 0否 1是 免密支付成功是为1")
    private Integer isPay;

    @Schema(description = "订单信息")
    private PollOrderInfoResponseDTO orderInfo;


}
