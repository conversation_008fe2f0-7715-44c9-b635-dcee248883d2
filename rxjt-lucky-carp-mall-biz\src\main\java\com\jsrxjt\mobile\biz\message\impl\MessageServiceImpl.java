package com.jsrxjt.mobile.biz.message.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.message.request.MessageUpdateReadRequest;
import com.jsrxjt.mobile.api.message.response.MessageCountResponse;
import com.jsrxjt.mobile.api.message.response.MessageDetailResponse;
import com.jsrxjt.mobile.api.message.response.MessageServeResponse;
import com.jsrxjt.mobile.biz.message.MessageService;
import com.jsrxjt.mobile.biz.message.request.MessageDetailQueryRequest;
import com.jsrxjt.mobile.biz.message.request.MessageDetailRequest;
import com.jsrxjt.mobile.domain.message.entity.*;
import com.jsrxjt.mobile.domain.message.repository.MessageAccountRepository;
import com.jsrxjt.mobile.domain.message.repository.MessageCatRepository;
import com.jsrxjt.mobile.domain.message.repository.MessageDetailRepository;
import com.jsrxjt.mobile.domain.message.repository.MessageServeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class MessageServiceImpl implements MessageService {
    private final MessageDetailRepository messageDetailRepository;
    private final MessageAccountRepository messageAccountRepository;
    private final MessageServeRepository messageServeRepository;
    private final MessageCatRepository messageCatRepository;

    /**
     * 瑞祥通知-用户创建后消息列表
     *
     * @param request
     * @return
     */
    @Override
    public PageDTO<MessageDetailResponse> findAllSysByTime(MessageDetailRequest request) {
        List<MessageDetailResponse> responses = new java.util.ArrayList<>();
        Long customerId = StpUtil.getLoginIdAsLong();
        //Long customerId = 191966495057921L;
        if (customerId == null) {
            return PageDTO.emptyBuild(request.getPage(), request.getSize());
        }
        PageDTO<MessageDetailEntity> allByUserTime = messageDetailRepository.findAllByUserTime(customerId, request.getPage(), request.getSize());
        List<MessageDetailEntity> records = allByUserTime.getRecords();
        List<Long> messageIds = new ArrayList<>();
        for (MessageDetailEntity record : records) {
            MessageDetailResponse bean = BeanUtil.toBean(record, MessageDetailResponse.class);
            Date modTime = record.getModTime();
            String msgTime = setMsgTime(modTime);
            bean.setMsgTime(msgTime);
            responses.add(bean);
            messageIds.add(record.getMessageId());
        }
        //设置已读状态
        if (CollectionUtils.isNotEmpty(messageIds)) {
            List<MessageUserEntity> messageUserByMsgIdList = messageDetailRepository.getMessageUserByMsgIdList(messageIds, customerId);
            Map<Long, MessageUserEntity> messageUserMap = messageUserByMsgIdList.stream().collect(Collectors.toMap(MessageUserEntity::getMessageId, messageUser -> messageUser));
            for (MessageDetailResponse respons : responses) {
                MessageUserEntity messageUserEntity = messageUserMap.get(respons.getMessageId());
                if (messageUserEntity != null) {
                    respons.setIsRead(1);
                }
            }
        }

        return PageDTO.build(responses, allByUserTime.getTotal(), allByUserTime.getSize(), allByUserTime.getCurrent());
    }

    /**
     * 瑞祥通知-详细详情
     *
     * @param request
     * @return
     */
    @Override
    public MessageDetailResponse findByMsgId(MessageDetailQueryRequest request) {
        Long messageId = request.getMessageId();
        Byte type = request.getType();
        MessageDetailResponse response = new MessageDetailResponse();
        if (type == 0) {
            MessageDetailEntity byId = messageDetailRepository.findById(messageId);
            if (byId == null) {
                throw new BizException("瑞祥消息不存在");
            }
            BeanUtil.copyProperties(byId, response);
            MessageCatEntity catNameById = messageCatRepository.findCatNameById(Long.valueOf(byId.getCatId()));
            if (catNameById != null) {
                response.setCatName(catNameById.getCatName());
            }
            Date createTime = byId.getCreateTime();
            String msgTime = setMsgTime(createTime);
            response.setMsgTime(msgTime);

        } else if (type == 1) {
            MessageServeEntity byId = messageServeRepository.findById(messageId);
            if (byId == null) {
                throw new BizException("客服该消息不存在");
            }
            BeanUtil.copyProperties(byId, response);
        }
        response.setIsRead(null);
        return response;
    }

    /**
     * 瑞祥通知-设置消息时间
     *
     * @return
     */
    private String setMsgTime(Date createTime) {
        //判断是否是今天
        boolean sameDay = DateUtil.isSameDay(createTime, new Date());
        if (sameDay) {
            return DateUtil.format(createTime, "HH:mm");
        }
        //判断是否是昨天
        boolean sameYesterday = DateUtil.isSameDay(createTime, DateUtil.yesterday());
        if (sameYesterday) {
            return "昨天" + DateUtil.format(createTime, "HH:mm");
        }
        //判断是否是今年
        if (DateUtil.thisYear() == DateUtil.year(createTime)) {
            return DateUtil.format(createTime, "MM-dd");
        }
        return DateUtil.format(createTime, "yyyy-MM-dd");
    }

    /**
     * 查询客服消息内容
     *
     * @param request
     * @return
     */
    @Override
    public PageDTO<MessageServeResponse> findAllServeMsg(MessageDetailRequest request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        if (customerId == null) {
            return PageDTO.emptyBuild(request.getPage(), request.getSize());
        }
        PageDTO<MessageServeEntity> allServeMsg = messageServeRepository.findAllServeMsg(customerId, request.getPage(), request.getSize());

        List<MessageServeEntity> records = allServeMsg.getRecords();
        List<MessageServeResponse> messageServeResponses = BeanUtil.copyToList(records, MessageServeResponse.class);
        return PageDTO.build(messageServeResponses, allServeMsg.getTotal(), allServeMsg.getSize(), allServeMsg.getCurrent());
    }

    /**
     * 更新所有用户消息已读
     *
     * @param customerId 客户id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAllIsRead(Long customerId) {
        if (customerId == null) {
            return;
        }
        // 记录开始时间（用于单独统计）
        long sysStartTime = System.currentTimeMillis();
        //系统消息
        messageDetailRepository.updateSysIsReadBatch(customerId);
        //客服消息
        messageServeRepository.updateAllServeIsRead(customerId);
        long sysDuration = System.currentTimeMillis() - sysStartTime;
        log.info("【消息查询】执行完成，耗时：{} 毫秒", sysDuration);
    }

    /**
     * 更新单个瑞祥消息已读
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSysIsRead(MessageUpdateReadRequest request) {

        if (request.getMessageId() == null) {
            log.error("接口：updateIsRead,messageId：参数为空错误");
            return;
        }
        Long customerId = StpUtil.getLoginIdAsLong();
        MessageUserEntity messageUser = messageDetailRepository.getMessageUser(request.getMessageId(), customerId);
        if (messageUser == null) {
            messageDetailRepository.updateSysReadNum(request.getMessageId());
            messageDetailRepository.saveMessageUser(request.getMessageId(), customerId);
        }
    }

    /**
     * 更新单个客服消息已读
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateServeIsRead(MessageUpdateReadRequest request) {
        if (request.getMessageId() == null) {
            return;
        }
        Long customerId = StpUtil.getLoginIdAsLong();
        //  Long customerId = 191966495057921L;
        messageServeRepository.updateServeIsRead(request.getMessageId(), customerId);
    }

    /**
     * 客户所有消息数量
     *
     * @param customerId
     */
    @Override
    public MessageCountResponse countAllMsg(Long customerId) {
        MessageCountResponse response = new MessageCountResponse();
        // 异步获取各个消息数量
        // 记录开始时间（用于单独统计）
        long sysStartTime = System.currentTimeMillis();
        //系统消息
        int sysMsg = messageDetailRepository.countSysMsg(customerId);
        //账户消息
        //  int accountMsg = messageAccountRepository.countAccountMsg(customerId);
        //客服消息
        int kfMsg = messageServeRepository.countServeMsg(customerId);
        long sysDuration = System.currentTimeMillis() - sysStartTime;
        log.info("【消息查询】执行完成，耗时：{} 毫秒", sysDuration);
        response.setAllMsgCount(sysMsg + kfMsg);
        response.setSysMsgCount(sysMsg);
        //  response.setAccountMsgCount(accountMsg);
        response.setServeMsgCount(kfMsg);
        return response;
    }
}