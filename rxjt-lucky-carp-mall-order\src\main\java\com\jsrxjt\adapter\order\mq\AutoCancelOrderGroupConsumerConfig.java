package com.jsrxjt.adapter.order.mq;

import lombok.SneakyThrows;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;

/**
 * 订单自动取消消费者配置
 * <AUTHOR> <PERSON>
 * @since 2025-8-27
 **/
@Configuration
@RefreshScope
public class AutoCancelOrderGroupConsumerConfig {

    @SneakyThrows
    @Bean(destroyMethod = "close")
    public PushConsumer autoCancelOrderGroupPushConsumer(ClientConfiguration clientConfiguration, AutoCancelOrderMessageListener autoCancelOrderMessageListener) {
        FilterExpression filterExpression = new FilterExpression("tag_cancel", FilterExpressionType.TAG);
        String consumerGroup = "auto_cancel_order_consumer_group";
        String topic = "delay_order_topic";
        return ClientServiceProvider.loadService().newPushConsumerBuilder()
                .setClientConfiguration(clientConfiguration)
                .setConsumerGroup(consumerGroup)
                .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
                .setMessageListener(autoCancelOrderMessageListener)
                .build();
    }
}
