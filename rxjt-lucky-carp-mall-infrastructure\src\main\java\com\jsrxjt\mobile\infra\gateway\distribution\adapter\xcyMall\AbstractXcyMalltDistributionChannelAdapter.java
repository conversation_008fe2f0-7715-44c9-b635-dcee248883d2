package com.jsrxjt.mobile.infra.gateway.distribution.adapter.xcyMall;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.jsrxjt.mobile.api.exception.DistributionApiException;
import com.jsrxjt.mobile.api.xcy.dto.request.notify.XcyMallPayNotifyRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.notify.XcyMallPayNotifyResponseDTO;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import com.jsrxjt.mobile.infra.gateway.distribution.config.XcyMallChannelConfig;
import com.jsrxjt.mobile.infra.xcyMall.util.XcyMallUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.SortedMap;
import java.util.UUID;

/**
 * Created by jeffery.yang on 2025/10/23 16:47
 *
 * @description:
 * @author: jeffery.yang
 * @date: 2025/10/23 16:47
 * @version: 1.0
 */
@Slf4j
public abstract class AbstractXcyMalltDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

    protected AbstractXcyMalltDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                                         DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        XcyMallChannelConfig xcyMallChannelConfig = (XcyMallChannelConfig) config;
        String requestUrl = xcyMallChannelConfig.getBaseUrl().concat(xcyMallChannelConfig.getUrlTag()).concat(xcyMallChannelConfig.getIndexUrl());
        // 此处传递的channel为渠道类型
        String channel = request.getChannelType().name();
        requestUrl = requestUrl.concat("?channel=").concat(channel).concat("&authCode=").concat(request.getOnceToken());
        log.info("### 祥采云免等url: {}", requestUrl);
        return DistAccessResponse.builder()
            .success(true)
            .redirectUrl(requestUrl)
            .build();
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {

        XcyMallPayNotifyRequestDTO xcyMallPayNotifyRequestDTO = new XcyMallPayNotifyRequestDTO();
        xcyMallPayNotifyRequestDTO.setTradeNo(request.getDistOrderNo());
        xcyMallPayNotifyRequestDTO.setOutTradeNo(request.getOrderNo());
        xcyMallPayNotifyRequestDTO.setTotalAmount(String.valueOf(request.getTradeAmount()));
        xcyMallPayNotifyRequestDTO.setPayTime(request.getPayTime().format(COMPACT_DAY_TIME_FORMAT));

        XcyMallChannelConfig xcyConfig = (XcyMallChannelConfig)config;
        // 构建带有签名的请求
        buildSignedRequest(xcyMallPayNotifyRequestDTO, xcyConfig);

        // 发送请求
        String url = xcyConfig.getBaseUrl() + xcyConfig.getUrlTag() + xcyConfig.getNotifyUrl();
        try {
            log.info("###祥采云回调通知 url :{} ,request: {}", url,JSONObject.toJSONString(xcyMallPayNotifyRequestDTO));
            String response = httpClientGateway.doPostJson(url, JSONObject.toJSONString(xcyMallPayNotifyRequestDTO), xcyConfig.getConnectTimeout(),xcyConfig.getReadTimeout());
            log.info("###祥采云回调通知 response: {}", response);
            XcyMallPayNotifyResponseDTO payNotifyResponseDTO = JSON.parseObject(response, XcyMallPayNotifyResponseDTO.class);
            if (payNotifyResponseDTO.getResCode().equals(0)) {
                return DistPaidNotifyResponse.builder()
                    .success(true)
                    .status(SUCCESS_CODE)
                    .message(payNotifyResponseDTO.getResDesc())
                    .build();
            }else {
                return DistPaidNotifyResponse.builder()
                    .success(false)
                    .status(payNotifyResponseDTO.getResCode())
                    .message(payNotifyResponseDTO.getResDesc())
                    .build();
            }

        }catch (Exception e){
            log.error("祥采云回调通知异常: {}", e.getMessage(), e);
            return DistPaidNotifyResponse.builder()
                .success(false)
                .status(500)
                .message( "祥采云支付回调通知异常: " + e.getMessage())
                .build();
        }
    }
    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        throw new DistributionApiException("不支持祥采云渠道的退款通知接口");
    }
    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        throw new DistributionApiException("不支持祥采云订单详情查询接口");
    }


    /**
     * 构建带有签名的祥采云支付通知请求
     * @param xcyMallPayNotifyRequestDTO 支付通知请求DTO
     * @param xcyConfig 祥采云渠道配置
     */
    protected void buildSignedRequest(XcyMallPayNotifyRequestDTO xcyMallPayNotifyRequestDTO,
                                                                     XcyMallChannelConfig xcyConfig) {
        xcyMallPayNotifyRequestDTO.setAppid(String.valueOf(xcyConfig.getAppId()));
        xcyMallPayNotifyRequestDTO.setTimestamp(Long.toString(System.currentTimeMillis() / 1000));
        xcyMallPayNotifyRequestDTO.setNounce(UUID.randomUUID().toString().replace("-", ""));
        SortedMap<Object, Object> parameters = JSONObject.parseObject(JSONObject.toJSONString(xcyMallPayNotifyRequestDTO), SortedMap.class);
        String sign = XcyMallUtils.createSign("UTF-8", parameters, xcyConfig.getAppSecret());
        xcyMallPayNotifyRequestDTO.setSign(sign);
    }
}
