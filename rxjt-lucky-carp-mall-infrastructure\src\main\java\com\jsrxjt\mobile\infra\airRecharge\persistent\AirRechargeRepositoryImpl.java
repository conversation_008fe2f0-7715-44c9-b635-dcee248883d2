package com.jsrxjt.mobile.infra.airRecharge.persistent;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jsrxjt.mobile.domain.airRecharge.entity.AirRechargeEntity;
import com.jsrxjt.mobile.domain.airRecharge.repository.AirRechargeRepository;
import com.jsrxjt.mobile.infra.airRecharge.persistent.mapper.AirRechargeMapper;
import com.jsrxjt.mobile.infra.airRecharge.persistent.po.BatchVipPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * @Description: 空中充值
 * @Author: ywt
 * @Date: 2025-08-26 15:14
 * @Version: 1.0
 */
@Repository
@RequiredArgsConstructor
public class AirRechargeRepositoryImpl implements AirRechargeRepository {
    private final AirRechargeMapper airRechargeMapper;

    @Override
    public List<AirRechargeEntity> getAirRechargeInfo(String mobile) {
        List<BatchVipPO> list = airRechargeMapper.getAirRechargeInfo(mobile);
        return BeanUtil.copyToList(list, AirRechargeEntity.class);
    }

    @Override
    public AirRechargeEntity getAirRechargeInfoById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        BatchVipPO batchVipPO = airRechargeMapper.selectById(id);
        return BeanUtil.copyProperties(batchVipPO, AirRechargeEntity.class);
    }

    @Override
    public Integer updateAirRechargeStatus(Integer id, String signPicUrl) {
        Integer result = 0;
        if (StringUtils.isNotEmpty(signPicUrl)) {
            LambdaUpdateWrapper<BatchVipPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BatchVipPO::getId, id)
                    .set(BatchVipPO::getIsMsg, 1)
                    .set(BatchVipPO::getIsWindows, 1)
                    .set(BatchVipPO::getSignPic, signPicUrl);
            result = airRechargeMapper.update(null, updateWrapper);
        } else {
            LambdaUpdateWrapper<BatchVipPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BatchVipPO::getId, id)
                    .set(BatchVipPO::getIsMsg, 1);
            result = airRechargeMapper.update(null, updateWrapper);
        }
        return result;
    }
}
