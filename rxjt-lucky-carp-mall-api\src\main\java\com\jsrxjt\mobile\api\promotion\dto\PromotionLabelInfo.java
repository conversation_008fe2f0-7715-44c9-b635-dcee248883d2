package com.jsrxjt.mobile.api.promotion.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PromotionLabelInfo {

    @Schema(description = "spuId")
    private Long spuId;

    @Schema(description = "促销标签文案 用在tab栏商品名称下面")
    private String promotionLabelCopy;

    @Schema(description = "活动标签文案 用在搜索结果页、tab栏商品名称前面")
    private String labelCopy;

    @Schema(description = "活动标签文案背景图")
    private String labelCopyBackgroundImg;

    @Schema(description = "活动角标图片地址 用在tab栏商品图片左上角")
    private String subscriptImgUrl;

}
