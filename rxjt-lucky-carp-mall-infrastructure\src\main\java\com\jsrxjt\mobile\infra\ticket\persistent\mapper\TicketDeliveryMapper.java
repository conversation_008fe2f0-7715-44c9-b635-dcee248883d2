package com.jsrxjt.mobile.infra.ticket.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketDeliveryPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 优惠券发放表
 * @Author: zy
 */
@Mapper
public interface TicketDeliveryMapper extends CommonBaseMapper<TicketDeliveryPO> {

  /**
   * 批量插入优惠券发放记录
   *
   * @param ticketDeliveries 优惠券发放记录列表
   * @return 插入条数
   */
  int batchInsert(List<TicketDeliveryPO> ticketDeliveries);
}
