package com.jsrxjt.mobile.infra.gateway.distribution.adapter.meituan;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 小象超市分销渠道适配器实现类
 **/
@Component
@Slf4j
public class XiaoXiangDistributionChannelAdapter extends AbstractMeiTuanDistributionChannelAdapter {

    public XiaoXiangDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                               DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getXiaoxiang();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.XIAOXIANG;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            // 校验必填参数
            if (StringUtils.isBlank(request.getUserId())) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID不能为空")
                        .build();
            }
            request.setProductType("mt_maicai");
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("user_id", request.getUserId());
            params.put("product_type", request.getProductType());

            // 添加公共参数和签名
            addCommonParams(params);

            // 构建请求URL
            String url = config.getBaseUrl() + config.getApiPath() + config.getAccessPath();
            String requestUrl = url + "&" + httpClientGateway.buildUrlParams(params);

            return DistAccessResponse.builder()
                    .success(true)
                    .redirectUrl(requestUrl)
                    .build();
        } catch (Exception e) {
            log.error("小象超市免登接入异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("小象超市免登接入异常: " + e.getMessage())
                    .build();
        }
    }
}
