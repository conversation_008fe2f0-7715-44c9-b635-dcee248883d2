package com.jsrxjt.mobile.domain.order.service.strategy.aftersale.impl;

import com.jsrxjt.mobile.api.order.dto.response.AfterSaleDetailResponseDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.service.strategy.aftersale.AfterSaleProgressBuilder;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AfterSaleAuditRejectedBuilder implements AfterSaleProgressBuilder {
    @Override
    public boolean supports(Integer status) {
        return AfterSaleStatusEnum.AUDIT_REJECTED.getCode().equals(status);
    }

    @Override
    public List<AfterSaleDetailResponseDTO.ProgressDetailDTO> build(AfterSaleEntity afterSale, OrderInfoEntity order) {
        return List.of(
                new AfterSaleDetailResponseDTO.ProgressDetailDTO("提交申请",
                        "您的申请已提交，请耐心等待客服审核", afterSale.getApplyTime()),
                new AfterSaleDetailResponseDTO.ProgressDetailDTO("客服审核",
                        "审核已驳回，请联系平台处理", afterSale.getAuditTime())
        );
    }
}
