package com.jsrxjt.mobile.api.customer.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户信息返回结果")
public class CustomerDetailResponse {

    private Long id;

    /**
     * 瑞祥会员中心会员id
     */
    @Schema(description = "瑞祥会员中心会员id")
    private Long vipId;
    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String userName;

    /**
     * 用户头像
     */
    @Schema(description = "用户头像")
    private String userUrl;
    /**
     * 用户密码
     */
    @Schema(description = "用户密码")
    private String password;
    /**
     * 微信unionid
     */
    @Schema(description = "微信unionid")
    private String unionid;
    /**
     * 微信移动应用openid
     */
    @Schema(description = "微信移动应用openid")
    private String openid;
    /**
     * 微信小程序openid
     */
    @Schema(description = "微信小程序openid")
    private String miniOpenid;
    /**
     * 手机
     */
    @Schema(description = "手机")
    private String phone;
    /**
     * 性别（1男，0女）
     */
    @Schema(description = "性别（1男，0女）")
    private Integer sex;
    /**
     * 生日
     */
    @Schema(description = "生日")
    private String birthday;
    /**
     * 启用状态 0：禁用 1：启用 2：已注销
     */
    @Schema(description = "启用状态 0：禁用 1：启用 2：已注销")
    private Integer status;
    /**
     * 登录ip
     */
    @Schema(description = "登录ip")
    private String loginIp;

    /**
     * 手机设备号
     */
    @Schema(description = "手机设备号")
    private String phoneToken;

    /**
     * 登录状态（1 是，0否）
     */
    @Schema(description = "登录状态（1 是，0否）")
    private Integer loginStatus;

    /**
     * 是否使用默认扣款顺序 1是 0否
     */
    @Schema(description = "是否使用默认扣款顺序 1是 0否")
    private Integer useDefaultPaySort;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long companyId;

    /**
     * 是否已开通免密支付 1是 0否
     */
    @Schema(description = "是否已开通免密支付 1是 0否")
    private Integer openPasswordFreePayment;

}
