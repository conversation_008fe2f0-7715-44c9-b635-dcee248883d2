package com.jsrxjt.mobile.infra.gateway.distribution.adapter.wumei;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 物美线下分销渠道适配器
 * 
 * <AUTHOR>
 * @since 2025/3/28
 **/
@Component
@Slf4j
public class WuMeiOfflineHuaDongDistributionChannelAdapter extends AbstractWumeiDistributionChannelAdapter {

    public WuMeiOfflineHuaDongDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                                         DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getWumeiOfflineHuadong();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.WUMEI_OFFLINE_HUADONG;
    }
}