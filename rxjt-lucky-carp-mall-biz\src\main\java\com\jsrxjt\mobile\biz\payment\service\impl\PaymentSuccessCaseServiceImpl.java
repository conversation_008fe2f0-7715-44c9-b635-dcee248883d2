package com.jsrxjt.mobile.biz.payment.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.payment.service.PaymentSuccessCaseService;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategyFactory;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformFactory;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.global.gateway.GlobalGateWay;
import com.jsrxjt.mobile.domain.global.request.GlobalTicketSendRequest;
import com.jsrxjt.mobile.domain.global.response.GlobalTicketSendResponse;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.payment.types.PaymentSuccessMessage;
import com.jsrxjt.mobile.domain.ticket.config.TicketPlatformConfig;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketInfo;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketOrderEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketDeliveryEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.gateway.TicketPlatformGateway;
import com.jsrxjt.mobile.domain.ticket.gateway.cmd.GiveOutTicketCmd;
import com.jsrxjt.mobile.domain.ticket.repository.GiftTicketOrderRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketDeliveryRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 支付成功业务处理（重构版 - 使用策略模式，基于flatProductType）
 * 
 * <AUTHOR> Fengping
 * @since 2025/1/27
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentSuccessCaseServiceImpl implements PaymentSuccessCaseService {

    private final OrderRepository orderRepository;
    private final DistributedLock distributedLock;
    private final PaymentSuccessStrategyFactory strategyFactory;
    private final RedisUtil redisUtil;
    private final GiftTicketOrderRepository giftTicketOrderRepository;
    private final TicketRepository ticketRepository;
    private final TicketDeliveryRepository ticketDeliveryRepository;
    private final GlobalGateWay globalGateWay;
    private final CustomerRepository customerRepository;
    private final TicketPlatformConfig ticketPlatformConfig;
    private final TicketPlatformGateway ticketPlatformGateway;

    private static final String PAYMENT_SUCCESS_LOCK_PREFIX = "payment_success_lock:";

    private static final String SEND_TICKETS_LOCK_PREFIX = "gift_tickets_send_lock:";

    @Override
    public void handlePaymentSucceeded(PaymentSuccessMessage event) {
        log.info("开始处理支付已成功事件，订单号：{}", event.getOrderNo());

        String orderNo = event.getOrderNo();
        String lockKey = PAYMENT_SUCCESS_LOCK_PREFIX + orderNo;

        // 加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                throw new BizException("获取支付成功处理锁失败，订单号：" + orderNo);
            }

            // 1. 查询订单信息
            OrderInfoEntity order = orderRepository.findByOrderNo(orderNo);
            if (order == null) {
                log.warn("订单不存在，订单号：{}", orderNo);
                return;
            }

            // 2. 幂等校验：检查发货状态，如果不是待发货状态或者发货中（为什么发货中也校验通过，因为套餐有部分发货的情况），则不处理
            if (!DeliveryStatusEnum.UNDELIVERED.getCode().equals(order.getDeliveryStatus().byteValue())
                    && !DeliveryStatusEnum.DELIVERING.getCode().equals(order.getDeliveryStatus().byteValue())) {
                log.info("订单发货状态不是待发货，跳过处理，订单号：{}，当前发货状态：{}",
                        orderNo, order.getDeliveryStatus());
                return;
            }

            // 3. 获取扁平化产品类型，选择对应的处理策略
            Integer flatProductType = order.getFlatProductType();

            if (flatProductType == null) {
                log.error("扁平化产品类型为空，订单号：{}", orderNo);
                throw new BizException("扁平化产品类型为空");
            }

            // 4. 使用策略处理业务逻辑
            PaymentSuccessStrategy strategy = strategyFactory.getStrategy(flatProductType);
            strategy.handle(order);

            log.info("成功处理支付已成功事件，订单号：{}，扁平化产品类型：{}",
                    orderNo, flatProductType);

        } catch (BizException e) {
            log.error("处理支付成功事件业务异常，订单号：{}，错误信息：{}", orderNo, e.getMsg(), e);
            throw e;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("获取分布式锁被中断，订单号：" + orderNo);
        } catch (Exception e) {
            log.error("处理支付成功事件系统异常，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            throw e;
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public void handleGiftTicketsSend(PaymentSuccessMessage event) {
        log.info("开始处理赠券发放事件，订单号：{}", event.getOrderNo());

        String orderNo = event.getOrderNo();

        // 1. 查询缓存
        String giftTicketCacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + orderNo;
        String giftTicketJson = redisUtil.get(giftTicketCacheKey);

        // 2. 缓存不存在直接返回
        if (!StringUtils.hasText(giftTicketJson)) {
            log.info("订单赠券缓存不存在，跳过处理，订单号：{}", orderNo);
            return;
        }

        // 3. 加分布式锁
        String lockKey = SEND_TICKETS_LOCK_PREFIX + orderNo;
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey);
            if (!lockAcquired) {
                log.warn("获取赠券发放锁失败，订单号：{}", orderNo);
                return;
            }

            // 4. 查询订单信息
            OrderInfoEntity order = orderRepository.findByOrderNo(orderNo);
            if (order == null) {
                log.warn("订单不存在，订单号：{}", orderNo);
                return;
            }

            // 5. 解析赠券信息
            List<GiftTicketInfo> giftTicketInfos = JSON.parseArray(giftTicketJson, GiftTicketInfo.class);
            if (giftTicketInfos == null || giftTicketInfos.isEmpty()) {
                log.info("订单赠券信息为空，订单号：{}", orderNo);
                return;
            }

            // 6. 处理赠券发放
            processGiftTickets(order, giftTicketInfos);

            // 7. 删除缓存  不管发不发成功 都不再重试
            redisUtil.delete(giftTicketCacheKey);

            log.info("赠券发放处理完成，订单号：{}", orderNo);

        } catch (BizException e) {
            log.error("处理赠券发放业务异常，订单号：{}，错误信息：{}", orderNo, e.getMsg(), e);
            throw e;
        } catch (Exception e) {
            log.error("处理赠券发放系统异常，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            throw e;
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    /**
     * 处理赠券发放
     */
    private void processGiftTickets(OrderInfoEntity order, List<GiftTicketInfo> giftTicketInfos) {
        List<GiftTicketOrderEntity> giftTicketOrders = new ArrayList<>();

        CustomerEntity customer = customerRepository.selectCustomerById(order.getCustomerId());

        // 遍历赠券信息
        for (int i = 0; i < giftTicketInfos.size(); i++) {
            GiftTicketInfo giftTicketInfo = giftTicketInfos.get(i);

            // 生成券订单号：T_订单号_ticketid_第i个
            String ticketOrderNo = "T_" + order.getOrderNo() + "_" + giftTicketInfo.getTicketId() + "_" + (i + 1);

            GiftTicketOrderEntity giftTicketOrder = new GiftTicketOrderEntity();
            giftTicketOrder.setTicketOrderNo(ticketOrderNo);
            giftTicketOrder.setOrderId(order.getId());
            giftTicketOrder.setOrderNo(order.getOrderNo());
            giftTicketOrder.setTicketType(giftTicketInfo.getTicketType());
            giftTicketOrder.setCustomerId(order.getCustomerId());
            giftTicketOrder.setPushStatus(0);
            giftTicketOrder.setCenterCouponId(giftTicketInfo.getCenterCouponId());
            giftTicketOrder.setTicketId(giftTicketInfo.getTicketId());
            giftTicketOrder.setTicketName(giftTicketInfo.getTicketName());
            giftTicketOrder.setSpecPicUrl(giftTicketInfo.getSpecPicUrl());
            giftTicketOrder.setTicketBrandId(giftTicketInfo.getBrandId());
            giftTicketOrder.setShouldNum(giftTicketInfo.getTicketNum());
            giftTicketOrder.setActualNum(0);
            giftTicketOrder.setCreateTime(LocalDateTime.now());
            giftTicketOrder.setModTime(LocalDateTime.now());
            giftTicketOrder.setDelFlag(0);
            //调用营销中台发券接口发券
            giveOutGiftTicket(giftTicketOrder, giftTicketInfo, customer);


            giftTicketOrders.add(giftTicketOrder);
        }


        // 批量保存信息到gift_ticket_order
        giftTicketOrderRepository.batchSave(giftTicketOrders);
    }

    private void giveOutGiftTicket(GiftTicketOrderEntity giftTicketOrder,
                                   GiftTicketInfo giftTicketInfo, CustomerEntity customer) {
        try {
            // 封装发券命令对象
            String userCode;
            if (giftTicketOrder.getTicketType() == 1) {
                userCode = customer.getUnionid();
            } else {
                userCode = String.valueOf(customer.getId());
            }
            GiveOutTicketCmd cmd = GiveOutTicketCmd.builder()
                    .userCode(userCode)
                    .ticketId(giftTicketInfo.getCenterTicketId())
                    .number(giftTicketInfo.getTicketNum())
                    .timestamp(System.currentTimeMillis())
                    .nonce(UUID.randomUUID().toString().replaceAll("-", ""))
                    .build();
            if (giftTicketOrder.getTicketType() == 2 || giftTicketOrder.getTicketType() == 3) {
                cmd.setNotifyUrl(ticketPlatformConfig.getGiveNotifyUrl());
            }

            // 调用发券接口
            List<String> ticketNumbers = ticketPlatformGateway.giveOutTicket(cmd, (code, msg) -> {
                // code != 200 时自动执行此回调
                log.warn("发券失败：券订单号={}, code={}, msg={}", giftTicketOrder.getTicketOrderNo(), code, msg);
                giftTicketOrder.setPushStatus(3);
                giftTicketOrder.setPushErrorMsg("发券失败：" + msg);
            });

            // 如果接口成功返回券号列表
            if (ticketNumbers != null && !ticketNumbers.isEmpty()) {
                giftTicketOrder.setActualNum(giftTicketInfo.getTicketNum());
                if (giftTicketInfo.getTicketType() == 2 || giftTicketInfo.getTicketType() == 3) {
                    // 卡管的券，发券接口调用成功后，状态是推送中，等待回调获卡
                    giftTicketOrder.setPushStatus(1);
                } else {
                    // 推送成功
                    giftTicketOrder.setPushStatus(2);
                }

                giftTicketOrder.setExternalOrderNo(ticketNumbers.get(0));
                giftTicketOrder.setCenterTicketCouponNumber(String.join(";", ticketNumbers));
                // 如果券类型是4 直接保存到ticket_delivery
                if (giftTicketInfo.getTicketType() == 4) {
                    TicketEntity ticket = ticketRepository.getTicketById(giftTicketInfo.getTicketId());
                    saveTicketDelivery(giftTicketOrder, ticket, ticketNumbers);
                }
            } else if (giftTicketOrder.getPushStatus() == null || giftTicketOrder.getPushStatus() != 3) {
                // 没进 onError 回调的情况下且没有返回券号，视为失败
                giftTicketOrder.setPushStatus(3);
                giftTicketOrder.setPushErrorMsg("发券失败：返回空券号列表");
            }

        } catch (Exception e) {
            log.error("营销中台发券异常，券订单号：{}，错误信息：{}", giftTicketOrder.getTicketOrderNo(), e.getMessage(), e);
            giftTicketOrder.setPushStatus(3);
            giftTicketOrder.setPushErrorMsg("发券异常：" + e.getMessage());
        }

    }

    private void saveTicketDelivery(GiftTicketOrderEntity giftTicketOrder, TicketEntity ticket, List<String> ticketNumbers) {
        List<TicketDeliveryEntity> ticketDeliveries = new ArrayList<>();
        Date now = new Date();
        Date beginTime = DateUtil.beginOfDay(now);
        Date  ticketValidDate = DateUtil.offsetDay(beginTime, ticket.getTicketValidDate());

        ticketNumbers.forEach(ticketNumber -> {
            TicketDeliveryEntity ticketDelivery = new TicketDeliveryEntity();
            ticketDelivery.setOrderId(giftTicketOrder.getOrderId());
            ticketDelivery.setTicketOrderNo(giftTicketOrder.getTicketOrderNo());
            ticketDelivery.setOrderNo(giftTicketOrder.getOrderNo());
            ticketDelivery.setExternalOrderNo(giftTicketOrder.getExternalOrderNo());
            ticketDelivery
                    .setTicketType(
                            giftTicketOrder.getTicketType() != null ? ticket.getTicketType().byteValue() : null);
            ticketDelivery.setCustomerId(giftTicketOrder.getCustomerId());
            ticketDelivery.setStatus((byte) 1); // 1-未核销

            // 券信息
            ticketDelivery.setCenterCouponId(giftTicketOrder.getCenterCouponId());
            ticketDelivery.setTicketId(ticket.getTicketId());
            ticketDelivery.setTicketName(ticket.getTicketName());
            ticketDelivery.setOffsetPageType(
                    ticket.getOffsetPageType() != null ? ticket.getOffsetPageType().byteValue() : null);

            // 品牌信息
            ticketDelivery.setBrandName(ticket.getBrandName());
            ticketDelivery.setOffsetLogo(ticket.getOffsetLogo());

            ticketDelivery.setIsBirthdayTicket(0);

            ticketDelivery.setCenterTicketCouponNumber(ticketNumber);

            ticketDelivery.setTicketCatCode(ticket.getTicketCatCode());
            ticketDelivery.setDiscountAmount(ticket.getDiscountAmount());
            ticketDelivery.setThresholdAmount(ticket.getThresholdAmount());
            ticketDelivery.setTicketValidDate(ticketValidDate);
            ticketDelivery.setCreateTime(now);
            ticketDelivery.setModTime(now);

            ticketDeliveries.add(ticketDelivery);

        });
        ticketDeliveryRepository.batchSave(ticketDeliveries);
    }

    /**
     * 处理卡管券发放
     */
    private void handleCouponTicket(GiftTicketOrderEntity giftTicketOrder, OrderInfoEntity order,
            GiftTicketInfo giftTicketInfo) {
        try {
            // 根据券类型获取对应的策略
            CouponTypeEnum couponType = getCouponTypeByTicketType(giftTicketInfo.getTicketType());
            if (couponType == null) {
                giftTicketOrder.setPushStatus(3); // 有异常
                giftTicketOrder.setPushErrorMsg("不支持的券类型：" + giftTicketInfo.getTicketType());
                return;
            }

            CouponPlatformStrategy couponPlatform = CouponPlatformFactory.getCouponPlatform(couponType);
            CouponCreateOrderResponseDTO response = couponPlatform.createTicketOrder(
                    giftTicketOrder.getTicketOrderNo(), order, giftTicketInfo);

            if (response != null && response.getCode() == 0) {
                // 发券成功
                giftTicketOrder.setExternalOrderNo(response.getExternalOrderNo());
                giftTicketOrder.setActualNum(giftTicketInfo.getTicketNum());
                giftTicketOrder.setPushStatus(2); // 推送成功
            } else {
                // 发券失败
                giftTicketOrder.setPushStatus(3); // 有异常
                giftTicketOrder.setPushErrorMsg(response != null ? response.getMsg() : "发券失败");
            }
        } catch (Exception e) {
            log.error("卡管发券异常，券订单号：{}，错误信息：{}", giftTicketOrder.getTicketOrderNo(), e.getMessage(), e);
            giftTicketOrder.setPushStatus(3); // 有异常
            giftTicketOrder.setPushErrorMsg("发券异常：" + e.getMessage());
        }
    }

    /**
     * 处理全球购券发放
     */
    private void handleGlobalTickets(List<GiftTicketOrderEntity> giftTicketOrders, OrderInfoEntity order,
            List<GlobalTicketSendRequest.CouponInfo> globalTicketInfos) {
        try {
            // 获取用户unionId
            CustomerEntity customer = customerRepository.selectCustomerById(order.getCustomerId());
            if (customer == null || customer.getUnionid() == null) {
                log.error("用户不存在或unionId为空，订单号：{}，用户ID：{}", order.getOrderNo(), order.getCustomerId());
                updateGlobalTicketOrdersWithError(giftTicketOrders, "用户不存在或unionId为空");
                return;
            }

            GlobalTicketSendRequest request = new GlobalTicketSendRequest();
            request.setUnionId(customer.getUnionid());
            request.setCouponInfoList(globalTicketInfos);

            GlobalTicketSendResponse response = globalGateWay.sendGlobalTicket(request);

            if (response != null && response.getCode() == 0) {
                // 发券成功，更新对应的赠券订单状态
                updateGlobalTicketOrders(giftTicketOrders, response, true);
            } else {
                // 发券失败
                String errorMsg = response != null ? response.getMsg() : "全球购发券失败";
                updateGlobalTicketOrders(giftTicketOrders, response, false);
                log.error("全球购发券失败，订单号：{}，错误信息：{}", order.getOrderNo(), errorMsg);
            }
        } catch (Exception e) {
            log.error("全球购发券异常，订单号：{}，错误信息：{}", order.getOrderNo(), e.getMessage(), e);
            // 更新所有全球购券订单为异常状态
            updateGlobalTicketOrdersWithError(giftTicketOrders, "发券异常：" + e.getMessage());
        }
    }

    /**
     * 更新全球购券订单状态
     */
    private void updateGlobalTicketOrders(List<GiftTicketOrderEntity> giftTicketOrders,
            GlobalTicketSendResponse response, boolean success) {
        Map<String, GlobalTicketSendResponse.SendResult> resultMap = new HashMap<>();
        if (response != null && response.getResponse() != null) {
            for (GlobalTicketSendResponse.SendResult result : response.getResponse()) {
                resultMap.put(result.getActivityId(), result);
            }
        }

        for (GiftTicketOrderEntity order : giftTicketOrders) {
            if (order.getTicketType() == 1) { // 全球购券
                GlobalTicketSendResponse.SendResult result = resultMap.get(order.getCenterCouponId());
                if (success && result != null) {
                    order.setActualNum(result.getActualCouponNum());
                    order.setPushStatus(2); // 推送成功
                    if (StringUtils.hasText(result.getMsg())) {
                        order.setPushErrorMsg(result.getMsg());
                    }
                } else {
                    order.setPushStatus(3); // 有异常
                    order.setPushErrorMsg(result != null ? result.getMsg() : "全球购发券失败");
                }
            }
        }
    }

    /**
     * 更新全球购券订单为异常状态
     */
    private void updateGlobalTicketOrdersWithError(List<GiftTicketOrderEntity> giftTicketOrders, String errorMsg) {
        for (GiftTicketOrderEntity order : giftTicketOrders) {
            if (order.getTicketType() == 1) { // 全球购券
                order.setPushStatus(3); // 有异常
                order.setPushErrorMsg(errorMsg);
            }
        }
    }

    /**
     * 根据券类型获取卡管券类型枚举
     */
    private CouponTypeEnum getCouponTypeByTicketType(Integer ticketType) {
        if (ticketType == 0 || ticketType == 2) {
            return CouponTypeEnum.REGULAR; // 默认使用普通卡券策略
        }
        return null;
    }
}
