package com.jsrxjt.common.core.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ApiResponse<T> implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;

    // 状态码
    private Integer status;
    // 消息
    private String message;
    // 数据
    private T data;

    // 饿了么状态码
    private Integer code;

    // 构造方法
    public ApiResponse() {}
    
    public ApiResponse(Integer status, String message, T data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    // 快速生成成功响应的静态方法
    public static <T> ApiResponse<T> success() {
        return success(null);
    }

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<T>(200, "SUCCESS", data);
    }

    public static <T> ApiResponse<T> success0(T data) {
        return new ApiResponse<T>(0, "SUCCESS", data);
    }

    public static <T> ApiResponse<T> success(T data,String message) {
        return new ApiResponse<T>(200, message, data);
    }

    // 快速生成失败响应的静态方法
    public static <T> ApiResponse<T> fail(Integer code, String message) {
        return new ApiResponse<T>(code, message, null);
    }

    public static <T> ApiResponse<T> fail(String message) {
        return fail(500, message);
    }

    public static <T> ApiResponse<T> successForCode(T data) {
        ApiResponse<T> response = new ApiResponse<T>();
        response.setCode(200);
        response.setMessage("SUCCESS");
        response.setData(data);
        return response;
    }

    public static <T> ApiResponse<T> successForCode() {
        return successForCode(null);
    }

    public static <T> ApiResponse<T> failForCode(String message) {
        ApiResponse<T> response = new ApiResponse<T>();
        response.setCode(500);
        response.setMessage(message);
        return response;
    }

}