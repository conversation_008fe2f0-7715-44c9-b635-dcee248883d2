package com.jsrxjt.mobile.domain.ticket.gateway.cmd;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class GiveOutTicketCmd {
    /**
     * 用户code 全球购发券对应unionId，其他为customerId
     */
    private String userCode;
    /**
     * 优惠券ID
     */
    private String ticketId;
    /**
     * 优惠券数量
     */
    private Integer number;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 随机串
     */
    private String nonce;

    /**
     * 回调地址url 当卡券类型2.商家自发优惠券、3.线下优惠券·瑞祥代发优惠券必填此参数，接入发券回调
     */
    private String notifyUrl;
}