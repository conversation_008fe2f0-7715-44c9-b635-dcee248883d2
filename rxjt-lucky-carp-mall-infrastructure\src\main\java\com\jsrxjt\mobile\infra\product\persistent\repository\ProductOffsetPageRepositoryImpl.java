package com.jsrxjt.mobile.infra.product.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jsrxjt.mobile.domain.product.entity.ProductExplainEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductOffsetPageEntity;
import com.jsrxjt.mobile.domain.product.repository.ProductExplainRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductOffsetPageRepository;
import com.jsrxjt.mobile.infra.product.persistent.mapper.ProductExplainMapper;
import com.jsrxjt.mobile.infra.product.persistent.mapper.ProductOffsetPageMapper;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductExplainPO;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductOffsetPagePO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 产品品牌Repository实现类
 * 
 * <AUTHOR> Fengping
 * @since 2025-06-17
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ProductOffsetPageRepositoryImpl implements ProductOffsetPageRepository {

    private final ProductOffsetPageMapper productOffsetPageMapper;
    /**
     * 根据SPUID查询说明信息
     *
     * @param spuId       spuId
     * @param productType
     * @return 品牌实体
     */
    @Override
    public List<ProductOffsetPageEntity> findBySpuId(Long spuId, Byte productType) {
        List<ProductOffsetPagePO> bySpuId = productOffsetPageMapper.findBySpuId(spuId, productType);
        return BeanUtil.copyToList(bySpuId, ProductOffsetPageEntity.class);
    }

    @Override
    public List<ProductOffsetPageEntity> findBySpuIdWithDel(Long spuId, Byte productType) {
        // 查询包括已删除的记录
        List<ProductOffsetPagePO> bySpuId = productOffsetPageMapper.selectBySpuIdWithDel(spuId, productType);
        return BeanUtil.copyToList(bySpuId, ProductOffsetPageEntity.class);
    }
}