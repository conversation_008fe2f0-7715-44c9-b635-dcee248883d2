package com.jsrxjt.mobile.infra.captcha.gateway;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.domain.captcha.gateway.CaptchaGateway;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaRequest;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaResponse;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaVerifyRequest;
import com.jsrxjt.mobile.domain.captcha.gateway.model.CaptchaVerifyResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 验证码网关实现类
 * 基于aj captcha实现
 * 
 * <AUTHOR>
 * @since 2025/11/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CaptchaGatewayImpl implements CaptchaGateway {

    private final CaptchaService captchaService;

    @Override
    public CaptchaResponse generateCaptcha(CaptchaRequest request) {
        // 转换为内部模型
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaType(request.getCaptchaType());
        captchaVO.setClientUid(request.getClientUid());
        captchaVO.setBrowserInfo(request.getBrowserInfo());
        captchaVO.setTs(request.getTimestamp());

        // 调用底层服务
        log.info("aj验证码 浏览器信息={}", captchaVO.getBrowserInfo());
        try {
            ResponseModel response = captchaService.get(captchaVO);
            log.info("aj验证码 响应 状态 {}", response.isSuccess());
            // 转换为领域模型
            if (response.isSuccess()) {
                CaptchaVO data = (CaptchaVO) response.getRepData();
                return new CaptchaResponse(
                        data.getToken(),
                        data.getOriginalImageBase64(),
                        data.getJigsawImageBase64(),
                        data.getWordList(),
                        data.getSecretKey(),
                        true
                );
            }
            log.warn("aj验证码 获取验证码失败，失败原因：code {}, msg {}", response.getRepCode(),response.getRepMsg());
            throw new BizException(response.getRepMsg());
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("aj验证码 获取验证码异常", e);
            throw new BizException("获取验证码异常");
        }

    }

    @Override
    public CaptchaVerifyResponse verifyCaptcha(CaptchaVerifyRequest request) {
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaType(request.getCaptchaType());
        captchaVO.setToken(request.getToken());
        captchaVO.setPointJson(request.getCoordinates());
        captchaVO.setClientUid(request.getClientUid());
        captchaVO.setBrowserInfo(request.getBrowserInfo());
        log.info("aj验证码 验证 请求参数 token: {}, pointJson: {}", captchaVO.getToken(), captchaVO.getPointJson());

        ResponseModel response = captchaService.check(captchaVO);

        log.info("aj验证码 验证 响应状态 {}", response.isSuccess());
        if (response.isSuccess()) {
            return CaptchaVerifyResponse.success();
        } else {
            return CaptchaVerifyResponse.failure(response.getRepMsg(), response.getRepCode());
        }

    }
}
