package com.jsrxjt.mobile.biz.mq;


import com.jsrxjt.mobile.biz.mq.constants.DestinationConstants;

public interface QueueSenderService {
	/**
	 * 队列消息发送服务
	 * @param destinationName
	 *            队列内容
	 * @param messageBody
	 *            队列消息内容

	 */
	public void send(DestinationConstants.DestinationName destinationName, final String messageBody);

	/**
	 * 同步发送消息
	 * @param destinationName 目的地
	 * @param tag 业务标签
	 * @param messageBody 消息体
	 */
	public void send(DestinationConstants.DestinationName destinationName, String tag,final String messageBody);

	/**
	 * 同步队列发送接口，延迟发送队列
	 * @param destinationName 队列目的地
	 * @param messageBody 消息内容
	 * @param delayMillisecond 延迟毫秒数，必须时1000的倍数
	 */
	public void send(DestinationConstants.DestinationName destinationName,final String messageBody, long delayMillisecond);


	/**
	 * 携带Keys的同步延迟发送
	 * @param destinationName 目的他
	 * @param tag 业务标签
	 * @param messageBody 消息体
	 * @param delayMillisecond 延迟时间 毫秒 1000的倍数
	 */
	public void send(DestinationConstants.DestinationName destinationName,String tag, final String messageBody, long delayMillisecond);

	/**
	 * 异步发送接口
	 * @param destinationName 发送目的地
	 * @param messageBody 消息体
	 */
	public void asyncSend(DestinationConstants.DestinationName destinationName,final String messageBody);

	/**
	 * 异步携带keys的发送接口
	 * @param destinationName 目的地
	 * @param tag 业务标签
	 * @param messageBody 消息体
	 */
	public void asyncSend(DestinationConstants.DestinationName destinationName,String tag, final String messageBody);

	/**
	 * 异步延迟发送消息接口
	 * @param destinationName 目的地
	 * @param messageBody 消息体
	 * @param delayMillisecond 延迟毫秒数
	 */
	public void asyncSend(DestinationConstants.DestinationName destinationName,final String messageBody,long delayMillisecond);

}
