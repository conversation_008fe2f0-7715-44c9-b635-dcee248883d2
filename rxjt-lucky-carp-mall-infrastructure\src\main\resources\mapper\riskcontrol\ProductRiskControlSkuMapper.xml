<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.riskcontrol.persistent.mapper.ProductRiskControlSkuMapper">

    <select id="getRiskDisableProducts"
            resultType="com.jsrxjt.mobile.infra.riskcontrol.persistent.po.ProductRiskControlSkuPO">
        SELECT
            b.id,
            b.risk_id,
            b.risk_type,
            b.product_spu_id,
            b.product_sku_id,
            b.product_type,
            b.risk_product_type,
            b.commission_fee,
            b.limit_num_per_day,
            b.limit_num_per_month,
            b.user_monthly_limit_amount,
            b.mod_id,
            b.mod_time,
            b.create_id,
            b.create_time,
            b.del_flag
        FROM product_risk_control_account a
        inner join product_risk_control_sku b on a.risk_id = b.risk_id
        inner join product_risk_control c on c.id = a.risk_id
        where a.del_flag = 0
        and b.del_flag = 0
        and a.risk_type = 1
        and c.status = 1
        <trim prefix="AND (" suffix=")" prefixOverrides="OR">
            <if test="accountName != null and accountName != ''">
                OR a.account_name = #{accountName}
            </if>
            <if test="companyId != null">
                OR a.company_id = #{companyId}
            </if>
        </trim>
        <if test="productType != null">
            AND b.product_type = #{productType}
        </if>

        <if test="productSpuId != null">
            AND b.product_spu_id = #{productSpuId}
        </if>
    </select>
</mapper>
