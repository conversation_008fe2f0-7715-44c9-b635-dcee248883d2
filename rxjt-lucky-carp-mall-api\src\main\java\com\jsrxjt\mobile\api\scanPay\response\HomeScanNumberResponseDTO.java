package com.jsrxjt.mobile.api.scanPay.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 卡号dto
 * @Author: ZY
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "二维码卡号dto")
public class HomeScanNumberResponseDTO {
    @Schema(description = "码号")
    private String code;

    @Schema(description = "是否已开通免密支付 type =4 必须开通免密")
    private Integer openPasswordFreePayment=0;

    @Schema(description = "到期时间-秒")
    private int expireCodeTime;

    @Schema(description = "展码付的二维码下面的提示，注意：非有效期时间提示,为空前端不显示")
    private String scanPayToast;
}
