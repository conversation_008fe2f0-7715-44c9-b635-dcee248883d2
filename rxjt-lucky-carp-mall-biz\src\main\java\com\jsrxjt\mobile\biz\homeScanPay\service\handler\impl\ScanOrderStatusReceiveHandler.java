package com.jsrxjt.mobile.biz.homeScanPay.service.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.distribution.dto.request.ScanOrderStatusWssReceiveDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.ScanPayWssReceiveDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.ScanPayOrderStatusDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.ScanPayReceiverResponseDTO;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.scanPay.request.UserCheckPassRequestDTO;
import com.jsrxjt.mobile.api.scanPay.request.UserHomePayRequestDTO;
import com.jsrxjt.mobile.api.scanPay.types.PosV2OrderStatusTypeEnum;
import com.jsrxjt.mobile.api.scanPay.types.ScanWsReceiveTypeEnum;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.homeScanPay.service.HomeScanOrderService;
import com.jsrxjt.mobile.biz.homeScanPay.service.handler.ScanReceiveHandler;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.payment.gateway.PosV2PaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.OfflinePayRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 扫码付支付接收处理
 * <AUTHOR>
 * @date 2025/11/04
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ScanOrderStatusReceiveHandler implements ScanReceiveHandler<ScanOrderStatusWssReceiveDTO> {

    private final CustomerRepository customerRepository;

    private final OrderRepository orderRepository;

    private final PosV2PaymentGateway posV2PaymentGateway;

    private final HomeScanOrderService homeScanOrderService;

    @Override
    public boolean supports(ScanWsReceiveTypeEnum type) {
        return ScanWsReceiveTypeEnum.ORDER_PAY_STATUS.equals(type);
    }

    @Override
    public ScanPayReceiverResponseDTO handle(ScanOrderStatusWssReceiveDTO data) {
        ScanPayReceiverResponseDTO response = new ScanPayReceiverResponseDTO();
        response.setType("WSS_ERROR"); //默认失败
        if (data.getCustomerId() == null || data.getTradeNo() == null){
            response.setMsg(Status.NULL_PARAM.getMessage());
            response.setCode(Status.NULL_PARAM.getCode());
            return response;
        }
        CustomerEntity customer = customerRepository.selectCustomerById(data.getCustomerId());
        if (customer == null || customer.getVipId() == null){
            response.setMsg(Status.USER_INFO_ERROR.getMessage());
            response.setCode(Status.USER_INFO_ERROR.getCode());
            return response;
        }
        try {
            BaseResponse baseResponse = posV2PaymentGateway.orderStatus(customer.getVipId(), data.getTradeNo());
            response.setResponse(baseResponse.getResponse());
            if (baseResponse.getResponse() != null){
                ScanPayOrderStatusDTO scanPayOrderStatusDTO = (ScanPayOrderStatusDTO) baseResponse.getResponse();
                homeScanOrderService.scanOrderStatusUpdate(data.getTradeNo(), PosV2OrderStatusTypeEnum.getByType(scanPayOrderStatusDTO.getTradeStatus()));
            }
        } catch (BizException e) {
            response.setType("ORDER_STATUS_ERROR");
            response.setMsg(e.getMsg());
            response.setCode(e.getCode());
            return response;
        } catch (Exception e){
            response.setType("ORDER_STATUS_ERROR");
            response.setCode(Status.WX_TRADE_PAYERROR.getCode());
            response.setMsg(Status.WX_TRADE_PAYERROR.getMessage());
            return response;
        }
        response.setCode(Status.LSucceed.getCode());
        response.setType("WSS_SUCCESS");
        return response;
    }
}
