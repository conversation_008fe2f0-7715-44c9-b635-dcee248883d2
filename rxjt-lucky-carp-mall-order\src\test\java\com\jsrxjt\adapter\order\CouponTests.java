package com.jsrxjt.adapter.order;

import com.coupon.rep.common.CouponResult;
import com.coupon.req.service.CouponService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
public class CouponTests {

    @Autowired
    private CouponService couponService;

    @Test
    public void testGetCardCode() {
        CouponResult couponResult = couponService.getCard("2509101200479206744657");
        System.out.println(couponResult.getCode());
    }
}
