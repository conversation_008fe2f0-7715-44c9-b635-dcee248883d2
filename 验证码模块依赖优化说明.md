# 验证码模块依赖优化说明

## 优化背景

根据DDD架构原则和项目的分层规范，将验证码模块的依赖从微服务层移动到基础设施层，实现更合理的依赖管理。

## 优化前后对比

### 优化前
```
微服务Demo模块
├── rxjt-lucky-carp-mall-infrastructure (依赖)
└── rxjt-lucky-carp-mall-common-captcha (直接依赖)
```

### 优化后
```
微服务Demo模块
└── rxjt-lucky-carp-mall-infrastructure (依赖)
    └── rxjt-lucky-carp-mall-common-captcha (传递依赖)
```

## 架构优势

### 1. 依赖层次清晰
- **基础设施层**: 管理所有基础设施相关依赖
- **微服务层**: 只需引入基础设施层，自动获得所有基础功能
- **避免重复**: 不同微服务不需要重复声明相同的基础依赖

### 2. 版本管理统一
- **集中管理**: 验证码版本在基础设施层统一管理
- **版本一致**: 所有使用验证码的模块版本自动保持一致
- **升级简单**: 只需在基础设施层升级版本

### 3. 符合DDD原则
- **基础设施层**: 负责提供技术基础设施，包括验证码服务
- **应用层**: 专注业务逻辑，不关心具体的技术实现
- **接口层**: 只负责HTTP接口实现

## 文件变更详情

### 修改的文件

1. **rxjt-lucky-carp-mall-infrastructure/pom.xml**
   ```xml
   <!-- 新增验证码依赖 -->
   <dependency>
       <groupId>com.jsrxjt</groupId>
       <artifactId>rxjt-lucky-carp-mall-common-captcha</artifactId>
   </dependency>
   ```

2. **rxjt-lucky-carp-mall-microservicesdemo/pom.xml**
   ```xml
   <!-- 移除直接的验证码依赖 -->
   <!-- 通过infrastructure传递获得 -->
   ```

## 使用指南

### 1. 新微服务集成验证码

**只需引入基础设施层依赖**：
```xml
<dependency>
    <groupId>com.jsrxjt</groupId>
    <artifactId>rxjt-lucky-carp-mall-infrastructure</artifactId>
    <version>${project.version}</version>
</dependency>
```

**自动获得验证码功能**：
```java
@Autowired
private CaptchaService captchaService; // 自动可用

// 直接使用验证码服务
ResponseModel response = captchaService.get(captchaVO);
```

### 2. HTTP接口实现

参考 `rxjt-lucky-carp-mall-microservicesdemo` 中的实现：
- `CaptchaController` - REST接口
- `CaptchaUtil` - 工具类

### 3. 配置管理

在 `application.yml` 中配置：
```yaml
jsrxjt:
  captcha:
    enabled: true
    type: blockPuzzle
    cache-type: redis
```

## 依赖传递链

```
微服务模块
└── infrastructure (直接依赖)
    ├── domain (传递依赖)
    ├── common-mybatis (传递依赖)
    ├── common-core (传递依赖)
    ├── common-captcha (传递依赖) ← 新增
    └── 其他基础设施依赖...
```

## 最佳实践

### 1. 依赖管理原则
- **基础设施层**: 管理所有技术基础设施依赖
- **业务层**: 只引入必要的业务依赖
- **接口层**: 只引入接口相关依赖

### 2. 版本管理
- **统一版本**: 在父pom中管理版本号
- **传递依赖**: 通过基础设施层传递基础功能
- **按需引入**: 特殊需求可以直接引入特定依赖

### 3. 模块职责
- **Common模块**: 提供可复用的基础功能
- **Infrastructure模块**: 集成和管理基础设施
- **Business模块**: 实现业务逻辑
- **Interface模块**: 提供外部接口

## 优化效果

### 1. 简化依赖管理
- 微服务只需关心业务依赖
- 基础功能自动可用
- 减少配置复杂度

### 2. 提高一致性
- 所有模块使用相同版本的基础组件
- 避免版本冲突
- 统一升级路径

### 3. 增强可维护性
- 依赖关系清晰
- 职责分离明确
- 便于问题排查

这次依赖优化使项目的架构更加清晰，符合DDD分层原则，为项目的长期发展提供了良好的基础。
