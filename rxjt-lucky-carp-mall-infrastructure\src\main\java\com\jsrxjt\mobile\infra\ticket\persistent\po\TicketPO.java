package com.jsrxjt.mobile.infra.ticket.persistent.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-08-15 15:25
 * @Version: 1.0
 */
@Data
@TableName("ticket")
public class TicketPO {
    @Schema(description = "id")
    @TableId(value = "ticket_id", type = IdType.AUTO)
    private Long ticketId;

    @Schema(description = "优惠券名称")
    @TableField("ticket_name")
    private String ticketName;

    @Schema(description = "优惠券类型: 0商家自发优惠券 1全球购线上商城优惠券 2瑞祥代发优惠券 3门店优惠券")
    @TableField("ticket_type")
    private Integer ticketType;

    @Schema(description = "优惠券品牌id")
    @TableField("brand_id")
    private Long brandId;

    @Schema(description = "营销中台的品牌名")
    @TableField("brand_name")
    private String brandName;

    @Schema(description = "卡管/商城coupon_id")
    @TableField("center_coupon_id")
    private String centerCouponId;

    @Schema(description = "营销中台的优惠券id")
    @TableField("center_ticket_id")
    private String centerTicketId;

    @Schema(description = "优惠券分类 1满减券 2无门槛券")
    @TableField("ticket_cat_code")
    private Integer ticketCatCode;

    @TableField("discount_amount")
    private BigDecimal discountAmount;//优惠金额 (ticket_type值为3或4存在此值)
    @TableField("threshold_amount")
    private BigDecimal thresholdAmount;//满减券优惠门槛金额 (ticket_type值为3或4存在此值)

    @Schema(description = "自发券后有效期日")
    @TableField("ticket_valid_date")
    private Integer ticketValidDate;

    @Schema(description = "规格图片url")
    @TableField("spec_pic_url")
    private String specPicUrl;

    @Schema(description = "使用说明")
    @TableField("use_manual")
    private String useManual;

    @Schema(description = "核销页样式 0卡号 1转吗")
    @TableField("offset_page_type")
    private Integer offsetPageType;

    /**
     * 核销logo
     */
    @TableField("offset_logo")
    private String offsetLogo;

    @Schema(description = "状态 0禁用 1启用")
    @TableField("status")
    private Integer status;

    @Schema(description = "营销中台的状态 0禁用 1启用")
    @TableField("center_status")
    private Integer centerStatus;

    private Integer manualDown;//是否手动下架卡券(0:否 1:是)

    @Schema(description = "上线城市：0非全国  1全国")
    @TableField("is_nationwide")
    private Integer isNationwide;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "创建人id")
    @TableField("create_id")
    private Long createId;

    @Schema(description = "编辑人id")
    @TableField("mod_id")
    private Long modId;

    @Schema(description = "编辑时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modTime;

    @Schema(description = "是否删除标志(0:否 1:是)")
    @TableField("del_flag")
    private Integer delFlag;

    @Schema(description = "删除人id")
    @TableField("del_id")
    private Long delId;

    @Schema(description = "删除时间")
    @TableField("del_time")
    private Date delTime;

    @Schema(description = "创建人姓名")
    @TableField("create_name ")
    private String createName;

    @Schema(description = "修改人姓名")
    @TableField("mod_name")
    private String modName;
}
