package com.jsrxjt.mobile.api.distribution.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ScanPayCardListDTO {
    @JsonProperty("card_type")
    private String cardType;
    @JsonProperty("pay_price")
    private int payPrice;
    @JsonProperty("Cards")
    private List<String> Cards;
}
