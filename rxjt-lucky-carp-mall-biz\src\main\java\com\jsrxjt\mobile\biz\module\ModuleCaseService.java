package com.jsrxjt.mobile.biz.module;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.module.dto.request.TabPageRequest;
import com.jsrxjt.mobile.api.module.dto.response.ModuleDetailResponse;
import com.jsrxjt.mobile.api.module.dto.response.PageResponse;

import java.util.List;

public interface ModuleCaseService {
    /**
     * 页面详情
     * @param activityId
     * @param regionId
     * @return {@link PageResponse}
     */
    List<PageResponse> page(Integer activityId, Integer regionId);

    /**
     * 根据页面区域获取页面详情(不包含tab栏)
     * @param pageRegionId
     * @return {@link PageResponse}
     */
    List<PageResponse> getPageInfo(Integer pageRegionId);

    /**
     * 更新所有页面区域缓存
     */
    void updateAllPageRegionCache();

    /**
     * 更新所有页面缓存
     */
    void updateAllPageCache();

    /**
     * 更新全球购tab商品缓存
     */
    void updateAllGlobalGoodsCache();

    /**
     * 更新本地生活tab商品缓存
     */
    void updateAllLocalLifeGoodsCache();

    /**
     * 更新tab商品缓存
     */
    void updateAllTabCache();

    /**
     * tab详情
     * @param request
     * @return {@link TabPageResponse}
     */
    PageDTO<ModuleDetailResponse> tabPage(TabPageRequest request);

    /**
     * 发布装修页
     * @param pageId
     * @param activityId
     */
    void handleReleasePage(Integer pageId, Integer activityId);
}
