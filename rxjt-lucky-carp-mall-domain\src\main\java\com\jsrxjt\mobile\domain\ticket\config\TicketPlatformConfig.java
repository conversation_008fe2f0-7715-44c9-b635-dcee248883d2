package com.jsrxjt.mobile.domain.ticket.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 营销中台的配置
 * @Author: ywt
 * @Date: 2025-10-16 14:51
 * @Version: 1.0
 */
@Data
@Configuration
public class TicketPlatformConfig {
    @Value("${marketing.appId:}")
    private String appId;
    @Value("${marketing.appSecret:}")
    private String appSecret;
    @Value("${marketing.host:}")
    private String host;
    @Value("${marketing.giveOut:}")
    private String giveOut;
    @Value("${marketing.giveNotifyUrl:}")
    private String giveNotifyUrl;
    @Value("${marketing.connectTimeout:}")
    private Integer connectTimeout;
    @Value("${marketing.readTimeout:}")
    private Integer readTimeout;
}
