package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.*;

/**
 * <AUTHOR>
 * @Date 2025/7/24 17:48
 */
public interface DistributionOrderService {

    /**
     * 分销应用通用创建订单通知
     *
     * @param distributionId 应用id
     * @param request        创建订单请求参数
     * @return 订单创建结果
     */
    ApiResponse<DistributionCreateOrderResponseDTO> distributionOrderCreate(Long distributionId, DistributionOrderCreateNotifyDTO request);

    /**
     * 沃尔玛应用创建订单通知
     *
     * @param distributionId 沃尔玛应用id
     * @param request        创建订单请求参数
     * @return 订单创建结果
     */
    ApiResponse<WalMartOrderCreateResponseDTO> walMartDistributionOrderCreateNotify(
            Long distributionId, WalMartOrderCreateNotifyDTO request);

    /**
     * 饿了么应用创建订单通知
     *
     * @param distributionId 饿了么应用id
     * @param request        创建订单请求参数
     * @return 订单创建结果
     */
    ApiResponse<ELeMeOrderCreateResponseDTO> eLeMeDistributionOrderCreateNotify(Long distributionId, ELeMeOrderCreateNotifyDTO request);

    /**
     * 分销应用退款
     *
     * @param request 订单退款通知请求参数
     * @return 订单退款结果
     */
    ApiResponse<DistributionOrderRefundResponseDTO> distributionOrderRefund(DistributionOrderRefundNotifyDTO request);

    /**
     * 沃尔玛应用退款
     *
     * @param request 订单退款通知请求参数
     * @return 订单退款结果
     */
    ApiResponse<WalMartOrderRefundResponseDTO> walMartDistributionOrderRefund(WalMartOrderRefundNotifyDTO request);

    /**
     * 饿了么应用退款
     *
     * @param request 订单退款通知请求参数
     * @return 订单退款结果
     */
    ApiResponse<ELeMeOrderRefundResponseDTO> eLeMeDistributionOrderRefund(ELeMeOrderRefundNotifyDTO request);

    /**
     * 订单支付状态查询包含支付金额(叮咚买菜,大润发,食行,卫岗,物美,永辉,中免日上,清美)
     *
     * @param request
     * @return
     */
    ApiResponse<DistributionOrderPayInfoWithAmountResponseDTO> distributionOrderPayInfoWithPayAmount(DistributionOrderPayInfoQueryDTO request);

    /**
     * 订单支付状态查询(美团,视听,水韵,同程,团油,西橙电影)
     *
     * @param request
     * @return
     */

    ApiResponse<DistributionOrderPayInfoResponseDTO> distributionOrderPayInfo(DistributionOrderPayInfoQueryDTO request);

    /**
     * 分销应用订单退款查询
     *
     * @param request 订单退款查询请求参数
     * @return 订单退款查询结果
     */
    ApiResponse<DistributionOrderRefundInfoResponseDTO> distributionOrderRefundInfo(DistributionOrderRefundQueryDTO request);

    /**
     * 沃尔玛应用订单查询接口
     *
     * @param request
     * @return
     */
    ApiResponse<WalMartOrderQueryResponseDTO> walMartDistributionOrderPayInfo(WalMartOrderQueryRequestDTO request);

    /**
     * 饿了么应用订单查询接口
     *
     * @param request
     * @return
     */
    ApiResponse<ELeMeOrderQueryResponseDTO> eLeMeDistributionOrderPayInfo(ELeMeOrderQueryRequestDTO request);

    /**
     * 饿了么应用退款状态查询接口
     *
     * @param request 查询参数
     * @return 订单退款结果
     */
    ApiResponse<ELeMeOrderRefundResponseDTO> eLeMeDistributionOrderRefundInfo(ELeMeOrderRefundNotifyDTO request);

    /**
     * 饿了么关闭订单
     * @param request
     * @return
     */
    ApiResponse<Object> eLeMeDistributionOrderClose(ELeMeOrderQueryRequestDTO request);

    /**
     * 通用应用订单状态通知
     * @param request
     * @return
     */
    ApiResponse<Object> commonDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request);

    /**
     * 水韵订单状态通知
     * @param request
     * @return
     */
    ApiResponse<Object> syfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request);

    ApiResponse<Object> yhfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request);

    ApiResponse<Object> yhfxDistributionOrderPackageStatusNotify(DistributionOrderStatusNotifyDTO request);

    ApiResponse<Object> bsfxDistributionOrderStatusNotify(DistributionOrderStatusNotifyDTO request);
}
