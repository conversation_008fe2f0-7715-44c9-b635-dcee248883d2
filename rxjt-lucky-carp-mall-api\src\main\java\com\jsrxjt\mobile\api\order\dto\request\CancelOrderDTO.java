package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 取消订单请求参数
 * <AUTHOR>
 * @since 2025/7/17
 **/
@Getter
@Setter
public class CancelOrderDTO extends BaseParam {
    @Schema(description = "订单编号")
    @NotNull(message = "订单编号不能为空")
    private String orderNo;
}
