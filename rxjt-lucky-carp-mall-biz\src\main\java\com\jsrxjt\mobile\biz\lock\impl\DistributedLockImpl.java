package com.jsrxjt.mobile.biz.lock.impl;

import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.domain.gateway.lock.DistributedLockGateway;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 *  DistributedLockImpl
 *
 * <AUTHOR> Fengping
 * 2023/4/3 17:24
 * 
 **/
@Service
@RequiredArgsConstructor
public class DistributedLockImpl implements DistributedLock {

    private final DistributedLockGateway lockGateway;

    @Override
    public boolean tryLock(String lockName) {
        return lockGateway.tryLock(lockName);
    }

    @Override
    public boolean tryLock(String lockName, long timeout, TimeUnit unit) throws InterruptedException {
        return lockGateway.tryLock(lockName, timeout, unit);
    }

    @Override
    public boolean isLock(String lockName) {
        return lockGateway.isLock(lockName);
    }

    @Override
    public void lock(String lockName) {
        lockGateway.lock(lockName);
    }

    @Override
    public void lock(String lockName, long timeout, TimeUnit unit) {
        lockGateway.lock(lockName, timeout, unit);
    }

    @Override
    public void unLock(String lockName) {
        lockGateway.unLock(lockName);
    }

    @Override
    public boolean isHeldByCurrentThread(String lockName) {
        return lockGateway.isHeldByCurrentThread(lockName);
    }
}
