package com.jsrxjt.adapter.pay.mq.consumer;

import lombok.SneakyThrows;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;

/**
 * 赠券发送消息队列消费者配置
 * 
 * <AUTHOR>
 * @date 2025/09/22
 */
@Configuration
@RefreshScope
public class SendGiftTicketsGroupConsumerConfig {

    @SneakyThrows
    @Bean(destroyMethod = "close")
    public PushConsumer sendGiftTicketsGroupPushConsumer(ClientConfiguration clientConfiguration, SendGiftTicketsMessageListener sendGiftTicketsMessageListener) {
        FilterExpression filterExpression = new FilterExpression("*", FilterExpressionType.TAG);
        String consumerGroup = "send_gift_tickets_consumer_group";
        String topic = "paid_topic";
        return ClientServiceProvider.loadService().newPushConsumerBuilder()
                .setClientConfiguration(clientConfiguration)
                .setConsumerGroup(consumerGroup)
                .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
                .setMessageListener(sendGiftTicketsMessageListener)
                .build();
    }
}
