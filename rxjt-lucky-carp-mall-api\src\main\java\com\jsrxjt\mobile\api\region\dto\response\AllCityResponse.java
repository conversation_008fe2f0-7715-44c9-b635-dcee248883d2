package com.jsrxjt.mobile.api.region.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "所有城市")
public class AllCityResponse {

    @Schema(description = "热门城市")
    private List<CityResponse> hotCity;

    @Schema(description = "首字母聚合城市列表")
    private List<PyPreCityResponse> pyPreCity;

    @Data
    public static class PyPreCityResponse {
        @Schema(description = "首字母拼音前缀")
        private String py;

        @Schema(description = "城市列表")
        private List<CityResponse> city;
    }
}
