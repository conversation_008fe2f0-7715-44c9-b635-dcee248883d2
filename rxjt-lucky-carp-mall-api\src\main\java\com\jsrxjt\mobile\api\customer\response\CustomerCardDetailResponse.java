package com.jsrxjt.mobile.api.customer.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CustomerCardDetailResponse {

    @Schema(description = "卡号")
    private String cardNo;

    @Schema(description = "卡余额")
    private BigDecimal validAmount;

    @Schema(description = "是否是零元卡,0:否,1:是 只针对于商联卡、提货凭证、工会凭证有效")
    private Integer isZero;

    @Schema(description = "create_time")
    private Date createTime;

}
