<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketMapper">

    <update id="removeById" parameterType="java.lang.Long">
        update ticket
        set del_flag = 1,
            del_time = now()
        where ticket_id = #{ticketId}
    </update>

    <select id="getTicketIncludeDelById" parameterType="java.lang.Long" resultType="com.jsrxjt.mobile.infra.ticket.persistent.po.TicketPO">
        select *
        from ticket
        where ticket_id = #{ticketId}
    </select>
</mapper>
