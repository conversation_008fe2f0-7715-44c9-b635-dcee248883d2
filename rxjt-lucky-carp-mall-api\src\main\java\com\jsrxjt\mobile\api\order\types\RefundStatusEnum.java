package com.jsrxjt.mobile.api.order.types;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款状态枚举
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Getter
@AllArgsConstructor
public enum RefundStatusEnum {
    
    /**
     * 未退款
     */
    NOT_REFUNDED(0, "未退款"),
    
    /**
     * 退款中
     */
    REFUNDING(1, "退款中"),
    
    /**
     * 退款成功
     */
    REFUND_SUCCESS(2, "退款成功"),
    
    /**
     * 退款失败
     */
    REFUND_FAILED(3, "退款失败"),
    
    /**
     * 拒绝退款
     */
    REFUND_REJECTED(4, "拒绝退款");
    
    private final Integer code;
    private final String desc;
    
    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static RefundStatusEnum getByCode(Integer code) {
        for (RefundStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 