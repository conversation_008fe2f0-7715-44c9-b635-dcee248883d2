package com.jsrxjt.mobile.api.coupon.dto.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 卡管创建订单响应DTO
 * <AUTHOR>
 * @since 2025/7/7
 **/
@Data
public class CouponCreateOrderResponseDTO {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 外部订单号（卡管订单号）
     */
    private String externalOrderNo;
    /**
     * 订单总售价
     */
    private BigDecimal totalAmount;
    /**
     * 订单总结算价
     */
    private BigDecimal totalSettlePrice;

    private Integer code;

    private String msg;
}
