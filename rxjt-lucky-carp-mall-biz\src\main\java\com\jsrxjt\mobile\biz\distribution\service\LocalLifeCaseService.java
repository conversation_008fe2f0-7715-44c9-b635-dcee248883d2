package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.locallife.dto.request.*;
import com.jsrxjt.mobile.api.locallife.dto.response.*;

public interface LocalLifeCaseService {

    /**
     * 本地生活侧获取福鲤圈会员信息
     *
     * @param requestDTO
     * @return
     */
    ApiResponse<LocalLifeUserInfoResponse> vipInfo(LocalLifeUserInfoRequest requestDTO);


    /**
     * 本地生活下单
     *
     * @param requestDTO
     * @return
     */
    ApiResponse<LocalLifeCreateOrderResponse> prePay(LocalLifeCreateOrderDTO requestDTO);

    /**
     * 本地生活退款
     *
     * @param requestDTO
     * @return
     */
    ApiResponse<LocalLifeOrderRefundResponse> refund(LocalLifeRefundOrderDTO requestDTO);


    /**
     * 订单支付状态查询
     * @param request
     * @return
     */
    ApiResponse<LocalLifeOrderPayQueryResponse> payQuery(LocalLifePayQueryRequest request);

    /**
     * 退款查询
     * @param request
     * @return
     */
    ApiResponse<LocalLifeOrderRefundQueryResponse> refundQuery(LocalLifeRefundQueryRequest request);

}
