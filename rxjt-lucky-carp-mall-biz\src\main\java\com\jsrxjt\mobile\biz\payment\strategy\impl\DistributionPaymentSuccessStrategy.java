package com.jsrxjt.mobile.biz.payment.strategy.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;


/**
 * 分销支付成功处理策略（基于flatProductType）
 * 支持 flatProductType = 301 和 appflag 的类型
 * <AUTHOR>
 * @date 2025/09/24
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DistributionPaymentSuccessStrategy implements PaymentSuccessStrategy {

    private final UnifiedDistributionApi unifiedDistributionApi;

    private final OrderRepository orderRepository;
    @Override
    public boolean supports(Integer flatProductType) {
        // 仅支持 flatProductType == 301
        return flatProductType != null && flatProductType == 301;
    }

    @Override
    public void handle(OrderInfoEntity order) {
        log.info("开始处理分销订单支付成功，订单号：{}，扁平化产品类型：{}",
                order.getOrderNo(), order.getFlatProductType());
        String appFlag = order.getAppFlag();
        if (StringUtils.isBlank(appFlag)){
            log.error("订单{}的appFlag为空", order.getOrderNo());
            return;
        }
        DistChannelType distChannelType = DistChannelType.getByCode(appFlag);
        if (distChannelType == null) {
            log.error("订单{}的appFlag{}对应的分销渠道类型为空", order.getOrderNo(), appFlag);
            return;
        }
        DistPaidNotifyRequest request = DistPaidNotifyRequest.builder()
                .channelType(distChannelType)
                .orderNo(order.getOrderNo())
                .distOrderNo(order.getExternalOrderNo())
                .distTradeNo(order.getDistTradeNo())
                .tradeAmount(order.getTotalSellAmount())
                .payTime(order.getPaymentTime())
                .tradeNo(order.getTradeNo())
                .build();
        DistPaidNotifyResponse paidNotifyResponse = unifiedDistributionApi.paidNotify(request);
        if (paidNotifyResponse == null || !paidNotifyResponse.isSuccess()){
            log.error("分销订单支付成功回调失败，订单号：{}，响应结果：{}", order.getOrderNo(), paidNotifyResponse);
            throw new BizException("分销订单支付成功回调通知调用失败");
        }
        // 更新订单状态为交易成功
        updateOrderStatus(order, orderRepository);
        log.info("分销订单支付成功回调通知成功，订单号：{}", order.getOrderNo());

    }

    public  void updateOrderStatus(OrderInfoEntity order, OrderRepository orderRepository) {
        OrderInfoEntity updateParam = new OrderInfoEntity();
        updateParam.setId(order.getId());
        updateParam.setCustomerId(order.getCustomerId());
        updateParam.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
        updateParam.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateParam);
    }
}
