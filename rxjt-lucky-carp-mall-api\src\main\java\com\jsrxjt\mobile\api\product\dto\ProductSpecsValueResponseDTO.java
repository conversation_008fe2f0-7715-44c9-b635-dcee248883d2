package com.jsrxjt.mobile.api.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 产品规格值响应
 * @Author: ywt
 * @Date: 2025-04-30 09:59
 * @Version: 1.0
 */
@Data
@Schema(description = "产品规格值响应")
public class ProductSpecsValueResponseDTO {
    @Schema(description = "规格值id")
    private Long id;
    @Schema(description = "规格名id")
    private Long specsNameId;
    @Schema(description = "规格值")
    private String specsValue;//规格值
    @Schema(description = "排序（数值越大排序越靠前）")
    private Integer sort;
}
