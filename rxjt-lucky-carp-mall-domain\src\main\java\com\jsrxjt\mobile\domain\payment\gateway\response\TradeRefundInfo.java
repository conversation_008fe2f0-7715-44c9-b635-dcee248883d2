package com.jsrxjt.mobile.domain.payment.gateway.response;

import java.time.LocalDateTime;

/**
 * 收银台退款接口返回的退款交易信息
 * <AUTHOR>
 * @since 2025/9/26
 */
public class TradeRefundInfo {
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 爷-业务订单号 */
    private String orderNo;

    /** 爷-外部业务订单号 */
    private String outOrderNo;

    /** 爷-交易订单ID */
    private Long tradeId;

    /** 父-交易单号 */
    private String tradeNo;

    /** 父-退款单ID */
    private Long refundId;

    /** 父-退款单 */
    private String refundNo;

    /** 父-外部退款单 */
    private String outRefundNo;

    /** 父-交易明细单ID */
    private Long tradeInfoId;

    /** 父-交易明细单号 */
    private String tradeInfoNo;

    /** 父-外部交易明细单号 */
    private String outTradeInfoNo;

    /** 子-退款单号 */
    private String refundInfoNo;

    /** 支付通道 (CARD / WECHAT_PAY) */
    private String refundChannel;

    /** 微信交易ID （退款需要使用该参数） */
    private String wxTransactionId;

    /** 微信退款单号 */
    private String wxRefundId;

    /** 微信支付金额 */
    private Long wxPayAmount;

    /** 渠道号 */
    private String chanId;

    /** 商户号 */
    private String mchId;

    /** 门店号 */
    private String storeId;

    /** 终端号 */
    private String termId;

    /** 卡号 */
    private String cardNo;

    /** 微信退款状态 (SUCCESS/FAIL/REFUNDING) */
    private String wxRefundStatus;

    /** 退款状态 (SUCCESS/FAIL/REFUNDING) */
    private String refundStatus;

    /** 退款金额 */
    private Long refundAmount;

    /** 交易卡类型 (RX_RED_CARD / RX_BLACK_CARD / RX_WHITE_CARD / RX_PICK_CARD) */
    private String cardTradeType;

    /** 业务卡类型 (RXHK / SL / HJ / BJ) */
    private String cardBusinessType;

    /** 售后时间 */
    private LocalDateTime refundAt;

    /** 微信退款时间 */
    private LocalDateTime wxRefundAt;

    /** 创建时间 */
    private LocalDateTime createdAt;

    /** 更新时间 */
    private LocalDateTime updatedAt;

    /** 删除时间 */
    private LocalDateTime deletedAt;
}
