package com.jsrxjt.mobile.api.app.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;


@Data
@Schema(description = "支付宝红包应用信息响应")
public class AlipayAppRequest {

     @Schema(description = "spuId isSelect=1时spu必填 ")
     private Long appSpuId;

     @Schema(description = "skuId 装修面值跳转必填")
     private Long appSkuId;

     @Schema(description = "分类id isSelect=0 必填 分类调整必填")
      private Long alipayTabCatId;

     @Schema(description = "是否选中1是0否  装修面值跳转、分类页跳转必填传1" )
     @NotNull(message = "是否选中不能为空")
     private Byte isSelect;

    @Schema(description = "三级地址id")
    @NotNull(message = "三级地址id不能为空")
    private Integer regionId;
}
