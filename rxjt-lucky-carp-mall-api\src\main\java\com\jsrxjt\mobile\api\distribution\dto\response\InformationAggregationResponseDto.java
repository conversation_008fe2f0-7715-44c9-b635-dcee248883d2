package com.jsrxjt.mobile.api.distribution.dto.response;

import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 资讯聚合页响应
 * @Author: ywt
 * @Date: 2025-06-16 08:54
 * @Version: 1.0
 */
@Data
public class InformationAggregationResponseDto {
    @Schema(description = "广告列表")
    List<AdvertisementInfoDTO> advertisementList;
    @Schema(description = "资讯分类列表")
    List<InformationCatResponseDto> catList;
}
