package com.jsrxjt.mobile.api.distribution.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 支付/退款请求参数DTO
 */
@Data
public class PickPaymentRefundRequestDTO {
    
    /**
     * 第三方ID (默认1-瑞祥商户)
     * 
     */
    @JSONField(name = "third_id")
    private String third_id;

    /**
     * 商户ID
     * 
     */
    @JSONField(name ="shop_id")
    private String shop_id;

    /**
     * 商户收银员ID
     */
    @JSONField(name ="shop_user_id")
    private String shop_user_id;

    /**
     * 业务类型描述 (例: place:分销订单)
     */
    @JSONField(name ="type")
    private String type;

    /**
     * 平台退款流水号
     */
    @JSONField(name ="refund_no")
    private String refund_no;

    /**
     * 平台订单流水号
     */
    @JSONField(name ="order_no")
    private String order_no;

    /**
     * 平台交易流水号
     */
    @JSONField(name ="trade_no")
    private String trade_no;

    /**
     * 订单金额
     */
    @JSONField(name ="amount")
    private String amount;

    /**
     * 交易时间
     */
    @JSONField(name ="trade_time")
    private String trade_time;
}
