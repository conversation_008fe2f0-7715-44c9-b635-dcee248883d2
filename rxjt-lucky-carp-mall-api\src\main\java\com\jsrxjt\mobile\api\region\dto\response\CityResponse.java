package com.jsrxjt.mobile.api.region.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "城市基本信息")
public class CityResponse {

    @Schema(description = "城市id")
    private Integer id;

    @Schema(description = "城市名称")
    private String regionName;

    @Schema(description = "1 省  2  市  3 区 4.镇")
    private Integer regionType;

    @Schema(description = "经度")
    private String lat;

    @Schema(description = "纬度")
    private String lng;
}
