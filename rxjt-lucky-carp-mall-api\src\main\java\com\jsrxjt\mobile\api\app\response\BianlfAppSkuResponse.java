package com.jsrxjt.mobile.api.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 便利蜂sku信息响应
 * @Author: ywt
 * @Date: 2025-10-21 14:12
 * @Version: 1.0
 */
@Data
@Schema(description = "便利蜂sku信息响应")
public class BianlfAppSkuResponse {
    @Schema(description = "SKU ID")
    private Long appSkuId;

    @Schema(description = "应用SPU ID")
    private Long appSpuId;

    @Schema(description = "面值名称")
    private String amountName;

    @Schema(description = "面值")
    private BigDecimal amount;

    @Schema(description = "建议售价")
    private BigDecimal platformPrice;

    @Schema(description = "手续费百分比")
    private BigDecimal commissionFee;

    /*@Schema(description = "面值备注")
    private String remark;*/

    @Schema(description = "是否选中 0否 1是")
    private Integer isSkuSelect = 0;
}
