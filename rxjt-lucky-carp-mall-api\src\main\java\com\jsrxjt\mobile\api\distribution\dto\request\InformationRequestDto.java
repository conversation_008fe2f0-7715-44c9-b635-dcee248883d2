package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 资讯点击参数
 * @Author: ywt
 * @Date: 2025-06-10 17:27
 * @Version: 1.0
 */
@Data
public class InformationRequestDto {
    @Schema(description = "资讯id")
    @NotNull(message = "资讯id为空错误")
    private Integer informationId;
}
