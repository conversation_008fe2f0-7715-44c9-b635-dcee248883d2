package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(name="CustomerCardRequest", description = "用户获取卡列表请求参数")
public class CustomerCardRequest extends BaseParam {

    @Schema(description = "用户Id")
    @NotNull(message = "用户Id不能为空")
    private Long customerId;

    @Schema(description = "卡类型(2商联卡/红卡 5白金 7黑金 23工会凭证) 查全部卡列表类型不传")
    private Integer cardType;

}
