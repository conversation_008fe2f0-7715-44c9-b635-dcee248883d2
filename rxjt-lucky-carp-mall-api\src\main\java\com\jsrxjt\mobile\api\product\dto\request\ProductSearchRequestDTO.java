package com.jsrxjt.mobile.api.product.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 产品搜索请求DTO
 * 
 * <AUTHOR>
 * @since 2025/5/8
 **/
@Getter
@Setter
@Schema(description = "产品搜索请求")
public class ProductSearchRequestDTO extends BaseParam {

    /**
     * 搜索关键词
     */
    @Schema(description = "搜索关键词",requiredMode = Schema.RequiredMode.REQUIRED)
    private String keyword;

    /**
     * 区域ID
     */
    @Schema(description = "区域ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Integer regionId;


}