package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 卡包列表请求参数
 * <AUTHOR>
 * @date 2025/07/22
 */
@Data
public class CouponPackageListRequestDTO extends BaseParam {

    @Schema(description = "卡券/品牌名称")
    private String queryName;

    /**
     * 刪除标志(0:未删除 2:回收站)
     */
    private Integer delFlag;

}
