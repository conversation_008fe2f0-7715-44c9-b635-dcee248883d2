package com.jsrxjt.mobile.biz.homeScanPay.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.scanPay.response.ScanPayWssResponseDTO;
import com.jsrxjt.mobile.api.scanPay.types.ScanWsReceiveTypeEnum;
import com.jsrxjt.mobile.biz.homeScanPay.service.ScanPayWssHandlerFactory;
import com.jsrxjt.mobile.domain.scanpay.entity.ScanPayWssEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class ScanWebSocketHandler extends TextWebSocketHandler implements DisposableBean {

    //本地会话缓存 key = customerId_payCode
    private final Map<String, WebSocketSession> localSessions = new ConcurrentHashMap<>();

    private final RedisUtil redisUtil;

    private final ScanPayWssHandlerFactory scanPayWssHandlerFactory;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        try {
            String sessionId = session.getId();
            Long customerId = Long.valueOf((String) session.getAttributes().get("customerId"));
            String token = (String) session.getAttributes().get("token");
            String sourceCode = (String) session.getAttributes().get("sourceCode");

            if (customerId == null || token == null) {
                log.error("WebSocket连接异常: 认证信息不完整");
                closeSessionWithError(session, "认证信息不完整", CloseStatus.NOT_ACCEPTABLE);
                return;
            }
            String sessionKey = customerId + "_" + sourceCode;
            localSessions.put(sessionKey, session);
            storeSessionInfo(sessionId, customerId, sourceCode,  token);
            log.info("WebSocket连接建立成功: {}, 用户ID: {}, 来源随机码: {}", sessionId, customerId, sourceCode);
        } catch (Exception e) {
            log.error("WebSocket连接建立异常", e);
            closeSessionWithError(session, "连接建立异常", CloseStatus.SERVER_ERROR);
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        Long customerId = Long.valueOf((String) session.getAttributes().get("customerId"));
        String payCode = (String) session.getAttributes().get("payCode");
        log.info("WebSocket session{}, 用户ID: {}, 支付码: {}, 获取到消息{}", session.getId(), customerId, payCode, payload);
        try {
            JSONObject jsonObject = JSONObject.parseObject(payload);
            if (jsonObject.containsKey("type")) {
                ScanWsReceiveTypeEnum receiveType = ScanWsReceiveTypeEnum.getByType(jsonObject.getString("type"));
                BaseResponse response = scanPayWssHandlerFactory.handleReceiveMessage(receiveType, jsonObject);
                sendMessage(session, JSONObject.toJSONString(response));
            }else {
                sendMessage(session, JSONObject.toJSONString(BaseResponse.fail(CloseStatus.NOT_ACCEPTABLE.getCode(), "无效的消息类型")));
            }
        }catch (Exception e){
            //忽略，防止心跳内容json解析异常
        }
    }

    public void sendMessageToCustomer(Long customerId, String payCode,String message) {
        String sourceCode = redisUtil.get(RedisKeyConstants.WSS_SCAN_PAY_CODE + payCode);
        if (StringUtils.isEmpty(sourceCode)){
            log.error("支付码{}对应的链接获取失败", payCode);
            return;
        }
        String sessionKey = customerId + "_" + sourceCode;
        WebSocketSession session = localSessions.get(sessionKey);
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
                log.info("发送消息成功 to{}, payCode={}, message={}", customerId, payCode, message);
            } catch (Exception e) {
                log.error("本地消息发送失败", e);
            }
        }
    }

    private void sendMessage(WebSocketSession session, String message){
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
            } catch (Exception e) {
                log.error("本地消息发送失败", e);
            }
        }
    }

    private void storeSessionInfo(String sessionId, Long customerId, String sourceCode, String token) {
        try {
            ScanPayWssEntity scanPayWssEntity = new ScanPayWssEntity();
            scanPayWssEntity.setCustomerId(customerId);
            scanPayWssEntity.setSessionId(sessionId);
            scanPayWssEntity.setToken(token);
            scanPayWssEntity.setSourceCode(sourceCode);
            scanPayWssEntity.setCreateTime(System.currentTimeMillis());
            String key = RedisKeyConstants.WSS_SCAN_CUSTOMER.formatted(customerId, sourceCode);
            redisUtil.set(key, JSONObject.toJSONString(scanPayWssEntity), 30*60);
        } catch (Exception e) {
            log.error("存储会话信息失败", e);
        }
    }


    private void sendError(WebSocketSession session, String errorMessage) {
        sendMessage(session, JSONObject.toJSONString(BaseResponse.fail(CloseStatus.SERVER_ERROR.getCode(), errorMessage)));
    }

    private void closeSessionWithError(WebSocketSession session, String errorMessage, CloseStatus closeStatus) {
        try {
            sendError(session, errorMessage);
            session.close(closeStatus);
        } catch (Exception e) {
            log.error("关闭会话时发生错误", e);
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String sessionId = session.getId();
        Long customerId = Long.valueOf((String) session.getAttributes().get("customerId"));
        String sourceCode = (String) session.getAttributes().get("sourceCode");
        String sessionKey = customerId + "_" + sourceCode;
        localSessions.remove(sessionKey);
        cleanupSessionInfo(customerId, sourceCode);

        log.info("WebSocket连接关闭: {}, 用户: {}, 状态: {}, 链接来源随机码: {}", sessionId, customerId, status, sourceCode);
    }

    private void cleanupSessionInfo(Long customerId,  String sourceCode) {
        try {
            //清除缓存信息
            redisUtil.delete(RedisKeyConstants.WSS_SCAN_CUSTOMER.formatted(customerId, sourceCode));
            Set<String> payCodeSet = redisUtil.sMembers(RedisKeyConstants.WSS_SCAN_SOURCE_CODE.formatted(customerId, sourceCode));
            if (CollectionUtil.isNotEmpty(payCodeSet)){
                payCodeSet.forEach(payCode -> {
                    redisUtil.delete(RedisKeyConstants.WSS_SCAN_PAY_CODE + payCode);
                });
                redisUtil.delete(RedisKeyConstants.WSS_SCAN_SOURCE_CODE.formatted(customerId,sourceCode));
            }
        } catch (Exception e) {
            log.error("清理会话信息失败", e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("WebSocket传输错误: {}", session.getId(), exception);
    }

    @Override
    public void destroy() throws Exception {
        localSessions.forEach((sessionKey, session) -> {
            String[]  keyParts = sessionKey.split("_");
            Long customerId = Long.valueOf(keyParts[0]);
            String sourceCode = keyParts[1];
            cleanupSessionInfo(customerId, sourceCode);
        });
        log.info("WebSocket 应用关闭，Redis 缓存已清理");
    }


}
