/*
 Navicat Premium Dump SQL

 Source Server         : 新福鲤圈dev
 Source Server Type    : MySQL
 Source Server Version : 80027 (8.0.27)
 Source Host           : **************:3306
 Source Schema         : rxflq_db

 Target Server Type    : MySQL
 Target Server Version : 80027 (8.0.27)
 File Encoding         : 65001

 Date: 25/07/2025 10:46:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for advertisement
-- ----------------------------
DROP TABLE IF EXISTS `advertisement`;
CREATE TABLE `advertisement`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '广告id',
  `adv_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '广告标题名称',
  `sub_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '广告副标题',
  `iml_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '广告图',
  `click_num` int NOT NULL DEFAULT 0 COMMENT '点击量',
  `type` tinyint NOT NULL COMMENT '所属位置，1卡券详情 2个人中心 3支付成功页 4分类页 5套餐详情 6资讯集合页',
  `is_nationwide` tinyint NOT NULL COMMENT '上线城市：0非全国 1全国',
  `begin_time` datetime NOT NULL COMMENT '有效期开始时间',
  `end_time` datetime NOT NULL COMMENT '有效期结束时间',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(数字越大排序越靠前)',
  `ios_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'iOS跳转链接',
  `android_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '安卓app跳转链接',
  `wechat_mini_appid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信小程序appid',
  `wechat_mini_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '小程序跳转链接',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `adv_name_idx`(`adv_name` ASC) USING BTREE,
  INDEX `type_idx`(`type` ASC) USING BTREE,
  INDEX `begin_time_idx`(`begin_time` ASC) USING BTREE,
  INDEX `create_time_idx`(`create_time` ASC) USING BTREE,
  INDEX `end_time_idx`(`end_time` ASC) USING BTREE,
  INDEX `advertisement_type_idx`(`type` ASC, `begin_time` ASC, `end_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '广告位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for advertisement_page_relation
-- ----------------------------
DROP TABLE IF EXISTS `advertisement_page_relation`;
CREATE TABLE `advertisement_page_relation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `adv_id` bigint NOT NULL COMMENT '广告id',
  `page_type` tinyint NOT NULL COMMENT '页面类型，1卡券详情页  4分类页  5套餐详情页',
  `out_id` int NOT NULL COMMENT 'page_type为1是卡券spuid; page_type为4是一级分类id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `page_out_id_idx`(`out_id` ASC, `page_type` ASC) USING BTREE,
  INDEX `adv_id_idx`(`adv_id` ASC) USING BTREE,
  INDEX `page_relation_adv_id_IDX`(`adv_id` ASC, `page_type` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '广告与页面绑定表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alipay_tab_category
-- ----------------------------
DROP TABLE IF EXISTS `alipay_tab_category`;
CREATE TABLE `alipay_tab_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `category_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'tab分类名称',
  `sort` int NOT NULL DEFAULT 1 COMMENT '排序（降序）',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态 0关闭 1启动',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `mod_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标记 0、未删除 1、已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付宝tab分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for app_coupon_explain
-- ----------------------------
DROP TABLE IF EXISTS `app_coupon_explain`;
CREATE TABLE `app_coupon_explain`  (
  `explain_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '说明id',
  `app_spu_id` bigint NULL DEFAULT NULL COMMENT '产品spuid',
  `app_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 2 支付宝红包 3 苏西话费',
  `explain_type` tinyint NULL DEFAULT NULL COMMENT '说明类型 1:适用范围  2:充值使用说明  3:温馨提示（下单确认提示） 4兑换须知  5跑马灯内容',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '说明内容富文本',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`explain_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 150 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付宝话费说明表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for app_coupon_goods
-- ----------------------------
DROP TABLE IF EXISTS `app_coupon_goods`;
CREATE TABLE `app_coupon_goods`  (
  `app_spu_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '应用spuId',
  `app_spu_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `sub_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '副标题',
  `brand_id` bigint NULL DEFAULT NULL COMMENT '品牌id',
  `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道id',
  `alipay_tab_cat_id` bigint NULL DEFAULT NULL COMMENT '支付宝tab分类id',
  `first_cat_id` bigint NULL DEFAULT NULL COMMENT '平台一级分类id',
  `second_cat_id` bigint NULL DEFAULT NULL COMMENT '平台二级分类id',
  `subscript_id` bigint NULL DEFAULT NULL COMMENT '角标id',
  `logo_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'logo图片url',
  `img_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主图&列表图url',
  `app_type` tinyint NOT NULL COMMENT '应用类型 2:支付宝红包 3:苏西话费',
  `pay_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付方式编码组合，多个用分号分隔，如：WX;RB',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态 0:下架 1:出售中',
  `virtual_stock` int NULL DEFAULT NULL COMMENT '销量（虚拟）',
  `forward_num` int NULL DEFAULT NULL COMMENT '转发量（虚拟）',
  `label_ids` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '说明标签id列表 逗号分隔',
  `category_display_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类页下展示的名称',
  `is_show_category` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否在分类页显示 1 显示 0 不显示',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志，0--正常，1--删除',
  `audit_status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态 0:待审核 1:审核通过 2:审核驳回',
  `latest_audit_id` bigint NULL DEFAULT NULL COMMENT '最新审核记录ID',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`app_spu_id`) USING BTREE,
  INDEX `idx_spu_type`(`app_spu_id` ASC, `app_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '应用表（支付宝话费）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for app_coupon_goods_sku
-- ----------------------------
DROP TABLE IF EXISTS `app_coupon_goods_sku`;
CREATE TABLE `app_coupon_goods_sku`  (
  `app_sku_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'skuId',
  `app_spu_id` bigint NOT NULL COMMENT '应用spuId',
  `out_platform_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外部平台id(支付宝对应活动编码)',
  `amount_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '面值名称',
  `amount` decimal(10, 2) NOT NULL COMMENT '面值',
  `platform_price` decimal(10, 2) NOT NULL COMMENT '建议售价',
  `commission_fee` decimal(10, 2) NOT NULL COMMENT '手续费百分比',
  `stock_warn_num` int NULL DEFAULT NULL COMMENT '库存预警数量',
  `sold_num` int NOT NULL DEFAULT 0 COMMENT '已售数量',
  `out_platform_status` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外部平台状态',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT 'sku状态 0:下架 1:出售中',
  `inventory` int NULL DEFAULT 0 COMMENT 'sku库存',
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注富文本',
  `budget_balance` decimal(10, 2) NULL DEFAULT NULL COMMENT '支付宝预算余额',
  `valid_period` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '有效期',
  `sort` tinyint NULL DEFAULT NULL COMMENT '排序',
  `sync_time` datetime NULL DEFAULT NULL COMMENT '外部平台数据同步时间',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志0--正常，1--删除',
  PRIMARY KEY (`app_sku_id`) USING BTREE,
  INDEX `sku_coupon_platform_id_IDX`(`out_platform_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '应用sku表（支付宝话费）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for app_goods
-- ----------------------------
DROP TABLE IF EXISTS `app_goods`;
CREATE TABLE `app_goods`  (
  `app_id` bigint NOT NULL AUTO_INCREMENT COMMENT '应用id',
  `app_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称',
  `sub_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '副标题',
  `type` tinyint NULL DEFAULT 1 COMMENT '应用类型：1普通应用 4扫码提货(展码付) 5纳客宝(废弃) 6 线下扫码应用(首页提货)',
  `brand_id` bigint NULL DEFAULT NULL COMMENT '品牌id',
  `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道id',
  `app_flag` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用标签(如 美团外卖对应mtwm,具体内容由开发确定)',
  `third_id` bigint NULL DEFAULT NULL COMMENT '商户id，提货券分销中台提供（仅type=4有效）',
  `pay_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付方式编码组合，多个用分号分隔，如：WX;RB',
  `first_cat_id` bigint NULL DEFAULT NULL COMMENT '平台一级分类id',
  `second_cat_id` bigint NULL DEFAULT NULL COMMENT '平台二级分类id',
  `logo_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'logo图片url',
  `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用图片url',
  `subscript_id` int NULL DEFAULT NULL COMMENT '角标id',
  `embed_type` tinyint NULL DEFAULT NULL COMMENT '嵌入福鲤圈方式(1:h5 2:小程序 3:原生)',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态 0:下架 1:出售中',
  `sort` int NULL DEFAULT 0 COMMENT '排序(数字越大排序越靠前)',
  `wechat_mini_status` tinyint NULL DEFAULT 0 COMMENT '微信小程序显示状态 0:不显示 1:显示',
  `ios_status` tinyint NULL DEFAULT 0 COMMENT 'ios显示状态 0:不显示 1:显示',
  `android_status` tinyint NULL DEFAULT 0 COMMENT '安卓显示状态 0:不显示 1:显示',
  `category_display_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类页下的展示名称',
  `is_show_category` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否在分类页显示 1 显示 0 不显示',
  `scan_pay_toast` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '展码付提示,，仅展码付有效',
  `mini_app_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信小程序appid',
  `is_sync_payment` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是同步支付方式 0 否 1 是',
  `gh_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信原始id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '0--正常 1--删除',
  PRIMARY KEY (`app_id`) USING BTREE,
  UNIQUE INDEX `uk_app_flag`(`app_flag` ASC) USING BTREE COMMENT '应用标签唯一索引',
  INDEX `idx_status`(`status` ASC) USING BTREE COMMENT '状态索引',
  INDEX `app_name_idx`(`app_name` ASC) USING BTREE,
  INDEX `channel_id_idx`(`channel_id` ASC) USING BTREE,
  INDEX `brand_id_idx`(`brand_id` ASC) USING BTREE,
  INDEX `cat_id_idx`(`first_cat_id` ASC, `second_cat_id` ASC) USING BTREE,
  INDEX `create_time_idx`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应用详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_version_control
-- ----------------------------
DROP TABLE IF EXISTS `app_version_control`;
CREATE TABLE `app_version_control`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `app_type` tinyint NOT NULL COMMENT '所属产品 1:安卓 2:ios',
  `is_force` tinyint NOT NULL COMMENT '是否强制更新 1是 0否',
  `status` tinyint NOT NULL COMMENT '启用状态 0禁用 1启用',
  `version_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本号',
  `app_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '安装包地址',
  `app_remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'app更新备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人名称',
  `create_id` bigint NOT NULL COMMENT '创建人id',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人名称',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'app版本控制表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for card_platform_origin_coupons
-- ----------------------------
DROP TABLE IF EXISTS `card_platform_origin_coupons`;
CREATE TABLE `card_platform_origin_coupons`  (
  `id` int NOT NULL,
  `brand_id` int NULL DEFAULT NULL COMMENT '品牌id',
  `brand_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '品牌名称',
  `coupon_platform_id` int NULL DEFAULT NULL COMMENT '卡管卡券id',
  `coupon_platform_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡券名称',
  `coupon_platform_type` int NULL DEFAULT NULL COMMENT '卡券类型101:平台自发券 201:卡号+卡密 202:卡号或卡密 203:卡号+卡密+校验码 301:链接 302:链接+验证码 303:链接+卡号+验证码 304:卡号+短链接',
  `coupon_platform_status` tinyint NULL DEFAULT NULL COMMENT '卡券状态 0、下架 1、上架',
  `amount` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '面值',
  `settle_price` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成本价',
  `price` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '售价',
  `stock` int NULL DEFAULT NULL COMMENT '库存',
  `coupon_platform_channnel` tinyint NULL DEFAULT NULL COMMENT '卡券渠道来源 1-卡管 2-品诺 3-支付宝 4-苏西',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡管平台卡券列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for content_region_relation
-- ----------------------------
DROP TABLE IF EXISTS `content_region_relation`;
CREATE TABLE `content_region_relation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content_id` bigint NOT NULL COMMENT '内容中心各模块主键ID',
  `content_type` tinyint NOT NULL COMMENT '1广告 2资讯 3启动页 4搜索关键词 5搜索发现',
  `region_id` int NOT NULL COMMENT '区域ID',
  `region_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区域名称',
  `region_type` int NOT NULL COMMENT '区域类型，1省  2 市  3区',
  `parent_region_id` int NOT NULL COMMENT '父级区域id',
  `parent_region_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父级区域名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_region_id`(`region_id` ASC) USING BTREE,
  INDEX `content_region_idx`(`content_id` ASC, `content_type` ASC, `region_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15484 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '广告区域关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_goods
-- ----------------------------
DROP TABLE IF EXISTS `coupon_goods`;
CREATE TABLE `coupon_goods`  (
  `coupon_spu_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '卡券id',
  `coupon_spu_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡券名称\r\n',
  `sub_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡券副标题',
  `brand_id` bigint NULL DEFAULT NULL COMMENT '品牌id',
  `platform_brand_id` bigint NULL DEFAULT NULL COMMENT '卡管品牌id（用于聚合面值sku）',
  `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道id',
  `first_cat_id` int NULL DEFAULT NULL COMMENT '平台一级分类id',
  `second_cat_id` int NULL DEFAULT NULL COMMENT '平台二级分类id',
  `subscript_id` bigint NULL DEFAULT NULL COMMENT '角标id',
  `logo_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'logo图片url',
  `img_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主图&列表图url',
  `coupon_type` tinyint NOT NULL COMMENT '卡券类型 1:普通卡券 2:视频账号直冲 3:品诺卡券 4:品诺直充',
  `type` int NULL DEFAULT NULL COMMENT '卡管的核销类型（未使用） 101: 平台自发券 201: 卡密+卡号 202: 卡号或卡密 203: 卡号+卡密+校验码 301: 链接类 302: 链接+验证码 303: 卡号+短链接+验证码 304：卡号+短链接',
  `flq_type` int NULL DEFAULT NULL COMMENT '福鲤圈的核销类型，1:面值+兑换码 2:面值+兑换码+一维二维码 3:面值+卡号+一维二维码  4面值+卡号卡密 5面值+卡号卡密+兑换码 6面值+卡号卡密+一维二维码 7面值+卡密+一维二维码 8面值+链接 9自发券(面值+提货券形式) 10跳转品牌方h5核销页',
  `is_self_coupon` tinyint NOT NULL DEFAULT 0 COMMENT '是否自发券，0:否  1:是',
  `pick_product_id` int NULL DEFAULT NULL COMMENT '提货券分销平台的产品id，isSelfCoupon = 1时有效',
  `pay_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付方式编码组合，多个用分号分隔，如：WX;RB',
  `status` tinyint NULL DEFAULT NULL COMMENT '卡券状态 0:下架 1:出售中',
  `exchange_process` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '兑换流程富文本',
  `sort` int NULL DEFAULT NULL COMMENT '排序(数字越大排序越靠前)',
  `virtual_stock` int NULL DEFAULT NULL COMMENT '卡券销量（虚拟）',
  `forward_num` int NULL DEFAULT NULL COMMENT '转发量（虚拟）',
  `label_ids` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡券说明标签id列表 逗号分隔',
  `theme_color` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主题背景色',
  `show_template` int NULL DEFAULT NULL COMMENT '用于前端详情页使用哪种模版样式显示',
  `audit_status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态 0:待审核 1:审核通过 2:审核驳回',
  `latest_audit_id` bigint NULL DEFAULT NULL COMMENT '最新审核记录ID',
  `account_type` tinyint NULL DEFAULT NULL COMMENT '充值类型 0 其他 1 手机号 2 QQ号',
  `category_display_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类页下展示的名称',
  `is_show_category` tinyint NOT NULL DEFAULT 1 COMMENT '是否在分类页显示 1 显示 0 不显示 ',
  `is_show_polymerize` tinyint NOT NULL DEFAULT 0 COMMENT '直充聚合页是否展示 1显示 0不显示',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志，0--正常，1--删除',
  `flat_product_type` int NULL DEFAULT NULL COMMENT '扁平化产品类型 1:普通卡券,2直充卡券，3品诺卡券，4品诺直充',
  PRIMARY KEY (`coupon_spu_id`) USING BTREE,
  INDEX `cg_brand_id_idx`(`brand_id` ASC, `del_flag` ASC) USING BTREE COMMENT '卡券品牌',
  INDEX `cg_channel_id_idx`(`channel_id` ASC) USING BTREE COMMENT '卡券渠道',
  INDEX `cg_first_cat_id_idx`(`first_cat_id` ASC) USING BTREE COMMENT '第一分类',
  INDEX `cg_second_cat_id_idx`(`second_cat_id` ASC) USING BTREE COMMENT '第二分类',
  INDEX `coupon_type_poly_idx`(`coupon_type` ASC, `is_show_polymerize` ASC) USING BTREE,
  INDEX `coupon_goods_label_ids_IDX`(`label_ids` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 284 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for coupon_goods_sku
-- ----------------------------
DROP TABLE IF EXISTS `coupon_goods_sku`;
CREATE TABLE `coupon_goods_sku`  (
  `coupon_sku_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '卡券skuid',
  `coupon_spu_id` bigint NOT NULL COMMENT '卡券id(coupon_goods主键)',
  `coupon_platform_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台卡券id',
  `amount_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '面值名称',
  `amount` decimal(10, 2) NOT NULL COMMENT '面值',
  `price` decimal(10, 2) NOT NULL COMMENT '卡管售价（福鲤圈暂用不到）',
  `cost_price` decimal(10, 2) NOT NULL COMMENT '福鲤圈成本价',
  `commission_fee` decimal(10, 2) NOT NULL COMMENT '手续费百分比',
  `platform_price` decimal(10, 2) NOT NULL COMMENT '福鲤圈的售价(建议售价，不含手续费)',
  `limit_num_per_month` int NULL DEFAULT NULL COMMENT '每月每人单卡限购数量',
  `stock_warn_num` int NULL DEFAULT NULL COMMENT '库存预警数量(废弃)',
  `sold_num` int NOT NULL DEFAULT 0 COMMENT '已售数量',
  `inventory` int NULL DEFAULT NULL COMMENT '卡券库存(仅普通卡券有效)',
  `specs_value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格值id，多个规格值用逗号分隔',
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注富文本',
  `is_more_offset` tinyint NULL DEFAULT NULL COMMENT '是否多次核销 0否 1是',
  `type` int NULL DEFAULT NULL COMMENT '卡管返回的卡券类型（未使用） 101: 平台自发券 201: 卡密+卡号 202: 卡号或卡密 203: 卡号+卡密+校验码 301: 链接类 302: 链接+验证码 303: 卡号+短链接+验证码 304：卡号+短链接',
  `pn_type` int NULL DEFAULT NULL COMMENT '1-直充类商品，2-卡券类商品（仅限：品诺)',
  `on_sale_num` int NULL DEFAULT NULL COMMENT '起售数量',
  `ration_sale_num` int NULL DEFAULT NULL COMMENT '单次限售数量',
  `coupon_valid_time` datetime NULL DEFAULT NULL COMMENT '卡券有效期',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '卡券状态 0:下架 1:出售中',
  `center_status` tinyint NULL DEFAULT NULL COMMENT '卡管平台状态0:下架 1:出售中',
  `account_type` tinyint NULL DEFAULT NULL COMMENT '品诺-视听充值类型 0 其他 1 手机号 2 QQ号',
  `sort` tinyint NULL DEFAULT NULL COMMENT '排序',
  `manual_down` tinyint NULL DEFAULT 0 COMMENT '是否手动下架卡券(0:否 1:是)',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志0--正常，1--删除',
  PRIMARY KEY (`coupon_sku_id`) USING BTREE,
  INDEX `sku_coupon_platform_id_idx`(`coupon_platform_id` ASC) USING BTREE COMMENT '第三方平台id',
  INDEX `sku_coupon_spu_id_idx`(`coupon_spu_id` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 216 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡券sku表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for coupon_service_charge
-- ----------------------------
DROP TABLE IF EXISTS `coupon_service_charge`;
CREATE TABLE `coupon_service_charge`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `phone_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户手机号',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `coupon_num` int NULL DEFAULT NULL COMMENT '卡券数量',
  `other_num` int NULL DEFAULT NULL COMMENT '第三方应用数量',
  `direct_charge_num` int NULL DEFAULT NULL COMMENT '直冲产品数量',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `mod_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人姓名',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `status` int NULL DEFAULT NULL COMMENT '启用状态 0未启用 1已启用',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '手续白名单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_service_label
-- ----------------------------
DROP TABLE IF EXISTS `coupon_service_label`;
CREATE TABLE `coupon_service_label`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `coupon_label_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡券服务说明标签',
  `status` tinyint NOT NULL COMMENT '启用状态 0关闭 1启动',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标记',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `mod_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡券服务标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for coupon_stock_warn
-- ----------------------------
DROP TABLE IF EXISTS `coupon_stock_warn`;
CREATE TABLE `coupon_stock_warn`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '库存预警id',
  `model` tinyint NULL DEFAULT NULL COMMENT '预警类型 0卡券库存预警 1每日放量预警',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '预警人员名称',
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `type` tinyint NULL DEFAULT 1 COMMENT '预警方式 1:手机短信',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
  `sort` int NULL DEFAULT NULL COMMENT '排序 数字越大越靠前',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '更新人id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标签 0:未删除 1:已删除',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `del_id` bigint NULL DEFAULT NULL COMMENT '删除人id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡券库存预警名单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for customer
-- ----------------------------
DROP TABLE IF EXISTS `customer`;
CREATE TABLE `customer`  (
  `id` bigint NOT NULL COMMENT '用户ID',
  `vip_id` bigint NULL DEFAULT NULL COMMENT '瑞祥会员中心会员id',
  `user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `unionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `openid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信移动应用openId',
  `mini_openid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信小程序openId',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `password` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户密码',
  `phone` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机',
  `sex` tinyint NULL DEFAULT NULL COMMENT '性别（1男，0女）',
  `birthday` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生日',
  `user_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户头像',
  `user_grade_id` bigint NULL DEFAULT 1 COMMENT '会员等级id',
  `status` tinyint NULL DEFAULT 1 COMMENT '启用状态 0：禁用 1：启用 2：已注销',
  `login_ip` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登录ip',
  `login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `login_num` bigint NULL DEFAULT NULL COMMENT '登录次数',
  `phone_token` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机设备号',
  `login_status` tinyint NULL DEFAULT 1 COMMENT '登录状态（1 是，0否）',
  `nkb_bind_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '纳客宝的绑定id，类似会员Id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '注册时间 ',
  `create_id` bigint NULL DEFAULT NULL COMMENT '添加人',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '0--正常，1--已删除',
  `use_default_pay_sort` tinyint NOT NULL DEFAULT 1 COMMENT '是否使用默认扣款顺序 1是 0否',
  `open_password_free_Payment` tinyint NOT NULL DEFAULT 0 COMMENT '是否开启免密支付 1是 0否',
  `company_id` bigint NULL DEFAULT NULL COMMENT '企业ID',
  `is_has_birth_coupon` tinyint NULL DEFAULT 0 COMMENT '是否领取生日券 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix1_t_user`(`user_name` ASC) USING BTREE,
  INDEX `ix2_t_user`(`phone` ASC) USING BTREE,
  INDEX `idx_user_u`(`unionid` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for customer_pay_sort
-- ----------------------------
DROP TABLE IF EXISTS `customer_pay_sort`;
CREATE TABLE `customer_pay_sort`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `customer_id` bigint NOT NULL,
  `pay_sort` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付方式优先级列表，编码参考payment_method表',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '0--正常，1--已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customer_phone_change_record
-- ----------------------------
DROP TABLE IF EXISTS `customer_phone_change_record`;
CREATE TABLE `customer_phone_change_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '变更记录id',
  `customer_id` bigint NOT NULL COMMENT '用户id',
  `change_method` tinyint NULL DEFAULT NULL COMMENT '变更方式 1用户主动修改 2后台修改 3用户主动注销',
  `original_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原手机号',
  `new_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '新手机号',
  `create_time` datetime NULL DEFAULT NULL COMMENT '变更时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '变更操作人id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for help_cat
-- ----------------------------
DROP TABLE IF EXISTS `help_cat`;
CREATE TABLE `help_cat`  (
  `cat_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类id',
  `cat_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类名称',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(数字越大排序越靠前)',
  `status` tinyint NOT NULL COMMENT '状态(0禁用  1启用)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`cat_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '帮助中心分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for help_detail
-- ----------------------------
DROP TABLE IF EXISTS `help_detail`;
CREATE TABLE `help_detail`  (
  `help_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '帮助id',
  `cat_id` int NOT NULL COMMENT '分类id',
  `help_question` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '问题',
  `type` tinyint NOT NULL COMMENT '资讯类型(1:图文详情 2:单链接)',
  `help_answer` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '帮助回答(类型1对应图文富文本,类型2对应链接)',
  `click_num` bigint NOT NULL DEFAULT 0 COMMENT '点击量',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(数字越大排序越靠前)',
  `status` tinyint NOT NULL COMMENT '状态(0禁用  1启用)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`help_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '帮助中心详情' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for home_page
-- ----------------------------
DROP TABLE IF EXISTS `home_page`;
CREATE TABLE `home_page`  (
  `page_id` int NOT NULL AUTO_INCREMENT COMMENT '页面id',
  `page_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '页面名称',
  `publish_time` datetime NULL DEFAULT NULL COMMENT '定时发布时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `del_id` bigint NULL DEFAULT NULL COMMENT '删除人id',
  PRIMARY KEY (`page_id`) USING BTREE,
  INDEX `home_publish`(`publish_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '首页列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hot_city
-- ----------------------------
DROP TABLE IF EXISTS `hot_city`;
CREATE TABLE `hot_city`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `city_id` bigint NOT NULL COMMENT '城市id',
  `city_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '城市名',
  `parent_region_id` bigint NOT NULL COMMENT '父地址id',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(数字越大排序越靠前)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_id` bigint NOT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `city_name_idx`(`city_name` ASC) USING BTREE,
  INDEX `create_time_idx`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 334 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '热门城市' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for information_cat
-- ----------------------------
DROP TABLE IF EXISTS `information_cat`;
CREATE TABLE `information_cat`  (
  `cat_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '资讯分类id',
  `cat_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `status` tinyint NOT NULL COMMENT '状态(0禁用  1启用)',
  `sort` int NULL DEFAULT NULL COMMENT '排序(数字越大排序越靠前)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NULL DEFAULT NULL COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`cat_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '资讯分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for information_detail
-- ----------------------------
DROP TABLE IF EXISTS `information_detail`;
CREATE TABLE `information_detail`  (
  `information_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '资讯id',
  `cat_id` int NOT NULL COMMENT '资讯分类id',
  `information_title` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '资讯名称',
  `sub_title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '副标题',
  `label` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资讯标签',
  `img_url` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '资讯图片url',
  `is_nationwide` tinyint NOT NULL COMMENT '上线城市：0非全国 1全国',
  `is_suggest` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '是否推荐，0不推荐 1推荐',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '资讯内容(图文富文本)',
  `sort` int NULL DEFAULT NULL COMMENT '排序(数字越大排序越靠前)',
  `top` tinyint NULL DEFAULT 0 COMMENT '是否置顶(0:否 1:是)',
  `read_num` int NULL DEFAULT NULL COMMENT '已读数量',
  `forward_num` int NULL DEFAULT NULL COMMENT '转发数量',
  `comment_num` int NOT NULL DEFAULT 0 COMMENT '评论数量',
  `start_time` datetime NOT NULL COMMENT '有效期开始时间',
  `end_time` datetime NOT NULL COMMENT '有效期到期时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NULL DEFAULT NULL COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`information_id`) USING BTREE,
  INDEX `cat_id_IDX`(`cat_id` ASC) USING BTREE,
  INDEX `create_time_IDX`(`create_time` ASC) USING BTREE,
  INDEX `start_time_IDX`(`start_time` ASC) USING BTREE,
  INDEX `end_time_IDX`(`end_time` ASC) USING BTREE,
  INDEX `time_top_nationwide_IDX`(`start_time` ASC, `end_time` ASC, `top` ASC, `is_nationwide` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '资讯详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for information_evaluate
-- ----------------------------
DROP TABLE IF EXISTS `information_evaluate`;
CREATE TABLE `information_evaluate`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论id',
  `information_id` int NOT NULL COMMENT '咨询id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `phone` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机',
  `content` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '评论内容',
  `praise_num` int NULL DEFAULT 0 COMMENT '点赞数量',
  `audit_status` tinyint NOT NULL COMMENT '审核状态：0待审核 1审核通过 2审核驳回',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `audit_id` bigint NULL DEFAULT NULL COMMENT '审核人id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `evaluate_information_id_IDX`(`information_id` ASC, `audit_status` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '资讯评价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for message_account
-- ----------------------------
DROP TABLE IF EXISTS `message_account`;
CREATE TABLE `message_account`  (
  `account_msg_id` bigint NOT NULL AUTO_INCREMENT COMMENT '账户消息id',
  `customer_id` bigint NULL DEFAULT NULL COMMENT '客户id',
  `thumb_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '缩略图',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
  `sub_title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '副标题',
  `type` int NOT NULL COMMENT '类型（待定）',
  `is_read` int NULL DEFAULT NULL COMMENT '是否已读 0否 1是',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '内容',
  `pay_type` int NULL DEFAULT NULL COMMENT '支付方式',
  `pay_type_content` int NULL DEFAULT NULL COMMENT '支付方式内容\n{\n  \"subType\": \" 1提货凭证兑换\",\n  \"remark\": \"这是一个示例描述\",\n  \"amount\": \"-100积分\",\n  \"children\": {\n    \"subType\": \"子类型\",\n    \"remark\": \"子节点描述\",\n    \"amount\": \"-50积分\",\n    \"children\": null\n  }\n}',
  `pay_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款卡号',
  `order_create_time` datetime NULL DEFAULT NULL COMMENT '订单创建时间',
  `order_pay_time` datetime NULL DEFAULT NULL COMMENT '订单支付时间',
  `order_id` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '订单id',
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '金额/积分',
  `nums` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数量',
  PRIMARY KEY (`account_msg_id`) USING BTREE,
  INDEX `create_time_idx`(`create_time` ASC) USING BTREE,
  INDEX `message_account_customer_id_IDX`(`customer_id` ASC, `is_read` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '账户消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for message_cat
-- ----------------------------
DROP TABLE IF EXISTS `message_cat`;
CREATE TABLE `message_cat`  (
  `cat_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类id',
  `cat_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类名称',
  `status` tinyint NOT NULL COMMENT '状态(0禁用  1启用)',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(数字越大排序越靠前)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`cat_id`) USING BTREE,
  INDEX `create_time_status_idx`(`create_time` ASC, `status` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '消息分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for message_detail
-- ----------------------------
DROP TABLE IF EXISTS `message_detail`;
CREATE TABLE `message_detail`  (
  `message_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '消息id',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
  `sub_title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '副标题',
  `cat_id` int NOT NULL COMMENT '分类id',
  `thumb_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '缩略图',
  `type` tinyint NOT NULL COMMENT '资讯类型(1:图文详情 2:单链接)',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '内容',
  `read_num` int NULL DEFAULT NULL COMMENT '点击阅读量',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(数字越大排序越靠前)',
  `status` tinyint NOT NULL COMMENT '状态(0禁用  1启用)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`message_id`) USING BTREE,
  INDEX `cat_id_idx`(`cat_id` ASC, `del_flag` ASC) USING BTREE,
  INDEX `create_time_idx`(`create_time` ASC) USING BTREE,
  INDEX `idx_del_update`(`del_flag` ASC, `mod_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '消息内容' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for message_scheduled_publish
-- ----------------------------
DROP TABLE IF EXISTS `message_scheduled_publish`;
CREATE TABLE `message_scheduled_publish`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `message_id` int NULL DEFAULT NULL COMMENT '消息id',
  `scheduled_publish_time` datetime NULL DEFAULT NULL COMMENT '预约发布时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NULL DEFAULT NULL COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '消息定时发布表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for message_serve
-- ----------------------------
DROP TABLE IF EXISTS `message_serve`;
CREATE TABLE `message_serve`  (
  `serve_msg_id` bigint NOT NULL AUTO_INCREMENT COMMENT '客户消息id',
  `customer_id` bigint NULL DEFAULT NULL COMMENT '客户id',
  `thumb_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '缩略图',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
  `sub_title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '副标题',
  `type` int NOT NULL COMMENT '类型（待定）',
  `is_read` int NULL DEFAULT NULL COMMENT '是否已读 0否 1是',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '内容',
  `pay_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款卡号',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `amount` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '金额/面值',
  `nums` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数量',
  `url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '连接',
  PRIMARY KEY (`serve_msg_id`) USING BTREE,
  INDEX `create_time_idx`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客服消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for message_user_system
-- ----------------------------
DROP TABLE IF EXISTS `message_user_system`;
CREATE TABLE `message_user_system`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `customer_id` bigint NOT NULL COMMENT '客户id',
  `message_id` bigint NOT NULL COMMENT '消息id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `message_customer_id_IDX`(`customer_id` ASC, `message_id` ASC) USING BTREE,
  INDEX `message_user_system_message_id_IDX`(`message_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '消息用户客服关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for module_basic
-- ----------------------------
DROP TABLE IF EXISTS `module_basic`;
CREATE TABLE `module_basic`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '装修组件id',
  `module_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '装修组件code',
  `module_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件名称',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '组件基础信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for module_detail
-- ----------------------------
DROP TABLE IF EXISTS `module_detail`;
CREATE TABLE `module_detail`  (
  `detail_id` int NOT NULL AUTO_INCREMENT COMMENT '详情id',
  `module_id` int NOT NULL COMMENT '所属组件id',
  `parent_detail_id` int NOT NULL DEFAULT 0 COMMENT '父详情id(相同的父详情在同一个位置)',
  `detail_title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详情标题',
  `detail_sub_title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详情副标题',
  `detail_type` int NOT NULL COMMENT '详情类型(100:普通卡券 200:组合套餐卡券 300:普通应用 400:支付宝红包应用/苏西话费应用 1000:普通广告位 1100:单图 1200:轮播图 1300:单资讯 1400:多资讯 1500:本地生活 1600:全球购商品 1700:装修活动页 1800:自定义链接 1900:分类页)',
  `product_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品id(根据type对应各表主键,分类产品特殊,多个分类逗号分割,全部分类直接传0)',
  `product_item_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品skuid',
  `product_name` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品名称',
  `product_sub_name` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品副标题',
  `product_price` float(10, 2) NULL DEFAULT NULL COMMENT '产品价格',
  `product_original_price` float(10, 2) NULL DEFAULT NULL COMMENT '产品原价',
  `img_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '背景图片链接(不包含卡券，应用类型等图片,关联对应表查询)',
  `detail_content` json NULL COMMENT '详情内容(包含各平台跳转链接等)',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态(0:下架 1:上架)',
  `sort_order` double NOT NULL COMMENT '排序',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '更新者',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `del_id` bigint NULL DEFAULT NULL COMMENT '删除者id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志（0存在 1删除）',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`detail_id`) USING BTREE,
  INDEX `module_basic_detail_module_id_IDX`(`module_id` ASC, `sort_order` ASC) USING BTREE,
  INDEX `module_detail_module_id_IDX`(`module_id` ASC, `parent_detail_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 105 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组件内容详情' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for module_info
-- ----------------------------
DROP TABLE IF EXISTS `module_info`;
CREATE TABLE `module_info`  (
  `module_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '组件id',
  `module_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组件code',
  `activity_id` int NOT NULL COMMENT '所属活动id(0:首页)',
  `page_id` int NOT NULL COMMENT '页面id',
  `module_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件名称',
  `national` tinyint NULL DEFAULT 1 COMMENT '是否全国楼层(0:否 1:是)',
  `content` json NULL COMMENT '装修样式属性json',
  `inherit_national_id` int NULL DEFAULT NULL COMMENT '继承自全国组件id(继承且自定义,说明关闭同步开关)',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '更新者',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `del_id` bigint NULL DEFAULT NULL COMMENT '删除者id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`module_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '基础装修组件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for module_region_relational
-- ----------------------------
DROP TABLE IF EXISTS `module_region_relational`;
CREATE TABLE `module_region_relational`  (
  `relational_id` int NOT NULL AUTO_INCREMENT COMMENT '关联id',
  `module_id` int NOT NULL COMMENT '组件id',
  `page_region_id` int NOT NULL COMMENT '装修区域id',
  `activity_id` int NOT NULL COMMENT '所属活动id(0:首页)',
  `page_id` int NOT NULL COMMENT '页面id',
  `show_status` tinyint NOT NULL DEFAULT 1 COMMENT '显示状态(0:不显示 1:显示)',
  `sort_order` double NOT NULL COMMENT '排序',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '更新者',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `del_id` bigint NULL DEFAULT NULL COMMENT '删除者id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`relational_id`) USING BTREE,
  INDEX `module_region_relational_page_region_id_IDX`(`page_region_id` ASC, `activity_id` ASC, `module_id` ASC) USING BTREE,
  INDEX `module_region_relational_module_id_IDX`(`module_id` ASC, `page_region_id` ASC) USING BTREE,
  INDEX `module_region_relational_page_region_id_status_IDX`(`page_region_id` ASC, `show_status` ASC, `sort_order` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 115 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组件和区域关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for package_sku
-- ----------------------------
DROP TABLE IF EXISTS `package_sku`;
CREATE TABLE `package_sku`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '套餐skuId',
  `package_spu_id` bigint NOT NULL COMMENT '套餐spu id',
  `commission_fee` decimal(10, 2) NOT NULL COMMENT '手续费百分比',
  `platform_price` decimal(10, 2) NOT NULL COMMENT '福鲤圈的售价',
  `limit_num_per_month` int NULL DEFAULT NULL COMMENT '每月每人单卡限购数量',
  `inventory_warn_num` int NULL DEFAULT NULL COMMENT '库存预警数量',
  `sold_num` int NULL DEFAULT 0 COMMENT '销量',
  `package_sku_status` tinyint NOT NULL DEFAULT 0 COMMENT '套餐sku状态 0:下架 1:出售中',
  `spec_value_id` bigint NULL DEFAULT NULL COMMENT '规格值id,1个套餐sku对应1个规格值',
  `manual_down` tinyint NULL DEFAULT 0 COMMENT '是否手动上下架 1是 0否',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注富文本',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `mod_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记 0 未删除 1已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_package_spu`(`package_spu_id` ASC) USING BTREE COMMENT '套餐spuid'
) ENGINE = InnoDB AUTO_INCREMENT = 147 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '套餐sku表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for package_spu
-- ----------------------------
DROP TABLE IF EXISTS `package_spu`;
CREATE TABLE `package_spu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键,套餐spu id',
  `package_spu_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '套餐名称',
  `sub_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '套餐副标题',
  `brand_id` bigint NULL DEFAULT NULL COMMENT '品牌id',
  `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道id',
  `second_cat_id` bigint NOT NULL COMMENT '平台二级分类id',
  `first_cat_id` bigint NULL DEFAULT NULL COMMENT '平台一级分类id',
  `subscript_id` bigint NULL DEFAULT NULL COMMENT '角标id',
  `logo_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'logo图片url',
  `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主图&列表图url',
  `coupon_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '套餐包含的卡券类型',
  `pay_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '支付方式编码组合，多个用分号分隔，如：WX;RB',
  `package_status` tinyint NOT NULL DEFAULT 0 COMMENT '卡券状态 0:下架 1:出售中',
  `exchange_process` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '兑换流程富文本',
  `sort` int NULL DEFAULT NULL COMMENT '排序(数字越大排序越靠前)',
  `virtual_stock` int NULL DEFAULT 0 COMMENT '销量（虚拟）',
  `forward_num` int NULL DEFAULT 0 COMMENT '转发量（虚拟）',
  `label_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '套餐服务标签id列表 逗号分隔',
  `theme_color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主题背景色',
  `audit_status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态 0:待审核 1:审核通过 2:审核驳回',
  `latest_audit_id` bigint NULL DEFAULT NULL COMMENT '最新审核记录ID',
  `category_display_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '分类页下展示的名称',
  `is_show_category` tinyint(1) NOT NULL COMMENT '是否在分类页显示 1 显示 0 不显示',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `del_flag` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除标记 0 未删除 1 已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ps_brand_id_idx`(`brand_id` ASC) USING BTREE COMMENT '套餐品牌',
  INDEX `ps_channel_id_idx`(`channel_id` ASC) USING BTREE COMMENT '套餐渠道',
  INDEX `ps_second_cat_id_idx`(`second_cat_id` ASC) USING BTREE COMMENT '套餐第二分类',
  INDEX `ps_first_cat_id_idx`(`first_cat_id` ASC) USING BTREE COMMENT '套餐第一分类'
) ENGINE = InnoDB AUTO_INCREMENT = 135 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '套餐spu表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for package_sub_sku
-- ----------------------------
DROP TABLE IF EXISTS `package_sub_sku`;
CREATE TABLE `package_sub_sku`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键,套餐子skuid 对应单个卡券',
  `package_spu_id` bigint NOT NULL COMMENT '套餐spuId',
  `package_sku_id` bigint NOT NULL COMMENT '套餐skuId',
  `brand_id` bigint NULL DEFAULT NULL COMMENT '品牌id',
  `outer_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '卡管等第三方数据的id(支付宝对应活动编码)',
  `amount_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '面值名称',
  `amount` decimal(10, 2) NOT NULL COMMENT '面值',
  `price` decimal(10, 2) NOT NULL COMMENT '卡管售价',
  `cost_price` decimal(10, 2) NOT NULL COMMENT '卡管成本价',
  `sold_num` int NULL DEFAULT 0 COMMENT '已售数量',
  `sub_sku_status` tinyint NOT NULL DEFAULT 0 COMMENT '子sku状态 0:下架 1:出售中',
  `inventory` int NOT NULL DEFAULT 0 COMMENT '卡券库存',
  `is_more_offset` tinyint NULL DEFAULT NULL COMMENT '是否多次核销 0否 1是',
  `account_type` tinyint NULL DEFAULT NULL COMMENT '品诺充值类型 0 其他 1 手机号 2 QQ号',
  `type` int NULL DEFAULT NULL COMMENT '卡券类型 101: 平台自发券 201: 卡密+卡号 202: 卡号或卡密 203: 卡号+卡密+校验码 301: 链接类 302: 链接+验证码 303: 卡号+短链接+验证码 304：卡号+短链接',
  `flq_type` int NULL DEFAULT NULL COMMENT '自定义核销类型 1:面值+兑换码 2:面值+兑换码+一维二维码 3:面值+卡号+一维二维码  4面值+卡号卡密 5面值+卡号卡密+兑换码 6面值+卡号卡密+一维二维码 7面值+卡密+一维二维码 8面值+链接 9自发券(面值+提货券形式)',
  `pn_type` int NULL DEFAULT NULL COMMENT '品诺类型 直充类商品2卡券类商品',
  `coupon_type` tinyint NULL DEFAULT NULL COMMENT '系统卡券类型：1:普通卡券 3:品诺',
  `package_coupon_num` int NOT NULL DEFAULT 1 COMMENT '该套餐中的子sku数量',
  `on_sale_num` int NULL DEFAULT NULL COMMENT '起售数量',
  `ration_sale_num` int NULL DEFAULT NULL COMMENT '限售数量',
  `coupon_valid_time` datetime NULL DEFAULT NULL COMMENT '卡券有效期',
  `center_status` tinyint NULL DEFAULT NULL COMMENT '卡管平台状态0:下架 1:出售中',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `del_flag` tinyint NOT NULL COMMENT '删除标记 0 未删除 1 已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pss_package_spu_id_idx`(`package_spu_id` ASC) USING BTREE COMMENT '套餐spuid',
  INDEX `pss_package_sku_id_idx`(`package_sku_id` ASC) USING BTREE COMMENT '套餐skuid'
) ENGINE = InnoDB AUTO_INCREMENT = 138 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '套餐子sku表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for page_info
-- ----------------------------
DROP TABLE IF EXISTS `page_info`;
CREATE TABLE `page_info`  (
  `page_id` int NOT NULL AUTO_INCREMENT COMMENT '装修页id',
  `activity_id` int NOT NULL COMMENT '活动id(0代表首页)',
  `page_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '页面名称',
  `release_begin_time` datetime NULL DEFAULT NULL COMMENT '发布开始时间',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态(0:未发布 1:已发布 2:定时发布)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `del_id` bigint NULL DEFAULT NULL COMMENT '删除人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (`page_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '装修页面' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for page_region
-- ----------------------------
DROP TABLE IF EXISTS `page_region`;
CREATE TABLE `page_region`  (
  `page_region_id` int NOT NULL AUTO_INCREMENT COMMENT '装修区域主键',
  `activity_id` int NOT NULL COMMENT '所属活动id(0:首页)',
  `page_id` int NULL DEFAULT 0 COMMENT '所属页面id(仅首页适应)',
  `province_id` int NOT NULL COMMENT '省份id(0:表示全国)',
  `city_id` int NOT NULL COMMENT '城市id',
  `district_id` int NULL DEFAULT NULL COMMENT '区id',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `del_id` bigint NULL DEFAULT NULL COMMENT '删除人id',
  PRIMARY KEY (`page_region_id`) USING BTREE,
  INDEX `page_region_activity_id_IDX`(`activity_id` ASC, `page_id` ASC) USING BTREE,
  INDEX `page_region_province_id_IDX`(`province_id` ASC) USING BTREE,
  INDEX `page_region_city_id_IDX`(`city_id` ASC) USING BTREE,
  INDEX `page_region_district_id_IDX`(`district_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '装修区域' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for payment_method
-- ----------------------------
DROP TABLE IF EXISTS `payment_method`;
CREATE TABLE `payment_method`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '支付方式编码',
  `name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '支付方式名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '支付方式描述',
  `enable_status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `use_center_card_type` int NULL DEFAULT NULL COMMENT '会员中台支付类型',
  `v1_shop_agent_card_type` int NULL DEFAULT NULL COMMENT '商户大全卡类型',
  `quick_pay_card_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '多卡支付卡类型',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_method_code`(`code` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '支付方式表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_brand
-- ----------------------------
DROP TABLE IF EXISTS `product_brand`;
CREATE TABLE `product_brand`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '品牌id',
  `brand_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '品牌名',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '启动状态：0禁用 1启用',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `brand_name_idx`(`brand_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品品牌' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_category
-- ----------------------------
DROP TABLE IF EXISTS `product_category`;
CREATE TABLE `product_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `category_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `lever` tinyint NOT NULL COMMENT '1一级分类，2二级分类',
  `sort` int NOT NULL DEFAULT 1 COMMENT '排序',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态 0关闭 1启动',
  `parent_id` int NOT NULL COMMENT '父id',
  `parent_category_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父类分类名称',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标记 0、未删除 1、已删除',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `mod_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人姓名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 192 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '产品分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_channel
-- ----------------------------
DROP TABLE IF EXISTS `product_channel`;
CREATE TABLE `product_channel`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '渠道id',
  `channel_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '渠道名',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '启动状态：0禁用 1启用',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `channel_name_idx`(`channel_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品渠道' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_dialog
-- ----------------------------
DROP TABLE IF EXISTS `product_dialog`;
CREATE TABLE `product_dialog`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `product_spu_id` bigint UNSIGNED NOT NULL COMMENT '卡券spuid',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1 卡券 2 套餐',
  `dialog_type` int NULL DEFAULT NULL COMMENT '1:提交订单弹框 2:核销页弹框',
  `dialog_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '弹框富文本',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标记',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 358 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡券弹框表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_explain
-- ----------------------------
DROP TABLE IF EXISTS `product_explain`;
CREATE TABLE `product_explain`  (
  `explain_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '说明id',
  `product_spu_id` bigint NOT NULL COMMENT '产品spuid',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1 卡券 2 套餐',
  `explain_type` tinyint NOT NULL COMMENT '说明类型 1:兑换须知 2:核销须知 3使用说明 4温馨提示',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '说明标题',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '说明内容富文本',
  `sort` int NOT NULL DEFAULT 0 COMMENT '说明排序(数字大的靠前)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`explain_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 645 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡券说明表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_offset_page
-- ----------------------------
DROP TABLE IF EXISTS `product_offset_page`;
CREATE TABLE `product_offset_page`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '核销页核销须知id',
  `product_spu_id` bigint NOT NULL COMMENT '产品spuid',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1 卡券 2 套餐',
  `scenarios` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '适用场景',
  `background_color` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '背景色',
  `validity_date` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '核销有效期',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 200 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡券核销页-核销须知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `product_oper_log`;
CREATE TABLE `product_oper_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `spu_id` bigint NOT NULL COMMENT '产品/风控的id',
  `sku_id` bigint NULL DEFAULT NULL COMMENT '产品的SKUID',
  `product_type` tinyint NOT NULL COMMENT '产品类型 1:卡券 2:套餐 3:应用 4支付宝红包 5苏西话费 6风控',
  `operation_type` int NULL DEFAULT 0 COMMENT '业务类型（1:新增产品 2:产品基本信息变更 3:SKU信息手动变更 4:区域限额变更 5:产品详情变更 6:spu上架 7:spu下架 8:spu删除 9:SKU跟随卡管上架 10:SKU跟随卡管下架 11:SKU跟随卡管信息变更 12:新增风控策略 13删除风控策略 14风控商品变更 15风控用户变更）',
  `submitter_id` bigint NOT NULL COMMENT '提交人id',
  `submit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `auditor_id` bigint NOT NULL COMMENT '审核人 ID',
  `audit_time` datetime NOT NULL COMMENT '审核时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `spu_type_idx`(`spu_id` ASC, `product_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1523 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_region_policy_relation
-- ----------------------------
DROP TABLE IF EXISTS `product_region_policy_relation`;
CREATE TABLE `product_region_policy_relation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `policy_id` bigint NOT NULL COMMENT '价格策略ID',
  `product_sku_id` bigint NOT NULL COMMENT '产品SKU ID',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1:卡券 2:套餐',
  `region_id` int NOT NULL COMMENT '区域ID',
  `region_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区域名称',
  `region_type` int NOT NULL COMMENT '区域类型 ',
  `parent_region_id` int NOT NULL DEFAULT 0 COMMENT '父级区域id',
  `parent_region_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父级区域名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_region_id`(`region_id` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE,
  INDEX `uk_policy_city`(`policy_id` ASC, `region_id` ASC) USING BTREE,
  INDEX `idx_policy_id`(`policy_id` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15856 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品区域价格策略关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_risk_audit
-- ----------------------------
DROP TABLE IF EXISTS `product_risk_audit`;
CREATE TABLE `product_risk_audit`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '审核记录 ID',
  `risk_id` bigint NULL DEFAULT NULL COMMENT '风控id',
  `product_type` tinyint NOT NULL COMMENT '产品类型 1:卡券 2套餐 3:应用 4支付宝红包/苏西话费',
  `risk_product_type` int NULL DEFAULT NULL COMMENT '扁平风控产品类型： 1卡券 2套餐 3普通应用4支付宝红包 5苏西话费 6扫码付应用',
  `operation_type` tinyint NOT NULL COMMENT '操作类型 1:新增风控 2修改商品 3 编辑会员 4删除风控',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核备注',
  `audit_status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态 0:待审核 1:审核通过 2:审核驳回 3审核失败',
  `before_data` json NULL COMMENT '变更前数据(JSON 格式)',
  `after_data` json NULL COMMENT '变更后数据(JSON 格式)',
  `change_fields` json NULL COMMENT '变更字段列表',
  `risk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '风控名称',
  `status` tinyint NULL DEFAULT NULL COMMENT '风控状态 0 关闭 1启动',
  `member_num` int NULL DEFAULT NULL COMMENT '会员数量',
  `submitter_id` bigint NOT NULL COMMENT '提交人id',
  `submitter_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '提交人姓名',
  `submit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `auditor_id` bigint NULL DEFAULT NULL COMMENT '审核人 ID',
  `auditor_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人姓名',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_spu_type`(`product_type` ASC) USING BTREE,
  INDEX `idx_submit_time`(`submit_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 96 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '风控审核记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_risk_company
-- ----------------------------
DROP TABLE IF EXISTS `product_risk_company`;
CREATE TABLE `product_risk_company`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '企业id',
  `company_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '企业名称',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '启动状态：0禁用 1启用',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `mod_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人姓名',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` bigint NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `brand_name_idx`(`company_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '产品风控企业表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_risk_control
-- ----------------------------
DROP TABLE IF EXISTS `product_risk_control`;
CREATE TABLE `product_risk_control`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '风控策略id',
  `risk_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风控策略名称',
  `risk_type` tinyint NULL DEFAULT 0 COMMENT '风控类型 0普通风控 1禁用风控',
  `product_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品类型 产品类型 1:卡券 2套餐 3:应用 4支付宝红包/苏西话费',
  `risk_product_type` tinyint NOT NULL DEFAULT 0 COMMENT '风控产品类型： 1卡券 2套餐 3应用，4支付宝红包 5苏西话费 6扫码付应用',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态 0 关闭 1启动',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标记',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `mod_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品风控策略主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_risk_control_account
-- ----------------------------
DROP TABLE IF EXISTS `product_risk_control_account`;
CREATE TABLE `product_risk_control_account`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '风控策略会员id',
  `risk_id` bigint NULL DEFAULT NULL COMMENT '风控策略id',
  `account_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账户名称',
  `account_type` tinyint NOT NULL COMMENT '账户类型 0手机号',
  `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `company_id` bigint NULL DEFAULT NULL COMMENT '企业id',
  `pay_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式，逗号隔开',
  `risk_type` tinyint NULL DEFAULT NULL COMMENT '风控类型 0风控策略 1禁用策略',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标记',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人',
  `mod_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品风控策略会员账户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_risk_control_sku
-- ----------------------------
DROP TABLE IF EXISTS `product_risk_control_sku`;
CREATE TABLE `product_risk_control_sku`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `risk_id` bigint NULL DEFAULT NULL COMMENT '风控策略id',
  `product_spu_id` bigint NOT NULL COMMENT '产品spuid',
  `product_sku_id` bigint NOT NULL COMMENT '产品skuid（应用id）',
  `risk_product_type` tinyint NULL DEFAULT NULL COMMENT '风控产品类型',
  `commission_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '手续费百分比',
  `sku_amount_per_day` decimal(10, 2) NULL DEFAULT NULL COMMENT '每人每日限额',
  `sku_amount_per_month` decimal(10, 2) NULL DEFAULT NULL COMMENT '每人每月限额',
  `suggest_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '建议零售价',
  `spu_amount_per_month` decimal(10, 2) UNSIGNED NULL DEFAULT NULL COMMENT 'spu每月限售总额',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人',
  `mod_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标记',
  `risk_type` tinyint NULL DEFAULT NULL COMMENT '风控类型 0风控策略 1禁用策略',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品风控策略sku绑定表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_sku_sell_region
-- ----------------------------
DROP TABLE IF EXISTS `product_sku_sell_region`;
CREATE TABLE `product_sku_sell_region`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '限制id',
  `product_spu_id` bigint NOT NULL COMMENT '产品spuid',
  `product_sku_id` bigint NOT NULL COMMENT '产品sku id',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1:卡券 2:套餐',
  `region_id` int NOT NULL COMMENT '区域id(0:全国)',
  `region_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '区域名称',
  `region_type` int NOT NULL COMMENT '区域类型',
  `parent_region_id` int NOT NULL DEFAULT 0 COMMENT '父级区域id，用于聚合前端展示',
  `parent_region_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父级区域名称',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `del_id` bigint NULL DEFAULT NULL COMMENT '删除人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_id` bigint NOT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_coupon_id`(`product_spu_id` ASC, `region_id` ASC) USING BTREE,
  INDEX `spu_sku_type_idx`(`product_spu_id` ASC, `product_sku_id` ASC, `product_type` ASC) USING BTREE,
  INDEX `product_sku_sell_region_region_id_IDX`(`region_id` ASC, `region_type` ASC) USING BTREE,
  INDEX `idx_sku_type_del`(`product_sku_id` ASC, `product_type` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 69274 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '产品SKU可售区域表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_specs_name
-- ----------------------------
DROP TABLE IF EXISTS `product_specs_name`;
CREATE TABLE `product_specs_name`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规格名称 id',
  `product_spu_id` bigint NOT NULL COMMENT '产品spu',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1 卡券 2 套餐',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `specs_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规格名称',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product`(`product_spu_id` ASC, `product_type` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 569 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡券规格名称表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_specs_value
-- ----------------------------
DROP TABLE IF EXISTS `product_specs_value`;
CREATE TABLE `product_specs_value`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规格id',
  `product_spu_id` bigint NOT NULL COMMENT '产品spuid',
  `specs_name_id` bigint NOT NULL COMMENT '属性名称id',
  `specs_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规格名称',
  `specs_value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规格值',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `product_type` tinyint NOT NULL COMMENT '产品类型 1 卡券 2 套餐',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_prd`(`product_spu_id` ASC, `product_type` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 823 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡券规格表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_spu_audit
-- ----------------------------
DROP TABLE IF EXISTS `product_spu_audit`;
CREATE TABLE `product_spu_audit`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '审核记录 ID',
  `spu_id` bigint NULL DEFAULT NULL COMMENT '产品 SPU ID',
  `product_type` tinyint NOT NULL COMMENT '产品类型 1:卡券 2:套餐 3:应用 4支付宝红包/苏西话费',
  `flat_product_type` int NULL DEFAULT NULL COMMENT '扁平化产品类型:101 普通卡券 102 视频直充 103 品诺卡券 104 品诺直充 201 套餐 301 普通应用 304 扫码提货 402 支付宝红包 403 苏西话费',
  `operation_type` tinyint NOT NULL COMMENT '操作类型 1:新增产品 2 产品基本信息变更 3 产品面值变更 4 区域限额变更 5 产品详情变更  6 spu上架架,7spu下架 8spu删除',
  `coupon_type` tinyint NULL DEFAULT NULL COMMENT '卡券类型 1卡券，2直充，3品诺，product_type=1有效',
  `audit_status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态 0:待审核 1:审核通过 2:审核驳回 3审核失败',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核备注',
  `before_data` json NULL COMMENT '变更前数据(JSON 格式)',
  `after_data` json NULL COMMENT '变更后数据(JSON 格式)',
  `change_fields` json NULL COMMENT '变更字段列表',
  `product_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品名称',
  `product_cat_id` bigint NULL DEFAULT NULL COMMENT '产品二级分类id',
  `brand_id` bigint NULL DEFAULT NULL COMMENT '品牌id',
  `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道id',
  `product_logo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品logo',
  `spu_center_status` tinyint NULL DEFAULT NULL COMMENT 'spu卡管中心状态',
  `spu_status` tinyint NULL DEFAULT NULL COMMENT 'spu状态 0:下架 1:出售中',
  `submitter_id` bigint NOT NULL COMMENT '提交人id',
  `submitter_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '提交人姓名',
  `submit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `auditor_id` bigint NULL DEFAULT NULL COMMENT '审核人 ID',
  `auditor_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人姓名',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_spu_type`(`spu_id` ASC, `product_type` ASC) USING BTREE,
  INDEX `idx_submit_time`(`submit_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1285 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品审核记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_subscript
-- ----------------------------
DROP TABLE IF EXISTS `product_subscript`;
CREATE TABLE `product_subscript`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `subscript_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角标名称',
  `subscript_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角标图片',
  `status` tinyint NULL DEFAULT NULL COMMENT '启用状态 0关闭 1启动',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标记',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `mod_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人名称',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '产品角标信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_template
-- ----------------------------
DROP TABLE IF EXISTS `product_template`;
CREATE TABLE `product_template`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品名',
  `type` int NOT NULL COMMENT '1普通卡券 2视听会员直充',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品模版' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_activity
-- ----------------------------
DROP TABLE IF EXISTS `promotion_activity`;
CREATE TABLE `promotion_activity`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动id',
  `activity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '卡券活动名称',
  `activity_type` int NOT NULL COMMENT '活动类型：1打折',
  `activity_coupon_type` int NULL DEFAULT NULL COMMENT '卡券类型：1普通卡券 2组合套餐卡券',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `activity_status` int NOT NULL COMMENT '活动状态 1 未开始 2 进行中 3已关闭 4已结束',
  `label_copy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动标签文案',
  `label_copy_background_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动标签文案背景图',
  `subscript_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动角标图片地址',
  `advance_release_time` datetime NULL DEFAULT NULL COMMENT '活动提前透出时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id` bigint NOT NULL COMMENT '创建人',
  `mod_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记 1:已删除状态 0:正常状态',
  `audit_status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态 0:待审核 1:审核通过 2:审核驳回',
  `latest_audit_id` bigint NULL DEFAULT NULL COMMENT '最新审核记录ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '促销活动表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_activity_audit
-- ----------------------------
DROP TABLE IF EXISTS `promotion_activity_audit`;
CREATE TABLE `promotion_activity_audit`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `audit_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核单号',
  `activity_id` bigint NULL DEFAULT NULL COMMENT '促销活动id',
  `operation_type` tinyint NULL DEFAULT NULL COMMENT '操作类型：1活动新增 2商品变更 3活动关闭 4延长活动时间',
  `before_data` json NULL COMMENT '变更前数据(JSON 格式)',
  `after_data` json NULL COMMENT '变更后数据(JSON 格式)',
  `submitter_id` bigint NOT NULL COMMENT '提交人id',
  `submit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `audit_status` tinyint NOT NULL DEFAULT 1 COMMENT '审核状态: 0-待审核, 1-审核通过, 2-审核被驳回',
  `auditor_id` bigint NULL DEFAULT NULL COMMENT '审核人id',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记 1:已删除状态 0:正常状态',
  `activity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡券活动名称',
  `activity_type` int NULL DEFAULT NULL COMMENT '活动类型：1打折',
  `activity_coupon_type` int NULL DEFAULT NULL COMMENT '卡券类型：1普通卡券 2组合套餐卡券',
  `start_time` datetime NULL DEFAULT NULL COMMENT '活动开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '活动结束时间',
  `activity_status` int NULL DEFAULT NULL COMMENT '活动状态 1 未开始 2 进行中 3已关闭 4已结束',
  `advance_release_time` datetime NULL DEFAULT NULL COMMENT '活动提前透出时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_activity_id_sku`(`activity_id` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 68 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '促销活动审核表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for promotion_activity_sku
-- ----------------------------
DROP TABLE IF EXISTS `promotion_activity_sku`;
CREATE TABLE `promotion_activity_sku`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `activity_id` bigint NULL DEFAULT NULL COMMENT '促销活动id',
  `sku_id` bigint NULL DEFAULT NULL COMMENT 'skuId',
  `discount` int NULL DEFAULT NULL COMMENT '卡券折扣，活动类型为1打折时生效',
  `activity_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '卡券活动价，活动类型为2直降时生效',
  `quota_qty` int NULL DEFAULT NULL COMMENT '限购数量',
  `promotion_label_copy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '促销标签文案',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id` bigint NOT NULL COMMENT '创建人',
  `mod_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记 1:已删除状态 0:正常状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_activity_id_sku`(`activity_id` ASC, `sku_id` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 52 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '促销活动卡券关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for region
-- ----------------------------
DROP TABLE IF EXISTS `region`;
CREATE TABLE `region`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `parent_id` int NULL DEFAULT NULL COMMENT '父级id(中国0，省份负数)',
  `region_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '城市名称',
  `region_type` int NULL DEFAULT NULL COMMENT ' 1 省  2  市  3 区 4.镇',
  `adcode` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '行政区域码',
  `pinyin` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拼音全拼(如 beijing)',
  `initials` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '首字母拼音(如 bj)',
  `location` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经纬度(纬度,经度)',
  `latitude` decimal(13, 10) NULL DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(13, 10) NULL DEFAULT NULL COMMENT '经度',
  `supply_id` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应链地址di',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `inx_supplyid`(`supply_id` ASC) USING BTREE,
  INDEX `inx_parent`(`parent_id` ASC) USING BTREE,
  INDEX `inx_name`(`region_name` ASC) USING BTREE,
  INDEX `inx_adcode`(`adcode` ASC) USING BTREE,
  INDEX `region_region_type_IDX`(`region_type` ASC, `latitude` ASC, `longitude` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4284912 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '省份,市,地区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for region_price_policy
-- ----------------------------
DROP TABLE IF EXISTS `region_price_policy`;
CREATE TABLE `region_price_policy`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '策略ID',
  `product_sku_id` bigint NOT NULL COMMENT '产品SKU ID',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1:卡券 2:套餐',
  `policy_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '策略名称',
  `special_price` decimal(10, 2) NOT NULL COMMENT '特殊售价',
  `special_fee_rate` decimal(5, 2) NOT NULL COMMENT '特殊手续费率(%)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 162 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '指定区域特殊价格策略表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for screen_detail
-- ----------------------------
DROP TABLE IF EXISTS `screen_detail`;
CREATE TABLE `screen_detail`  (
  `screen_id` bigint NOT NULL AUTO_INCREMENT COMMENT '开屏页id',
  `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '启动页名称',
  `img_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '图片url',
  `begin_time` datetime NOT NULL COMMENT '启动时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `click_num` int NOT NULL DEFAULT 0 COMMENT '点击量',
  `is_nationwide` tinyint NOT NULL COMMENT '是否全国上线(0非全国 1全国)',
  `countdown` int UNSIGNED NOT NULL DEFAULT 5 COMMENT '停留倒计时',
  `ios_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ios的跳转路径',
  `android_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'android的跳转链接',
  `wechat_mini_appid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信小程序appid',
  `wechat_mini_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信小程序跳转链接',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(数字越大排序越靠前)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`screen_id`) USING BTREE,
  INDEX `create_time_idx`(`create_time` ASC) USING BTREE,
  INDEX `begin_end_time_IDX`(`begin_time` ASC, `end_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '启动页表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for screen_region_restrict
-- ----------------------------
DROP TABLE IF EXISTS `screen_region_restrict`;
CREATE TABLE `screen_region_restrict`  (
  `restrict_id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '限制id',
  `screen_id` bigint NULL DEFAULT NULL COMMENT '启动页id',
  `region_id` int NULL DEFAULT NULL COMMENT '限制区域id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `del_flag` tinyint NULL DEFAULT NULL COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`restrict_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '启动页限制区域表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for search_detail
-- ----------------------------
DROP TABLE IF EXISTS `search_detail`;
CREATE TABLE `search_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `keyword` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '关键词名称',
  `iml_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图标广告图',
  `jump_type` tinyint NOT NULL COMMENT '跳转类型：0文本 1链接',
  `jump_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '跳转链接地址',
  `type` tinyint NOT NULL COMMENT '类型：1搜索关键词 2搜索发现',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '启动状态：0禁用 1启用',
  `is_nationwide` tinyint NOT NULL COMMENT '0非全国 1全国',
  `click_num` int NOT NULL DEFAULT 0 COMMENT '点击量',
  `keyword_color` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字体颜色',
  `bg_color` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '背景色',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(数字越大排序越靠前)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `mod_id` int NULL DEFAULT NULL COMMENT '编辑人id',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除标志(0:否 1:是)',
  `del_id` int NULL DEFAULT NULL COMMENT '删除人id',
  `del_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `ios_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ios的跳转路径',
  `android_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'android的跳转链接',
  `wechat_mini_appid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信小程序appid',
  `wechat_mini_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信小程序跳转链接',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '搜索关键词/发现表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sku_region_release_plan
-- ----------------------------
DROP TABLE IF EXISTS `sku_region_release_plan`;
CREATE TABLE `sku_region_release_plan`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `policy_id` bigint NOT NULL COMMENT '价格策略ID',
  `product_sku_id` bigint NOT NULL COMMENT '产品SKU ID',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1:卡券 2:套餐',
  `release_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '额外放量时间点',
  `extra_quantity` int NULL DEFAULT NULL COMMENT '额外放量数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_policy_id`(`policy_id` ASC) USING BTREE,
  INDEX `idx_release_time`(`release_time` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品sku区域特殊放量计划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sku_region_release_plan_copy1
-- ----------------------------
DROP TABLE IF EXISTS `sku_region_release_plan_copy1`;
CREATE TABLE `sku_region_release_plan_copy1`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `policy_id` bigint NOT NULL COMMENT '价格策略ID',
  `product_sku_id` bigint NOT NULL COMMENT '产品SKU ID',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1:卡券 2:套餐',
  `release_time` time NULL DEFAULT NULL COMMENT '额外放量时间点',
  `extra_quantity` int NULL DEFAULT NULL COMMENT '额外放量数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_policy_id`(`policy_id` ASC) USING BTREE,
  INDEX `idx_release_time`(`release_time` ASC) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 65 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品sku区域特殊放量计划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sku_release_inventory
-- ----------------------------
DROP TABLE IF EXISTS `sku_release_inventory`;
CREATE TABLE `sku_release_inventory`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `product_sku_id` bigint NOT NULL COMMENT '产品SKU ID',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1:卡券 2:套餐',
  `plan_id` bigint NOT NULL DEFAULT 0 COMMENT '放量计划id',
  `policy_id` bigint NOT NULL DEFAULT 0 COMMENT '价格策略ID，0表示标准放量',
  `release_status` tinyint NOT NULL DEFAULT 0 COMMENT '放量状态 0-未开始 1-已生效 2-已结束',
  `total_quantity` int NOT NULL COMMENT '总放量数量',
  `remaining_quantity` int NOT NULL COMMENT '剩余数量',
  `sold_quantity` int NOT NULL DEFAULT 0 COMMENT '已售出数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `release_start_time` datetime NOT NULL COMMENT '放量开始时间',
  `release_end_time` datetime NOT NULL COMMENT '放量结束时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_inventory`(`product_sku_id` ASC, `product_type` ASC, `policy_id` ASC) USING BTREE,
  INDEX `idx_product_date`(`product_sku_id` ASC) USING BTREE,
  INDEX `idx_release_times`(`release_start_time` ASC, `release_end_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 405 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品sku放量库存记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sku_release_plan
-- ----------------------------
DROP TABLE IF EXISTS `sku_release_plan`;
CREATE TABLE `sku_release_plan`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '放量计划ID',
  `product_sku_id` bigint NOT NULL COMMENT '产品SKU ID',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '产品类型 1:卡券 2:套餐',
  `release_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '放量时间点 HH:mm:ss',
  `release_quantity` int NULL DEFAULT NULL COMMENT '放量数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE,
  INDEX `idx_release_time`(`release_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 155 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品SKU基础放量计划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spu_limit_strategy
-- ----------------------------
DROP TABLE IF EXISTS `spu_limit_strategy`;
CREATE TABLE `spu_limit_strategy`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_spu_id` bigint NOT NULL COMMENT '产品SPU ID',
  `product_type` tinyint NOT NULL COMMENT '产品类型 1卡券 2套餐 3应用 4 红包话费',
  `is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认全国策略：0-否，1-是',
  `user_monthly_limit_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '每人每月限额金额',
  `is_exceed_allow` tinyint NULL DEFAULT NULL COMMENT '是否允许超额：0-不允许，1-允许',
  `exceed_fee_rate` decimal(7, 2) NULL DEFAULT NULL COMMENT '超额手续费率(%)',
  `charge_fee_rate` decimal(7, 2) NULL DEFAULT NULL COMMENT '加点手续费(%)，product_type为3时生效',
  `diverse_usage_scene` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '多元使用场景，product_type为4时生效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_spu`(`product_spu_id` ASC) USING BTREE,
  INDEX `idx_product_type`(`product_type` ASC) USING BTREE,
  INDEX `idx_default_strategy`(`product_spu_id` ASC, `is_default` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1111122 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品SPU限额策略表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spu_limit_strategy_region
-- ----------------------------
DROP TABLE IF EXISTS `spu_limit_strategy_region`;
CREATE TABLE `spu_limit_strategy_region`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `limit_strategy_id` bigint NOT NULL COMMENT '限额策略ID',
  `product_spu_id` bigint NOT NULL COMMENT '产品SPU ID',
  `product_type` tinyint NOT NULL COMMENT '产品类型 1卡券 2套餐 3应用 4支付宝红包 5苏西话费',
  `region_id` int NOT NULL COMMENT '区域id',
  `region_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区域名称',
  `region_type` int NOT NULL COMMENT '区域类型，1 省  2  市  3 区',
  `parent_region_id` int NOT NULL COMMENT '父级区域id',
  `parent_region_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父级区域名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_spu`(`product_spu_id` ASC, `product_type` ASC) USING BTREE,
  INDEX `idx_region_id`(`region_id` ASC, `product_spu_id` ASC, `product_type` ASC) USING BTREE,
  INDEX `uk_strategy_city`(`limit_strategy_id` ASC, `region_id` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11111492 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品spu限额策略城市关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sub_sku_order
-- ----------------------------
DROP TABLE IF EXISTS `sub_sku_order`;
CREATE TABLE `sub_sku_order`  (
  `id` bigint NOT NULL COMMENT '主键ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '父订单号',
  `sub_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '子sku订单号',
  `product_type` int NOT NULL DEFAULT 2 COMMENT '产品类型 2 套餐',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `spu_id` bigint NOT NULL COMMENT 'spuId',
  `sku_id` bigint NOT NULL COMMENT 'skuId',
  `sub_sku_id` bigint NOT NULL COMMENT '子skuId',
  `outer_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '卡管等第三方数据的id',
  `face_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '面值名称',
  `face_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '面值',
  `cost_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '记录的成本价',
  `settle_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '与第三方结算价',
  `recharge_account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '充值账号',
  `external_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '外部订单号(去卡管下单对应卡管的订单号)',
  `coupon_type` tinyint NULL DEFAULT NULL COMMENT '系统卡券类型：1:普通卡券 3:品诺',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '子sku的购买数量(套餐的购买数量*套装子商品数量)',
  `out_check_type` int NULL DEFAULT NULL COMMENT '卡券类型 101:平台自发券 201:卡密+卡号 202:卡号或卡密 203:卡号+卡密+校验码 301:链接类 302:链接+验证码 303:卡号+短链接+验证码 304:卡号+短链接',
  `check_type` tinyint NOT NULL COMMENT '自定义核销类型 1:面值+兑换码 2:面值+兑换码+一维二维码 3:面值+卡号+一维二维码 4:面值+卡号卡密 5:面值+卡号卡密+兑换码 6:面值+卡号卡密+一维二维码 7:面值+卡密+一维二维码 8:面值+链接 9:自发券(面值+提货券形式)',
  `sub_sku_delivery_status` tinyint NOT NULL DEFAULT 0 COMMENT '子发货状态 0-待发货 1-发货中 2-已发货 3-发货失败',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_sub_order_no`(`sub_order_no` ASC) USING BTREE COMMENT '子订单号唯一索引',
  INDEX `idx_order_no`(`order_no` ASC) USING BTREE COMMENT '父订单号索引',
  INDEX `idx_customer_id`(`customer_id` ASC) USING BTREE COMMENT '客户ID索引',
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE COMMENT '创建时间索引',
  INDEX `idx_external_order_no`(`external_order_no` ASC) USING BTREE COMMENT '外部订单号索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '子sku订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_config`;
CREATE TABLE `sys_web_config`  (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_dept`;
CREATE TABLE `sys_web_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_dict_data`;
CREATE TABLE `sys_web_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 488 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_dict_type`;
CREATE TABLE `sys_web_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 190 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_job`;
CREATE TABLE `sys_web_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注信息',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_job_log`;
CREATE TABLE `sys_web_job_log`  (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_logininfor`;
CREATE TABLE `sys_web_logininfor`  (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`info_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3835 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统访问记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_menu`;
CREATE TABLE `sys_web_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由参数',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2453 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_notice`;
CREATE TABLE `sys_web_notice`  (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_oper_log`;
CREATE TABLE `sys_web_oper_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `trace_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跟踪id',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数',
  `json_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `oper_timestamp` bigint NULL DEFAULT NULL COMMENT '操作时间戳',
  `oper_browser` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `service_type` int NULL DEFAULT NULL COMMENT '服务类型',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `sy_service_type`(`service_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58662 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_post`;
CREATE TABLE `sys_web_post`  (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_role`;
CREATE TABLE `sys_web_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_role_dept`;
CREATE TABLE `sys_web_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_role_menu`;
CREATE TABLE `sys_web_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_user`;
CREATE TABLE `sys_web_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_user_post`;
CREATE TABLE `sys_web_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_web_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_web_user_role`;
CREATE TABLE `sys_web_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `tenant_id` int NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_after_sale
-- ----------------------------
DROP TABLE IF EXISTS `t_after_sale`;
CREATE TABLE `t_after_sale`  (
  `id` bigint NOT NULL COMMENT '售后ID',
  `after_sale_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '售后单号',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `order_item_id` bigint NOT NULL COMMENT '订单项ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `customer_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户手机号',
  `after_sale_type` tinyint NOT NULL COMMENT '售后类型: 1-部分退款 2-全额退款',
  `original_order_status` tinyint NOT NULL COMMENT '售后发起时的订单状态: 1-进行中 2-交易成功 3-交易关闭 4-超时取消 5-手动取消',
  `after_sale_status` tinyint NOT NULL DEFAULT 1 COMMENT '售后状态: 1-待审核 20-审核通过 30-审核驳回 32-退款驳回 33-售后撤销 34-售后完成',
  `after_sale_quantity` int NOT NULL DEFAULT 1 COMMENT '申请售后数量',
  `refund_status` tinyint NOT NULL DEFAULT 0 COMMENT '退款状态: 0-未退款 1-退款中 2-退款成功 3-退款失败 4-拒绝退款',
  `refund_images` json NULL COMMENT '售后凭证图片列表',
  `refund_description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款补充描述',
  `after_sale_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后原因',
  `audit_reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后审核驳回原因',
  `refund_reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝退款原因',
  `apply_refund_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '申请退款金额',
  `refundable_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '可退款金额',
  `refund_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际退款金额',
  `apply_time` datetime NOT NULL COMMENT '售后申请时间',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '售后审核时间',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后审核备注',
  `customer_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户备注',
  `staff_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作人员备注',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '撤销时间',
  `refund_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款单号',
  `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付交易号',
  `refund_trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款交易号',
  `payment_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式',
  `payment_method_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式名称',
  `refund_account_details` json NULL COMMENT '退款账户信息(json格式)',
  `refund_reject_time` datetime NULL DEFAULT NULL COMMENT '退款驳回时间',
  `refund_time` datetime NULL DEFAULT NULL COMMENT '退款时间',
  `refund_request_time` datetime NULL DEFAULT NULL COMMENT '退款申请时间',
  `refund_success_time` datetime NULL DEFAULT NULL COMMENT '退款成功时间',
  `after_sale_complete_time` datetime NULL DEFAULT NULL COMMENT '售后完成时间',
  `refund_notify_time` datetime NULL DEFAULT NULL COMMENT '退款通知时间',
  `refund_notify_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款通知地址',
  `refund_notify_result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款通知结果',
  `refund_error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款错误码',
  `refund_error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_after_sale_no`(`after_sale_no` ASC) USING BTREE COMMENT '售后单号唯一索引',
  UNIQUE INDEX `idx_refund_no`(`refund_no` ASC) USING BTREE COMMENT '退款单号唯一索引',
  INDEX `idx_order_no`(`order_no` ASC) USING BTREE COMMENT '订单编号索引',
  INDEX `idx_customer_id`(`customer_id` ASC) USING BTREE COMMENT '客户ID索引',
  INDEX `idx_after_sale_status`(`after_sale_status` ASC) USING BTREE COMMENT '售后状态索引',
  INDEX `idx_refund_status`(`refund_status` ASC) USING BTREE COMMENT '退款状态索引',
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE COMMENT '创建时间索引',
  INDEX `idx_order_item_id`(`order_item_id` ASC) USING BTREE COMMENT '订单项ID索引',
  INDEX `idx_apply_time`(`apply_time` ASC) USING BTREE COMMENT '申请时间索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '售后订单主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_after_sale_log
-- ----------------------------
DROP TABLE IF EXISTS `t_after_sale_log`;
CREATE TABLE `t_after_sale_log`  (
  `id` bigint NOT NULL COMMENT '日志ID',
  `after_sale_id` bigint NOT NULL COMMENT '售后ID',
  `after_sale_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '售后单号',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `operation_type` tinyint NOT NULL COMMENT '操作类型: 1-申请售后 2-审核通过 3-审核拒绝 4-退款成功 5-退款失败 6-撤销售后单 7-拒绝退款',
  `operator` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人',
  `operator_id` bigint NULL DEFAULT NULL COMMENT '操作人ID',
  `operation_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作内容',
  `operation_result` json NULL COMMENT '操作结果',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operation_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作IP',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_after_sale_id`(`after_sale_id` ASC) USING BTREE COMMENT '售后ID索引',
  INDEX `idx_after_sale_no`(`after_sale_no` ASC) USING BTREE COMMENT '售后单号索引',
  INDEX `idx_order_no`(`order_no` ASC) USING BTREE COMMENT '订单编号索引',
  INDEX `idx_operation_type`(`operation_type` ASC) USING BTREE COMMENT '操作类型索引',
  INDEX `idx_operation_time`(`operation_time` ASC) USING BTREE COMMENT '操作时间索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '售后操作日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_config
-- ----------------------------
DROP TABLE IF EXISTS `t_config`;
CREATE TABLE `t_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `value` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '值',
  `type` tinyint NOT NULL COMMENT '类型 1:总后台登录页面背景图  2:后台登录后logo 3:订单自动取消时间（卡券类） 4:修改手机号限制次数  5:账户注销次数  6:注销后再次注册间隔 7:tab中本地生活和全球购是否显示的商品数阈值 8:白金卡账户微信充值限额 9:黑金卡账户微信充值限额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `create_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志 0:否 1:删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type_idx`(`type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '基本配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_order
-- ----------------------------
DROP TABLE IF EXISTS `t_order`;
CREATE TABLE `t_order`  (
  `id` bigint NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `external_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部订单号',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `customer_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户手机号',
  `recharge_account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值账号:类型为直充/红包时不为空',
  `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人姓名',
  `receiver_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人手机号',
  `receiver_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货地址',
  `channel_id` bigint NOT NULL COMMENT '渠道id',
  `brand_id` bigint NOT NULL COMMENT '品牌id',
  `channel_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道名称',
  `brand_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌名称',
  `order_channel` int NOT NULL DEFAULT 0 COMMENT '订单对接渠道 0 未知 1 卡管 2 支付宝红包 3 苏西话费 4 分销中心 5 本地生活 6 展码付 7 线下扫码 ',
  `product_spu_id` bigint NULL DEFAULT NULL COMMENT '商品spuid(只有1个商品时记录)',
  `product_sku_id` bigint NULL DEFAULT NULL COMMENT '商品skuid(只有1个商品时记录)',
  `category_id` bigint NOT NULL DEFAULT 0 COMMENT '商品类目id(只有1个商品时不为0)',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称(只有1个商品时记录)',
  `flat_product_type` int NULL DEFAULT NULL COMMENT '扁平化产品类型（只有一个商品时记录）',
  `app_flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用标记(只有一个商品时可能会记录)',
  `order_type` tinyint NOT NULL DEFAULT 1 COMMENT '订单类型: 1-卡券 2-普通应用 3-红包/充值，4扫码付应用',
  `order_status` tinyint NOT NULL DEFAULT 1 COMMENT '订单状态: 0-待付款 10-进行中 20-交易成功 30-交易关闭 40-超时取消 41-手动取消',
  `external_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部订单状态',
  `after_sale_status` tinyint NOT NULL DEFAULT 0 COMMENT '售后状态: 1-待审核 20-审核通过 30-审核驳回 32-退款驳回 33-售后撤销 34-售后完成',
  `delivery_status` tinyint NULL DEFAULT 0 COMMENT '发货状态: 0-未发货/未充值  1-发货中/充值中 2-已发货/已充值 3-发货失败/充值失败',
  `payment_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态: 0-未支付 1-已支付 2-部分支付 3-部分退款中 4-部分退款完成 5-全额退款中 6 全额退款已完成',
  `pay_expire_timestamp` bigint NULL DEFAULT NULL COMMENT '支付失效时间戳(秒)',
  `pay_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品支持的支付类型',
  `is_sync_payment` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是同步支付方式 0 否  1 是',
  `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付交易号-支付中心提供',
  `payment_details` json NULL COMMENT '支付详情--预支付时记录自收银台',
  `total_sell_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品原售价总价',
  `total_discount_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠总金额',
  `order_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '订单金额(商品原售价总价 + 加点手续费 + 超额手续费 = 支付金额 + 已优惠金额)',
  `limit_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '限额金额(超出限额计算超额手续费)',
  `total_service_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '加点手续费',
  `total_service_fee_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '加点手续费率(%)',
  `exceed_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总销售价超额手续费',
  `exceed_fee_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '总销售价超额手续费率(%)',
  `payment_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际支付金额(商品原售价总价 + 加点手续费 + 超额手续费 - 已优惠金额)',
  `payment_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `delivery_time` datetime NULL DEFAULT NULL COMMENT '发货时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注',
  `order_detail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单详情链接',
  `longitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '纬度',
  `external_shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部店铺id',
  `third_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方id',
  `external_shop_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部店员id',
  `risk_control_id` bigint NULL DEFAULT NULL COMMENT '风控策略id,触发风控时不为null',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除: 0-未删除 1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_order_no`(`order_no` ASC) USING BTREE COMMENT '订单号唯一索引',
  INDEX `idx_customer_id`(`customer_id` ASC) USING BTREE COMMENT '客户ID索引',
  INDEX `idx_order_status`(`order_status` ASC) USING BTREE COMMENT '订单状态索引',
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE COMMENT '创建时间索引',
  INDEX `idx_external_order_no`(`external_order_no` ASC) USING BTREE COMMENT '外部订单号索引',
  INDEX `idx_customer_mobile`(`customer_mobile` ASC) USING BTREE COMMENT '客户手机号索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_order_delivery
-- ----------------------------
DROP TABLE IF EXISTS `t_order_delivery`;
CREATE TABLE `t_order_delivery`  (
  `id` bigint NOT NULL COMMENT '发货ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `order_item_id` bigint NOT NULL COMMENT '订单项ID',
  `mini_sku_id` bigint NOT NULL COMMENT '最小化的产品Id,如果是卡券对应卡券skuId，如果是套餐 对应subSkuId',
  `delivery_type` tinyint NOT NULL COMMENT '发货类型: 1-卡券发放   2-实物发货 3-红包或充值发放',
  `delivery_status` tinyint NOT NULL DEFAULT 0 COMMENT '发货状态: 0-未发货 1-发货中 2-已发货 3-发货失败',
  `flat_product_type` int NULL DEFAULT NULL COMMENT '扁平化产品类型',
  `coupon_verification_type` int NULL DEFAULT NULL COMMENT '卡券核销类型:卡券类型 101: 平台自发券 201: 卡密+卡号 202: 卡号或卡密 203: 卡号+卡密+校验码 301: 链接类 302: 链接+验证码 303:链接+卡号+验证码',
  `coupon_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡券卡号',
  `coupon_pin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡券卡密',
  `coupon_crc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡券crc',
  `coupon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡券兑换链接',
  `coupon_url_pass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡券兑换短链接密码',
  `coupon_batch_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡券批次号',
  `brand_id` bigint NULL DEFAULT NULL COMMENT '品牌id',
  `valid_date` date NULL DEFAULT NULL COMMENT '有效日期',
  `delivery_content` json NULL COMMENT '发货内容',
  `delivery_time` datetime NULL DEFAULT NULL COMMENT '发货时间',
  `recharge_account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值账号',
  `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人姓名',
  `receiver_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人手机号',
  `receiver_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货地址',
  `express_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `delivery_error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货错误码',
  `delivery_error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货错误信息',
  `delivery_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记 0 未删除 1 已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE COMMENT '订单ID索引',
  INDEX `idx_order_no`(`order_no` ASC) USING BTREE COMMENT '订单编号索引',
  INDEX `idx_order_item_id`(`order_item_id` ASC) USING BTREE COMMENT '订单项ID索引',
  INDEX `idx_delivery_status`(`delivery_status` ASC) USING BTREE COMMENT '发货状态索引',
  INDEX `idx_delivery_time`(`delivery_time` ASC) USING BTREE COMMENT '发货时间索引',
  INDEX `idx_receive_account`(`recharge_account` ASC) USING BTREE COMMENT '接收账号索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单发货表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_order_item
-- ----------------------------
DROP TABLE IF EXISTS `t_order_item`;
CREATE TABLE `t_order_item`  (
  `id` bigint NOT NULL COMMENT '订单项ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `customer_id` bigint NULL DEFAULT NULL COMMENT '客户id',
  `product_type` tinyint NOT NULL COMMENT '商品类型: 1-卡券 2-套餐 3-普通应用 4-红包/充值应用',
  `flat_product_type` int NOT NULL COMMENT '扁平化商品类型: 商品类型 *100 + 子类型',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `product_sku_id` bigint NOT NULL COMMENT '商品SKU ID',
  `category_id` bigint NOT NULL DEFAULT 0 COMMENT '商品类目id',
  `product_sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品SKU名称',
  `product_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品logo',
  `extra_info` json NULL COMMENT '商品额外信息',
  `product_specs_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品规格名称',
  `brand_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌名称',
  `brand_id` bigint NOT NULL COMMENT '品牌ID',
  `channel_id` bigint NOT NULL COMMENT '渠道ID',
  `channel_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道名称',
  `sell_price` decimal(10, 2) NOT NULL COMMENT '销售单价',
  `actual_price` decimal(10, 2) NOT NULL COMMENT '实际销售单价',
  `promotion_activity_id` bigint NULL DEFAULT NULL COMMENT '促销活动id',
  `cost_price` decimal(10, 2) NOT NULL COMMENT '成本单价',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `service_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '单个商品加点手续费',
  `service_fee_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '单个商品加点手续费率(%)',
  `face_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT 'sku面值',
  `source_table` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品来源表',
  `out_goods_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部商品id(productType=1时对应卡管的coupon_id)',
  `out_goods_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部商品类型',
  `app_flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用型产品的appFlag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE COMMENT '订单ID索引',
  INDEX `idx_order_no`(`order_no` ASC) USING BTREE COMMENT '订单编号索引',
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE COMMENT '商品ID索引',
  INDEX `idx_product_sku_id`(`product_sku_id` ASC) USING BTREE COMMENT '商品SKU ID索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_order_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `t_order_operation_log`;
CREATE TABLE `t_order_operation_log`  (
  `id` bigint NOT NULL COMMENT '日志ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `operation_type` tinyint NOT NULL COMMENT '操作类型: 1-创建订单 2-支付订单 3-取消订单 4-完成订单 5-售后申请 6-退款处理 7-发货 8-修改订单',
  `operator` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人',
  `operator_id` bigint NULL DEFAULT NULL COMMENT '操作人ID',
  `operation_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作内容',
  `operation_result` json NULL COMMENT '操作结果',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operation_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作IP',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE COMMENT '订单ID索引',
  INDEX `idx_order_no`(`order_no` ASC) USING BTREE COMMENT '订单编号索引',
  INDEX `idx_operation_type`(`operation_type` ASC) USING BTREE COMMENT '操作类型索引',
  INDEX `idx_operation_time`(`operation_time` ASC) USING BTREE COMMENT '操作时间索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单操作日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_order_payment_copy1
-- ----------------------------
DROP TABLE IF EXISTS `t_order_payment_copy1`;
CREATE TABLE `t_order_payment_copy1`  (
  `id` bigint NOT NULL COMMENT '支付ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付交易号',
  `payment_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付方式: RW-瑞祥白金卡,RB-瑞祥黑金卡,RBIZ-瑞祥商联卡,RR-瑞祥红卡,WX-微信,GPI-全球购积分,VT-提货凭证',
  `payment_method_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式名称',
  `account_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付账号/卡号',
  `amount` decimal(10, 2) NOT NULL COMMENT '支付金额',
  `service_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '手续费',
  `payment_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态: 0-未支付 1-支付成功 2-支付失败 3-已撤销 4-已退款',
  `payment_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `payment_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付IP',
  `payment_notify_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付回调地址',
  `payment_request_time` datetime NULL DEFAULT NULL COMMENT '请求支付时间',
  `payment_success_time` datetime NULL DEFAULT NULL COMMENT '支付成功时间',
  `payment_notify_time` datetime NULL DEFAULT NULL COMMENT '支付通知时间',
  `payment_notify_result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付通知结果',
  `payment_error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付错误码',
  `payment_error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_trade_no`(`trade_no` ASC) USING BTREE COMMENT '支付交易号唯一索引',
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE COMMENT '订单ID索引',
  INDEX `idx_order_no`(`order_no` ASC) USING BTREE COMMENT '订单编号索引',
  INDEX `idx_payment_status`(`payment_status` ASC) USING BTREE COMMENT '支付状态索引',
  INDEX `idx_payment_time`(`payment_time` ASC) USING BTREE COMMENT '支付时间索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单支付表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_order_transaction_copy1
-- ----------------------------
DROP TABLE IF EXISTS `t_order_transaction_copy1`;
CREATE TABLE `t_order_transaction_copy1`  (
  `id` bigint NOT NULL COMMENT '交易ID',
  `transaction_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '交易编号',
  `order_id` bigint NULL DEFAULT NULL COMMENT '订单ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `customer_id` bigint NOT NULL COMMENT '用户ID',
  `transaction_type` tinyint NOT NULL COMMENT '交易类型: 1-订单支付 2-订单退款',
  `amount` decimal(10, 2) NOT NULL COMMENT '交易金额',
  `transaction_status` tinyint NOT NULL DEFAULT 0 COMMENT '交易状态: 0-处理中 1-成功 2-失败',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式',
  `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付平台交易号',
  `transaction_time` datetime NULL DEFAULT NULL COMMENT '交易时间',
  `transaction_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易IP',
  `transaction_device` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易设备',
  `transaction_channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易渠道',
  `transaction_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mod_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_transaction_no`(`transaction_no` ASC) USING BTREE COMMENT '交易编号唯一索引',
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE COMMENT '订单ID索引',
  INDEX `idx_order_no`(`order_no` ASC) USING BTREE COMMENT '订单编号索引',
  INDEX `idx_user_id`(`customer_id` ASC) USING BTREE COMMENT '用户ID索引',
  INDEX `idx_transaction_type`(`transaction_type` ASC) USING BTREE COMMENT '交易类型索引',
  INDEX `idx_transaction_status`(`transaction_status` ASC) USING BTREE COMMENT '交易状态索引',
  INDEX `idx_transaction_time`(`transaction_time` ASC) USING BTREE COMMENT '交易时间索引',
  INDEX `idx_trade_no`(`trade_no` ASC) USING BTREE COMMENT '支付平台交易号索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单交易流水表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_setup_log
-- ----------------------------
DROP TABLE IF EXISTS `t_setup_log`;
CREATE TABLE `t_setup_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `admin_id` decimal(20, 0) NULL DEFAULT NULL COMMENT '管理员id',
  `user_type` tinyint NULL DEFAULT NULL COMMENT '1：管理员，2：用户，3:店铺',
  `operate_log` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日志',
  `operate_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作ip',
  `log_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作模块',
  `remark` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间 ',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '设置的操作日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_test
-- ----------------------------
DROP TABLE IF EXISTS `t_test`;
CREATE TABLE `t_test`  (
  `id` bigint NOT NULL COMMENT 'id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_test_account
-- ----------------------------
DROP TABLE IF EXISTS `t_test_account`;
CREATE TABLE `t_test_account`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `account_number` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `available` decimal(19, 4) NOT NULL,
  `daily_limit` decimal(19, 4) NOT NULL,
  `currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_test_user
-- ----------------------------
DROP TABLE IF EXISTS `t_test_user`;
CREATE TABLE `t_test_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `unionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户密码',
  `phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机',
  `sex` tinyint NULL DEFAULT NULL COMMENT '性别（1男，0女）',
  `birthday` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生日',
  `user_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户头像',
  `user_grade_id` bigint NULL DEFAULT 1 COMMENT '会员等级id',
  `enable` tinyint NULL DEFAULT 1 COMMENT '启用状态 1：启用 0：禁用',
  `login_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录ip',
  `login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `login_num` bigint NULL DEFAULT NULL COMMENT '登录次数',
  `login_error_count` bigint NULL DEFAULT 0 COMMENT '记录当前会员登陆的错误次数',
  `login_lock_time` datetime NULL DEFAULT NULL COMMENT '登陆错误账户锁定时间',
  `continue_login_count` bigint NULL DEFAULT 0 COMMENT '连续登录天数(0 表示注册尚未登录，1 表示第一次登录或者间断后首次登录)',
  `phone_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机设备号',
  `login_status` tinyint NULL DEFAULT 1 COMMENT '登录状态（1 是，0否）',
  `origin` tinyint NULL DEFAULT NULL COMMENT '来源：1用户注册 2系统创建 3第三方对接',
  `schq` tinyint NULL DEFAULT 0 COMMENT '是否获得首次登录送虎券  0还未登录过，1最少登录一次了',
  `old_type` tinyint NULL DEFAULT 1 COMMENT '1，新用户  2老用户',
  `create_time` datetime NULL DEFAULT NULL COMMENT '注册时间 ',
  `create_id` bigint NULL DEFAULT NULL COMMENT '添加人',
  `mod_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `mod_id` bigint NULL DEFAULT NULL COMMENT '修改人',
  `user_real_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `user_id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户身份证',
  `user_auth_status` tinyint NULL DEFAULT 1 COMMENT '用户认证状态，1-未认证，2-认证中，3-已认证，4认证不通过',
  `openid` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '0--正常，1--已注销',
  `mch_id` int NULL DEFAULT NULL COMMENT '商户号',
  `mini_user_id` bigint NULL DEFAULT NULL COMMENT '小程序端用户id',
  `card_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡号',
  `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业名称',
  `company_id` bigint NULL DEFAULT NULL COMMENT '企业ID',
  PRIMARY KEY (`user_id`) USING BTREE,
  INDEX `ix1_t_user`(`user_name` ASC) USING BTREE,
  INDEX `ix2_t_user`(`phone` ASC) USING BTREE,
  INDEX `ix3_t_user`(`login_time` ASC) USING BTREE,
  INDEX `idx_user_u_e`(`unionid` ASC, `enable` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_order_0
-- ----------------------------
DROP TABLE IF EXISTS `test_order_0`;
CREATE TABLE `test_order_0`  (
  `id` bigint NOT NULL,
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for test_order_1
-- ----------------------------
DROP TABLE IF EXISTS `test_order_1`;
CREATE TABLE `test_order_1`  (
  `id` bigint NOT NULL,
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for test_order_item_0
-- ----------------------------
DROP TABLE IF EXISTS `test_order_item_0`;
CREATE TABLE `test_order_item_0`  (
  `id` bigint NOT NULL,
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `sku_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for test_order_item_1
-- ----------------------------
DROP TABLE IF EXISTS `test_order_item_1`;
CREATE TABLE `test_order_item_1`  (
  `id` bigint NOT NULL,
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `sku_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trade_config
-- ----------------------------
DROP TABLE IF EXISTS `trade_config`;
CREATE TABLE `trade_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置分组',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '键值类型:string,int,uint,bool,datetime,date',
  `key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参数键值',
  `default_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '默认值',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `tip` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变量描述',
  `is_default` tinyint(1) NULL DEFAULT 0 COMMENT '是否为系统默认',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trade_order
-- ----------------------------
DROP TABLE IF EXISTS `trade_order`;
CREATE TABLE `trade_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NULL DEFAULT NULL COMMENT '订单ID',
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务订单号',
  `out_order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部业务单号',
  `trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易号',
  `pay_type` enum('ONLINE','OFFLINE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付类型   ONLINE  线上支付   OFFLINE 线下支付',
  `user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  `order_source` enum('APP_ANDROID','APP_IOS','WX_MINI') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单来源   APP_ANDROID  APP_IOS  WX_MINI',
  `order_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单类型',
  `order_channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单渠道',
  `pay_amount` int NULL DEFAULT NULL COMMENT '支付金额',
  `pay_status` enum('WAIT','PAYING','SUCCESS','FAIL','CLOSE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付状态   WAIT  待交易  PAYING  交易中  SUCCESS  交易成功  FAIL  支付失败  CLOSE  交易完成',
  `refund_amount` int NULL DEFAULT NULL COMMENT '退款金额',
  `refund_status` enum('NONE','PART','FULL') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款状态   NONE 无退款  PART 部分退款  FULL 全部退款',
  `trade_time` datetime NULL DEFAULT NULL COMMENT '交易时间',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trade_order_info
-- ----------------------------
DROP TABLE IF EXISTS `trade_order_info`;
CREATE TABLE `trade_order_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL DEFAULT 0 COMMENT '父-订单ID',
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父-订单号',
  `out_order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '父-外部订单号',
  `trade_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父-支付订单号',
  `pay_channel` enum('CARD','WECHAT_PAY','WECHAT_PAY_EXCHANGE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'CARD' COMMENT '支付通道      CARD   卡系统   WECHAT_PAY  微信支付  ',
  `trade_info_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子-交易单号     微信交易单号',
  `out_trade_info_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子-预付卡交易单号',
  `wx_mch_id` int NULL DEFAULT NULL COMMENT '微信关联商户号ID',
  `wx_open_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信支付openid',
  `wx_transaction_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信交易ID （退款需要使用该参数）',
  `wx_exchange_recharge_order` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信支付兑换的卡充值订单',
  `wx_pay_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '微信支付配置',
  `wx_refund_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信自动退款单号 原路退回',
  `chan_id` char(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道号',
  `mch_id` char(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户号',
  `store_id` char(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店号',
  `term_id` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '终端号',
  `card_no` char(19) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡号',
  `card_trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易卡类型   RX_RED_CARD 商联商户 RX_BLACK_CARD 黑金商户 RX_WHITE_CARD 白金商户 RX_PICK_CARD 凭证商户',
  `card_business_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务卡类型   RXHK 瑞祥红卡  SL 商联卡 HJ 黑金卡 BJ 白金卡',
  `pay_amount` int UNSIGNED NULL DEFAULT 0 COMMENT '支付金额',
  `pay_status` enum('WAIT_PAY','SUCCESS_PAY','FAIL_PAY') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'WAIT_PAY' COMMENT '支付状态  1、WAIT_PAY 待支付  2、SUCCESS_PAY 支付成功  3、FAIL_PAY 支付失败',
  `refund_amount` int UNSIGNED NULL DEFAULT 0 COMMENT '已退款金额',
  `refund_status` enum('WAIT_REFUND','PART_REFUND','FULL_REFUND') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'WAIT_REFUND' COMMENT '退款状态  1、WAIT_REFUND 无退款  2、PART_REFUND  部分退款 3、FULL_REFUND 全部退款',
  `trade_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trade_pay_channel
-- ----------------------------
DROP TABLE IF EXISTS `trade_pay_channel`;
CREATE TABLE `trade_pay_channel`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_channel_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单渠道名称',
  `order_channel` int NULL DEFAULT NULL COMMENT '订单渠道',
  `pay_type` enum('ONLINE','OFFLINE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for xxl_job_group
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_group`;
CREATE TABLE `xxl_job_group`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `app_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行器AppName',
  `title` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行器名称',
  `address_type` tinyint NOT NULL DEFAULT 0 COMMENT '执行器地址类型：0=自动注册、1=手动录入',
  `address_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '执行器地址列表，多地址逗号分隔',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_info
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_info`;
CREATE TABLE `xxl_job_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `job_group` int NOT NULL COMMENT '执行器主键ID',
  `job_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `add_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `author` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作者',
  `alarm_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报警邮件',
  `alarm_robot` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报警机器人(企业微信)',
  `schedule_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'NONE' COMMENT '调度类型',
  `schedule_conf` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '调度配置，值含义取决于调度类型',
  `misfire_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'DO_NOTHING' COMMENT '调度过期策略',
  `executor_route_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器路由策略',
  `executor_handler` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务handler',
  `executor_param` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务参数',
  `executor_block_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '阻塞处理策略',
  `executor_timeout` int NOT NULL DEFAULT 0 COMMENT '任务执行超时时间，单位秒',
  `executor_fail_retry_count` int NOT NULL DEFAULT 0 COMMENT '失败重试次数',
  `glue_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'GLUE类型',
  `glue_source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'GLUE源代码',
  `glue_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'GLUE备注',
  `glue_updatetime` datetime NULL DEFAULT NULL COMMENT 'GLUE更新时间',
  `child_jobid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '子任务ID，多个逗号分隔',
  `trigger_status` tinyint NOT NULL DEFAULT 0 COMMENT '调度状态：0-停止，1-运行',
  `trigger_last_time` bigint NOT NULL DEFAULT 0 COMMENT '上次调度时间',
  `trigger_next_time` bigint NOT NULL DEFAULT 0 COMMENT '下次调度时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_lock
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_lock`;
CREATE TABLE `xxl_job_lock`  (
  `lock_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '锁名称',
  PRIMARY KEY (`lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_log
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log`;
CREATE TABLE `xxl_job_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `job_group` int NOT NULL COMMENT '执行器主键ID',
  `job_id` int NOT NULL COMMENT '任务，主键ID',
  `executor_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器地址，本次执行的地址',
  `executor_handler` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务handler',
  `executor_param` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务参数',
  `executor_sharding_param` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务分片参数，格式如 1/2',
  `executor_fail_retry_count` int NOT NULL DEFAULT 0 COMMENT '失败重试次数',
  `trigger_time` datetime NULL DEFAULT NULL COMMENT '调度-时间',
  `trigger_code` int NOT NULL COMMENT '调度-结果',
  `trigger_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '调度-日志',
  `handle_time` datetime NULL DEFAULT NULL COMMENT '执行-时间',
  `handle_code` int NOT NULL COMMENT '执行-状态',
  `handle_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '执行-日志',
  `alarm_status` tinyint NOT NULL DEFAULT 0 COMMENT '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `I_trigger_time`(`trigger_time` ASC) USING BTREE,
  INDEX `I_handle_code`(`handle_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56465 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_log_report
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log_report`;
CREATE TABLE `xxl_job_log_report`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `trigger_day` datetime NULL DEFAULT NULL COMMENT '调度-时间',
  `running_count` int NOT NULL DEFAULT 0 COMMENT '运行中-日志数量',
  `suc_count` int NOT NULL DEFAULT 0 COMMENT '执行成功-日志数量',
  `fail_count` int NOT NULL DEFAULT 0 COMMENT '执行失败-日志数量',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `i_trigger_day`(`trigger_day` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 443 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_logglue
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_logglue`;
CREATE TABLE `xxl_job_logglue`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `job_id` int NOT NULL COMMENT '任务，主键ID',
  `glue_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'GLUE类型',
  `glue_source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'GLUE源代码',
  `glue_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'GLUE备注',
  `add_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_registry
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_registry`;
CREATE TABLE `xxl_job_registry`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `registry_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `registry_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `registry_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_g_k_v`(`registry_group` ASC, `registry_key` ASC, `registry_value` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_user
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_user`;
CREATE TABLE `xxl_job_user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '账号',
  `password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `role` tinyint NOT NULL COMMENT '角色：0-普通用户、1-管理员',
  `permission` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限：执行器ID列表，多个逗号分割',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `i_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
