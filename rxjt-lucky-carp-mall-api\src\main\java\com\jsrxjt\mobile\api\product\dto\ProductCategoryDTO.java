package com.jsrxjt.mobile.api.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 产品分类DTO
 * 
 * <AUTHOR>
 * @since 2025/5/12
 */
@Data
@Schema(description = "产品分类信息")
public class ProductCategoryDTO {

    @Schema(description = "分类ID", example = "1")
    private Long id;

    @Schema(description = "分类名称", example = "手机数码")
    private String categoryName;

    @Schema(description = "分类等级：1-一级分类, 2-二级分类", example = "1")
    private Byte level;

    @Schema(description = "排序值，数值越大越靠前", example = "100")
    private Integer sort;

    @Schema(description = "父级分类ID", example = "0")
    private Long parentId;

    @Schema(description = "父级分类名称", example = "")
    private String parentCategoryName;
}