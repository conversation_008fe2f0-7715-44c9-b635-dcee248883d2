package com.jsrxjt.mobile.biz.xcyMall;

import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallCancelOrderRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallCreateOrderRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallQueryUserRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallRefundRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCancelResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallQueryUserResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallRefundResponseDTO;

/**
 * Created by jeffery.yang on 2025/10/21 15:23
 *
 * @description: 祥采云商城service
 * @author: jeffery.yang
 * @date: 2025/10/21 15:23
 * @version: 1.0
 */
public interface IXcyMallService {

	/**
	 * 祥采云场景-查询福鲤圈用户信息
	 * @param requestDTO
	 * @return
	 */
	XcyMallQueryUserResponseDTO getVipInfo(XcyMallQueryUserRequestDTO requestDTO,String appFlag);

	/**
	 * 祥采云场景-下单
	 * @param requestDTO
	 * @param appFlag 应用标识
	 * @return
	 */
	XcyMallCreateOrderResponseDTO createOrder(XcyMallCreateOrderRequestDTO requestDTO,String appFlag);

	/**
	 * 祥采云场景-退款
	 * @param requestDTO
	 * @return
	 */
	XcyMallRefundResponseDTO refundOrder(XcyMallRefundRequestDTO requestDTO,String appFlag);


	/**
	 * 祥采云场景-取消订单
	 * @param requestDTO
	 * @return
	 */
	XcyMallCancelResponseDTO cancelOrder(XcyMallCancelOrderRequestDTO requestDTO,String appFlag);
}
