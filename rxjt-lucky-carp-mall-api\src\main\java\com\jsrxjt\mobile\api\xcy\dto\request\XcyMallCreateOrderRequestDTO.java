package com.jsrxjt.mobile.api.xcy.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by jeffery.yang on 2023/12/21 17:02
 *
 * @description: 预下单入参
 * @author: jeffery.yang
 * @date: 2023/12/21 17:02
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XcyMallCreateOrderRequestDTO extends XcyMallBaseRequest {

	/**
	 * 这里传商城订单号
	 * <p>required</p>
	 */
	private String tradeNo;

	/**
	 * 瑞祥侧用户uid，登录时提供
	 * <p>required</p>
	 */
	private String uid;


	/**
	 * 订单金额，单位元（保留2位小数）
	 * <p>required</p>
	 */
	private String totalAmount;

	/**
	 * 订单成本金额
	 */
	private String costAmount;

	/**
	 * 下单时间，格式yyyyMMddHHmmss
	 * <p>required</p>
	 */
	private String orderTime;

	/**
	 * 订单过期时间，单位分钟。最小单位1分钟
	 * <p>required</p>
	 */
	private String orderExpire;

	/**
	 * 支付结果通知url，间隔轮询3-5次,成功结束
	 * <p>required</p>  https://%s/%s/mobile-web-pay/ws/mobile/v1/async/payCenterNotify
	 */
	private String notifyUrl;

	/**
	 * （前端）支付结果页面（支付结束后的跳转页面），附带参数
	 * tradeNo=?&status=?
	 * 例如 https://xxxx/payResult?tradeNo=xxxx&status=00
	 * <p>required</p> https://%s/%s/wrap/paysuccess.html?orderType=%s
	 */
	private String resultPageUrl;

	/**
	 * 商城租户号
	 */
	private String outMch;

	/**
	 * 商城租户名称
	 */
	private String outMchName;

	/**
	 * 额外请求体
	 * 暂定给食堂收银台用
	 */
	private String body;


	/**
	 * 秒杀订单标识flashFlag， 0否、1是
	 */
	private Integer flashFlag;

	/**
	 * 订单详情页地址
	 */
	private String orderDetailUrl;


}
