package com.jsrxjt.mobile.domain.order.service.impl;

import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleLogEntity;
import com.jsrxjt.mobile.domain.order.service.AfterSaleLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 售后日志领域服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
@RequiredArgsConstructor
public class AfterSaleLogServiceImpl implements AfterSaleLogService {

    private final BusinessIdGenerator businessIdGenerator;

    @Override
    public AfterSaleLogEntity createAfterSaleLog(AfterSaleEntity afterSale, Integer operationType,
                                                 String operationContent, String operator, Long operatorId) {
        
        AfterSaleLogEntity log = new AfterSaleLogEntity();
        
        log.setId(businessIdGenerator.generateId());
        log.setAfterSaleId(afterSale.getId());
        log.setAfterSaleNo(afterSale.getAfterSaleNo());
        log.setOrderNo(afterSale.getOrderNo());
        log.setOperationType(operationType);
        log.setOperator(operator);
        log.setOperatorId(operatorId);
        log.setOperationContent(operationContent);
        
        LocalDateTime now = LocalDateTime.now();
        log.setOperationTime(now);
        log.setCreateTime(now);
        log.setModTime(now);
        
        return log;
    }
}