package com.jsrxjt.mobile.domain.ticket.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.birth.request.BirthCouponRequestDTO;
import com.jsrxjt.mobile.api.coupon.dto.request.TicketStatusNotifyRequestDTO;
import com.jsrxjt.mobile.domain.ticket.entity.TicketBrandEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketCallBackEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketConsumeCallBackEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;

import java.util.List;

/**
 * @Description: 优惠券领域服务接口
 * @Author: ywt
 * @Date: 2025-08-15 15:52
 * @Version: 1.0
 */
public interface TicketService {
    List<TicketEntity> getTicketsBySkuIdAndPrdType(Long productSkUID, Integer productType);
    boolean isOnlineInRegion(Long ticketId, Integer regionId);
    List<TicketBrandEntity> getTicketBrandsByIds(List<Long> brandIds);
    /**
     * 描述：根据优惠券Id获取对应sku的优惠券信息
     * 参数：
     *  productType--1：普通卡券 2：套餐
     */
    List<TicketEntity> getTicketsBySkuIdsAndTicketIds(Long productSkUID, Integer productType, List<Long> ticketIds);
    /**
     * 营销中台优惠券状态回调
     */
    BaseResponse callback(TicketCallBackEntity callBackEntity);
    /**
     * 营销中台优惠券状态回调
     */
    BaseResponse consumeCallback(TicketConsumeCallBackEntity entity);
    /**
     * 发放门店券
     */
    List<String> receiveBirthCoupon(String ticketId, Integer number, Long timeStamp, String nonce);
}
