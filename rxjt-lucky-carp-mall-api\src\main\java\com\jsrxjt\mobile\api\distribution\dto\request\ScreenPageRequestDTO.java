package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 启动页请求参数
 * @Author: ywt
 * @Date: 2025-06-09 15:06
 * @Version: 1.0
 */
@Data
public class ScreenPageRequestDTO {
    @Schema(description = "三级地址id")
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
}
