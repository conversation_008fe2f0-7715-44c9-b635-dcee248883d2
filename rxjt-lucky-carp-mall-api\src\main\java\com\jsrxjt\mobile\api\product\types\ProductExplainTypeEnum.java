package com.jsrxjt.mobile.api.product.types;

import lombok.Getter;

/**
 * @Description: 产品说明枚举
 * @Author: ywt
 * @Date: 2025-04-28 14:16
 * @Version: 1.0
 */
@Getter
public enum ProductExplainTypeEnum {
    EXCHANGE(1, "兑换须知"),

    OFFSET(2, "核销须知"),
    USE_DETAIL(3, "使用说明"),
    WARM_TOAST(4, "温馨提示");

    /**
     * 类型值
     */
    private final Integer type;

    /**
     * 类型描述
     */
    private final String desc;

    ProductExplainTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
