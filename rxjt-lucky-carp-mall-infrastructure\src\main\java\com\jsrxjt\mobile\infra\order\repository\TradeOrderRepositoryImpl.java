package com.jsrxjt.mobile.infra.order.repository;

import com.jsrxjt.mobile.domain.order.entity.TradeOrderEntity;
import com.jsrxjt.mobile.domain.order.entity.TradeOrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.TradeOrderRepository;
import com.jsrxjt.mobile.domain.order.types.PayChannelEnum;
import com.jsrxjt.mobile.domain.order.types.TradePayStatusEnum;
import com.jsrxjt.mobile.domain.order.types.TradeRefundStatusEnum;
import com.jsrxjt.mobile.infra.order.persistent.mapper.TradeOrderInfoMapper;
import com.jsrxjt.mobile.infra.order.persistent.mapper.TradeOrderMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.TradeOrderInfoPO;
import com.jsrxjt.mobile.infra.order.persistent.po.TradeOrderPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 交易订单仓储实现类
 * <AUTHOR>
 * @since 2025/8/15
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TradeOrderRepositoryImpl implements TradeOrderRepository {

    private final TradeOrderMapper tradeOrderMapper;

    private final TradeOrderInfoMapper tradeOrderInfoMapper;

    @Override
    public TradeOrderEntity findTradeOrderByOrderNoAndTradeNo(String orderNo, String tradeNo) {
        TradeOrderPO tradeOrderPO = tradeOrderMapper.selectByOrderNoAndTradeNo(orderNo, tradeNo);
        if (tradeOrderPO != null) {
            return convertToTradeOrderEntity(tradeOrderPO);
        }
        return null;
    }

    @Override
    public List<TradeOrderInfoEntity> listTradeOrderInfoByTradeNoAndOrderNo(String tradeNo, String orderNo) {
        List<TradeOrderInfoPO> tradeOrderInfoPOs = tradeOrderInfoMapper.selectByTradeNoAndOrderNo(tradeNo, orderNo);
        
        if (tradeOrderInfoPOs == null || tradeOrderInfoPOs.isEmpty()) {
            return Collections.emptyList();
        }
        
        return tradeOrderInfoPOs.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }


    /**
     * PO转换为Entity
     */
    private TradeOrderInfoEntity convertToEntity(TradeOrderInfoPO tradeOrderInfoPO) {
        TradeOrderInfoEntity entity = new TradeOrderInfoEntity();
        BeanUtils.copyProperties(tradeOrderInfoPO, entity);
        
        // 枚举类型转换
        if (tradeOrderInfoPO.getPayChannel() != null) {
            entity.setPayChannel(PayChannelEnum.fromCode(tradeOrderInfoPO.getPayChannel()));
        }
        if (tradeOrderInfoPO.getPayStatus() != null) {
            entity.setPayStatus(TradePayStatusEnum.fromCode(tradeOrderInfoPO.getPayStatus()));
        }
        if (tradeOrderInfoPO.getRefundStatus() != null) {
            entity.setRefundStatus(TradeRefundStatusEnum.fromCode(tradeOrderInfoPO.getRefundStatus()));
        }
        
        // 金额转换：分转元
        if (tradeOrderInfoPO.getPayAmount() != null) {
            entity.setPayAmount(new BigDecimal(tradeOrderInfoPO.getPayAmount())
                    .divide(new BigDecimal("100"),2, RoundingMode.HALF_UP));
        }
        if (tradeOrderInfoPO.getRefundAmount() != null) {
            entity.setRefundAmount(new BigDecimal(tradeOrderInfoPO.getRefundAmount())
                    .divide(new BigDecimal("100"),2, RoundingMode.HALF_UP));
        }
        
        return entity;
    }

    /**
     * TradeOrderPO转换为TradeOrderEntity
     */
    private TradeOrderEntity convertToTradeOrderEntity(TradeOrderPO tradeOrderPO) {
        TradeOrderEntity entity = new TradeOrderEntity();
        BeanUtils.copyProperties(tradeOrderPO, entity);
        
        // 金额转换：分转元
        if (tradeOrderPO.getPayAmount() != null) {
            entity.setPayAmount(new BigDecimal(tradeOrderPO.getPayAmount())
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        }
        if (tradeOrderPO.getRefundAmount() != null) {
            entity.setRefundAmount(new BigDecimal(tradeOrderPO.getRefundAmount())
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        }
        if (tradeOrderPO.getCardPayAmount() != null) {
            entity.setCardPayAmount(new BigDecimal(tradeOrderPO.getCardPayAmount())
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        }
        if (tradeOrderPO.getThirdPayAmount() != null) {
            entity.setThirdPayAmount(new BigDecimal(tradeOrderPO.getThirdPayAmount())
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        }
        
        return entity;
    }
}
