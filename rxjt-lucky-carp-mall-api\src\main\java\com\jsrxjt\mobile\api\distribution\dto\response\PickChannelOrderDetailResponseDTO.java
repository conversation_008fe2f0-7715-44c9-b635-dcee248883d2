package com.jsrxjt.mobile.api.distribution.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 扫码提货创建订单返回参数
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "订单数据转换本地")
public class PickChannelOrderDetailResponseDTO {

    @Schema(description = "第三方Id")
    private String thirdId;

    @Schema(description = "第三方店员id")
    private String externalShopUserId;

    @Schema(description = "第三方门店id")
    private String externalShopId;

    @Schema(description = "本系统订单Id")
    private Long orderId;

    @Schema(description = "本系统客户id")
    private Long customerId;

    @Schema(description = "码号")
    private String code;

    @Schema(description = "第三方订单号")
    private String externalOrderNo;

    @Schema(description = "平台交易流水号")
    private String  tradeNo;

    @Schema(description = "订单对接渠道 4 分销中心 9逸刻，10屈臣氏")
    private Integer orderChannel;

    @Schema(description = "商户费率 收银入账时可能会用到")
    private String rate;

    @Schema(description = "付款总金额")
    private BigDecimal paymentAmount;

    @Schema(description = "付款方式")
    private String payType;

    @Schema(description = "订单创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreateTime;

    @Schema(description = "订单过期时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date payExpireTime;



    
}
