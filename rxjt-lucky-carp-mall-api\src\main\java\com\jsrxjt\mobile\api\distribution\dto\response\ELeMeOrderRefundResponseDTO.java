package com.jsrxjt.mobile.api.distribution.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 饿了么订单退款结果
 * <AUTHOR>
 * @since 2025/12/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ELeMeOrderRefundResponseDTO {

    /**
     * 支付网关的订单号
     */
    private String transactionId;

    /**
     * 三方订单编号（福鲤圈订单编号）
     */
    private String outTradeNo;

    /**
     * 支付网关的退款单号
     */
    private String refundNo;

    /**
     * 三方退款单号（福鲤圈退款单号）
     */
    private String outRefundNo;

    /**
     * 退款金额 单位分
     */
    private Integer refundAmount;

    /**
     * 退款状态(SUCCESS=退款成功；FAIL=退款失败；PROCESSING=退款中)
     */
    private String refundStatus;

}
