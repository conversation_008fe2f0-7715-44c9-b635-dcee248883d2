package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(name="HisCouponInfoResponse", description="用户历史券信息")
public class HisCouponInfoResponse {

    @Schema(description = "券名称")
    private String couponName;

    @Schema(description = "券图片")
    private String couponImg;

    @Schema(description = "券核销页地址")
    private String couponUrl;

    @Schema(description = "券类型 1电子卡 2提货券 3套餐")
    private Integer label;

    @Schema(description = "券发放时间")
    private Date createTime;
}