package com.jsrxjt.mobile.api.coupon.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 充值账户类型
 * @Author: ywt
 * @Date: 2025-07-23 15:43
 * @Version: 1.0
 */
@Data
public class VideoCouponAccountTypeDTO {
    @Schema(description = "充值类型,0其他 1手机号 2QQ号")
    private Integer accountType;
    @Schema(description = "是否选中， 0--未选中 1--选中")
    private Integer isSelected;
    @Schema(description = "SKU信息，仅isSelected为1有效")
    private CouponSkusResponseDTO skuInfo;
}
