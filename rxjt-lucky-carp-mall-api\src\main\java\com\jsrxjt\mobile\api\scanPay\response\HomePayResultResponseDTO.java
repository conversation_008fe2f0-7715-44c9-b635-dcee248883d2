package com.jsrxjt.mobile.api.scanPay.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "收银台订单轮询信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HomePayResultResponseDTO {

    @Schema(description = "订单编号")
    private String orderNo;
    @Schema(description = "订单状态")
    private Integer orderStatus;


}
