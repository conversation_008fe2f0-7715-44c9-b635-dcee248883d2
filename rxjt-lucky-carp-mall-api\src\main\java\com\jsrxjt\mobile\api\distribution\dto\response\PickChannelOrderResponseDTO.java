package com.jsrxjt.mobile.api.distribution.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 扫码提货创建订单返回参数
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "外部调用创建订单接口")
@AllArgsConstructor
@NoArgsConstructor
public class PickChannelOrderResponseDTO {

    @Schema(description = "外部订单号")
    @JSONField(name = "out_order_sn")
    private String out_order_sn;


    
}
