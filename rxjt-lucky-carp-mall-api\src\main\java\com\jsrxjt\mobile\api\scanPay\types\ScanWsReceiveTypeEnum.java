package com.jsrxjt.mobile.api.scanPay.types;

/**
 * 扫码付接收消息类型
 * <AUTHOR>
 * @date 2025/10/30
 */
public enum ScanWsReceiveTypeEnum {
    /**
     * 支付
     */
    PAY("PAY"),

    /**
     * 订单支付状态
     */
    ORDER_PAY_STATUS("ORDER_PAY_STATUS");

    private String type;

    ScanWsReceiveTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static ScanWsReceiveTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (ScanWsReceiveTypeEnum receiveTypeEnum : values()) {
            if (receiveTypeEnum.getType().equals(type)) {
                return receiveTypeEnum;
            }
        }
        return null;
    }
}
