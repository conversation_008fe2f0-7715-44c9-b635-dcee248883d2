package com.jsrxjt.common.adapter.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.SpringContextHolder;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Optional;

/**
 * PostSignInterceptor
 * 对servlet请求进行拦截，对@verifySign的注解的类或方法进行验签
 * 
 * <AUTHOR> Fengping
 *         2023/3/14 11:44
 * 
 **/
@SuppressWarnings("NullableProblems")
@Slf4j
public class PostSignInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        request.setAttribute("_startTime", stopWatch);

        log.info("接口路径{}", request.getRequestURI());

        // 让模拟的用户id登录
        String testId = request.getHeader("Test-Customer-Id");
        if ( testId != null) {
            StpUtil.switchTo(testId);
        }

        if(isIgnoreSign()) {
            return true;
        }

        VerifySign annotation = getVerifySignAnnotation((HandlerMethod) handler);
        if (annotation == null) {
            // 没有该注解，不用验签
            return true;
        }
        String sign = getSign(request);

        if (annotation.hasToken()) {
            // 需要验证token
            verifyToken();
        }

        JSONObject requestBody = getRequestBody(request);

        if (CollectionUtils.isEmpty(requestBody)) {
            // 如果没有请求参数 不用验签
            return true;
        }

        verifyTimestamp(requestBody);

        verifySign(sign, requestBody);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return;
        }
        StopWatch stopWatch = (StopWatch) request.getAttribute("_startTime");
        if (stopWatch == null || !stopWatch.isRunning()) {
            return;
        }
        stopWatch.stop();
        log.info("接口路径{},请求耗时{}ms", request.getRequestURI(), stopWatch.getTotalTimeMillis());

    }

    private static boolean isIgnoreSign() {
        return true;


    }

    private static VerifySign getVerifySignAnnotation(HandlerMethod handlerMethod) {
        Method method = handlerMethod.getMethod();
        Class<?> clazz = Objects.requireNonNull(method, "method is null").getDeclaringClass();
        VerifySign annotation = method.getAnnotation(VerifySign.class);
        VerifySign clazzAnnotation = clazz.getAnnotation(VerifySign.class);
        return Optional.ofNullable(annotation).orElse(clazzAnnotation);
    }

    private void verifyToken() {
        try {
            StpUtil.checkLogin();
        } catch (Exception e) {
            log.error("verify login token error", e);
            throw new BizException(Status.NO_CACHE_USER);
        }
    }

    private void verifyTimestamp(JSONObject requestBody) {
        Long timeStamp = requestBody.getLong("timeStamp");
        String nonce = requestBody.getString("nonce");
        if (CharSequenceUtil.isBlank(nonce) || Objects.isNull(timeStamp)) {
            throw new BizException(Status.NULL_PARAM.getCode(), Status.NULL_PARAM.getMessage());
        }
        if (System.currentTimeMillis() - timeStamp > 1000 * 60 * 5) {
            throw new BizException(Status.SIGN_TIME_OUT.getCode(), Status.SIGN_TIME_OUT.getMessage());
        }
        RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
        if (redisUtil.exists(RedisKeyConstants.SIGN_NONCE + nonce)) {
            throw new BizException(Status.SIGN_REPEAT.getCode(), Status.SIGN_REPEAT.getMessage());
        } else {
            redisUtil.setAdd(RedisKeyConstants.SIGN_NONCE + nonce, "1", 60);
        }

    }

    private static String getSign(HttpServletRequest request) {
        String sign = request.getHeader("X-Sign");
        if (CharSequenceUtil.isBlank(sign)) {
            throw new BizException(Status.SIGN_NONE.getCode(), Status.SIGN_NONE.getMessage());
        }
        return sign;
    }

    private static void verifySign(String sign, JSONObject requestBody) {
        Environment env = SpringContextHolder.getBean(Environment.class);
        String key = env.getProperty("api.sign.key");
        Assert.notNull(key, "密钥不能为空");

        // 添加密钥
        String waitSignStr = requestBody.toString();
        if (log.isDebugEnabled()) {
            log.debug("待签名字符串：{}", waitSignStr);
        }
        // 使用HMAC-SHA256签名
        String calculatedSign = new HMac(HmacAlgorithm.HmacSHA256, key.getBytes()).digestHex(waitSignStr);

        log.info("正确的签名是{}", calculatedSign);
        if (!Objects.equals(calculatedSign, sign)) {
            throw new BizException(Status.SIGN_VERIFY_FAILED.getCode(), Status.SIGN_VERIFY_FAILED.getMessage());
        }
    }

    private JSONObject getRequestBody(HttpServletRequest request) throws IOException {
        try (InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder buffer = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                buffer.append(line);
            }

            String content = buffer.toString();
            if (CharSequenceUtil.isBlank(content)  || !JSON.isValid(content)) {
                return new JSONObject();
            }

            return JSON.parseObject(content, Feature.OrderedField);
        }
    }
}
