package com.jsrxjt.mobile.api.enums;

//订单状态: 0-待付款 10-进行中 20-交易成功 30-交易关闭 40-超时取消 41-手动取消
public enum OrderStatusEnum {
    PENDING_PAYMENT(0, "待付款"),
    IN_PROGRESS(10, "进行中"),
    TRADE_SUCCESS(20, "交易成功"),
    TRADE_CLOSED(30, "交易关闭"),
    TIMEOUT_CANCEL(40, "超时取消"),
    MANUAL_CANCEL(41, "手动取消");

    private final Integer code;

    private final String value;

    OrderStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getNameByType(Integer code) {
        for (OrderStatusEnum enums : OrderStatusEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

    public static OrderStatusEnum getByCode(Integer code) {
        for (OrderStatusEnum enums : OrderStatusEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
