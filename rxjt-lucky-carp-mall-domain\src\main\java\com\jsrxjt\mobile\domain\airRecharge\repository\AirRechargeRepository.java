package com.jsrxjt.mobile.domain.airRecharge.repository;

import com.jsrxjt.mobile.domain.airRecharge.entity.AirRechargeEntity;

import java.util.List;

/**
 * @Description: 空中充值领域接口层
 * @Author: ywt
 * @Date: 2025-08-26 15:11
 * @Version: 1.0
 */
public interface AirRechargeRepository {
    List<AirRechargeEntity> getAirRechargeInfo(String mobile);
    AirRechargeEntity getAirRechargeInfoById(Integer id);
    Integer updateAirRechargeStatus(Integer id, String signPicUrl);
}
