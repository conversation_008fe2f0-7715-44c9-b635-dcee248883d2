package com.jsrxjt.mobile.api.coupon.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 自发券的适用门店列表请求参数
 * @Author: ywt
 * @Date: 2025-05-07 10:12
 * @Version: 1.0
 */
@Data
@Schema(description = "自发券的适用门店列表请求参数")
public class CouponShopRequestDTO {
    @Schema(description = "提货券分销平台的产品id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "产品id为空错误")
    private Long pickProductId;
    @Schema(description = "精度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "精度为空错误")
    private String longitude;
    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "纬度为空错误")
    private String latitude;
    /*@Schema(description = "城市编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "城市编码为空错误")
    private String cityCode;*/
    @Schema(description = "搜索关键词")
    private String keyword;//关键词
    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "时间戳为空错误")
    private String timeStamp;
    @Schema(description = "32位随机字符串", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "参数nonce为空错误")
    private String nonce;
    @Schema(description = "当前页码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当前页码为空错误")
    private Integer page = 1;
    @Schema(description = "每页数量")
    @NotNull(message = "每页数量为空错误")
    private Integer pageSize = 10;
}
