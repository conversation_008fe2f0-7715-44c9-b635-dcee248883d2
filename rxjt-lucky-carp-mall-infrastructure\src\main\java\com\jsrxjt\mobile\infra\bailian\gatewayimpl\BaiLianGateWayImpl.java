package com.jsrxjt.mobile.infra.bailian.gatewayimpl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.api.bailian.dto.request.BaiLianShopRequestDTO;
import com.jsrxjt.mobile.domain.bailian.gateway.BaiLianGateWay;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianBrandResponse;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianCategoryResponse;
import com.jsrxjt.mobile.domain.bailian.response.BaiLianShopResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import com.jsrxjt.mobile.infra.gateway.distribution.util.SignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/10/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaiLianGateWayImpl implements BaiLianGateWay {

    private static final String STATUS_KEY = "status";

    private static final Integer SUCCESS_CODE = 200;

    private final DistributionConfig distributionConfig;

    private final HttpClientGateway httpClientGateway;


    @Override
    public List<BaiLianCategoryResponse> getCategoryList() {
        List<BaiLianCategoryResponse> responseList = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        addCommonParams(params);
        try {
            String url = distributionConfig.getBailian().getBaseUrl() + distributionConfig.getBailian().getApiPath() + distributionConfig.getBailian().getCategoryListPath();
            String response = httpClientGateway.doPost(url, params, distributionConfig.getBailian().getConnectTimeout(),
                    distributionConfig.getBailian().getReadTimeout());
            JSONObject result = JSON.parseObject(response);
            if (Objects.equals(result.getInteger(STATUS_KEY), SUCCESS_CODE)) {
                JSONArray dataObj = result.getJSONArray("data");
                if (dataObj != null && !dataObj.isEmpty()) {
                    responseList = JSON.parseArray(dataObj.toJSONString(), BaiLianCategoryResponse.class);
                }
            }
        } catch (Exception e) {
            log.info("百联获取分类列表异常: {}", e.getMessage(), e);
        }
        return responseList;
    }

    @Override
    public List<BaiLianBrandResponse> getBrandList(Long categoryId) {
        List<BaiLianBrandResponse> responseList = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        params.put("category_id", categoryId);
        addCommonParams(params);
        try {
            String url = distributionConfig.getBailian().getBaseUrl() + distributionConfig.getBailian().getApiPath() + distributionConfig.getBailian().getBrandListPath();
            String response = httpClientGateway.doPost(url, params, distributionConfig.getBailian().getConnectTimeout(),
                    distributionConfig.getBailian().getReadTimeout());
            JSONObject result = JSON.parseObject(response);
            if (Objects.equals(result.getInteger(STATUS_KEY), SUCCESS_CODE)) {
                JSONArray dataObj = result.getJSONArray("data");
                if (dataObj != null && !dataObj.isEmpty()) {
                    responseList = JSON.parseArray(dataObj.toJSONString(), BaiLianBrandResponse.class);
                }
            }
        } catch (Exception e) {
            log.info("百联获取品牌列表异常: {}", e.getMessage(), e);
        }
        return responseList;
    }

    @Override
    public List<BaiLianShopResponse> getShopList(BaiLianShopRequestDTO request) {
        List<BaiLianShopResponse> responseList = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        params.put("brand_id", request.getBrandId());
        params.put("search", request.getSearch());
        addCommonParams(params);
        try {
            String url = distributionConfig.getBailian().getBaseUrl() + distributionConfig.getBailian().getApiPath() + distributionConfig.getBailian().getShopListPath();
            String response = httpClientGateway.doPost(url, params, distributionConfig.getBailian().getConnectTimeout(),
                    distributionConfig.getBailian().getReadTimeout());
            JSONObject result = JSON.parseObject(response);
            if (Objects.equals(result.getInteger(STATUS_KEY), SUCCESS_CODE)) {
                JSONArray dataObj = result.getJSONArray("data");
                if (dataObj != null && !dataObj.isEmpty()) {
                    responseList = JSON.parseArray(dataObj.toJSONString(), BaiLianShopResponse.class);
                }
            }
        } catch (Exception e) {
            log.info("百联获取门店列表异常: {}", e.getMessage(), e);
        }
        return responseList;
    }

    private void addCommonParams(Map<String, Object> params) {
        params.put("appid", distributionConfig.getBailian().getAppId());
        params.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        params.put("nounce", UUID.randomUUID().toString().replace("-", "").substring(0, 32));
        String signature = SignUtil.sign(params, distributionConfig.getBailian().getAppSecret());
        params.put("signature", signature);
    }
}
