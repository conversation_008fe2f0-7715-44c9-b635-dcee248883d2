# AJ-Captcha验证码模块集成说明

## 模块概述

基于AJ-Captcha开源组件创建了新的验证码模块 `rxjt-lucky-carp-mall-common-captcha`，提供滑动拼图和点选文字两种验证码类型，支持Redis分布式缓存。

## 模块结构

```
rxjt-lucky-carp-mall-common-captcha/
├── src/main/java/com/jsrxjt/common/captcha/
│   ├── CaptchaAutoConfiguration.java           # 自动配置类
│   ├── config/
│   │   └── CaptchaProperties.java             # 配置属性类
│   ├── service/
│   │   ├── CaptchaService.java                # 验证码服务接口
│   │   └── impl/CaptchaServiceImpl.java       # 验证码服务实现
│   ├── controller/
│   │   └── CaptchaController.java             # REST控制器
│   ├── cache/
│   │   └── CaptchaCacheServiceRedisImpl.java  # Redis缓存实现
│   ├── util/
│   │   └── CaptchaUtil.java                   # 工具类
│   └── example/
│       └── CaptchaExample.java                # 使用示例
├── src/main/resources/
│   ├── META-INF/
│   │   ├── services/                          # SPI配置
│   │   └── spring/                            # Spring Boot自动配置
│   ├── captcha/images/                        # 验证码图片目录
│   └── application-captcha.yml                # 默认配置
├── src/test/                                  # 测试代码
├── pom.xml                                    # Maven配置
└── README.md                                  # 使用文档
```

## 核心功能

### 1. 验证码类型
- **滑动拼图验证码**：用户拖动滑块完成拼图验证
- **点选文字验证码**：用户点击指定文字完成验证

### 2. 缓存支持
- **Redis缓存**：支持分布式部署
- **本地缓存**：适合单机部署（测试环境）

### 3. 安全特性
- **AES加密**：坐标信息加密传输
- **频率限制**：防止暴力破解
- **失败锁定**：多次失败后临时锁定

### 4. 自定义配置
- **自定义底图**：支持自定义验证码背景图片
- **水印设置**：可配置水印文字和字体
- **误差控制**：可调整滑动验证的误差范围

## 技术架构

### 依赖关系
```
AJ-Captcha 1.4.0
├── Spring Boot Starter Web
├── Spring Boot Data Redis
├── Jackson JSON
└── Hutool Core
```

### 设计模式
- **自动配置模式**：Spring Boot AutoConfiguration
- **SPI机制**：缓存服务扩展
- **工厂模式**：验证码类型创建
- **策略模式**：不同缓存实现

## 集成步骤

### 1. 模块依赖
已添加到 `rxjt-lucky-carp-mall-common` 父模块中：
```xml
<module>rxjt-lucky-carp-mall-common-captcha</module>
```

### 2. 使用方式

#### 方式一：REST接口
```bash
# 获取验证码
POST /captcha/get
{
    "captchaType": "blockPuzzle",
    "clientUid": "unique-id"
}

# 校验验证码
POST /captcha/check
{
    "captchaType": "blockPuzzle",
    "token": "验证码token",
    "pointJson": "坐标信息"
}

# 二次校验
POST /captcha/verify
{
    "captchaVerification": "验证参数"
}
```

#### 方式二：编程式调用
```java
@Autowired
private CaptchaService captchaService;

// 获取验证码
CaptchaVO captchaVO = CaptchaUtil.createBlockPuzzleCaptcha("client-123");
ResponseModel response = captchaService.get(captchaVO);

// 二次校验（登录场景）
CaptchaVO verifyVO = CaptchaUtil.createVerificationCaptcha(captchaVerification);
ResponseModel result = captchaService.verification(verifyVO);
```

### 3. 配置示例
```yaml
jsrxjt:
  captcha:
    enabled: true
    type: blockPuzzle
    cache-type: redis

aj:
  captcha:
    jigsaw: classpath:captcha/images/jigsaw
    pic-click: classpath:captcha/images/pic-click
    water-mark: 我的水印
    slip-offset: 5
    aes-status: true
    req-frequency-limit-enable: true
```

## 与现有验证码的对比

### 原有实现（hutool-captcha）
- ✅ 简单易用
- ❌ 功能单一（仅数字字母）
- ❌ 安全性较低
- ❌ 用户体验一般

### 新实现（AJ-Captcha）
- ✅ 功能丰富（滑动拼图、点选文字）
- ✅ 安全性高（AES加密、防刷机制）
- ✅ 用户体验好（图形化交互）
- ✅ 分布式友好（Redis缓存）
- ✅ 可扩展性强（SPI机制）

## 迁移建议

### 1. 渐进式迁移
- 保留现有hutool验证码实现
- 新功能使用AJ-Captcha
- 逐步替换现有验证码

### 2. 配置兼容
- 通过配置开关控制使用哪种验证码
- 支持同时存在两种实现

### 3. 接口统一
- 封装统一的验证码服务接口
- 屏蔽底层实现差异

## 部署注意事项

### 1. Redis配置
- 确保Redis连接正常
- 配置合适的过期时间
- 考虑Redis高可用

### 2. 图片资源
- 准备自定义底图（可选）
- 图片格式：jpg、png、gif等
- 建议尺寸：320x155（滑动）、310x155（点选）

### 3. 性能优化
- 合理设置缓存过期时间
- 启用频率限制防止滥用
- 监控验证码成功率

## 后续扩展

### 1. 多语言支持
- 支持国际化配置
- 多语言水印和提示

### 2. 主题定制
- 支持多套UI主题
- 品牌化定制

### 3. 统计分析
- 验证码使用统计
- 成功率分析
- 性能监控

这个新的验证码模块为项目提供了更强大、更安全、用户体验更好的验证码解决方案，建议在新功能中优先使用。
