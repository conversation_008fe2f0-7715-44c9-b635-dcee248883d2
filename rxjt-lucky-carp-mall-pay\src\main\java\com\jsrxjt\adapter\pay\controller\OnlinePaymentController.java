package com.jsrxjt.adapter.pay.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.enums.PaymentChannelEnum;
import com.jsrxjt.mobile.api.payment.dto.request.PaymentCallbackRequestDTO;
import com.jsrxjt.mobile.api.payment.request.PaymentRequestDTO;
import com.jsrxjt.mobile.api.payment.request.PrePayRequestDTO;
import com.jsrxjt.mobile.api.payment.response.PaymentResponseDTO;
import com.jsrxjt.mobile.api.payment.response.PrePayResponseDTO;
import com.jsrxjt.mobile.biz.payment.PaymentCaseService;
import com.jsrxjt.mobile.biz.payment.service.PaymentCallbackCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

/**
 * 线上支付控制器
 * <AUTHOR>
 * @since 2025/8/8
 */
@RestController
@RequestMapping("/v1/online-payment")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "线上支付接口", description = "线上支付相关接口")
public class OnlinePaymentController {
    
    private final PaymentCaseService paymentCaseService;

    private final PaymentCallbackCaseService paymentCallbackCaseService;
    
    /**
     * 预支付接口
     */
    @PostMapping("/pre-pay")
    @Operation(summary = "预支付", description = "查询订单、校验订单状态、发起预支付")
    @VerifySign(hasToken = true)
    public BaseResponse<PrePayResponseDTO> prePay(@RequestBody @Valid PrePayRequestDTO request) {
        log.info("接收到预支付请求，订单号：{}", request.getOrderNo());
        
        PrePayResponseDTO response = paymentCaseService.prePay(request);
        
        log.info("预支付请求处理完成，订单号：{}", request.getOrderNo());
        return BaseResponse.succeed(response);
    }

    /**
     * 发起支付接口
     */
    @PostMapping("/pay")
    @Operation(summary = "发起支付", description = "根据预支付订单号发起支付")
    @VerifySign(hasToken = true)
    public BaseResponse<PaymentResponseDTO> pay(@RequestBody @Valid PaymentRequestDTO request) {
        log.info("接收到发起支付请求，订单号：{}，预支付订单号：{}", 
                request.getOrderNo(), request.getPreOrderNo());
        
        PaymentResponseDTO response = paymentCaseService.pay(request);
        
        log.info("发起支付请求处理完成，订单号：{}，支付状态：{}", 
                request.getOrderNo(), response.getPayStatus());
        return BaseResponse.succeed(response);
    }


    @PostMapping("/notify")
    @Operation(summary = "支付回调通知", description = "支付回调通知")
    public BaseResponse<Void> paymentNotify(HttpServletRequest request, @RequestBody Map<String, Object> requestBody) {
        log.info("接收到支付成功回调请求，请求参数：{}", requestBody);
        PaymentCallbackRequestDTO requestDTO = new PaymentCallbackRequestDTO();
        requestDTO.setPaymentChannel(PaymentChannelEnum.ONLINE_AGGREGATE);
        requestDTO.setHttpServletRequest(request);
        requestDTO.setRequestBody(requestBody);
        paymentCallbackCaseService.handlePaymentCallback(requestDTO);
        log.info("支付成功回调处理完成");
        return BaseResponse.succeed();
    }
}
