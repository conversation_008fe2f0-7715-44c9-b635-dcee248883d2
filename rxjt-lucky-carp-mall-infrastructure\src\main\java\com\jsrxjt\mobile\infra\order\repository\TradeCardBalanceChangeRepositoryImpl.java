package com.jsrxjt.mobile.infra.order.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.customer.request.CustomerCardTradeRequest;
import com.jsrxjt.mobile.domain.order.entity.TradeCardBalanceChangeEntity;
import com.jsrxjt.mobile.domain.order.repository.TradeCardBalanceChangeRepository;
import com.jsrxjt.mobile.infra.order.persistent.mapper.TradeCardBalanceChangeMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.TradeCardBalanceChangePO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡交易流水实现
 * <AUTHOR>
 * @date 2025/10/11
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TradeCardBalanceChangeRepositoryImpl implements TradeCardBalanceChangeRepository {

    private final TradeCardBalanceChangeMapper tradeCardBalanceChangeMapper;

    @Override
    public List<TradeCardBalanceChangeEntity> findAllByCardNo(String cardNo) {
        List<TradeCardBalanceChangePO> list = tradeCardBalanceChangeMapper.selectList(new LambdaQueryWrapper<TradeCardBalanceChangePO>()
                .eq(TradeCardBalanceChangePO::getCardNo, cardNo));
        return BeanUtil.copyToList(list, TradeCardBalanceChangeEntity.class);
    }

    @Override
    public PageDTO<TradeCardBalanceChangeEntity> getCardTradeList(CustomerCardTradeRequest request) {
        Page<TradeCardBalanceChangePO> page = new Page<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<TradeCardBalanceChangePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TradeCardBalanceChangePO::getCardNo, request.getCardNo())
                .orderByDesc(TradeCardBalanceChangePO::getTradeTime);
        if (request.getYearMonth() != null){
            LocalDateTime startTime = request.getYearMonth().atDay(1).atStartOfDay();
            LocalDateTime endTime = request.getYearMonth().atEndOfMonth().atTime(23, 59, 59);
            queryWrapper.between(TradeCardBalanceChangePO::getTradeTime, startTime, endTime);
        }
        IPage<TradeCardBalanceChangePO> resultPage = tradeCardBalanceChangeMapper.selectPage(page, queryWrapper);
        List<TradeCardBalanceChangeEntity> list = resultPage.getRecords().stream()
                .map(po -> {
                    TradeCardBalanceChangeEntity entity = new TradeCardBalanceChangeEntity();
                    BeanUtils.copyProperties(po, entity);
                    return entity;
                }).collect(Collectors.toList());
        return PageDTO.<TradeCardBalanceChangeEntity>builder()
                .records(list)
                .total(resultPage.getTotal())
                .current(resultPage.getCurrent())
                .size(resultPage.getSize())
                .pages(resultPage.getPages())
                .build();
    }

    @Override
    public Integer saveAirRecharge(TradeCardBalanceChangeEntity entity) {
        TradeCardBalanceChangePO tradeCardBalanceChangePO = new TradeCardBalanceChangePO();
        BeanUtils.copyProperties(entity, tradeCardBalanceChangePO);
        tradeCardBalanceChangePO.setCreatedAt(new Date());
        tradeCardBalanceChangePO.setDeletedAt(0);
        return tradeCardBalanceChangeMapper.insert(tradeCardBalanceChangePO);
    }
}
