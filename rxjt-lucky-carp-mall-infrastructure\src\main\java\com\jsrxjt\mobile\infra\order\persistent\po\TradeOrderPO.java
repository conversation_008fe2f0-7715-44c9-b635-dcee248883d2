package com.jsrxjt.mobile.infra.order.persistent.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 交易订单表实体类
 * <AUTHOR>
 * @since 2025/8/18
 */
@Data
@TableName("trade_order")
public class TradeOrderPO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 业务订单号
     */
    private String orderNo;

    /**
     * 外部业务单号
     */
    private String outOrderNo;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 支付类型 ONLINE 线上支付 OFFLINE 线下支付
     */
    private String payType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单来源 APP_ANDROID APP_IOS WX_MINI
     */
    private String orderSource;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单渠道
     */
    private Integer orderChannel;

    /**
     * 支付金额（分）
     */
    private Long payAmount;

    /**
     * 支付状态 WAIT 待交易 PAYING 交易中 SUCCESS 交易成功 FAIL 支付失败 CLOSE 交易关闭
     */
    private String payStatus;

    /**
     * 退款金额（分）
     */
    private Long refundAmount;

    /**
     * 退款状态 NONE 无退款 PART 部分退款 FULL 全部退款
     */
    private String refundStatus;

    /**
     * 瑞祥卡支付金额（分）
     */
    private Long cardPayAmount;

    /**
     * 第三方支付金额（分）
     */
    private Long thirdPayAmount;

    /**
     * 交易时间
     */
    private LocalDateTime tradeTime;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 删除时间
     */
    @TableField("deleted_at")
    private LocalDateTime deletedAt;
}