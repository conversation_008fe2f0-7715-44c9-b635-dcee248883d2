package com.jsrxjt.mobile.infra.ticket.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.ticket.types.TicketStatusEnum;
import com.jsrxjt.mobile.api.ticket.types.TicketTypeEnum;
import com.jsrxjt.mobile.domain.ticket.entity.TicketDeliveryEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketDeliveryRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketDeliveryMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketDeliveryPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 优惠券发放
 * @Author: zy
 */
@Repository
@RequiredArgsConstructor
public class TicketDeliveryRepositoryImpl implements TicketDeliveryRepository {
    private final TicketDeliveryMapper ticketDeliveryMapper;

    /**
     * @param userId
     * @return
     */
    @Override
    public PageDTO<TicketDeliveryEntity> getMerchantTicketByUserId(Long userId, Long pageNum, Long pageSize) {
        Page<TicketDeliveryPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<TicketDeliveryPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TicketDeliveryPO::getCustomerId, userId)
                .in(TicketDeliveryPO::getTicketType, TicketTypeEnum.MERCHANT_COUPON.getCode(), TicketTypeEnum.RX_DISTRIBUTION_COUPON.getCode())
                .eq(TicketDeliveryPO::getDelFlag, 0)
                .orderByAsc(TicketDeliveryPO::getStatus)
                .orderByDesc(TicketDeliveryPO::getCreateTime);
        Page<TicketDeliveryPO> ticketDeliveryPOPage = ticketDeliveryMapper.selectPage(page, queryWrapper);
        if (ticketDeliveryPOPage.getTotal() == 0) {
            return PageDTO.emptyBuild(pageNum, pageSize);
        }
        List<TicketDeliveryPO> records = ticketDeliveryPOPage.getRecords();
        List<TicketDeliveryEntity> ticketDeliveryEntities = records.stream().map(item -> {
            TicketDeliveryEntity ticketDeliveryEntity = new TicketDeliveryEntity();
            BeanUtil.copyProperties(item, ticketDeliveryEntity);
            return ticketDeliveryEntity;
        }).toList();
        return PageDTO.build(ticketDeliveryEntities, ticketDeliveryPOPage.getTotal(), pageSize, pageNum,
                ticketDeliveryPOPage.getPages());
    }

    /**
     * @param userId
     * @return
     */
    @Override
    public PageDTO<TicketDeliveryEntity> getTicketDeliveryByUserId(Long userId, Integer ticketType, Long pageNum, Long pageSize) {
        Page<TicketDeliveryPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<TicketDeliveryPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TicketDeliveryPO::getCustomerId, userId);
        queryWrapper.eq(TicketDeliveryPO::getTicketType, ticketType);
        queryWrapper.eq(TicketDeliveryPO::getDelFlag, 0);
        queryWrapper.orderByAsc(TicketDeliveryPO::getStatus);
        queryWrapper.orderByDesc(TicketDeliveryPO::getCreateTime);
        Page<TicketDeliveryPO> ticketDeliveryPOPage = ticketDeliveryMapper.selectPage(page, queryWrapper);
        if (ticketDeliveryPOPage.getTotal() == 0) {
            return PageDTO.emptyBuild(pageNum, pageSize);
        }
        List<TicketDeliveryPO> records = ticketDeliveryPOPage.getRecords();
        List<TicketDeliveryEntity> ticketDeliveryEntities = records.stream().map(item -> {
            TicketDeliveryEntity ticketDeliveryEntity = new TicketDeliveryEntity();
            BeanUtil.copyProperties(item, ticketDeliveryEntity);
            return ticketDeliveryEntity;
        }).toList();
        return PageDTO.build(ticketDeliveryEntities, ticketDeliveryPOPage.getTotal(), pageSize, pageNum,
                ticketDeliveryPOPage.getPages());
    }

    /**
     * 获取优惠券详情
     *
     * @param id
     * @return
     */
    @Override
    public TicketDeliveryEntity getTicketDeliveryById(Long customerId, Long id) {
        TicketDeliveryPO po = ticketDeliveryMapper.selectOne(new LambdaQueryWrapper<TicketDeliveryPO>()
                .eq(TicketDeliveryPO::getCustomerId, customerId)
                .eq(TicketDeliveryPO::getId, id));
//        TicketDeliveryPO po = ticketDeliveryMapper.selectById(id);
        if (po != null) {
            TicketDeliveryEntity ticketDeliveryEntity = new TicketDeliveryEntity();
            BeanUtil.copyProperties(po, ticketDeliveryEntity);
            return ticketDeliveryEntity;
        }
        return null;
    }

    /**
     * 删除优惠券发放
     *
     * @param id
     */
    @Override
    public void delTicketDelivery(Long coustomerId, Long id) {
        LambdaUpdateWrapper<TicketDeliveryPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(TicketDeliveryPO::getId, id);
        updateWrapper.eq(TicketDeliveryPO::getCustomerId, coustomerId);
        updateWrapper.set(TicketDeliveryPO::getDelFlag, 1);
        ticketDeliveryMapper.update(null, updateWrapper);
    }

    @Override
    public TicketDeliveryEntity getByTicketCode(String ticketCode) {
        LambdaQueryWrapper<TicketDeliveryPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TicketDeliveryPO::getTicketCode, ticketCode);
        queryWrapper.eq(TicketDeliveryPO::getDelFlag, NumberUtils.INTEGER_ZERO);
        TicketDeliveryPO ticketDeliveryPO = ticketDeliveryMapper.selectOne(queryWrapper);
        return BeanUtil.copyProperties(ticketDeliveryPO, TicketDeliveryEntity.class);
    }

    @Override
    public List<TicketDeliveryEntity> getByCenterNumber(String CenterNumber) {
        LambdaQueryWrapper<TicketDeliveryPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TicketDeliveryPO::getCenterTicketCouponNumber, CenterNumber);
        queryWrapper.eq(TicketDeliveryPO::getDelFlag, NumberUtils.INTEGER_ZERO);
        List<TicketDeliveryPO> ticketDeliveryList = ticketDeliveryMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(ticketDeliveryList, TicketDeliveryEntity.class);
    }

    @Override
    public boolean updateTicketStatusToUsed(Long ticketDeliveryId) {
        // 使用数据库原子操作更新状态，防止并发问题
        LambdaUpdateWrapper<TicketDeliveryPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(TicketDeliveryPO::getId, ticketDeliveryId);
        updateWrapper.eq(TicketDeliveryPO::getStatus, TicketStatusEnum.UNUSED.getStatus());
        updateWrapper.set(TicketDeliveryPO::getStatus, TicketStatusEnum.USED.getStatus());
        updateWrapper.set(TicketDeliveryPO::getModTime, new Date());
        int updated = ticketDeliveryMapper.update(null, updateWrapper);
        return updated > 0;
    }

    @Override
    public void batchSave(List<TicketDeliveryEntity> ticketDeliveries) {
        if (ticketDeliveries == null || ticketDeliveries.isEmpty()) {
            return;
        }

        List<TicketDeliveryPO> poList = ticketDeliveries.stream()
                .map(entity -> {
                    TicketDeliveryPO po = new TicketDeliveryPO();
                    BeanUtil.copyProperties(entity, po);
                    return po;
                })
                .collect(Collectors.toList());

        // 批量插入
        if (!poList.isEmpty()) {
            ticketDeliveryMapper.batchInsert(poList);
        }
    }

    @Override
    public TicketDeliveryEntity getBirthdayTicket(Long customId) {
        LocalDate date = LocalDate.now();
        LocalDateTime yearStart = LocalDateTime.of(
                date.getYear(),  // 当前年份
                1,  // 1月
                1,  // 1日
                0,  // 时
                0,  // 分
                0   // 秒
        );

        // 当前年的结束时间：当年12月31日 23:59:59
        LocalDateTime yearEnd = LocalDateTime.of(
                date.getYear(),  // 当前年份
                12,  // 12月
                31,  // 31日
                23,  // 时
                59,  // 分
                59   // 秒
        );
        List<TicketDeliveryPO> list = ticketDeliveryMapper.selectList(new LambdaQueryWrapper<TicketDeliveryPO>()
                .eq(TicketDeliveryPO::getCustomerId, customId)
                .eq(TicketDeliveryPO::getIsBirthdayTicket, 1)
                .ge(TicketDeliveryPO::getCreateTime, yearStart)
                .le(TicketDeliveryPO::getCreateTime, yearEnd));
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return BeanUtil.copyProperties(list.get(0), TicketDeliveryEntity.class);
    }

    @Override
    public int countCustomerTicketNum(Long customerId) {
        LambdaQueryWrapper<TicketDeliveryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TicketDeliveryPO::getCustomerId, customerId)
                .eq(TicketDeliveryPO::getStatus, TicketStatusEnum.UNUSED.getStatus())
                .eq(TicketDeliveryPO::getDelFlag, NumberUtils.INTEGER_ZERO);
        return Math.toIntExact(ticketDeliveryMapper.selectCount(queryWrapper));
    }
}
