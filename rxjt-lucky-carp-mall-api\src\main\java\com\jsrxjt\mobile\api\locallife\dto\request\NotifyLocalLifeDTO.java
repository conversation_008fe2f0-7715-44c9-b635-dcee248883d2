package com.jsrxjt.mobile.api.locallife.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2025/6/17 14:17
 */
@Data
@Schema(description = "回调本地生活请求参数")
public class NotifyLocalLifeDTO {

    @NotBlank(message = "交易单Id不能为空")
    private String netpayId;

    @NotBlank(message = "时间戳不能为空")
    private String requestTime;

    @NotBlank(message = "签名不能为空")
    private String requestRand;

    @NotBlank(message = "签名不能为空")
    private String sign;
}
