package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 支付结果通知
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "支付结果通知")
public class PollPickChannelOrderResponseDTO {

    @Schema(description = "客户id")
    private Long customerId;

    @Schema(description = "平台订单流水号")
    private String orderId;

    @Schema(description = "实际支付金额")
    private String paymentAmount;

    @Schema(description = "订单创建时间")
    private String orderCreateTime;

    @Schema(description = "订单过期时间")
    private String payExpireTimestamp;



}
