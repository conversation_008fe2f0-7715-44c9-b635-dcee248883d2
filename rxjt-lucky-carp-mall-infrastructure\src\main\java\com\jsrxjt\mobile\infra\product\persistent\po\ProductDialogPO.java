package com.jsrxjt.mobile.infra.product.persistent.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;


/**
 * 核销页弹框
 * <AUTHOR>
 * @date 2025/09/12
 */
@Data
@TableName("product_dialog")
public class ProductDialogPO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long productSpuId;
    /**
     * 弹框类型，1:提交订单弹框 2:核销页弹框
     */
    private Byte productType;

    private int dialogType;

    private String dialogContent;

    private Date createTime;

    private Long createId;

    private Byte delFlag;

    private Long modId;

    private Date modTime;
}
