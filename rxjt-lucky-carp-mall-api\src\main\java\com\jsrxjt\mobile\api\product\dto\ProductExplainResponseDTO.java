package com.jsrxjt.mobile.api.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 产品兑换须知/核销须知
 * @Author: ywt
 * @Date: 2025-04-16 15:30
 * @Version: 1.0
 */
@Data
public class ProductExplainResponseDTO {
    @Schema(description = "说明id")
    private Long explainId;

    @Schema(description = "说明标题")
    private String title;

    @Schema(description = "说明内容富文本")
    private String content;
}
