package com.jsrxjt.mobile.api.enums;

/**
 * 支付渠道枚举
 * 
 * <AUTHOR>
 * @since 2025/6/17
 */
public enum PaymentChannelEnum {
    WECHAT_PAY("WX", "微信支付"),
    ALIPAY("ALIPAY", "支付宝"),
    ONLINE_CARD("ONLINE_CARD", "线上纯卡支付"),
    ONLINE_AGGREGATE("ONLINE_AGG", "线上聚合支付"),
    OFFLINE_SCAN("OFFLINE_SCAN", "线下扫码支付");

    private final String code;
    private final String name;

    PaymentChannelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static PaymentChannelEnum getByCode(String code) {
        for (PaymentChannelEnum channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        throw new IllegalArgumentException("不支持的支付渠道: " + code);
    }
} 