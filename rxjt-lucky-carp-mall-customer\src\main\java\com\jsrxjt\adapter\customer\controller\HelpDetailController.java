package com.jsrxjt.adapter.customer.controller;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.help.Respone.HelpCatResponseDTO;
import com.jsrxjt.mobile.api.help.Respone.HelpDetailResponseDTO;
import com.jsrxjt.mobile.api.help.request.HelpInfoClockRequestDTO;
import com.jsrxjt.mobile.biz.help.HelpCatService;
import com.jsrxjt.mobile.biz.help.HelpDetailService;
import com.jsrxjt.mobile.domain.help.entity.HelpDetailEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/help")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "帮助中心接口", description = "帮助中心接口")
public class HelpDetailController {
    private final HelpCatService helpCatService;
    private final HelpDetailService helpDetailService;

    @GetMapping("/listByCatId")
    @Operation(summary = "根据分类获取帮助详情")
    public BaseResponse<List<HelpDetailResponseDTO>> helpDetailList(@RequestParam("catId") Long catId){
        return BaseResponse.succeed(helpDetailService.helpDetailList(catId));
    }

    @PostMapping("/clickNum")
    @Operation(summary = "更新帮助详情点击数")
    public BaseResponse<Void> updateClickNum(@RequestBody HelpInfoClockRequestDTO requestDTO){
        helpDetailService.updateClickNum(requestDTO.getHelpId());
        return BaseResponse.succeed();
    }

    @GetMapping("/catList")
    @Operation(summary = "获取帮助分类列表")
    public BaseResponse<List<HelpCatResponseDTO>> catList(){
        List<HelpCatResponseDTO> helpCatEntities = helpCatService.helpCatList();
        return BaseResponse.succeed(helpCatEntities);
    }

}
