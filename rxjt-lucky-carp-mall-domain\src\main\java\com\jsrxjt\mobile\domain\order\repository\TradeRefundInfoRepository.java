package com.jsrxjt.mobile.domain.order.repository;

import com.jsrxjt.mobile.domain.order.entity.TradeRefundInfoEntity;

import java.util.List;

/**
 * 交易退款信息仓储接口
 * <AUTHOR>
 * @since 2025/9/26
 */
public interface TradeRefundInfoRepository {

    /**
     * 根据外部退款单号查询退款信息列表
     * 
     * @param outRefundNo 外部退款单号（对应我们的refundNo）
     * @return 退款信息列表
     */
    List<TradeRefundInfoEntity> findByOutRefundNo(String outRefundNo);
}
