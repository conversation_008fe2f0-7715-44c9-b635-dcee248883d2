package com.jsrxjt.adapter.order.mq;

import com.alibaba.fastjson.JSON;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.order.types.OrderMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.MessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 取消订单消息监听器
 * <AUTHOR> <PERSON>
 * @since 2025/7/8
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class AutoCancelOrderMessageListener implements MessageListener {

    private final OrderCaseService orderCaseService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        String msgBody  = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();

        log.info("取消订单消费分组 received message {}",messageView);
        OrderMessage event = JSON.parseObject(msgBody, OrderMessage.class);
        try {
            orderCaseService.cancelOrderBySystem(event.getOrderNo());
        } catch (BizException e) {
            if (log.isDebugEnabled()) {
                log.debug("订单状态不需要取消 : {}",e.getMsg());
            }
        } catch (Exception e) {
            log.warn("取消订单异常",e);
        }

        log.info("取消订单消费分组 消费完成 message {}",messageView);
        return ConsumeResult.SUCCESS;
    }
}
