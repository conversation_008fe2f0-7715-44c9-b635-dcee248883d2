package com.jsrxjt.mobile.domain.pickplatform.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 卡信息DTO
 */
@Data
public class PickCardItemDetailResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 核销编号
     */
    private String checkNo;
    
    /**
     * 卡号
     */
    private String cardNo;
    
    /**
     * 余额(分)
     */
    private String balance;
    
    /**
     * 有效期
     */
    private String validTime;
    
    /**
     * 面值
     */
    private String amount;
    
    /**
     * 卡状态 EXPIRED 已过期 USED 已使用 USABLE 能使用 INACTIVE 未激活
     */
    private String cardStatus;

}
