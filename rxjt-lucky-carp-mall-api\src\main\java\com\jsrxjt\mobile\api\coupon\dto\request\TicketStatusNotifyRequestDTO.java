package com.jsrxjt.mobile.api.coupon.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 优惠券状态变更通知
 * @Author: ywt
 * @Date: 2025-10-15 15:08
 * @Version: 1.0
 */
@Data
public class TicketStatusNotifyRequestDTO {
    @NotBlank(message = "appid为空错误")
    private String appid;
    @NotBlank(message = "参数为空错误")
    private String nounce;
    @NotBlank(message = "时间戳不能为空")
    private String timestamp;
    @NotBlank(message = "签名不能为空")
    private String signature;
    @NotNull(message = "卡券id不能为空")
    private Integer couponId;
    @NotNull(message = "优惠券状态不能为空")
    @Schema(description = "优惠券状态：0暂停使用 1正常使用 2删除卡券")
    private Integer status;
}
