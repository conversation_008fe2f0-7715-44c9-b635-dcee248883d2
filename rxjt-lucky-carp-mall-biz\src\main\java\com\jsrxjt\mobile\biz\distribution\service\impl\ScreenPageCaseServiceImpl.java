package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.api.distribution.dto.request.ScreenPageClickRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.ScreenPageRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.ScreenPageResponseDTO;
import com.jsrxjt.mobile.biz.distribution.service.ScreenPageCaseService;
import com.jsrxjt.mobile.domain.screenpage.entity.ScreenPageEntity;
import com.jsrxjt.mobile.domain.screenpage.repository.ScreenPageRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 启动页服务
 * @Author: ywt
 * @Date: 2025-06-09 15:12
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ScreenPageCaseServiceImpl implements ScreenPageCaseService {
    private final ScreenPageRepository screenPageRepository;

    @Override
    public List<ScreenPageResponseDTO> getScreenList(ScreenPageRequestDTO requestDTO) {
        List<ScreenPageEntity> pageEntityList = screenPageRepository.getScreenListByRegionId(requestDTO.getRegionId());
        return BeanUtil.copyToList(pageEntityList, ScreenPageResponseDTO.class);
    }

    @Override
    public boolean clickScreenPage(ScreenPageClickRequestDTO requestDTO) {
        return screenPageRepository.clickScreenPage(requestDTO.getScreenId());
    }
}
