<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketProductMapper">

    <select id="selectTickets" resultType="com.jsrxjt.mobile.infra.ticket.persistent.po.TicketProductPO">
        select r.id,
               r.ticket_activity_id,
               r.product_sku_id,
               r.product_type,
               r.ticket_id,
               r.create_time,
               r.create_id,
               r.mod_id,
               r.mod_time,
               r.del_flag,
               r.del_id,
               r.del_time,
               r.create_name,
               r.mod_name
        from ticket_product_relation r
                 left join ticket_activity a on a.id = r.ticket_activity_id
        where r.del_flag = 0
          and r.product_sku_id = #{productSkUID}
          and r.product_type = #{productType}
          and a.del_flag = 0
          and a.status = 1
    </select>
</mapper>