package com.jsrxjt.mobile.domain.order.service;

import com.jsrxjt.mobile.domain.order.entity.AfterSaleApplyRequest;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;

/**
 * 售后领域服务
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface AfterSaleService {

    /**
     * 申请售后
     * 
     * @param order 订单信息
     * @param request 申请售后请求
     * @return 售后实体
     */
    AfterSaleEntity applyAfterSale(OrderInfoEntity order, AfterSaleApplyRequest request);

    /**
     * 同步售后状态到订单
     *
     * @param afterSale 售后订单
     * @param order 订单信息
     */
    void syncOrderAfterSaleStatus(AfterSaleEntity afterSale, OrderInfoEntity order);
}