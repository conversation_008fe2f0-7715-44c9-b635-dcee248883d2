package com.jsrxjt.mobile.api.product.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品联想词响应DTO
 * 
 * <AUTHOR>
 * @since 2025/5/9
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "产品联想词响应")
public class ProductSuggestionResponseDTO {
    
    /**
     * 联想词内容
     */
    @Schema(description = "联想词原始内容")
    private String keyword;
    
    /**
     * 高亮后的联想词内容
     */
    @Schema(description = "高亮后的联想词内容，包含HTML标签")
    private String highlightKeyword;
    
    /**
     * 产品类型
     * 1-卡券 2-套餐 3-应用 4-支付宝红包 5-苏西话费
     */
    @Schema(description = "产品类型: 1-卡券 2-套餐 3-应用 4-支付宝红包 5-话费充值")
    private Integer productType;

} 