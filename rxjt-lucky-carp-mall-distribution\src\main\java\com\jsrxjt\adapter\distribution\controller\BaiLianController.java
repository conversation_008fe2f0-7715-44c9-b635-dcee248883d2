package com.jsrxjt.adapter.distribution.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.bailian.dto.request.BaiLianBrandRequestDTO;
import com.jsrxjt.mobile.api.bailian.dto.request.BaiLianQRCodeRequestDTO;
import com.jsrxjt.mobile.api.bailian.dto.request.BaiLianShopRequestDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianBrandResponseDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianCategoryResponseDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianQRCodeResponseDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianShopResponseDTO;
import com.jsrxjt.mobile.biz.distribution.service.BaiLianCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17 11:12
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/baiLian")
@Tag(name = "百联", description = "百联提货码页面接口")
public class BaiLianController {

    private final BaiLianCaseService baiLianCaseService;

    @PostMapping("/getPaymentQRCode")
    @Operation(summary = "获取付款二维码")
    @VerifySign(hasToken = true)
    public BaseResponse<BaiLianQRCodeResponseDTO> getPaymentQRCode(@RequestBody @Valid BaiLianQRCodeRequestDTO requestDTO) {
        return baiLianCaseService.getPaymentQRCode(requestDTO);
    }

    @PostMapping("/getCategoryList")
    @Operation(summary = "获取分类列表")
    @VerifySign(hasToken = true)
    public BaseResponse<List<BaiLianCategoryResponseDTO>> getCategoryList() {
        return baiLianCaseService.getCategoryList();
    }

    @PostMapping("/getBrandList")
    @Operation(summary = "获取品牌列表")
    @VerifySign(hasToken = true)
    public BaseResponse<List<BaiLianBrandResponseDTO>> getBrandList(@RequestBody @Valid BaiLianBrandRequestDTO requestDTO) {
        return baiLianCaseService.getBrandList(requestDTO.getCategoryId());
    }

    @PostMapping("/getShopList")
    @Operation(summary = "获取门店列表")
    @VerifySign(hasToken = true)
    public BaseResponse<List<BaiLianShopResponseDTO>> getShopList(@RequestBody @Valid BaiLianShopRequestDTO requestDTO) {
        return baiLianCaseService.getShopList(requestDTO);
    }
}
