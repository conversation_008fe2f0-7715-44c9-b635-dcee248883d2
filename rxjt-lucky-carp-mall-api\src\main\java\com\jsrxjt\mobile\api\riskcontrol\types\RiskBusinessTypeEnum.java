package com.jsrxjt.mobile.api.riskcontrol.types;

/**
 * @Description: 风控的业务类型
 * @Author: ywt
 * @Date: 2025-09-25 16:33
 * @Version: 1.0
 */
public enum RiskBusinessTypeEnum {
    LOGIN_TYPE(0, "Login", "登录"),
    REGISTER_TYPE(1, "Register", "注册"),
    PLACEORDER_TYPE(2, "PlaceOrder", "下单"),
    BIND_CARD_TYPE(3, "BindCard", "绑卡");

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    private final Integer status;
    private final String name;
    private final String desc;

    RiskBusinessTypeEnum(Integer status, String name, String desc) {
        this.status = status;
        this.name = name;
        this.desc = desc;
    }
}
