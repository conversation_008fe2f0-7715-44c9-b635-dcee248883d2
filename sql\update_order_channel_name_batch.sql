-- 分批更新t_order表中的channel_name字段
-- 条件：product_type = 2 且 channel_name为null时，根据channel_id去渠道表查询并更新渠道名称
-- 适用于大数据量场景，分批处理避免长时间锁表

-- ========================================
-- 准备工作：创建临时表和存储过程
-- ========================================

-- 创建临时表存储需要更新的订单ID
DROP TEMPORARY TABLE IF EXISTS temp_update_orders;
CREATE TEMPORARY TABLE temp_update_orders (
    order_id BIGINT PRIMARY KEY,
    channel_id BIGINT,
    new_channel_name VARCHAR(255),
    INDEX idx_channel_id (channel_id)
);

-- 插入需要更新的订单数据
INSERT INTO temp_update_orders (order_id, channel_id, new_channel_name)
SELECT 
    o.id,
    o.channel_id,
    pc.channel_name
FROM t_order o
INNER JOIN product_channel pc ON o.channel_id = pc.id AND pc.del_flag = 0
WHERE o.product_type = 2 
  AND o.channel_name IS NULL 
  AND o.channel_id IS NOT NULL;

-- 查看临时表统计信息
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT channel_id) as distinct_channels,
    MIN(order_id) as min_order_id,
    MAX(order_id) as max_order_id
FROM temp_update_orders;

-- ========================================
-- 创建分批更新存储过程
-- ========================================

DELIMITER $$

DROP PROCEDURE IF EXISTS UpdateOrderChannelNameBatch$$

CREATE PROCEDURE UpdateOrderChannelNameBatch()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE batch_size INT DEFAULT 1000;
    DECLARE current_batch INT DEFAULT 0;
    DECLARE total_updated INT DEFAULT 0;
    DECLARE batch_updated INT DEFAULT 0;
    DECLARE start_time DATETIME DEFAULT NOW();
    
    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    -- 获取总记录数
    SELECT COUNT(*) INTO total_updated FROM temp_update_orders;
    
    -- 输出开始信息
    SELECT CONCAT('开始分批更新，总记录数: ', total_updated, '，批次大小: ', batch_size) as message;
    
    -- 分批更新循环
    batch_loop: LOOP
        SET current_batch = current_batch + 1;
        
        -- 开始事务
        START TRANSACTION;
        
        -- 执行批量更新
        UPDATE t_order o
        INNER JOIN temp_update_orders tuo ON o.id = tuo.order_id
        SET 
            o.channel_name = tuo.new_channel_name,
            o.mod_time = NOW()
        WHERE o.id IN (
            SELECT order_id 
            FROM temp_update_orders 
            ORDER BY order_id 
            LIMIT batch_size OFFSET (current_batch - 1) * batch_size
        );
        
        -- 获取本批次更新的记录数
        SET batch_updated = ROW_COUNT();
        
        -- 提交事务
        COMMIT;
        
        -- 输出进度信息
        SELECT CONCAT(
            '批次 ', current_batch, ' 完成，',
            '本批次更新: ', batch_updated, ' 条，',
            '累计更新: ', (current_batch - 1) * batch_size + batch_updated, ' 条，',
            '进度: ', ROUND(((current_batch - 1) * batch_size + batch_updated) * 100.0 / total_updated, 2), '%'
        ) as progress;
        
        -- 如果本批次更新记录数小于批次大小，说明已经完成
        IF batch_updated < batch_size THEN
            LEAVE batch_loop;
        END IF;
        
        -- 批次间休息0.1秒，减少系统负载
        SELECT SLEEP(0.1);
        
    END LOOP;
    
    -- 输出完成信息
    SELECT CONCAT(
        '分批更新完成！',
        '总批次: ', current_batch, '，',
        '总耗时: ', TIMESTAMPDIFF(SECOND, start_time, NOW()), ' 秒'
    ) as completion_message;
    
END$$

DELIMITER ;

-- ========================================
-- 执行分批更新
-- ========================================

-- 调用存储过程执行分批更新
CALL UpdateOrderChannelNameBatch();

-- ========================================
-- 验证更新结果
-- ========================================

-- 1. 统计更新结果
SELECT 
    'product_type=2订单统计' as description,
    COUNT(*) as total_orders,
    SUM(CASE WHEN channel_name IS NOT NULL THEN 1 ELSE 0 END) as has_channel_name,
    SUM(CASE WHEN channel_name IS NULL THEN 1 ELSE 0 END) as no_channel_name,
    ROUND(SUM(CASE WHEN channel_name IS NOT NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as completion_rate
FROM t_order 
WHERE product_type = 2;

-- 2. 按渠道统计更新结果
SELECT 
    o.channel_id,
    pc.channel_name,
    COUNT(*) as order_count,
    SUM(CASE WHEN o.channel_name IS NOT NULL THEN 1 ELSE 0 END) as updated_count,
    SUM(CASE WHEN o.channel_name IS NULL THEN 1 ELSE 0 END) as not_updated_count
FROM t_order o
LEFT JOIN product_channel pc ON o.channel_id = pc.id AND pc.del_flag = 0
WHERE o.product_type = 2
GROUP BY o.channel_id, pc.channel_name
ORDER BY order_count DESC;

-- 3. 检查仍然为空的记录
SELECT 
    o.id,
    o.order_no,
    o.channel_id,
    o.channel_name,
    o.create_time,
    CASE 
        WHEN pc.id IS NULL THEN '渠道不存在'
        WHEN pc.del_flag = 1 THEN '渠道已删除'
        WHEN pc.status = 0 THEN '渠道已禁用'
        ELSE '其他原因'
    END as reason
FROM t_order o
LEFT JOIN product_channel pc ON o.channel_id = pc.id
WHERE o.product_type = 2 
  AND o.channel_name IS NULL 
  AND o.channel_id IS NOT NULL
ORDER BY o.create_time DESC
LIMIT 20;

-- 4. 验证最近更新的记录
SELECT 
    o.id,
    o.order_no,
    o.channel_id,
    o.channel_name,
    o.mod_time,
    o.create_time
FROM t_order o
WHERE o.product_type = 2 
  AND o.channel_name IS NOT NULL
  AND o.mod_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY o.mod_time DESC
LIMIT 10;

-- ========================================
-- 清理工作
-- ========================================

-- 删除临时表
DROP TEMPORARY TABLE IF EXISTS temp_update_orders;

-- 删除存储过程
DROP PROCEDURE IF EXISTS UpdateOrderChannelNameBatch;

-- ========================================
-- 性能监控查询（可选）
-- ========================================

-- 查看更新操作的性能影响
-- SHOW PROCESSLIST;

-- 查看表的锁定情况
-- SHOW OPEN TABLES WHERE In_use > 0;

-- 查看最近的慢查询（如果启用了慢查询日志）
-- SELECT * FROM mysql.slow_log WHERE start_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR) ORDER BY start_time DESC LIMIT 10;
