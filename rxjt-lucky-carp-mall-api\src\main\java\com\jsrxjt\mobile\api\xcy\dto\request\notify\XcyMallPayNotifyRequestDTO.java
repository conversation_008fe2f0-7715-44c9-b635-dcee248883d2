package com.jsrxjt.mobile.api.xcy.dto.request.notify;

import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallBaseRequest;
import lombok.Data;

/**
 * Created by jeffery.yang on 2025/10/23 9:53
 *
 * @description: 祥采云支付通知入参
 * @author: jeffery.yang
 * @date: 2025/10/23 9:53
 * @version: 1.0
 */
@Data
public class XcyMallPayNotifyRequestDTO extends XcyMallBaseRequest {



	/**
	 * 祥采云商城订单号
	 */
	private String tradeNo;


	/**
	 * 福鲤圈订单号
	 */
	private String outTradeNo;


	/**
	 * 支付金额，单位元，精确到小数点后2位。例如100.00
	 */
	private String totalAmount;
	/**
	 * 支付完成时间，当支付成功时返回，固定格式yyyyMMddHHmmss
	 */
	private String payTime;
}
