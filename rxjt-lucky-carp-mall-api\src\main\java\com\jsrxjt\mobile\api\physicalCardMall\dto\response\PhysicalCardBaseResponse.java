package com.jsrxjt.mobile.api.physicalCardMall.dto.response;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-10-24 11:57
 * @Version: 1.0
 */
@Data
public class PhysicalCardBaseResponse {
    private String nounce;

    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

    @NotBlank(message = "签名不能为空")
    private String signature;
}
