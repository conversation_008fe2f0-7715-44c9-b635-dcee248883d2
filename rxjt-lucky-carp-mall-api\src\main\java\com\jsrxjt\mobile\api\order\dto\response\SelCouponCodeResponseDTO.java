package com.jsrxjt.mobile.api.order.dto.response;

import com.jsrxjt.mobile.api.common.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "自发券code返回参数")
public class SelCouponCodeResponseDTO {

    @Schema(description = "卡包id")
    private String code;

    @Schema(description = "有效时间")
    private String validTime;

    @Schema(description = "卡余额 分")
    private String balance;

    @Schema(description = "购券明细")
    private List<SelCouponCodeDetailResponseDTO> detailList;

    @Schema(description = "历史明细")
    private List<SelCouponCodeDetailResponseDTO> historyList;

}
