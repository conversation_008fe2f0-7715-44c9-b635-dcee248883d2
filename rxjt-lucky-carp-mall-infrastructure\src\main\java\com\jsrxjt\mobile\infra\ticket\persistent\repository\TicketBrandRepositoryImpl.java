package com.jsrxjt.mobile.infra.ticket.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.ticket.entity.TicketBrandEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketBrandRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketBrandMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketBrandPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description: 优惠券品牌实现类
 * @Author: ywt
 * @Date: 2025-08-20 17:51
 * @Version: 1.0
 */
@Repository
@RequiredArgsConstructor
public class TicketBrandRepositoryImpl implements TicketBrandRepository {
    private final TicketBrandMapper ticketBrandMapper;

    @Override
    public List<TicketBrandEntity> getTicketBrandsByIds(List<Long> brandIds) {
        List<TicketBrandPO> list = ticketBrandMapper.selectList(new LambdaQueryWrapper<TicketBrandPO>()
                .in(TicketBrandPO::getId, brandIds));
        return BeanUtil.copyToList(list, TicketBrandEntity.class);
    }

    @Override
    public TicketBrandEntity getTicketBrandById(Long brandId) {
        TicketBrandPO ticketBrandPO = ticketBrandMapper.selectOne(new LambdaQueryWrapper<TicketBrandPO>()
                .eq(TicketBrandPO::getId, brandId));
        return BeanUtil.copyProperties(ticketBrandPO, TicketBrandEntity.class);
    }
}
