package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 启动页点击请求
 * @Author: ywt
 * @Date: 2025-06-10 14:46
 * @Version: 1.0
 */
@Data
public class ScreenPageClickRequestDTO {
    @Schema(description = "开屏页id")
    @NotNull(message = "开屏页id为空错误")
    private Long screenId;
}
