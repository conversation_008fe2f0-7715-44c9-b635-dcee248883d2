package com.jsrxjt.mobile.api.scanPay.types;


/**
 * 扫码付pos订单状态类型
 * <AUTHOR>
 * @date 2025/11/07
 */
public enum PosV2OrderStatusTypeEnum {
    /**
     * 交易创建
     */
    WAIT("WAIT"),

    /**
     * 支付中
     */
    PAYING("PAYING"),

    /**
     * 交易成功
     */
    SUCCESS("SUCCESS"),

    /**
     * 交易失败
     */
    FAIL("FAIL"),

    /**
     * 交易关闭
     */
    CLOSED("CLOSED"),

    /**
     * 交易完成
     */
    FULL("FULL");

    private String type;

    PosV2OrderStatusTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static PosV2OrderStatusTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (PosV2OrderStatusTypeEnum posV2PushType : values()) {
            if (posV2PushType.getType().equals(type)) {
                return posV2PushType;
            }
        }
        return null;
    }
}
