package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionRedirectBaseDTO;

/**
 * <AUTHOR>
 * @Date 2025-10-22
 */
public interface DistributionService {

    /**
     * 获取应用的跳转链接
     * @param requestDTO 获取跳转链接请求参数
     * @return 应用跳转链接
     */
    BaseResponse<String> getRedirectUrl(DistributionRedirectBaseDTO requestDTO);


    /**
     * 判断是否是祥采云应用
     *
     * @param distributionType
     * @return
     */
    Boolean isXcyMallApp(String distributionType);
}
