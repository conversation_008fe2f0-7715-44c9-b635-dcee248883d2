package com.jsrxjt.mobile.infra.customer.persistent.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.customer.entity.CustomerPhoneChangeRecordEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerPhoneChangeRecordRepository;
import com.jsrxjt.mobile.infra.customer.persistent.mapper.CustomerPhoneChangeRecordMapper;
import com.jsrxjt.mobile.infra.customer.persistent.po.CustomerPhoneChangeRecordPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class CustomerPhoneChangeRecordImpl implements CustomerPhoneChangeRecordRepository {

    private final CustomerPhoneChangeRecordMapper customerPhoneChangeRecordMapper;


    @Override
    public long getPhoneChangeRecordCount(Long customerId) {
        LambdaQueryWrapper<CustomerPhoneChangeRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerPhoneChangeRecordPO::getCustomerId,customerId);
        queryWrapper.eq(CustomerPhoneChangeRecordPO::getChangeMethod, NumberUtils.INTEGER_ONE);
        return customerPhoneChangeRecordMapper.selectCount(queryWrapper);
    }

    @Override
    public int savePhoneChangeRecord(CustomerPhoneChangeRecordEntity entity) {
        CustomerPhoneChangeRecordPO po = new CustomerPhoneChangeRecordPO();
        BeanUtils.copyProperties(entity,po);
        return customerPhoneChangeRecordMapper.insert(po);
    }
}
