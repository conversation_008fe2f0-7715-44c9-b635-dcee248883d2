package com.jsrxjt.mobile.infra.customer.persistent.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户历史充值账号持久化对象
 * 
 * <AUTHOR>
 * @since 2025/8/5
 */
@Data
@TableName("recharge_account_history")
public class RechargeAccountHistoryPO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 产品SPU ID
     */
    private Long productSpuId;
    
    /**
     * 产品类型
     */
    private Integer productType;
    
    /**
     * 充值类型 0-其他 1-手机号 2-QQ号
     */
    private Integer accountType;
    
    /**
     * 账户
     */
    private String account;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modTime;
    
    /**
     * 删除标志 0--正常 1--删除
     */
    @TableLogic
    private Integer delFlag;
}