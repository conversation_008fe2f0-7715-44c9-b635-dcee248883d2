package com.jsrxjt.mobile.domain.order.service.strategy.aftersale.impl;

import com.jsrxjt.mobile.api.order.dto.response.AfterSaleDetailResponseDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.service.strategy.aftersale.AfterSaleProgressBuilder;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AfterSaleCompletedBuilder implements AfterSaleProgressBuilder {
    @Override
    public boolean supports(Integer status) {
        return AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode().equals(status);
    }

    @Override
    public List<AfterSaleDetailResponseDTO.ProgressDetailDTO> build(AfterSaleEntity afterSale, OrderInfoEntity order) {

        return List.of(
                new AfterSaleDetailResponseDTO.ProgressDetailDTO("提交申请",
                        "您的申请已提交，请耐心等待客服审核", afterSale.getApplyTime()),
                new AfterSaleDetailResponseDTO.ProgressDetailDTO("客服审核",
                        "客服审核通过，请等待退款", afterSale.getAuditTime()),
                new AfterSaleDetailResponseDTO.ProgressDetailDTO("平台退款",
                        "退款处理中", afterSale.getAuditTime()),
                new AfterSaleDetailResponseDTO.ProgressDetailDTO("退款成功",
                        "退款已完成，请查看到款信息", afterSale.getRefundSuccessTime())
        );

    }
}
