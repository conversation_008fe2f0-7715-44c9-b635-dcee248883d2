package com.jsrxjt.mobile.domain.payment.gateway;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.response.ScanPayOrderStatusDTO;
import com.jsrxjt.mobile.domain.payment.gateway.request.OfflinePayRequest;

/**
 * pos-v2支付gateway接口
 * <AUTHOR>
 * @date 2025/10/27
 */
public interface PosV2PaymentGateway {

    BaseResponse pay(OfflinePayRequest request);

    BaseResponse<ScanPayOrderStatusDTO> orderStatus(Long vipId, String tradeNo);
}
