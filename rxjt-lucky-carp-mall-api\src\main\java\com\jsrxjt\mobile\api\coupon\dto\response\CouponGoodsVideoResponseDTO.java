package com.jsrxjt.mobile.api.coupon.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 视频直充卡券响应
 * @Author: ywt
 * @Date: 2025-05-12 17:21
 * @Version: 1.0
 */
@Data
@Schema(description = "视频直充卡券响应")
public class CouponGoodsVideoResponseDTO {
    @Schema(description = "视频直充卡券id")
    private Long couponSpuId;
    @Schema(description = "卡券名称")
    private String couponSpuName;
    @Schema(description = "logo图片url")
    private String logoUrl;
    @Schema(description = "品牌名")
    private String brandName;
    @Schema(description = "是否选中， 0--未选中 1--选中")
    private Integer isSelected;
    @Schema(description = "充值类型列表，列表已按accountType大小倒序排序，仅isSelected为1有效")
    private List<RechargeType> accountTypeList;

    @Data
    public static class RechargeType {
        @Schema(description = "充值类型,0其他 1手机号 2QQ号")
        private Integer accountType;
        @Schema(description = "是否选中， 0--未选中 1--选中")
        private Integer isSelected;
        @Schema(description = "SKU信息，仅isSelected为1有效")
        private CouponSkusResponseDTO skuInfo;
    }
}
