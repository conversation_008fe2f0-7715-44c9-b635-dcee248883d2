package com.jsrxjt.mobile.api.distribution.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 扫码提货创建订单返回参数
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "外部调用订单退款状态接口")
@AllArgsConstructor
@NoArgsConstructor
public class PickOrderRefundStatusResponseDTO {

    @Schema(description = "平台订单流水好")
    @JSONField(name = "order_no")
    private String order_no;

    @Schema(description = "平台交易流水号")
    @JSONField(name = "trade_no")
    private String trade_no;

    @Schema(description = "外部退款单号")
    @JSONField(name = "out_refund_sn")
    private String out_refund_sn;

    @Schema(description = "状态 11退款中 10退款成功 12退款失败")
    @JSONField(name = "trade_status")
    private String trade_status;

    @Schema(description = "交易时间 非成功状态统一留空")
    @JSONField(name = "trade_time")
    private String trade_time;
}
