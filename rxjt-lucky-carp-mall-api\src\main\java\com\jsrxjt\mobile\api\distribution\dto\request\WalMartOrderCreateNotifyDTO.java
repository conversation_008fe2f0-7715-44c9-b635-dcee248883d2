package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025-10-31
 */
@Data
public class WalMartOrderCreateNotifyDTO extends DistributionNotifyCommonDTO {
    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "分销业务中心订单号")
    private String orderNumber;

    @Schema(description = "订单金额，单位元，两位小数")
    private String payMoney;

    @Schema(description = "创建时间，格式yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @Schema(description = "格式yyyy-MM-dd HH:mm:ss 超过此时间将取消订单，不可支付")
    private String expireTime;

    @Schema(description = "支付结果页面(支付结束后的跳转页面)")
    private String resultPageUrl;
}
