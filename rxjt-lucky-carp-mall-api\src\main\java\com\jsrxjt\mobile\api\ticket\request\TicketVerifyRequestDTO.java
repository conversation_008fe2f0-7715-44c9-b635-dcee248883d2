package com.jsrxjt.mobile.api.ticket.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 券核销请求参数
 * <AUTHOR>
 * @Date 2025/9/13
 */
@Data
public class TicketVerifyRequestDTO {

    @Schema(description = "门店编号")
    @NotBlank(message = "门店编号不能为空")
    private String mdId;

    @Schema(description = "券码")
    @NotBlank(message = "券码不能为空")
    private String no;

}
