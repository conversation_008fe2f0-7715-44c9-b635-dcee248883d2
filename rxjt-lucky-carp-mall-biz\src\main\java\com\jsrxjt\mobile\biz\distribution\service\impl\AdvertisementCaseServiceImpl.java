package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.contentcenter.types.ContentcenterTypeEnum;
import com.jsrxjt.mobile.api.distribution.dto.request.AdvertisementListRequestDto;
import com.jsrxjt.mobile.api.distribution.dto.request.AdvertisementRequestDto;
import com.jsrxjt.mobile.biz.distribution.service.AdvertisementCaseService;
import com.jsrxjt.mobile.domain.advertisement.entity.AdvertisementEntity;
import com.jsrxjt.mobile.domain.advertisement.repository.AdvertisementRepository;
import com.jsrxjt.mobile.domain.advertisement.service.AdvertisementService;
import com.jsrxjt.mobile.domain.contentcenter.service.ContentRegionService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 广告服务
 * @Author: ywt
 * @Date: 2025-06-10 16:01
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class AdvertisementCaseServiceImpl implements AdvertisementCaseService {
    private final AdvertisementRepository advertisementRepository;
    private final AdvertisementService advertisementService;
    private final ContentRegionService contentRegionService;

    @Override
    public boolean clickAdvertisement(AdvertisementRequestDto requestDTO) {
        return advertisementRepository.clickAdvertisement(requestDTO.getAdvId());
    }

    @Override
    public List<AdvertisementInfoDTO> getAdvertisementList(AdvertisementListRequestDto requestDTO) {
        if (requestDTO.getPageType() != 3) {
            throw new BizException("参数错误");
        }
        List<AdvertisementInfoDTO> advertisementInfoDTOList = new ArrayList<>();
        List<AdvertisementEntity> advertisementEntityList = advertisementService.getAdvertisementList(requestDTO.getPageType(), null);
        if (CollectionUtil.isNotEmpty(advertisementEntityList)) {
            advertisementEntityList.forEach(item -> {
                if (item.getIsNationwide() == 1
                        || contentRegionService.isOnlineInRegion(item.getId(), ContentcenterTypeEnum.CONTENT_ADV.getCode(), requestDTO.getRegionId())) {
                    AdvertisementInfoDTO advertisementInfoDTO = new AdvertisementInfoDTO();
                    BeanUtils.copyProperties(item, advertisementInfoDTO);
                    advertisementInfoDTOList.add(advertisementInfoDTO);
                }
            });
        }
        return advertisementInfoDTOList;
    }
}
