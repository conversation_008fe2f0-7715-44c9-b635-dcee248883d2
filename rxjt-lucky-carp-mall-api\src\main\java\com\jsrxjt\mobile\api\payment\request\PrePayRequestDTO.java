package com.jsrxjt.mobile.api.payment.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 预支付请求DTO
 * <AUTHOR>
 * @since 2025/8/8
 */
@Data
@Schema(description = "预支付请求参数")
public class PrePayRequestDTO {
    
    /**
     * 业务订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Schema(description = "业务订单号",requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;
    
    /**
     * 来源标识
     */
    @NotBlank(message = "来源标识不能为空")
    @Schema(description = "来源标识 APP_ANDROID,APP_IOS,WX_MINI",requiredMode = Schema.RequiredMode.REQUIRED)
    private String source;
}