package com.jsrxjt.mobile.api.product.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 分类详情请求DTO
 * 
 * <AUTHOR>
 * @since 2025/5/12
 */
@Data
@Schema(description = "分类详情请求参数")
public class CategoryDetailRequestDTO extends BaseParam {

    @NotNull(message = "一级分类ID不能为空")
    @Schema(description = "一级分类ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long parentCategoryId;

    @Schema(description = "区域ID",requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "区域ID不能为空")
    private Integer regionId;
}