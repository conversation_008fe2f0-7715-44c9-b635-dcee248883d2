package com.jsrxjt.mobile.api.order.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 删除订单请求DTO
 * 
 * <AUTHOR>
 * @since 2025/1/20
 */
@Data
@Schema(description = "删除订单请求")
public class DeleteOrderRequestDTO {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Schema(description = "订单号")
    private String orderNo;
}