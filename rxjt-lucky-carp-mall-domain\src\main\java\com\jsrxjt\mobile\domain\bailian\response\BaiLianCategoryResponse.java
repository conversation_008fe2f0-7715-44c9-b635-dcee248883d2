package com.jsrxjt.mobile.domain.bailian.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 百联分类信息
 * <AUTHOR>
 * @Date 2025/10/28
 **/
@Data
@Schema(description = "百联分类信息")
public class BaiLianCategoryResponse {

    @Schema(description = "百联分类id")
    @JSONField(name = "category_id")
    private String categoryId;

    @Schema(description = "百联分类名称")
    @JSONField(name = "category_name")
    private String categoryName;
}
