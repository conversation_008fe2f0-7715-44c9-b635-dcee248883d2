package com.jsrxjt.mobile.domain.airRecharge.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 空中充值实体类
 * @Author: ywt
 * @Date: 2025-08-26 15:17
 * @Version: 1.0
 */
@Data
public class AirRechargeEntity {
    private Integer id;
    private String mobile;
    private BigDecimal truePrice;//真实已经成功充值的金额
    private String cardNo;//充值卡号
    private Integer isMsg;//0 未弹出通知窗口 1 已弹出通知窗口
    private Integer rechargeType;//1 黑金主卡 2 白金主卡 3 提货凭证
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 是否需要签名 1:需要 2：不需要
     */
    private Integer needSign;

    /**
     * 是否已经签名 1：是 2：否
     */
    private Integer isWindows;

    /**
     * 签名用  公司ID
     */
    private Integer companyId;
    /**
     * 创建时间
     */
    private Integer createTime;
}
