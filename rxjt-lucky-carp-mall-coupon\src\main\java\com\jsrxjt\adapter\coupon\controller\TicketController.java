package com.jsrxjt.adapter.coupon.controller;

import com.alibaba.fastjson2.JSON;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.coupon.dto.request.TicketConsumeStatusNotifyRequestDTO;
import com.jsrxjt.mobile.api.coupon.dto.request.TicketStatusNotifyRequestDTO;
import com.jsrxjt.mobile.biz.ticket.TicketCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description: 优惠券控制器
 * @Author: ywt
 * @Date: 2025-10-15 14:33
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/ticket")
@Tag(name = "优惠券接口", description = "优惠券接口")
public class TicketController {
    private final TicketCaseService ticketCaseService;

    @PostMapping("/callback")
    @Operation(summary = "优惠券状态变更通知接口")
    public BaseResponse callback(@RequestBody @Valid TicketStatusNotifyRequestDTO requestDTO) {
        log.info("营销中台-优惠券状态回调：" + JSON.toJSONString(requestDTO));
        return ticketCaseService.callback(requestDTO);
    }

    @PostMapping("/consumeCallback")
    @Operation(summary = "优惠券核销通知接口")
    public BaseResponse consumeCallback(@RequestBody @Valid TicketConsumeStatusNotifyRequestDTO requestDTO) {
        log.info("营销中台-优惠券核销状态回调：" + JSON.toJSONString(requestDTO));
        return ticketCaseService.consumeCallback(requestDTO);
    }
}
