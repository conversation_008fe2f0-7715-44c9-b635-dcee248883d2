package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
public class CustomerEditRequest extends BaseParam {

    @Schema(description = "用户ID")
    @NotNull(message = "用户Id不能为空")
    private Long customerId;

    @Schema(description = "编辑类型 1头像 2昵称 3性别 4更换手机号码 5生日")
    @NotNull(message = "编辑类型不能为空")
    private Integer editType;

    @Schema(description = "用户名")
    @Size(max = 16, message = "用户名长度不能超过16")
    private String userName;

    @Schema(description = "用户头像")
    private String userUrl;

    @Schema(description = "性别 0女 1男")
    @Range(min = 0, max = 1, message = "用户性别只能为0或1")
    private Integer sex;

    @Schema(description = "生日")
    private String birthday;

    @Schema(description = "手机号码")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String phone;

    @Schema(description = "验证码，editType=4必传")
    private String verificationCode;

}
