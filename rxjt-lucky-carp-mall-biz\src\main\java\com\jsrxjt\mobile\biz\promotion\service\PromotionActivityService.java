package com.jsrxjt.mobile.biz.promotion.service;

import com.jsrxjt.mobile.domain.promotion.entity.PromotionActivityEntity;

import java.util.List;

public interface PromotionActivityService {

    List<PromotionActivityEntity> listShouldStartActivity();

    List<PromotionActivityEntity> listShouldExpireActivity();

    void startActivity(PromotionActivityEntity entity);

    void expireActivity(PromotionActivityEntity  entity);

}
