package com.jsrxjt.mobile.api.scanPay.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 支付信息
 * @Author: ZY
 * @Date: 20250530
 */
@Data
@Schema(description = "用户密码验证")
public class UserHomePayRequestDTO {
    @Schema(description = "客户id")
    @NotBlank(message = "客户id不能为空")
    private Long customerId;

    @Schema(description = "订单编号")
    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

     @Schema(description = "卡号")
     private String code;

     @Schema(description = "付款密码")
     private String payPassword;

     @Schema(description = "付款金额")
     private String payAmount;

     @Schema(description = "订单来源 APP MINI")
     @NotBlank(message = "订单来源不能为空")
     private String source;

}
