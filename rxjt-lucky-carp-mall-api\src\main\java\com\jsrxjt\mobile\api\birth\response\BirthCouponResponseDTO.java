package com.jsrxjt.mobile.api.birth.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description:生日券参数
 */
@Data
public class BirthCouponResponseDTO {

    @Schema(description = "用户ID")
    @NotNull(message = "用户Id不能为空")
    private Long customerId;

    @Schema(description = "优惠券ID")
    private Long discountCouponId = 0L;

    @Schema(description = "面值")
    private BigDecimal couponAmount = new BigDecimal("100");

    @Schema(description = "优惠券来源 0 生日")
    private Byte couponSource = 0;

    @Schema(description = "优惠券类型 0满减券 1折扣券")
    private Byte discountCouponType = 0;

    @Schema(description = "是否满足金额 0否 1是")
    private Byte isSatisfyMoney = 1;

    @Schema(description = "满足金额")
    private BigDecimal couponSatisfyMoney = new BigDecimal("300");

    @Schema(description = "优惠券状态 0未使用 1已使用 2已过期")
    private Byte usrStatus = 0;

    @Schema(description = "优惠券使用开始时间")
    private Date couponStartTime;

    @Schema(description = "优惠券使用结束时间")
    private Date couponEndTime;

}
