package com.jsrxjt.mobile.api.ticket.types;

import lombok.Getter;

/**
 * @Description: 优惠券类型
 * @Author: ywt
 * @Date: 2025-10-17 10:37
 * @Version: 1.0
 */
@Getter
public enum TicketTypeEnum {
    /**
     * 全球购线上商城优惠券
     */
    GLOBAL_MALL_COUPON((byte) 1, "全球购线上商城优惠券"),
    /**
     * 商家自发优惠券
     */
    MERCHANT_COUPON((byte) 2, "商家自发优惠券"),

    /**
     * 瑞祥代发优惠券
     */
    RX_DISTRIBUTION_COUPON((byte) 3, "瑞祥代发优惠券"),

    /**
     * 门店优惠券
     */
    RX_SHOP_COUPON((byte) 4, "门店优惠券");

    private final Byte code;
    private final String description;

    TicketTypeEnum(Byte code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 优惠券类型code
     * @return 优惠券类型枚举
     */
    public static TicketTypeEnum fromCode(Byte code) {
        for (TicketTypeEnum type : TicketTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的优惠券类型: " + code);
    }
}
