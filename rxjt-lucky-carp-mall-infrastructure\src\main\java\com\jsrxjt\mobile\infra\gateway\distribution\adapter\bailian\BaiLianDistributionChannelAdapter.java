package com.jsrxjt.mobile.infra.gateway.distribution.adapter.bailian;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 百联分销渠道适配器
 * <AUTHOR>
 * @Date 2025/10/16
 */
@Component
@Slf4j
public class BaiLianDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

    public BaiLianDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                             DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getBailian();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.BAILIAN;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            // 校验必填参数
            if (StringUtils.isBlank(request.getUserId()) || StringUtils.isBlank(request.getMobile())) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID和手机号码不能为空")
                        .build();
            }
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("user_id", request.getUserId());
            params.put("mobile", request.getMobile());
            params.put("source", request.getSource());

            // 添加公共参数和签名
            addCommonParams(params);

            // 发送请求
            String url = config.getBaseUrl() + config.getApiPath() + config.getAccessPath();
            String response = httpClientGateway.doPost(url, params, config.getConnectTimeout(),
                    config.getReadTimeout());
            JSONObject result = JSON.parseObject(response);

            // 解析响应
            if (Objects.equals(result.getInteger(STATUS_KEY), SUCCESS_CODE)) {
                return DistAccessResponse.builder()
                        .success(true)
                        .baiLianAccessResponse(result.getObject("data", com.jsrxjt.mobile.domain.bailian.response.BaiLianAccessResponse.class))
                        .build();
            } else {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage(result.getString(MESSAGE_KEY))
                        .build();
            }
        } catch (Exception e) {
            log.error("百联获取付款二维码异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("百联获取付款二维码异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        // 调用父类的通用实现，使用紧凑的日期时间格式（yyyyMMddHHmmss）
        return doPaidNotify(request, COMPACT_DAY_TIME_FORMAT);
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        return DistRefundResultNotifyResponse.builder()
                .success(true)
                .status(SUCCESS_CODE)
                .build();
    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        return null;
    }
}
