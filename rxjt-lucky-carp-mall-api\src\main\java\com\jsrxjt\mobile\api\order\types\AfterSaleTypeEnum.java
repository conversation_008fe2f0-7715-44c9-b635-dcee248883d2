package com.jsrxjt.mobile.api.order.types;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后类型枚举
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Getter
@AllArgsConstructor
public enum AfterSaleTypeEnum {

    /**
     * 部分退款
     */
    PARTIAL_REFUND(1, "部分退款"),

    /**
     * 全额退款
     */
    FULL_REFUND(2, "全额退款");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static AfterSaleTypeEnum getByCode(Integer code) {
        for (AfterSaleTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}