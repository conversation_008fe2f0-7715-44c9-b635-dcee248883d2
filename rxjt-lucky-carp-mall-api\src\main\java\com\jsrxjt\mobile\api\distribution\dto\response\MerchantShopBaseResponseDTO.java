package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 商户大全的门店详情base响应
 */
@Data
@Schema(description = "商户大全门店详情base响应")
public class MerchantShopBaseResponseDTO {
    @Schema(description = "门店id")
    private int id;

    @Schema(description = "城市编码")
    private Long city_code;

    @Schema(description = "第三方ID")
    private String third_id;

    @Schema(description = "商户品牌ID")
    private String brand_id;

    @Schema(description = "商户品牌名称")
    private String brand_name;

    @Schema(description = "商户分组ID")
    private String group_id;

    @Schema(description = "商户分组名称")
    private String group_name;

    @Schema(description = "门店类型ID")
    private String type_id;

    @Schema(description = "门店类型名称")
    private String type_name;

    @Schema(description = "商户分类ID")
    private String cate_id;

    @Schema(description = "商户分类名称")
    private String cate_name;

    @Schema(description = "省ID")
    private String province_id;

    @Schema(description = "市ID")
    private String city_id;

    @Schema(description = "区ID")
    private String area_id;

    @Schema(description = "门店名称")
    private String name;

    @Schema(description = "门店编号")
    private String no;

    @Schema(description = "经度")
    private String lng;

    @Schema(description = "纬度")
    private String lat;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "电话")
    private String tel;

    @Schema(description = "特约商户LOGO")
    private String special_logo;

    @Schema(description = "联盟商户类别")
    private String special_type;

    @Schema(description = "是否显示在特约商户")
    private String show_special;

    @Schema(description = "是否显示在扫码提货")
    private String show_pick;

    @Schema(description = "是否开启提货券支付")
    private String is_th_open;

    @Schema(description = "是否提货券商户")
    private String is_th_shop;

    @Schema(description = "商户标签")
    private String tag;

    @Schema(description = "门店LOGO角标")
    private String icon;

    @Schema(description = "门店LOGO角标有效期")
    private String icon_valid_at;

    @Schema(description = "门店标签")
    private String label;

    @Schema(description = "门店标签颜色")
    private String label_color;

    @Schema(description = "门店标签背景色")
    private String label_bg_color;

    @Schema(description = "品牌信息")
    private MerchantShopBrandResponseDTO brand_info;

}
