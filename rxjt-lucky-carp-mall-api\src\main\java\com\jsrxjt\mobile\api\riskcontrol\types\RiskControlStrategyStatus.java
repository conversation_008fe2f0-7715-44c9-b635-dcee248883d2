package com.jsrxjt.mobile.api.riskcontrol.types;
/**
 * 商品风控策略状态枚举
 * 用于表示商品在风控策略中的状态及用户命中情况
 */
public enum RiskControlStrategyStatus {
    /**
     * 当前商品未在风控策略中
     */
    NOT_IN_STRATEGY(0,  "当前商品未在风控策略中"),

    /**
     * 当前商品在风控策略中且用户命中策略
     */
    HIT_STRATEGY(1,  "当前商品在风控策略中且用户命中策略"),

    /**
     * 当前商品在策略中，用户未命中策略
     */
    NOT_HIT_STRATEGY(2,  "当前商品在策略中，用户未命中策略");

    private final Integer status;

    private final String desc;

    RiskControlStrategyStatus(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static RiskControlStrategyStatus getByStatus(Integer status) {
        for (RiskControlStrategyStatus value : values()) {
            if (value.status.equals(status)) {
                return value;
            }
        }
        return null;
    }
}
