package com.jsrxjt.mobile.api.captcha.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 获取验证码响应DTO
 * 
 * <AUTHOR>
 * @since 2025-12-04
 */
@Data
@Schema(description = "获取验证码响应")
public class CaptchaResponseDTO {

    @Schema(description = "验证码token", example = "abc123def456")
    private String token;

    @Schema(description = "背景图片Base64", example = "iVBORw0KGgoAAAANSUhEUgAA...")
    private String backgroundImage;

    @Schema(description = "验证码单词序列", example = "[\"你\", \"好\", \"吗\"]")
    private List<String> wordList;

    @Schema(description = "拼图图片Base64", example = "iVBORw0KGgoAAAANSUhEUgAA...")
    private String puzzleImage;

    @Schema(description = "加密密钥", example = "secretKey123")
    private String secretKey;

    @Schema(description = "是否需要加密", example = "true")
    private boolean needEncryption;

}
