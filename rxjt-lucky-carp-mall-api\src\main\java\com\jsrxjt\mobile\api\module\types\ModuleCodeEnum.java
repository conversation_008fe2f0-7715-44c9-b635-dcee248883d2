package com.jsrxjt.mobile.api.module.types;

/**
 * 组件码
 * <AUTHOR>
 * @date 2025/05/19
 */
public enum ModuleCodeEnum {

    BASIC_CONFIG("100001","顶部底图&总背景色"),

    JG_DISTINCT("100002","金刚区"),

    SLIDER_IMAGE ("100003","轮播图"),

    AVERAGE_IMAGE("100004","等分图"),

    BRANDS("100005","品牌墙"),

    MULTI_IMAGE("100006", "滑动多图"),

    TAB("200001","tab栏"),

    LOCAL_LIFE_TAB("20000101","本地生活tab栏"),

    GLOBAL_TAB("20000102","全球购tab栏");

    private String code;

    private String description;

    ModuleCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}
