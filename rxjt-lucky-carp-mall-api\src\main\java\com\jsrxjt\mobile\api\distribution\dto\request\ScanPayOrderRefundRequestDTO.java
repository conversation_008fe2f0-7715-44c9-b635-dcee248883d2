package com.jsrxjt.mobile.api.distribution.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jsrxjt.common.core.vo.SignRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 951订单退款请求
 * <AUTHOR>
 * @date 2025/12/30
 */
@Data
@Schema(description = "951订单退款请求")
public class ScanPayOrderRefundRequestDTO extends SignRequest {

    @Schema(description = "平台退款流水号")
    @JSONField(name = "refundNo")
    private String refundNo;

    @Schema(description = "平台交易流水号")
    @JSONField(name = "tradeNo")
    private String tradeNo;

    @Schema(description = "订单金额(单位 分)")
    @JSONField(name = "refundAmount")
    private String refundAmount;

}
