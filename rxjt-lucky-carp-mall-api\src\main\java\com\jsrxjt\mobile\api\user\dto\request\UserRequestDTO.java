package com.jsrxjt.mobile.api.user.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import com.jsrxjt.common.core.xss.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 *  UserRequest
 *
 * <AUTHOR> Fengping
 * 2023/3/14 11:02
 * 
 **/
@Getter
@Setter
@Schema(description = "用户请求参数 测试用")
public class UserRequestDTO extends BaseParam {
    @Schema(description = "在该时间之后注册的，包括该时间")
    private LocalDateTime createTime;

    @Xss
    @NotBlank(message = "用户名称不能为空")
    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "用户id")
    private Long userId;

}
