package com.jsrxjt.mobile.api.scanPay.response;

import com.jsrxjt.mobile.api.customer.response.CustomerDetailResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "返回给第三方用户信息和订单id")
@Data
public class ThirdOrderInfoResponseDTO {

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "用户信息")
    private CustomerDetailResponse customerResponse;

    @Schema(description = "卡支付顺序")
    List<String> cardSort;

    @Schema(description = "卡类型")
    List<CardInfo> cardInfo;
    @Data
    public  static class CardInfo{
        private String type;

        private List< String> list;
    }
}
