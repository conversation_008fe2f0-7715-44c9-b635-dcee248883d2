package com.jsrxjt.mobile.api.scanPay.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PollOrderInfoResponseDTO {
    /**
     * 订单ID
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 外部订单号
     */
    private String externalOrderNo;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户手机号
     */
    private String customerMobile;

    /**
     * 充值账号:类型为直充/红包时不为空
     */
    private String rechargeAccount;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人手机号
     */
    private String receiverMobile;

    /**
     * 收货地址
     */
    private String receiverAddress;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 订单对接渠道 0 未知 1 卡管 2 支付宝红包 3 苏西话费 4 分销中心 5 本地生活 6 扫码付 7 线下扫码 8 纳客宝
     */
    private Integer orderChannel;
    /**
     * 商品spuid(只有1个商品时记录)
     */
    private Long productSpuId;

    /**
     * 商品skuid(只有1个商品时记录)
     */
    private Long productSkuId;

    /**
     * 商品名称(只有1个商品时记录)
     */
    private String productName;

    /**
     * 扁平化产品类型（只有一个商品时记录）
     */
    private Integer flatProductType;

    /**
     * 应用标记
     */
    private String appFlag;

    /**
     * 订单类型: 1-卡券 2-普通应用 3-红包/充值，4扫码付应用
     */
    private Integer orderType;

    /**
     * 订单状态: 0-待付款 10-进行中 20-交易成功 30-交易关闭 40-超时取消 41-手动取消
     */
    private Integer orderStatus;

    /**
     * 外部订单状态
     */
    private String externalStatus;

    /**
     * 售后状态: 1-待审核 20-审核通过 30-审核驳回 32-退款驳回 33-售后撤销 34-售后完成
     */
    private Integer afterSaleStatus;

    /**
     * 发货状态: 0-未发货/未充值  1-发货中/充值中 2-已发货/已充值 3-发货失败/充值失败
     */
    private Integer deliveryStatus;

    /**
     * 支付状态: 0-未支付 1-已支付 2-部分支付 3-退款中 4-已退款
     */
    private Integer paymentStatus;

    /**
     * 支付失效时间戳(秒)
     */
    private Long payExpireTimestamp;

    /**
     * 支付交易号-支付中心提供
     */
    private String tradeNo;

    /**
     * 支付详情--预支付时记录自收银台
     */
    private String paymentDetails;

    /**
     * 商品原售价总价
     */
    private BigDecimal totalSellAmount;

    /**
     * 优惠总金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 订单金额(商品原售价总价 + 加点手续费 + 超额手续费 = 支付金额 + 已优惠金额)
     */
    private BigDecimal orderAmount;

    /**
     * 加点手续费
     */
    private BigDecimal totalServiceFee;

    /**
     * 加点手续费率(%)
     */
    private BigDecimal totalServiceFeeRate;

    /**
     * 总销售价超额手续费
     */
    private BigDecimal exceedFee;

    /**
     * 总销售价超额手续费率(%)
     */
    private BigDecimal exceedFeeRate;

    /**
     * 实际支付金额(商品原售价总价 + 加点手续费 + 超额手续费 - 已优惠金额)
     */
    private BigDecimal paymentAmount;

    /**
     * 限额金额(超出限额计算超额手续费)
     */
    private BigDecimal limitAmount;

    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 订单详情链接
     */
    private String orderDetailUrl;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 外部店铺id
     */
    private String externalShopId;

    /**
     * 第三方id
     */
    private String thirdId;

    /**
     * 外部门店员id
     */
    private String externalShopUserId;

    /**
     * 风控策略id,触发风控时不为null
     */
    private Long riskControlId;

    /**
     * 是否删除: 0-未删除 1-已删除
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime modTime;

    /**
     * 支持的支付方式
     */
    private String payType;

}
