package com.jsrxjt.mobile.biz.ticket;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryDetailRequestDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryRequestDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketVerifyRequestDTO;
import com.jsrxjt.mobile.api.ticket.response.*;

public interface TicketDeliveryService {
    PageDTO<TicketDeliveryResponseDTO> getTicketDelivery(TicketDeliveryRequestDTO  request);

    TicketDeliveryDetailResponseDTO getTicketDeliveryById(TicketDeliveryDetailRequestDTO request);

    void delTicketDelivery(TicketDeliveryDetailRequestDTO request);

    /**
     * 分页获取门店券列表
     * @param request 请求参数
     * @return 返回结果
     */
    PageDTO<TicketShopListResponseDTO> getShopTicketList(TicketDeliveryRequestDTO request);


    /**
     * 分页获取全球购优惠券列表
     * @param request 请求参数
     * @return 返回结果
     */
    PageDTO<GlobalTicketResponseDTO> getGlobalTicketList(TicketDeliveryRequestDTO request);

    /**
     * 券核销
     * @param request 请求参数
     * @return 核销结果
     */
    TicketVerifyResponseDTO verifyTicket(TicketVerifyRequestDTO request);

}
