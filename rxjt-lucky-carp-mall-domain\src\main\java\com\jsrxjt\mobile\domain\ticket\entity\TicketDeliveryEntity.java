package com.jsrxjt.mobile.domain.ticket.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券发放体类
 */
@Data
public class TicketDeliveryEntity {
    
    /**
     * 优惠券订单编号
     */
    @Schema(description = "id")
    private Long id;

    private Long orderId;
    
    /**
     * 关联的订单id
     */
    @Schema(description = "优惠券订单号")
    private String ticketOrderNo;
    
    /**
     * 关联订单编号
     */
    @Schema(description = "关联订单编号")
    private String orderNo;
    
    /**
     * 外部订单编号
     */
    @Schema(description = "外部订单编号")
    private String externalOrderNo;
    
    /**
     * 优惠券类型：1全球购线上商城优惠券 2商家自发优惠券  3瑞祥代发优惠券 4门店优惠券
     */
    @Schema(description = "优惠券类型：1全球购线上商城优惠券 2商家自发优惠券  3瑞祥代发优惠券 4门店优惠券")
    private Byte ticketType;
    
    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long customerId;
    
    /**
     * 核销状态: 1未核销 2已核销，仅瑞祥代发优惠券有效
     */
    @Schema(description = "核销状态: 1未核销 2已核销，仅瑞祥代发优惠券有效")
    private Byte status;
    
    /**
     * 卡营coupon_id
     */
    @Schema(description = "卡营coupon_id")
    private String centerCouponId;
    
    /**
     * 卡券卡号
     */
    @Schema(description = "卡券卡号")
    private String ticketCode;
    
    /**
     * 卡券卡密
     */
    @Schema(description = "卡券卡密")
    private String ticketPin;

    /**
     * 卡券链接
     */
    private String url;

    /**
     * 卡券链接密码
     */
    private String urlPass;

    /**
     * 卡券crc校验码
     */
    private String crc;

    /**
     * 批次号
     */
    private String batchNum;

    /**
     * 卡券类型
     */
    private String h5Url;
    /**
     *      * 101: 平台自发券
     *      * 201: 卡密+卡号
     *      * 202: 卡号或卡密
     *      * 203: 卡号+卡密+校验码
     *      * 301: 链接类
     *      * 302: 链接+验证码
     *      * 303: 链接+卡号+验证码
     */
    private Integer checkType;
    
    /**
     * 优惠券id
     */
    @Schema(description = "优惠券id")
    private Long ticketId;
    
    /**
     * 优惠券名称
     */
    @Schema(description = "优惠券名称")
    private String ticketName;
    
    /**
     * 券的有效期
     */
    @Schema(description = "券的有效期")
    private Date ticketValidDate;
    
    /**
     * 核销页样式 0卡号 1转码
     */
    @Schema(description = "核销页样式 0卡号 1转码")
    private Byte offsetPageType;
    
    /**
     * 品牌名
     */
    @Schema(description = "品牌名")
    private String brandName;
    
    /**
     * 核销logo
     */
    @Schema(description = "核销logo")
    private String offsetLogo;

    /**
     * 营销中台的优惠券编号
     */
    private String centerTicketCouponNumber;

    /**
     * 卡管平台用户卡券id
     */
    private String couponNumberId;
    
    /**
     * 使用说明
     */
    @Schema(description = "使用说明")
    private String useManual;
    @Schema(description = "是否生日券，0-不是  1-是")
    private Integer isBirthdayTicket;
    @Schema(description = "优惠券分类 1满减券 2无门槛券")
    private Integer ticketCatCode;
    private BigDecimal discountAmount;//优惠金额 (ticket_type值为3或4存在此值)
    private BigDecimal thresholdAmount;//满减券优惠门槛金额 (ticket_type值为3或4存在此值)

    @Schema(description = "领取时间")
    private Date createTime;

    private Date modTime;
}
