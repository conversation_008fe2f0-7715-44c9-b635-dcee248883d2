package com.jsrxjt.mobile.api.app.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@Data
@Schema(description = "支付宝红包应用信息响应")
public class AlipayAppOrderRequest {

    @Schema(description = "用户id")
    @NotNull(message = "用户id不能为空")
    private Long customerId;

    @Schema(description = "客户手机号")
    @NotNull(message = "客户手机号不能为空")
    private Long customerIphone;

    @Schema(description = "spuId")
    @NotNull(message = "spuId不能为空")
    private Long appSpuId;

    @Schema(description = "skuId")
    @NotNull(message = "skuId不能为空")
    private Long appSkuId;

    @Schema(description = "分类id")
    @NotNull(message = "分类id不能为空")
    private Long alipayTabCatId;

    @Schema(description = "三级地址id")
    @NotNull(message = "三级地址id不能为空")
    private Integer regionId;

    @Schema(description = "是否勾选-充值使用说明")
    @NotNull(message = "是否勾选-充值使用说明不能为空")
    private Boolean isCheckedRecharge;

    @Schema(description = "是否勾选-银行红包说明 银行专项红包必填")
    private Boolean isCheckedBank;

    @Schema(description = "充值账号:类型为直充/红包时不为空")
    @NotNull(message = "充值账号时不为空")
    private String rechargeAccount;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

}
