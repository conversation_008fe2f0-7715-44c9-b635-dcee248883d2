package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.AdvertisementListRequestDto;
import com.jsrxjt.mobile.api.distribution.dto.request.AdvertisementRequestDto;

import java.util.List;

/**
 * @Description: 广告服务接口
 * @Author: ywt
 * @Date: 2025-06-10 15:58
 * @Version: 1.0
 */
public interface AdvertisementCaseService {
    boolean clickAdvertisement(AdvertisementRequestDto requestDTO);
    List<AdvertisementInfoDTO> getAdvertisementList(AdvertisementListRequestDto requestDTO);
}
