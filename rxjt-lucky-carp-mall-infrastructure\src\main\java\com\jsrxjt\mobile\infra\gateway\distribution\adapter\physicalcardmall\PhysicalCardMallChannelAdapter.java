package com.jsrxjt.mobile.infra.gateway.distribution.adapter.physicalcardmall;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.jsrxjt.mobile.api.customer.types.CustomerCardTypeEnum;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.order.dto.response.OrderDetailResponseDTO;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.locallife.gateway.LocalLifeGateWay;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.TradeOrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.repository.TradeOrderRepository;
import com.jsrxjt.mobile.domain.physicalCardMall.PhysicalCardMallGateWay;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-10-23 14:12
 * @Version: 1.0
 */
@Component
@Slf4j
public class PhysicalCardMallChannelAdapter extends AbstractDistributionChannelAdapter {
    @Autowired
    private PhysicalCardMallGateWay physicalCardMallGateWay;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private TradeOrderRepository tradeOrderRepository;

    public PhysicalCardMallChannelAdapter(HttpClientGateway httpClientGateway, DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getPhysicalcardmall();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.PHYSICALCARDMALL;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            String userId = request.getUserId();
            if (StringUtils.isBlank(userId)) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID")
                        .build();
            }
            String userCode = physicalCardMallGateWay.encodeUserId(Long.valueOf(userId));
            if (StringUtils.isEmpty(userCode)) {
                return null;
            }
            // 构建请求参数
            Map<String, Object> map = Maps.newLinkedHashMap();
            map.put("formFlq", 1);
            map.put("userId", userCode);
            // 构建请求URL
            String url = config.getBaseUrl() + config.getApiPath();
            String requestUrl = url + httpClientGateway.buildUrlParams(map);
            return DistAccessResponse.builder()
                    .success(true)
                    .redirectUrl(requestUrl)
                    .build();
        } catch (Exception e) {
            log.error("实体卡商城免登接入异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("实体卡商城免登接入异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        log.error("实体卡商城支付结果通知: {}", JSON.toJSONString(request));
        OrderInfoEntity order = orderRepository.findByOrderNo(request.getOrderNo());
        List<OrderDetailResponseDTO.OrderPaymentDTO> orderPaymentList
                = new ArrayList<>();
        String detail = null;
        List<TradeOrderInfoEntity> tradeOrderInfoEntities = tradeOrderRepository
                .listTradeOrderInfoByTradeNoAndOrderNo(order.getTradeNo(), order.getOrderNo());
        if (CollectionUtils.isNotEmpty(tradeOrderInfoEntities)) {
            detail = tradeOrderInfoEntities.stream()
                    .map(tradeOrderInfoEntity -> {
                        CustomerCardTypeEnum byCardCodeType = CustomerCardTypeEnum.getByCardCodeType(tradeOrderInfoEntity.getCardTradeType());
                        switch (Objects.requireNonNull(byCardCodeType)) {
                            case WHITE_CARD -> {
                                return "bjmain," + tradeOrderInfoEntity.getCardNo() + "," + tradeOrderInfoEntity.getPayAmount();
                            }
                            case BLACK_CARD -> {
                                return "hjmain," + tradeOrderInfoEntity.getCardNo() + "," + tradeOrderInfoEntity.getPayAmount();
                            }
                            case BUSINESS_CARD -> {
                                return "sl," + tradeOrderInfoEntity.getCardNo() + "," + tradeOrderInfoEntity.getPayAmount();
                            }
                            case TRADE_UNION_VOUCHER -> {
                                return "th," + tradeOrderInfoEntity.getCardNo() + "," + tradeOrderInfoEntity.getPayAmount();
                            }
                            default -> {
                                return null;
                            }
                        }
                    })
                    .filter(s -> !Objects.isNull(s))
                    .collect(Collectors.joining("|"));
        }
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("detail", detail);
        requestData.put("orderNo", order.getExternalOrderNo());
        requestData.put("flqOrderNo", order.getOrderNo());
        requestData.put("tradeAmount", order.getTotalSellAmount());
        requestData.put("payTime", order.getPaymentTime());
        return DistPaidNotifyResponse.builder().success(callBackLocalLife(requestData, 1))
                .build();
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        log.error("实体卡商城退款结果通知: {}", JSON.toJSONString(request));
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("orderNo", request.getDistOrderNo());
        requestData.put("flqOrderNo", request.getOutOrderNo());
        requestData.put("refundNo", request.getDistRefundNo());
        requestData.put("flqRefundNo", request.getOutRefundNo());
        requestData.put("refundTime", request.getRefundTime().format(COMPACT_DAY_TIME_FORMAT));
        requestData.put("refundAmount", request.getRefundAmount());
        requestData.put("status", "10");
        return DistRefundResultNotifyResponse
                .builder()
                .success(callBackLocalLife(requestData,2))
                .build();
    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        return doThrowUnsupportedOrderDetailQuery("实体卡商城");
    }

    private boolean callBackLocalLife(Map<String, Object> requestData, int type) {
        boolean callResult = true;
        if (type == 1) {
            callResult = physicalCardMallGateWay.notifyPaySuccess(requestData);
        } else if (type == 2) {
            callResult = physicalCardMallGateWay.notifyRefundSuccess(requestData);
        }
        if (!callResult) {
            // TODO 是否重试通知本地生活
        }
        return callResult;
    }
}
