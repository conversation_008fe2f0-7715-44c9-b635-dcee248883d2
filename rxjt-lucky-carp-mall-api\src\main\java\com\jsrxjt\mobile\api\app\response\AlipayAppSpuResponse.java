package com.jsrxjt.mobile.api.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "支付宝红包应用信息响应")
public class AlipayAppSpuResponse {

    @Schema(description = "应用SPU ID")
    private Long appSpuId;

    @Schema(description = "应用SPU名称")
    private String appSpuName;

    @Schema(description = "logo图片URL")
    private String logoUrl;

    @Schema(description = "副标题")
    private String subTitle;

    @Schema(description = "支付宝tab分类ID")
    private Long alipayTabCatId;

    @Schema(description = "角标id")
    private Long subscriptId;

    @Schema(description = "角标url")
    private String subscriptUrl;

    @Schema(description = "是否选中 0否 1是")
    private Byte isSpuSelect = 0;

    @Schema(description = "应用说明信息")
    List<AlipayAppExplainResponse> explainEntityList;

}
