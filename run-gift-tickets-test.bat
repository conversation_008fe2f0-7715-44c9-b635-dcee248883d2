@echo off
echo 正在运行handleGiftTicketsSend方法的单元测试...
echo.
echo 注意：此版本的测试避免了静态Mock，专注于核心业务逻辑测试
echo.

REM 运行完整的测试类
echo 1. 运行完整测试类:
mvn test -Dtest=PaymentSuccessCaseServiceImplTest -f rxjt-lucky-carp-mall-biz/pom.xml
echo.

REM 运行特定测试方法示例
echo 2. 运行特定测试方法示例:
echo 基础功能测试:
echo mvn test -Dtest=PaymentSuccessCaseServiceImplTest#testHandleGiftTicketsSend_NoCacheData -f rxjt-lucky-carp-mall-biz/pom.xml
echo mvn test -Dtest=PaymentSuccessCaseServiceImplTest#testHandleGiftTicketsSend_LockFailed -f rxjt-lucky-carp-mall-biz/pom.xml
echo.
echo 券类型测试:
echo mvn test -Dtest=PaymentSuccessCaseServiceImplTest#testHandleGiftTicketsSend_OnlyCouponTickets_Success -f rxjt-lucky-carp-mall-biz/pom.xml
echo mvn test -Dtest=PaymentSuccessCaseServiceImplTest#testHandleGiftTicketsSend_MixedTickets_Success -f rxjt-lucky-carp-mall-biz/pom.xml
echo mvn test -Dtest=PaymentSuccessCaseServiceImplTest#testHandleGiftTicketsSend_CouponPlatformFactoryTest -f rxjt-lucky-carp-mall-biz/pom.xml
echo.

REM 生成测试报告
echo 3. 生成测试覆盖率报告:
echo mvn test -Dtest=PaymentSuccessCaseServiceImplTest jacoco:report -f rxjt-lucky-carp-mall-biz/pom.xml
echo.

echo 当前测试覆盖的场景：
echo 基础功能测试：
echo - 缓存不存在或为空的情况
echo - 分布式锁获取失败
echo - 订单不存在
echo - 赠券列表为空
echo.
echo 券类型测试：
echo - 只有卡管券的处理（包含卡管券和瑞祥代发券）
echo - 混合券类型的处理（全球购券+卡管券）
echo - CouponPlatformFactory工厂方法的直接使用
echo - 不使用静态Mock的真实调用测试
echo.
echo 测试完成！
pause
