package com.jsrxjt.mobile.infra.ticket.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.mobile.domain.ticket.config.TicketPlatformConfig;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketPO;
import com.jsrxjt.mobile.infra.ticket.util.TicketPlateformUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * @Description: 优惠券
 * @Author: ywt
 * @Date: 2025-08-15 15:19
 * @Version: 1.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TicketRepositoryImpl implements TicketRepository {
    private final TicketMapper ticketMapper;
    private final TicketPlatformConfig ticketPlateformConfig;

    @Override
    public List<TicketEntity> getTicketsByIds(List<Long> ids) {
        List<TicketPO> ticketList = ticketMapper.selectList(new LambdaQueryWrapper<TicketPO>()
                .in(TicketPO::getTicketId, ids)
                .eq(TicketPO::getStatus, 1));
        return BeanUtil.copyToList(ticketList, TicketEntity.class);
    }

    @Override
    public TicketEntity getTicketById(Long ticketId) {
        TicketPO ticketPO = ticketMapper.selectOne(new LambdaQueryWrapper<TicketPO>()
                .eq(TicketPO::getTicketId, ticketId));
        return BeanUtil.copyProperties(ticketPO, TicketEntity.class);
    }

    @Override
    public TicketEntity getTicketIncludeDelById(Long ticketId) {
        TicketPO ticketPO = ticketMapper.getTicketIncludeDelById(ticketId);
        return BeanUtil.copyProperties(ticketPO, TicketEntity.class);
    }

    @Override
    public List<TicketEntity> getTicketByCenterId(String centerTicketId) {
        List<TicketPO> ticketList = ticketMapper.selectList(new LambdaQueryWrapper<TicketPO>()
                .eq(TicketPO::getCenterTicketId, centerTicketId));
        return BeanUtil.copyToList(ticketList, TicketEntity.class);
    }

    @Override
    public Integer updateTicketStatusByPlateform(Long ticketId, Integer status) {
        if (status == 0 || status == 1) {
            TicketPO ticketPO = new TicketPO();
            ticketPO.setTicketId(ticketId);
            ticketPO.setStatus(status);
            ticketPO.setCenterStatus(status);
            ticketPO.setModTime(new Date());
            return ticketMapper.updateById(ticketPO);
        } else if (status == 2) {
            return ticketMapper.removeById(ticketId);
        }
        return -1;
    }

    @Override
    public String getPlateformSign(Map<String, Object> requestMap) {
        SortedMap<Object, Object> parameters = JSONObject.parseObject(JSONObject.toJSONString(requestMap), SortedMap.class);
        return TicketPlateformUtil.getSignature(ticketPlateformConfig.getAppSecret(), parameters);
    }
}
