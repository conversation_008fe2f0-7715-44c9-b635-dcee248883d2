package com.jsrxjt.mobile.api.scanPay;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Schema(description = "收银台订单卡支付顺序信息")
@Data
public class ThirdVipOrderCardDTO {

    @JSONField(name = "card_id")
    private Integer card_id; //卡id
    @JSONField(name ="card_no")
    private String card_no; //卡号
    @JSONField(name ="compay_order_id")
    private Integer compay_order_id; //线下订单id
    @JSONField(name ="mch_id")
    private String mch_id;//商户号？
    @JSONField(name ="md_id")
    private String mdId;//门店id
    @JSONField(name ="order_no")
    private String order_no;//订单号
    @JSONField(name ="out_order_no")
    private String out_order_no;//外部订单号
    @JSONField(name ="shop_id")
    private Integer shop_id;//门店编号
    @JSONField(name ="son_price")
    private Integer son_price;//子单价格
    @JSONField(name ="term_id")
    private String termId;//交易终端的唯一编号
    @JSONField(name ="type")
    private Integer type;//支付卡类型
    @JSONField(name ="unionid")
    private String unionid;
    @JSONField(name ="vip_id")
    private Integer vip_id;
}
