package com.jsrxjt.adapter.customer.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.region.dto.request.UpdateRegionRequest;
import com.jsrxjt.mobile.api.region.dto.request.UserLocationRequest;
import com.jsrxjt.mobile.biz.region.service.RegionCaseService;
import com.jsrxjt.mobile.api.region.dto.response.AllCityResponse;
import com.jsrxjt.mobile.api.region.dto.response.SearchCityResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 定位/区域相关
 */
@RestController
@RequestMapping("/v1/location")
@Slf4j
@RequiredArgsConstructor
public class LocationController {

    private final RegionCaseService regionService;

    @GetMapping("/search")
    @Operation(summary = "查询市/县区")
    public BaseResponse<List<SearchCityResponse>> search(@RequestParam("searchQuery") @NotBlank(message = "查询内容不能为空")
                                                             @Parameter(description = "查询区域") String searchQuery){
        return BaseResponse.succeed(regionService.searchCity(searchQuery));
    }

    @GetMapping("/allCity")
    @Operation(summary = "查询市/县区")
    public BaseResponse<AllCityResponse> allCity(){
        return BaseResponse.succeed(regionService.allCity());
    }

    @PostMapping("/userLocation")
    @Operation(summary = "用户当前区域")
    public BaseResponse<SearchCityResponse> userLocation(@RequestBody @Valid UserLocationRequest request){
        return BaseResponse.succeed(regionService.userLocation(request));
    }

    @GetMapping("/region")
    @Operation(summary = "手动获取区域信息")
    public BaseResponse<SearchCityResponse> region(@RequestParam("regionId") @NotBlank(message = "区域不能为空")
                                                       @Parameter(description = "区域id") Integer regionId){
        return BaseResponse.succeed(regionService.getRegion(regionId));
    }

    @PostMapping("/updateRegionByParentId")
    public BaseResponse updateRegionByParentId(@RequestBody UpdateRegionRequest request){
        return BaseResponse.succeed(regionService.updateRegionByParentId(request));
    }

    @PostMapping("/updateRegionCache")
    public BaseResponse updateRegionCacheByParentId(@RequestBody UpdateRegionRequest request){
        return BaseResponse.succeed(regionService.updateALLRegionCache(request));
    }

    @PostMapping("/updateAllRegionToTrie")
    public BaseResponse updateAllRegionToTrie(@RequestBody UpdateRegionRequest request){
        return BaseResponse.succeed(regionService.updateAllRegionToTrie(request));
    }
}
