package com.jsrxjt.mobile.biz.app;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.app.request.*;
import com.jsrxjt.mobile.api.app.response.AlipayAppCatResponse;
import com.jsrxjt.mobile.api.app.response.AlipayAppResponse;
import com.jsrxjt.mobile.api.app.response.AlipayVoucherRechargeRecordResponse;
import com.jsrxjt.mobile.api.app.response.BianlfAppResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;

import java.util.List;

public interface AlipayAppService {
    /**
     * 获取支付宝红包分类
     * @return
     */
    List<AlipayAppCatResponse> getAlipayAppCats(Integer regionId);

    /**
     * 获取支付宝红包详情信息
     * @param request
     * @return
     */
    AlipayAppResponse getAlipayAppInfo(AlipayAppRequest request);

    /**
     * 获取便利蜂直充详情信息
     * @param request
     * @return
     */
    BaseResponse<BianlfAppResponse> getBianlfAppInfo(BianlfAppRequest request);

    /**
     * 获取上次充值账号
     * @param customerId 客户id
     * @return 上次充值账号
     */
    String  getLastRechargeAccount(Long customerId);

    void processDeliveringAlipayVoucherOrder(List<OrderInfoEntity> list);
}
