package com.jsrxjt.common.core.util.geo;

import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GlobalCoordinates;

public class GeoUtil {
    public static double getDistance(double lat1, double lon1,  double lat2, double lon2) {
        GeodeticCalculator calculator = new GeodeticCalculator();
        GlobalCoordinates point1 = new GlobalCoordinates(lat1, lon1);
        GlobalCoordinates point2 = new GlobalCoordinates(lat2, lon2);
        return calculator.calculateGeodeticCurve(Ellipsoid.WGS84, point1, point2).getEllipsoidalDistance();
    }
}
