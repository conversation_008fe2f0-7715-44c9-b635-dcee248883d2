package com.jsrxjt.adapter.customer.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.birth.request.BirthCouponRequestDTO;
import com.jsrxjt.mobile.biz.birth.CustomerBirthCouponService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 门店优惠券接口
 */
@RestController
@RequestMapping("/v1/birth")
@Slf4j
@RequiredArgsConstructor
@Tag(name = "生日券接口", description = "生日券接口")
public class BirthTicketController {

    private final CustomerBirthCouponService customerBirthCouponService;

    @PostMapping("/receiveBirth")
    @Operation(summary = "领取门店生日券")
    @VerifySign(hasToken=true)
    public BaseResponse<Void> receiveBirth(@RequestBody @Valid BirthCouponRequestDTO request) {
        customerBirthCouponService.receiveBirthCoupon(request);
        return BaseResponse.succeed();
    }
}
