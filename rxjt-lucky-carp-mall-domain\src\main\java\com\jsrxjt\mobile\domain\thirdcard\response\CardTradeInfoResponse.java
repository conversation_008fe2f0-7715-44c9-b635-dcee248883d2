package com.jsrxjt.mobile.domain.thirdcard.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/11/4
 */
@Data
public class CardTradeInfoResponse {

    /**
     * 卡号
     */
    @JSONField(name = "card_id")
    private String cardId;

    /**
     * 交易类型
     */
    @JSONField(name = "txn_type")
    private String txnType;

    /**
     * 交易时间
     */
    @JSONField(name = "txn_time")
    private Date txnTime;

    /**
     * 交易金额
     */
    @JSONField(name = "txn_amt")
    private String txnAmt;

    /**
     * 当前余额
     */
    @JSONField(name = "acc_total_bal")
    private String accTotalBal;

    /**
     * 可用余额
     */
    @JSONField(name = "acc_valid_bal")
    private String accValidBal;

    /**
     * 交易渠道号
     */
    @JSONField(name = "channel_id")
    private String channelId;

    /**
     * 交易商户号
     */
    @JSONField(name = "merchant_code")
    private String merchantCode;

    /**
     * 交易商户名称
     */
    @JSONField(name = "merchant_name")
    private String merchantName;

    /**
     * 交易门店id
     */
    @JSONField(name = "store_id")
    private String storeId;

    /**
     *  交易门店名称
     */
    @JSONField(name = "store_name")
    private String storeName;

    /**
     * 交易终端id
     */
    @JSONField(name = "term_id")
    private String termId;

    /**
     * 外部业务流水号
     */
    @JSONField(name = "out_flow_no")
    private String outFlowNo;

    /**
     * 内部业务流水号
     */
    @JSONField(name = "inner_flow_no")
    private String innerFlowNo;

    /**
     * 交易备注
     */
    @JSONField(name = "remark")
    private String remark;
}
