package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CouponPackageDetailResponseDTO {

    @Schema(description = "卡包id")
    private Long couponPackageId;

    @Schema(description = "logo")
    private String logoUrl;

    @Schema(description = "卡券名称")
    private String couponName;

    @Schema(description = "卡券面值名称")
    private String amountName;

    @Schema(description = "卡券面值")
    private BigDecimal amount;

    @Schema(description = "spuId")
    private Long spuId;
    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "自定义核销类型 1:面值+兑换码 2:面值+兑换码+一维二维码 3:面值+卡号+一维二维码  4面值+卡号卡密 5面值+卡号卡密+兑换码 6面值+卡号卡密+一维二维码 7面值+卡密+一维二维码 8面值+链接 9自发券(面值+提货券形式) 10跳转品牌方h5核销页 11面值+链接(卡密)" )
    private Integer flqType;

    @Schema(description = "卡券卡号")
    private String couponCode;

    @Schema(description = "卡券卡密")
    private String couponPin;

    @Schema(description = "卡券crc")
    private String couponCrc;

    @Schema(description = "卡券兑换链接")
    private String couponUrl;

    @Schema(description = "卡券兑换短链接密码")
    private String couponUrlPass;

    @Schema(description = "使用说明")
    private String useManual;

    @Schema(description = "核销背景色")
    private String backgroundColor;

    @Schema(description = "核销弹框")
    private String offsetDialog;

    @Schema(description = "产品类型（扁平化）")
    private Integer flatProductType;

    @Schema(description = "购券明细 自发券相关信息 flqType=9时使用")
    private SelCouponCodeResponseDTO  selCouponCodeResponseDTO;

    @Schema(description = "卡管平台卡券核销类型")
    private Integer couponVerificationType;
}
