package com.jsrxjt.mobile.api.dp;

import lombok.Value;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Value
public class ExchangeRate {
    BigDecimal rate;
    Currency from;
    Currency to;

    public ExchangeRate(BigDecimal rate, Currency from, Currency to) {
        this.rate = rate;
        this.from = from;
        this.to = to;
    }

    public Money exchangeTo(Money targetMoney) {
        Assert.notNull(targetMoney,"计算汇率的金额不能为空");
        Assert.isTrue(this.to.equals(targetMoney.getCurrency()),"货币种类校验失败");
        BigDecimal toCurrencyFromAmount = targetMoney.getAmount().divide(rate,2, RoundingMode.DOWN);
        return new Money(toCurrencyFromAmount, from);
    }

}
