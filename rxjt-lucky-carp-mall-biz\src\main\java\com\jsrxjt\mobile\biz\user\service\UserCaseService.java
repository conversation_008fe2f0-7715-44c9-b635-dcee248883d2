package com.jsrxjt.mobile.biz.user.service;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.user.dto.request.UserRequestDTO;
import com.jsrxjt.mobile.domain.user.entity.UserEntity;

import java.util.List;

/**
 * 用户业务服务接口
 * <AUTHOR>
 * @since 2024/4/10
 **/
public interface UserCaseService {
    UserEntity getUser(Long userId);


    List<UserEntity> listUser(UserRequestDTO userRequestDTO);


    PageDTO<UserEntity> pageUser(UserRequestDTO userRequestDTO);

}
