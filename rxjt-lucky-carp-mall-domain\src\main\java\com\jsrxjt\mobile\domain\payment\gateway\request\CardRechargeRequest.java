package com.jsrxjt.mobile.domain.payment.gateway.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class CardRechargeRequest {

    /**
     * 支付类型 RW,RB,RBIZ,RR,WX,LT,VT
     */
    @JSONField(name = "trade_type")
    private String payType;

    /**
     * 充值类型 RW,RB
     */
    @JSONField(name = "recharge_type")
    private String rechargeType;

    /**
     * 消费类型卡号 卡充值必填
     */
    @JSONField(name = "trade_card")
    private String tradeCard;

    /**
     * 消费类型卡密 卡充值必填
     */
    @JSONField(name = "trade_card_pwd")
    private String tradeCardPwd;

    /**
     * 消费类型卡CVV 卡充值必填
     */
    @JSONField(name = "trade_card_cvv")
    private String tradeCardCvv;

    /**
     * 充值卡号
     */
    @JSONField(name = "recharge_card")
    private String rechargeCard;

    /**
     * 外部业务订单号
     */
    @JSONField(name = "recharge_out_order_no")
    private String rechargeOutOrderNo;

    /**
     * 充值金额 微信充值的时候必填
     */
    @JSONField(name = "recharge_amount")
    private Integer rechargeAmount;

    /**
     * 来源 APP_ANDROID APP_IOS WX_MINI
     */
    private String source;

    /**
     * 用户id
     */
    @JSONField(name = "customer_id")
    private Long customerId;

}
