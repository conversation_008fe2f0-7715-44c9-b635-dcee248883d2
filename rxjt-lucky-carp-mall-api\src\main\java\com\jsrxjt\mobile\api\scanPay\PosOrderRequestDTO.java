package com.jsrxjt.mobile.api.scanPay;

import com.jsrxjt.common.core.vo.SignRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "pos返回订单信息")
@Data
public class PosOrderRequestDTO extends SignRequest {
    //卡号
    @NotBlank(message = "卡号不能为空")
    private String code;
    //订单号
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    // 外部店铺ID
    private String externalShopId;
    //外部门店员ID
    private String externalShopUserId;
    //金额
    @NotNull(message = "金额不能为空")
    private BigDecimal price;



}
