package com.jsrxjt.mobile.api.user.dto;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserDTO {

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.user_id
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */

    private Long userId;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.user_name
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private String userName;
    
    /**
     * 用户微信unionid
     *表字段 ： t_user.unionid
     */
    private String unionid;

    private String openid;
    
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.password
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private String nickName;
    
    private String password;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.phone
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private String phone;
  
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.sex
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Integer sex;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.birthday
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private String birthday;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.user_url
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private String userUrl;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.user_grade_id
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Long userGradeId;
   
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.enable
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Boolean enable;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.login_ip
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private String loginIp;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.login_time
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private LocalDateTime loginTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.login_num
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Long loginNum;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.login_error_count
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Long loginErrorCount;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.login_lock_time
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private LocalDateTime loginLockTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.continue_login_count
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Long continueLoginCount;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.phone_token
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private String phoneToken;
   
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.login_status
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Integer loginStatus;
    
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.origin
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Integer origin;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.schq
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Integer schq;
 
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.old_type
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Integer oldType;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.create_time
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */

    private LocalDateTime createTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.create_id
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Long createId;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.mod_time
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */

    private LocalDateTime modTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column t_user.mod_id
     * @mbg.generated  Tue Feb 21 14:40:56 CST 2017
     */
    private Long modId;

    /**
     * 用户姓名
     */
    private String userRealName;
    /**
     * 用户身份证
     */
    private String userIdCard;

    /**
     * 用户认证状态，0-未认证，1-认证中，3-已认证，4认证不通过
     */
    private Integer userAuthStatus;

    /**
     * 0--正常，1--已注销
     */
    private Integer delFlag;

    private Integer mchId;

    private Integer miniUserId;

    private String cardNo;

}