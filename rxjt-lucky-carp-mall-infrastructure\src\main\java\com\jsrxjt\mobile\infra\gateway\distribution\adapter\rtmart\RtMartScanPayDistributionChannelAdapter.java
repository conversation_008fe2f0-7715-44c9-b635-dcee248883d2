package com.jsrxjt.mobile.infra.gateway.distribution.adapter.rtmart;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 大润发扫码付分销渠道适配器
 **/
@Component
@Slf4j
public class RtMartScanPayDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

  public RtMartScanPayDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                                 DistributionConfig distributionConfig) {
    super(httpClientGateway, distributionConfig);
  }

  @Override
  protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
    return distributionConfig.getRtmartscanpay();
  }

  @Override
  public DistChannelType getChannelType() {
    return DistChannelType.RTMARTSCANPAY;
  }

  @Override
  public DistAccessResponse access(DistAccessRequest request) {
    try {
      // 校验必填参数
      if (StringUtils.isBlank(request.getUserId()) || StringUtils.isBlank(request.getMobile()) ||
          request.getLatitude() == null || request.getLongitude() == null) {
        return DistAccessResponse.builder()
            .success(false)
            .errorMessage("用户ID、手机号、经纬度不能为空")
            .build();
      }

      // 构建请求参数
      Map<String, Object> params = new HashMap<>();
      params.put("user_id", request.getUserId());
      params.put("mobile", request.getMobile());
      params.put("latitude", request.getLatitude());
      params.put("longitude", request.getLongitude());

      // 添加公共参数和签名
      addCommonParams(params);

      // 构建请求URL
      String url = config.getBaseUrl() + config.getApiPath() + config.getAccessPath();
      String requestUrl = url + "&" + httpClientGateway.buildUrlParams(params);

      return DistAccessResponse.builder()
          .success(true)
          .redirectUrl(requestUrl)
          .build();
    } catch (Exception e) {
      log.error("大润发扫码付免登接入异常: {}", e.getMessage(), e);
      return DistAccessResponse.builder()
          .success(false)
          .errorMessage("大润发扫码付免登接入异常: " + e.getMessage())
          .build();
    }
  }

  @Override
  public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
    // 调用父类的通用实现，使用紧凑的日期时间格式（yyyyMMddHHmmss）
    // 注意：大润发不需要API路径前缀
    return doPaidNotify(request, COMPACT_DAY_TIME_FORMAT);
  }

  @Override
  public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
    try {
      // 校验必填参数
      if (StringUtils.isBlank(request.getDistTradeNo()) || StringUtils.isBlank(request.getOutTradeNo()) ||
          StringUtils.isBlank(request.getDistRefundNo()) || StringUtils.isBlank(request.getOutRefundNo()) ||
          request.getRefundAmount() == null || request.getStatus() == null) {
        return DistRefundResultNotifyResponse.builder()
            .success(false)
            .status(400)
            .message("必填参数不能为空")
            .build();
      }

      // 构建请求参数
      Map<String, Object> params = new HashMap<>();
      params.put("tradeNo", request.getDistTradeNo());
      params.put("outTradeNo", request.getOutTradeNo());
      params.put("refundNo", request.getDistRefundNo());
      params.put("outRefundNo", request.getOutRefundNo());
      params.put("refundAmount", request.getRefundAmount().toString());
      params.put("status", request.getStatus());

      // 转换时间格式，若有退款时间
      if (request.getRefundTime() != null) {
        params.put("refundTime", request.getRefundTime().format(COMPACT_DAY_TIME_FORMAT));
      } else {
        params.put("refundTime", "");
      }

      // 添加公共参数和签名
      addCommonParams(params);

      // 发送请求
      String url = config.getBaseUrl() + config.getApiPath() + config.getRefundCallbackPath();
      String response = httpClientGateway.doPost(url, params, config.getConnectTimeout(), config.getReadTimeout());
      JSONObject result = JSON.parseObject(response);

      // 解析响应
      if (Objects.equals(result.getInteger(STATUS_KEY), SUCCESS_CODE)) {
        return DistRefundResultNotifyResponse.builder()
            .success(true)
            .status(SUCCESS_CODE)
            .message(result.getString(MESSAGE_KEY))
            .build();
      } else {
        return DistRefundResultNotifyResponse.builder()
            .success(false)
            .status(result.getInteger(STATUS_KEY))
            .message(result.getString(MESSAGE_KEY))
            .build();
      }
    } catch (Exception e) {
      log.error("大润发退款结果通知异常: {}", e.getMessage(), e);
      return DistRefundResultNotifyResponse.builder()
          .success(false)
          .status(500)
          .message("大润发退款结果通知异常: " + e.getMessage())
          .build();
    }
  }

  @Override
  public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
    // 大润发只支持通过分销订单号查询，提供特殊的参数构建逻辑和响应处理逻辑
    return doQueryOrderDetail(request,
        // 参数构建器：只使用分销订单号进行查询
        (params, req) -> {
          if (StringUtils.isNotBlank(req.getDistOrderNo())) {
            params.put("orderNo", req.getDistOrderNo());
          }
        },
        // 响应处理器：处理大润发特有的响应字段
        (result, channelName) -> {
          JSONObject data = result.getJSONObject("data");
          JSONObject order = data.getJSONObject("order");

          return DistOrderDetailQueryResponse.builder()
              .success(true)
              .distOrderNo(order.getString("order_no"))
              .outOrderNo(order.getString("store_order_no"))
              .tradeAmount(new BigDecimal(order.getString("total_amount"))) // 大润发使用total_amount字段
              .payStatus(order.getString("pay_status"))
              .orderStatus(order.getString("order_status"))
              .createTime(order.getString("create_time"))
              .build();
        });
  }
}
