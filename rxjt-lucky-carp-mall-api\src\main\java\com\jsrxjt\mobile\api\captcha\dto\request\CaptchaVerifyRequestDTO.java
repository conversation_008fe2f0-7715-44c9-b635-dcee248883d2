package com.jsrxjt.mobile.api.captcha.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 验证码校验请求DTO
 * 
 * <AUTHOR>
 * @since 2025-12-04
 */
@Data
@Schema(description = "验证码校验请求")
public class CaptchaVerifyRequestDTO extends BaseParam {

    @Schema(description = "验证码类型", example = "blockPuzzle")
    @NotBlank(message = "验证码类型不能为空")
    private String captchaType;

    @Schema(description = "验证码token", example = "abc123def456")
    @NotBlank(message = "验证码token不能为空")
    private String token;

    @Schema(description = "坐标信息")
    @NotBlank(message = "坐标信息不能为空")
    private String coordinates;

    @Schema(description = "浏览器信息,前端不用传")
    private String browserInfo;

}
