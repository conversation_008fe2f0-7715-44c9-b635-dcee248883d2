package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 扫码提货的付款码请求参数
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "扫码提货的付款码请求参数")
public class PickPaymentCodeNotTimeRequestDTO {
    @Schema(description = "第三方商户id，提货券分销平台提供", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "第三方商户id不能为空")
    private String thirdId;
    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "客户id不能为空")
    private Long customerId;
    @Schema(description = "应用id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "应用id不能为空")
    private Long appId;

}
