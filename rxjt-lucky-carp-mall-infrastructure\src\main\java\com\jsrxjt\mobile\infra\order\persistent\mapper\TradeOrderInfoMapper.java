package com.jsrxjt.mobile.infra.order.persistent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.TradeOrderInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交易订单信息表Mapper
 * <AUTHOR>
 * @since 2025/8/15
 */
@Mapper
public interface TradeOrderInfoMapper extends BaseMapper<TradeOrderInfoPO> {

    /**
     * 根据订单号和交易号查询交易信息列表
     */
    List<TradeOrderInfoPO> selectByTradeNoAndOrderNo(@Param("tradeNo") String tradeNo, @Param("orderNo") String orderNo);

}