package com.jsrxjt.mobile.api.enums;

/**
 * 订单来源渠道枚举
 */
public enum OrderChannelEnum {
   // 订单对接渠道 0 未知 1 卡管 2 支付宝红包 3 苏西话费 4 分销中心 5 本地生活 6 扫码付 7 线下扫码 8 瑞祥全球购 9 便利蜂 10逸刻 11屈臣氏
   UNKNOWN(0, "未知"),
    CARD_MANAGEMENT(1, "卡管"),
    ALIPAY_RED_ENVELOPE(2, "支付宝红包"),
    SUXI_TELECOM(3, "苏西话费"),
    DISTRIBUTION_CENTER(4, "分销中心"),
    LOCAL_LIFE(5, "本地生活"),
    SCAN_PAY(6, "展码付"),
    OFFLINE_SCAN(7, "线下扫码"),
    RX_GLOBAL_MALL(8, "瑞祥全球购"),
    BIAN_LI_FENG(9,"便利蜂"),
    PHYSICAL_CARD_MALL(10, "实体卡商城"),
    XCY_MALL(11, "祥采云商城");

    private final Integer code;
    private final String description;

    OrderChannelEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据渠道编码获取描述信息
     */
    public static String getDescriptionByCode(Integer code) {
        for (OrderChannelEnum channel : OrderChannelEnum.values()) {
            if (channel.getCode().equals(code)) {
                return channel.getDescription();
            }
        }
        return null;
    }
}
