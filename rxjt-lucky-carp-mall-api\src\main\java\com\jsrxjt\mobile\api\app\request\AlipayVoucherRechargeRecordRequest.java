package com.jsrxjt.mobile.api.app.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.YearMonth;

/**
 * 充值记录请求参数
 *
 * <AUTHOR>
 * @Date 2025/8/5
 */
@Data
public class AlipayVoucherRechargeRecordRequest extends BaseParam {

    @Schema(description = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    @Schema(description = "年-月份，格式：yyyy-MM，不传默认查当月数据")
    @DateTimeFormat(pattern = "yyyy-MM")
    private YearMonth yearMonth;

    public YearMonth getYearMonth() {
        return yearMonth != null ? yearMonth : YearMonth.now(); // 默认当前年月
    }
}
