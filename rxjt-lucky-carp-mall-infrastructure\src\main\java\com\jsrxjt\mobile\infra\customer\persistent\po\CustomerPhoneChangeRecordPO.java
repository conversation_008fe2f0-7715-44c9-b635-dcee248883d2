package com.jsrxjt.mobile.infra.customer.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("customer_phone_change_record")
@Schema(name = "CustomerPhoneChangeRecord", description = "用户手机号变更记录表")
public class CustomerPhoneChangeRecordPO {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "变更记录id")
    private Long id;

    @Schema(description = "用户id")
    private Long customerId;

    @Schema(description = "变更方式 1用户主动修改 2后台修改 3用户主动注销")
    private Integer changeMethod;

    @Schema(description = "原手机号")
    private String originalPhone;

    @Schema(description = "新手机号")
    private String newPhone;

    @Schema(description = "变更时间")
    private Date createTime;

    @Schema(description = "变更操作人id")
    private Long createId;
}
