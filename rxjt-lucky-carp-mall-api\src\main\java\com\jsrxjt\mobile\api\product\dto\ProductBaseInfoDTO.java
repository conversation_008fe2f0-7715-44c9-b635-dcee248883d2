package com.jsrxjt.mobile.api.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 产品基本信息DTO
 * 
 * <AUTHOR>
 * @since 2025/5/12
 */
@Data
@Schema(description = "产品基本信息")
public class ProductBaseInfoDTO {

    @Schema(description = "id 产品SPUID和类型的组合")
    private String id;

    @Schema(description = "产品SPUID")
    private String spuId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品副标题")
    private String subTitle;

    @Schema(description = "品牌ID")
    private String brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "产品类型")
    private Integer productType;

    @Schema(description = "扁平化的产品类型")
    private Integer flatProductType;

    @Schema(description = "产品状态")
    private Integer status;

    @Schema(description = "产品一级分类ID")
    private String firstCategoryId;

    @Schema(description = "产品二级分类ID")
    private String secondCategoryId;

    @Schema(description = "产品分类名称")
    private String productCategoryName;

    @Schema(description = "产品Logo图片地址")
    private String productLogo;

    @Schema(description = "产品在分类页的显示名称")
    private String categoryDisplayName;

    @Schema(description = "是否在分类页显示")
    private Boolean showCategory;

    /**
     * 产品角标ID
     */
    @Schema(description = "产品角标ID")
    private Long subscriptId;
    /**
     * 产品角标URL
     */
    @Schema(description = "产品角标URL")
    private String subscriptUrl;

    /**
     * 应用标签
     */
    @Schema(description = "应用标签")
    private String appFlag;

    /**
     * 微信公众号原始ID
     */
    @Schema(description = "微信公众号原始ID")
    private String ghId;



    /**
     * 微信小程序appID
     */
    @Schema(description = "微信小程序appID")
    private String miniAppId;

    /**
     * 活动标签文案
     */
    @Schema(description = "活动标签文案")
    private String labelCopy;

    /**
     * 活动标签文案背景图
     */
    @Schema(description = "活动标签文案背景图")
    private String labelCopyBackgroundImg;

    /**
     * 活动角标图片地址
     */
    @Schema(description = "活动角标图片地址")
    private String activitySubscriptUrl;
}