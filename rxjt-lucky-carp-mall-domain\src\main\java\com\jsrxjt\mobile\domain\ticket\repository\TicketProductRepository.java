package com.jsrxjt.mobile.domain.ticket.repository;

import com.jsrxjt.mobile.domain.ticket.entity.TicketProductEntity;

import java.util.List;

/**
 * @Description: 优惠券与卡券关联接口
 * @Author: ywt
 * @Date: 2025-08-15 15:33
 * @Version: 1.0
 */
public interface TicketProductRepository {
    List<Long> getTicketsIdBySkuIdAndPrdType(Long productSkUID, Integer productType);
    List<TicketProductEntity> getProductsByTicket(Long ticketId);
}
