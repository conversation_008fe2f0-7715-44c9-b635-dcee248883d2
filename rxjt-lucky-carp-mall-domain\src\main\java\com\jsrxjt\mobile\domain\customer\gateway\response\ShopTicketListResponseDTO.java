package com.jsrxjt.mobile.domain.customer.gateway.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 门店优惠券列表响应DTO
 * @Author: 
 * @Date: 2025-09-03
 * @Version: 1.0
 */
@Data
public class ShopTicketListResponseDTO {
    /**
     * 优惠券ID
     */
    private Long id;
    
    /**
     * 开始时间戳
     */
    private Long stime;
    
    /**
     * 结束时间戳
     */
    private Long etime;
    
    /**
     * 优惠券编号
     */
    private String no;
    
    /**
     * 优惠券面值
     */
    private BigDecimal value;
    
    /**
     * 优惠券标题
     */
    private String title;
    
    /**
     * 优惠券摘要
     */
    private String summary;
    
    /**
     * 开始时间 yyyy-MM-dd
     */
    @JSONField(name = "start_time",format = "yyyy-MM-dd")
    private Date startTime;

    
    /**
     * 结束时间 yyyy-MM-dd
     */
    @JSONField(name = "end_time",format = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 券使用状态 1-未使用 2-已使用 3-过期
     */
    private String tab;

    /**
     * 优惠券使用规则
     */
    private String rule;

    private String logo;
}
