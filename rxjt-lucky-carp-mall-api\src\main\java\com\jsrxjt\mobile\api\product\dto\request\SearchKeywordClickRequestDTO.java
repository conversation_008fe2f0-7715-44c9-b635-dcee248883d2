package com.jsrxjt.mobile.api.product.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 搜索关键词点击请求DTO
 * 
 * <AUTHOR>
 * @since 2025/11/14
 */
@Data
@Schema(description = "搜索关键词点击请求DTO")
public class SearchKeywordClickRequestDTO extends BaseParam {

    /**
     * 搜索关键词ID
     */
    @NotNull(message = "搜索关键词ID不能为空")
    @Schema(description = "搜索关键词ID", example = "1", required = true)
    private Long id;
}
