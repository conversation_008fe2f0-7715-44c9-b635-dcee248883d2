<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.shop.mapper.ShopAgentMapper">

    <sql id="Base_Column_List">
        sa.id,
        sa.shop_agent_id,
        sa.shop_group_id,
        sa.store_id,
        sa.term_id,
        sa.pay_code,
        sa.dynamic_union_switch,
        sa.dynamic_voucher_switch,
        sa.static_code,
        sa.order_type,
        sa.card_select,
        sa.card_select_map,
        sa.is_birth,
        sa.birth_brand_id,
        sa.birth_is_index,
        sa.shb_week_tj_start,
        sa.shb_auth,
        sa.verification_module,
        sa.cashier_type,
        sa.tj_show,
        sa.token,
        sa.erp_agent_no,
        sa.huifu_sn,
        sa.yly_key,
        sa.yly_mch_id,
        sa.jh_md,
        sa.refund_pre_day_price_limit,
        sa.refund_day_limit,
        sa.show_shop_switch,
        sa.flq_pay_type,
        sa.pay_type_select,
        sa.config_lock,
        sa.pay_mode,
        sa.wechat_pay,
        sa.service_type,
        sa.service_rate,
        sa.created_at,
        sa.updated_at,
        sa.deleted_at
    </sql>

    <!--<select id="getScreenListByRegionId" resultType="com.jsrxjt.mobile.infra.shop.po.ShopAgentPO">
        SELECT sa.*,
               ROUND(
                    6371 * 2 * ATAN2(
                        SQRT(
                            POW(SIN((params.lat_rad - RADIANS(sa.lat)) / 2), 2) +
                            COS(params.lat_rad) * COS(RADIANS(sa.lat)) *
                            POW(SIN((params.lng_rad - RADIANS(sa.lng)) / 2), 2)
                        ),
                        SQRT(1 - (
                            POW(SIN((params.lat_rad - RADIANS(sa.lat)) / 2), 2) +
                            COS(params.lat_rad) * COS(RADIANS(sa.lat)) *
                            POW(SIN((params.lng_rad - RADIANS(sa.lng)) / 2), 2)
                        ))
                    ), 2
                   ) AS distance
        FROM shop_agent sa
        INNER JOIN shop_agent_config sac ON sa.id = sac.shop_agent_id AND sac.pay_code = 'Y' AND sac.order_type = 'compay' AND sac.deleted_at IS NULL
        CROSS JOIN (SELECT RADIANS(#{lat}) AS lat_rad, RADIANS(#{lng}) AS lng_rad) AS params
        WHERE sa.city_code in
            <foreach collection="adcodeList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
            AND sa.deleted_at IS null
            AND sa.lng != ''
            AND sa.lat != ''
            AND sa.lng REGEXP '^[-+]?([0-9]*\\.[0-9]+|[0-9]+)$'
            AND sa.lat REGEXP '^[-+]?([0-9]*\\.[0-9]+|[0-9]+)$'
        ORDER BY
            distance ASC
            LIMIT 3;
    </select>-->

    <!--<select id="getScreenListByRegionId" resultType="com.jsrxjt.mobile.infra.shop.po.ShopAgentPO">
        SET @user_lng = #{lng};
        SET @user_lat = #{lat};
        SELECT sa.*, ROUND(ST_Distance_Sphere(POINT(sa.lng, sa.lat), POINT(@user_lng, @user_lat)) / 1000, 2) AS distance
        FROM shop_agent sa INNER JOIN shop_agent_config sac ON sa.id = sac.shop_agent_id AND sac.pay_code = 'Y' AND sac.order_type = 'compay' AND sac.deleted_at IS NULL
        WHERE sa.city_code in
            <foreach collection="adcodeList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
            AND sa.deleted_at IS null
            AND sa.lng != ''
            AND sa.lat != ''
            AND sa.lng REGEXP '^[-+]?([0-9]*\\.[0-9]+|[0-9]+)$'
            AND sa.lat REGEXP '^[-+]?([0-9]*\\.[0-9]+|[0-9]+)$'
        ORDER BY distance ASC
        LIMIT 3;
    </select>-->

    <select id="getCommonShopList" resultType="com.jsrxjt.mobile.infra.shop.po.ShopAgentPO">
        SELECT sa.*, ROUND(ST_Distance_Sphere(POINT(sa.lng, sa.lat), POINT(#{lng}, #{lat}))/ 1000, 2) AS distance
        FROM shop_agent sa
        INNER JOIN shop_agent_config sac ON sa.id = sac.shop_agent_id AND sac.pay_code = 'Y' AND sac.deleted_at IS NULL
        WHERE sa.city_code in
        <foreach collection="adcodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND (sa.show_special = 'Y' OR sa.show_pick = 'Y')
        AND sa.deleted_at IS null
        AND sa.lng IS NOT NULL
        AND sa.lat IS NOT NULL
        AND sa.lng != ''
        AND sa.lat != ''
        ORDER BY distance ASC
        LIMIT 3;
    </select>
</mapper>
