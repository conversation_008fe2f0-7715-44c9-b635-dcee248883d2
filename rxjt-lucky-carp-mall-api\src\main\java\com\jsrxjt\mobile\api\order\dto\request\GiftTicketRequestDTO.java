package com.jsrxjt.mobile.api.order.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 赠送券请求DTO
 * 
 * <AUTHOR>
 * @since 2025/1/20
 */
@Data
@Schema(description = "赠送券请求")
public class GiftTicketRequestDTO {

    /**
     * 券ID
     */
    @NotNull(message = "券ID不能为空")
    @Schema(description = "券ID", example = "123456")
    private Long ticketId;

    /**
     * 券数量
     */
    @NotNull(message = "券数量不能为空")
    @Min(value = 1, message = "券数量必须大于0")
    @Schema(description = "券数量", example = "2")
    private Integer ticketNum;
}