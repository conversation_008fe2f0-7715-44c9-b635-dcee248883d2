package com.jsrxjt.mobile.api.user.types;

/**
 * <AUTHOR>
 * Created by 9:42 AM 2019/3/4
 */
public enum UserAuthStatusEnum {
    NOT_AUTH(1,"未认证"),
    IN_AUTH(2,"认证中"),
    SUCCEED_AUTH(3,"已认证"),
    FAILED_AUTH(4,"认证失败");

    UserAuthStatusEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }

    private int code;
    private String text;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
