package com.jsrxjt.mobile.biz.ticket;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.coupon.dto.request.TicketConsumeStatusNotifyRequestDTO;
import com.jsrxjt.mobile.api.coupon.dto.request.TicketStatusNotifyRequestDTO;

/**
 * @Description: 优惠券
 * @Author: ywt
 * @Date: 2025-10-15 15:16
 * @Version: 1.0
 */
public interface TicketCaseService {
    BaseResponse callback(TicketStatusNotifyRequestDTO requestDTO);
    BaseResponse consumeCallback(TicketConsumeStatusNotifyRequestDTO requestDTO);
}
