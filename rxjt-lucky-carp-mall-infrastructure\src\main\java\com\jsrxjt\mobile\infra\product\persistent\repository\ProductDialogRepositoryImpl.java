package com.jsrxjt.mobile.infra.product.persistent.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.product.repository.ProductDialogRepository;
import com.jsrxjt.mobile.infra.product.persistent.mapper.ProductDialogMapper;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductDialogPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 核销页弹框
 * <AUTHOR>
 * @date 2025/09/12
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ProductDialogRepositoryImpl implements ProductDialogRepository {

    private final ProductDialogMapper productDialogMapper;

    @Override
    public String getDialog(Long productSpuId, Integer productType, Integer dialogType) {
        List<ProductDialogPO> list = productDialogMapper.selectList(new LambdaQueryWrapper<ProductDialogPO>()
                .eq(ProductDialogPO::getProductSpuId, productSpuId)
                .eq(ProductDialogPO::getProductType, productType)
                .eq(ProductDialogPO::getDialogType, dialogType)
                .eq(ProductDialogPO::getDelFlag, 0));
        if (CollectionUtils.isNotEmpty(list)){
            return list.get(0).getDialogContent();
        }
        return null;
    }

    @Override
    public String getDialogWithDel(Long productSpuId, Integer productType, Integer dialogType) {
        List<ProductDialogPO> list = productDialogMapper.selectDialogWithDel(productSpuId, productType, dialogType);
        if (CollectionUtils.isNotEmpty(list)){
            return list.get(0).getDialogContent();
        }
        return null;
    }
}
