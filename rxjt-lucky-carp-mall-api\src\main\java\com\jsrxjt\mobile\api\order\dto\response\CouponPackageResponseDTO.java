package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CouponPackageResponseDTO {

    @Schema(description = "卡包id")
    private Long couponPackageId;

    @Schema(description = "卡券名称")
    private String couponName;

    @Schema(description = "卡券面值")
    private BigDecimal amount;

    @Schema(description = "兑换时间")
    private Date createTime;

    @Schema(description = "福鲤圈的核销类型，1:面值+兑换码 2:面值+兑换码+一维二维码 3:面值+卡号+一维二维码  4面值+卡号卡密 5面值+卡号卡密+兑换码 6面值+卡号卡密+一维二维码 7面值+卡密+一维二维码 8面值+链接 9自发券(面值+提货券形式) 10跳转品牌方h5核销页")
    private Integer flqType;

    @Schema(description = "卡券卡号")
    private String couponCode;

    @Schema(description = "卡券卡密")
    private String couponPin;

    @Schema(description = "卡券crc")
    private String couponCrc;

    @Schema(description = "卡券兑换链接")
    private String couponUrl;

    @Schema(description = "卡券兑换短链接密码")
    private String couponUrlPass;

    @Schema(description = "卡管平台卡券核销类型")
    private Integer couponVerificationType;
}
