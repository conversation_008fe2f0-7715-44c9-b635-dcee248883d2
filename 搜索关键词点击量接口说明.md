# 搜索关键词点击量增加接口说明

## 接口概述

该接口用于增加搜索关键词的点击量，每次调用将指定关键词的点击量加1。

## 接口信息

- **接口路径**: `POST /v1/product-search/keyword-click`
- **接口描述**: 增加搜索关键词点击量
- **认证方式**: 需要签名验证 (`@VerifySign`)

## 请求参数

### 请求体 (JSON)

```json
{
    "id": 1
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| id | Long | 是 | 搜索关键词ID | 1 |

## 响应结果

### 成功响应

```json
{
    "code": "200",
    "message": "操作成功",
    "data": null,
    "success": true
}
```

### 失败响应

```json
{
    "code": "500",
    "message": "增加搜索关键词点击量失败",
    "data": null,
    "success": false
}
```

## 业务逻辑

1. **参数校验**: 检查关键词ID是否为空
2. **数据库更新**: 使用原子性SQL操作 `click_num = click_num + 1`
3. **异常处理**: 捕获并处理数据库操作异常
4. **日志记录**: 记录操作过程和结果

## 数据库影响

- **表名**: `search_detail`
- **字段**: `click_num` (点击量字段)
- **操作**: 指定ID记录的点击量加1

## 错误处理

| 错误场景 | 处理方式 |
|----------|----------|
| ID为空 | 记录警告日志，直接返回 |
| 记录不存在 | 数据库操作返回0，记录警告日志 |
| 数据库异常 | 抛出RuntimeException，记录错误日志 |

## 使用示例

### cURL 请求示例

```bash
curl -X POST "http://localhost:8080/v1/product-search/keyword-click" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{"id": 1}'
```

### JavaScript 请求示例

```javascript
fetch('/v1/product-search/keyword-click', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        id: 1
    })
})
.then(response => response.json())
.then(data => console.log(data));
```

## 注意事项

1. **并发安全**: 使用数据库级别的原子操作，避免并发问题
2. **性能考虑**: 操作简单高效，适合高频调用
3. **数据一致性**: 确保点击量数据的准确性
4. **监控建议**: 建议监控接口调用频率和成功率

## 相关接口

- `POST /v1/product-search/default-keywords` - 获取默认搜索词列表
- `POST /v1/product-search/suggestions` - 获取搜索建议
- `POST /v1/product-search/search` - 产品搜索接口
