package com.jsrxjt.mobile.api.exception;

/**
 * 分销API异常类
 */
public class DistributionApiException extends RuntimeException {

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 构造方法
     *
     * @param message 错误信息
     */
    public DistributionApiException(String message) {
        super(message);
    }

    /**
     * 构造方法
     *
     * @param message 错误信息
     * @param cause   异常原因
     */
    public DistributionApiException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造方法
     *
     * @param errorCode 错误码
     * @param message   错误信息
     */
    public DistributionApiException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造方法
     *
     * @param errorCode 错误码
     * @param message   错误信息
     * @param cause     异常原因
     */
    public DistributionApiException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public String getErrorCode() {
        return errorCode;
    }
}