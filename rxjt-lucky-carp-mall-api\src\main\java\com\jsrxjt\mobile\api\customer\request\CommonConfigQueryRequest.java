package com.jsrxjt.mobile.api.customer.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(name="CommonConfigQueryRequest", description="通用配置查询参数")
public class CommonConfigQueryRequest {

    @Schema(description = "配置类型 4用户修改手机号限制次数 5账户注销次数 6:注销后再次注册间隔 8:白金卡账户微信充值限额 9:黑金卡账户微信充值限额")
    @NotNull(message = "配置类型类型不能为空")
    private Integer configType;
}