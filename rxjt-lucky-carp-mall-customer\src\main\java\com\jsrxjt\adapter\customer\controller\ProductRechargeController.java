package com.jsrxjt.adapter.customer.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.product.dto.request.AccountRechargeRecordRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.DelAccountHistoryRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.RechargeAccountHistoryRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.RechargeAccountHistoryResponseDTO;
import com.jsrxjt.mobile.api.product.dto.response.RechargeRecordResponse;
import com.jsrxjt.mobile.biz.product.service.ProductRechargeCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 产品充值相关接口
 * @Author: ywt
 * @Date: 2025-08-07 11:29
 * @Version: 1.0
 */
@RestController
@RequestMapping("/v1/recharge")
@Slf4j
@RequiredArgsConstructor
@Tag(name = "历史充值账户", description = "历史充值账户")
public class ProductRechargeController {
    private final ProductRechargeCaseService productRechargeCaseService;

    @PostMapping("/getHisRechargeAccounts")
    @Operation(summary = "获取历史充值账户，最多返回一条记录")
    @VerifySign(hasToken = true)
    public BaseResponse<List<RechargeAccountHistoryResponseDTO>> getHisRechargeAccounts(@RequestBody @Valid RechargeAccountHistoryRequestDTO requestDTO) {
        return BaseResponse.succeed(productRechargeCaseService.getHisRechargeAccounts(requestDTO));
    }

    @PostMapping("/DelHisRechargeAccount")
    @Operation(summary = "删除历史充值账户")
    @VerifySign(hasToken = true)
    public BaseResponse DelHisRechargeAccount(@RequestBody @Valid DelAccountHistoryRequestDTO requestDTO) {
        int res = productRechargeCaseService.DelHisRechargeAccount(requestDTO);
        if (res <= 0) {
            return BaseResponse.fail("删除失败");
        }
        return BaseResponse.succeed();
    }

    @PostMapping("/rechargeRecords")
    @Operation(summary = "产品的充值记录")
    @VerifySign(hasToken = true)
    public BaseResponse<PageDTO<RechargeRecordResponse>> getRechargeRecords(@RequestBody @Valid AccountRechargeRecordRequestDTO requestDTO) {
        return BaseResponse.succeed(productRechargeCaseService.getRechargeRecords(requestDTO));
    }
}
