package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.advertisement.types.AdvertisementTypeEnum;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.contentcenter.types.ContentcenterTypeEnum;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.InformationAggregationResponseDto;
import com.jsrxjt.mobile.api.distribution.dto.response.InformationCatResponseDto;
import com.jsrxjt.mobile.api.distribution.dto.response.InformationEvaResponseDto;
import com.jsrxjt.mobile.api.distribution.dto.response.InformationResponseDto;
import com.jsrxjt.mobile.api.enums.EvaluateStatusEnum;
import com.jsrxjt.mobile.biz.distribution.service.InformationCaseService;
import com.jsrxjt.mobile.domain.advertisement.entity.AdvertisementEntity;
import com.jsrxjt.mobile.domain.advertisement.service.AdvertisementService;
import com.jsrxjt.mobile.domain.contentcenter.service.ContentRegionService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.information.entity.EvaluateEntity;
import com.jsrxjt.mobile.domain.information.entity.InformationCatEntity;
import com.jsrxjt.mobile.domain.information.entity.InformationEntity;
import com.jsrxjt.mobile.domain.information.query.EvaListQuery;
import com.jsrxjt.mobile.domain.information.query.InformationListQuery;
import com.jsrxjt.mobile.domain.information.repository.InformationCatRepository;
import com.jsrxjt.mobile.domain.information.repository.InformationEvaRepository;
import com.jsrxjt.mobile.domain.information.repository.InformationRepository;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 资讯服务
 * @Author: ywt
 * @Date: 2025-06-10 18:58
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class InformationCaseServiceImpl implements InformationCaseService {
    private final InformationRepository informationRepository;
    private final InformationEvaRepository informationEvaRepository;
    private final CustomerRepository customerRepository;
    private final InformationCatRepository informationCatRepository;
    private final AdvertisementService advertisementService;
    private final ContentRegionService contentRegionService;

    @Override
    public InformationAggregationResponseDto infoAggregation(InformationAggregationRequestDto requestDto) {
        InformationAggregationResponseDto responseData = new InformationAggregationResponseDto();
        List<InformationCatEntity> list = informationCatRepository.informationCatList();
        if (CollectionUtils.isNotEmpty(list)) {
            List<InformationCatResponseDto> catResponseDtoList = BeanUtil.copyToList(list, InformationCatResponseDto.class);
            responseData.setCatList(catResponseDtoList);
        }
        List<AdvertisementEntity> advList = advertisementService.getAdvertisementList(AdvertisementTypeEnum.ADV_INFORMATION.getCode(), null);
        if (CollectionUtil.isNotEmpty(advList)) {
            List<AdvertisementInfoDTO> advertisementInfoDTOList = new ArrayList<>();
            advList.forEach(item -> {
                if (item.getIsNationwide() == 1
                        || contentRegionService.isOnlineInRegion(item.getId(), ContentcenterTypeEnum.CONTENT_ADV.getCode(), requestDto.getRegionId())) {
                    AdvertisementInfoDTO advertisementInfoDTO = new AdvertisementInfoDTO();
                    BeanUtils.copyProperties(item, advertisementInfoDTO);
                    advertisementInfoDTOList.add(advertisementInfoDTO);
                }
            });
            responseData.setAdvertisementList(advertisementInfoDTOList);
        }
        return responseData;
    }

    @Override
    public List<InformationCatResponseDto> catList() {
        List<InformationCatEntity> list = informationCatRepository.informationCatList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<AdvertisementEntity> advList = advertisementService.getAdvertisementList(AdvertisementTypeEnum.ADV_INFORMATION.getCode(), null);
        return BeanUtil.copyToList(list, InformationCatResponseDto.class);
    }

    @Override
    public List<InformationResponseDto> topInformationList(InformationTopListRequestDto requestDto) {
        List<InformationEntity> list = informationRepository.topInformationList(requestDto.getCatId(), requestDto.getRegionId());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(list, InformationResponseDto.class);
    }

    @Override
    public PageDTO<InformationResponseDto> informationList(InformationListRequestDto requestDto) {
        List<InformationResponseDto> list = new ArrayList<>();
        InformationListQuery query = InformationListQuery.builder()
                .catId(requestDto.getCatId())
                .pageNum(requestDto.getPageNum())
                .pageSize(requestDto.getPageSize())
                .regionId(requestDto.getRegionId())
                .build();
        PageDTO<InformationEntity> informationPage = informationRepository.informationList(query);
        if (Objects.nonNull(informationPage) && CollectionUtil.isNotEmpty(informationPage.getRecords())) {
            list = BeanUtil.copyToList(informationPage.getRecords(), InformationResponseDto.class);
        }
        return PageDTO.<InformationResponseDto>builder()
                .records(list)
                .total(informationPage.getTotal())
                .current(informationPage.getCurrent())
                .size(informationPage.getSize())
                .build();
    }

    @Override
    public List<InformationResponseDto> searchInformations(InformationSearchRequestDto requestDto) {
        List<InformationEntity> list = informationRepository.searchInformations(requestDto.getKeywrod());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<InformationResponseDto> responseDtoList = new ArrayList<>();
        //这里使用遍历的方式过滤资讯，如果查询到的资讯很多可能有性能瓶颈
        list.forEach(item -> {
            if (item.getIsNationwide() == 1 ||
                    contentRegionService.isOnlineInRegion(item.getInformationId().longValue(), ContentcenterTypeEnum.CONTENT_INFO.getCode(), requestDto.getRegionId())) {
                InformationResponseDto responseDto = new InformationResponseDto();
                BeanUtil.copyProperties(item, responseDto);
                responseDtoList.add(responseDto);
            }
        });

        return responseDtoList;
    }

    @Override
    public List<InformationResponseDto> suggestInfoList(InformationAggregationRequestDto requestDto) {
        List<InformationEntity> list = informationRepository.suggestInfoList(requestDto.getRegionId());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
//        return BeanUtil.copyToList(list, InformationResponseDto.class);
        List<InformationResponseDto> responseDtoList = new ArrayList<>();
        //这里使用遍历的方式过滤资讯，如果查询到的资讯很多可能有性能瓶颈
        list.forEach(item -> {
            if (item.getIsNationwide() == 1 ||
                    contentRegionService.isOnlineInRegion(item.getInformationId().longValue(), ContentcenterTypeEnum.CONTENT_INFO.getCode(), requestDto.getRegionId())) {
                InformationResponseDto responseDto = new InformationResponseDto();
                BeanUtil.copyProperties(item, responseDto);
                responseDtoList.add(responseDto);
            }
        });

        return responseDtoList;
    }

    @Override
    public boolean clickInformation(InformationRequestDto requestDTO) {
        return informationRepository.clickInformation(requestDTO.getInformationId());
    }

    @Override
    public InformationResponseDto infoDetail(InformationRequestDto requestDTO) {
        InformationResponseDto responseDto = new InformationResponseDto();
        InformationEntity entity = informationRepository.infoDetail(requestDTO.getInformationId());
        if (Objects.isNull(entity)) {
            return null;
        }
        BeanUtils.copyProperties(entity, responseDto);
        return responseDto;
    }

    @Override
    public boolean forwardInformation(InformationRequestDto requestDTO) {
        return informationRepository.forwardInformation(requestDTO.getInformationId());
    }

    @Override
    public PageDTO<InformationEvaResponseDto> evaList(InformationEvaListRequestDto requestDTO) {
        EvaListQuery query = EvaListQuery.builder()
                .informationId(requestDTO.getInformationId())
                .pageSize(requestDTO.getPageSize())
                .pageNum(requestDTO.getPageNum())
                .build();
        PageDTO<EvaluateEntity> resultPage = informationEvaRepository.evaList(query);
        List<InformationEvaResponseDto> list = BeanUtil.copyToList(resultPage.getRecords(), InformationEvaResponseDto.class);
        return PageDTO.<InformationEvaResponseDto>builder()
                .records(list)
                .total(resultPage.getTotal())
                .current(resultPage.getCurrent())
                .size(resultPage.getSize())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitEvaluate(InformationEvaAddRequestDto requestDTO) {
        Long customerId = StpUtil.getLoginIdAsLong();
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if (Objects.isNull(customerEntity)) {
            throw new BizException("用户不存在");
        }
        EvaluateEntity evaluateEntity = new EvaluateEntity();
        evaluateEntity.setInformationId(requestDTO.getInformationId());
        evaluateEntity.setUserId(customerId);
        evaluateEntity.setContent(requestDTO.getContent());
        evaluateEntity.setAuditStatus(EvaluateStatusEnum.AUDITING.getCode());
        evaluateEntity.setPraiseNum(0);
        evaluateEntity.setPhone(customerEntity.getPhone());
        evaluateEntity.setUserName(customerEntity.getUserName());
        evaluateEntity.setUserUrl(customerEntity.getUserUrl());
        evaluateEntity.setCreateTime(LocalDateTime.now());
        return informationEvaRepository.submitEvaluate(evaluateEntity);
    }

    @Override
    public boolean likeEvaluate(InformationEvaRequestDto requestDTO) {
        return informationEvaRepository.likeEvaluate(requestDTO.getEvaId());
    }
}
