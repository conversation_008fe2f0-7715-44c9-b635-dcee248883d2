package com.jsrxjt.adapter.order.controller;

import com.jsrxjt.common.core.util.ServletUtils;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaGenerateDTO;
import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaRequestDTO;
import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaVerifyRequestDTO;
import com.jsrxjt.mobile.api.captcha.dto.response.CaptchaResponseDTO;
import com.jsrxjt.mobile.api.captcha.dto.response.CaptchaVerifyResponseDTO;
import com.jsrxjt.mobile.biz.captcha.CaptchaCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 验证码控制器
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/v1/captcha")
@RequiredArgsConstructor
@Tag(name = "验证码接口", description = "验证码相关接口")
public class CaptchaController {
    private final CaptchaCaseService captchaCaseService;

    @PostMapping("/generate")
    @Operation(summary = "获取验证码", description = "获取验证码")
    public BaseResponse<CaptchaResponseDTO> getCaptcha(@RequestBody @Valid CaptchaRequestDTO request,
                                                       HttpServletRequest servletRequest) {
        log.info("获取验证码请求：类型={}", request.getCaptchaType());
        CaptchaGenerateDTO generateDTO = new CaptchaGenerateDTO();
        generateDTO.setCaptchaType(request.getCaptchaType());
        generateDTO.setBrowserInfo(getRemoteId(servletRequest));
        CaptchaResponseDTO response = captchaCaseService.getCaptcha(generateDTO);
        return BaseResponse.succeed(response);
    }

    @PostMapping("/verify")
    @Operation(summary = "校验验证码", description = "校验验证码")
    public BaseResponse<CaptchaVerifyResponseDTO> verifyCaptcha(@RequestBody @Valid CaptchaVerifyRequestDTO request,
                                                                HttpServletRequest servletRequest) {
        log.info("校验验证码请求：类型={}, token={}", request.getCaptchaType(), request.getToken());
        request.setBrowserInfo(getRemoteId(servletRequest));
        CaptchaVerifyResponseDTO response = captchaCaseService.verifyCaptcha(request);
        return BaseResponse.succeed(response);
    }

    public static String getRemoteId(HttpServletRequest request) {
        String ip = ServletUtils.getIpAddress(request);
        String ua = ServletUtils.getUserAgent(request);
        return ip + ua;
    }
    

}
