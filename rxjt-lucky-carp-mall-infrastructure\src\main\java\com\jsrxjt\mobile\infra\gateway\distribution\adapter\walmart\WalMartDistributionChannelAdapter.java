package com.jsrxjt.mobile.infra.gateway.distribution.adapter.walmart;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 沃尔玛分销渠道适配器
 *
 * <AUTHOR>
 * @Date 2025/10/16
 */
@Component
@Slf4j
public class WalMartDistributionChannelAdapter extends AbstractDistributionChannelAdapter {

    public WalMartDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                             DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getWalmart();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.WALMART;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            if (StringUtils.isBlank(request.getUserId())) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID不能为空")
                        .build();
            }
            String baseUrl = config.getBaseUrl() + config.getApiPath();
            String url = baseUrl + "?appid=" + config.getAppId() + "&userId=" + request.getUserId();

            return DistAccessResponse.builder()
                    .success(true)
                    .redirectUrl(url)
                    .build();
        } catch (Exception e) {
            log.error("沃尔玛免登接入异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("沃尔玛免登接入异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        try {
            // 校验必填参数
            if (StringUtils.isBlank(request.getDistOrderNo()) || StringUtils.isBlank(request.getOrderNo())
                    || request.getTradeAmount() == null || request.getPayTime() == null) {
                return DistPaidNotifyResponse.builder()
                        .success(false)
                        .status(400)
                        .message("必填参数不能为空")
                        .build();
            }

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("orderNumber", request.getDistOrderNo());
            params.put("thirdOrderSn", request.getOrderNo());
            params.put("payMoney", request.getTradeAmount().toString());
            params.put("status", 1);
            // 转换时间格式，使用传入的格式化器
            params.put("payTime", request.getPayTime().format(DAY_TIME_SPLIT_FORMAT));

            // 添加公共参数和签名
            otherAddCommonParams(params);

            // 发送请求
            String url = config.getBaseUrl() + config.getCashierCallbackPath();
            String response = httpClientGateway.doPostJson(url, JSONUtil.toJsonStr(params), config.getConnectTimeout(),
                    config.getReadTimeout());
            JSONObject result = JSON.parseObject(response);

            // 解析响应
            if (Objects.equals(result.getInteger(STATUS_KEY), 0)) {
                return DistPaidNotifyResponse.builder()
                        .success(true)
                        .status(SUCCESS_CODE)
                        .message(result.getString(MESSAGE_KEY))
                        .build();
            } else {
                return DistPaidNotifyResponse.builder()
                        .success(false)
                        .status(result.getInteger(STATUS_KEY))
                        .message(result.getString(MESSAGE_KEY))
                        .build();
            }
        } catch (Exception e) {
            log.error("{}支付回调通知异常: {}", request.getChannelType().name(), e.getMessage(), e);
            return DistPaidNotifyResponse.builder()
                    .success(false)
                    .status(500)
                    .message(request.getChannelType().name() + "支付回调通知异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        return DistRefundResultNotifyResponse.builder()
                .success(true)
                .status(SUCCESS_CODE)
                .build();
        //        try {
//            // 校验基础必填参数，具体的额外参数校验由调用方自行处理
//            if (StringUtils.isBlank(request.getDistTradeNo()) || StringUtils.isBlank(request.getDistRefundNo()) ||
//                    request.getRefundAmount() == null || request.getStatus() == null) {
//                return DistRefundResultNotifyResponse.builder()
//                        .success(false)
//                        .status(400)
//                        .message("必填参数不能为空")
//                        .build();
//            }
//
//            // 构建请求参数
//            Map<String, Object> params = new HashMap<>();
//
//            // 默认参数构建逻辑
//            params.put("orderNumber", request.getDistTradeNo());
//            params.put("afterSaleNumber", request.getDistRefundNo());
//            params.put("thirdOrderSn", request.getDistRefundNo());
//            params.put("refundAmount", request.getRefundAmount().toString());
//            params.put("refundStatus", request.getStatus() == 10 ? "1": "0");
//
//            // 默认处理退款时间
//            if (request.getRefundTime() != null) {
//                params.put("refundTime", request.getRefundTime().format(DAY_TIME_SPLIT_FORMAT));
//            } else {
//                params.put("refundTime", "");
//            }
//
//            // 添加公共参数和签名
//            addCommonParams(params);
//
//            // 发送请求
//            String url = config.getBaseUrl() + config.getApiPath() + config.getRefundCallbackPath();
//            String response = httpClientGateway.doPost(url, params, config.getConnectTimeout(),
//                    config.getReadTimeout());
//            JSONObject result = JSON.parseObject(response);
//
//            // 解析响应
//            if (Objects.equals(result.getInteger(STATUS_KEY), 0)) {
//                return DistRefundResultNotifyResponse.builder()
//                        .success(true)
//                        .status(SUCCESS_CODE)
//                        .message(result.getString(MESSAGE_KEY))
//                        .build();
//            } else {
//                return DistRefundResultNotifyResponse.builder()
//                        .success(false)
//                        .status(result.getInteger(STATUS_KEY))
//                        .message(result.getString(MESSAGE_KEY))
//                        .build();
//            }
//        } catch (Exception e) {
//            log.error("{}退款结果通知异常: {}", request.getChannelType().name(), e.getMessage(), e);
//            return DistRefundResultNotifyResponse.builder()
//                    .success(false)
//                    .status(500)
//                    .message(request.getChannelType().name() + "退款结果通知异常: " + e.getMessage())
//                    .build();
//        }
    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        return null;
    }
}
