package com.jsrxjt.mobile.biz.payment.strategy;

import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;

/**
 * 支付成功处理策略接口
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/1/27
 */
public interface PaymentSuccessStrategy {

    /**
     * 是否支持该扁平化产品类型
     * 
     * @param flatProductType 扁平化产品类型
     * @return true:支持 false:不支持
     */
    boolean supports(Integer flatProductType);

    /**
     * 处理支付成功后的业务逻辑
     * 
     * @param order 订单信息
     */
    void handle(OrderInfoEntity order);
}