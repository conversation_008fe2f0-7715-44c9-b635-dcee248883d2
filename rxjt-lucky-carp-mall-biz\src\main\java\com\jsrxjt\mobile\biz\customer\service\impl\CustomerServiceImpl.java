package com.jsrxjt.mobile.biz.customer.service.impl;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.CommonUtils;
import com.jsrxjt.common.core.util.SM4DecryptUtil;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.advertisement.types.AdvertisementTypeEnum;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.config.types.SystemConfigTypeEnum;
import com.jsrxjt.mobile.api.contentcenter.types.ContentcenterTypeEnum;
import com.jsrxjt.mobile.api.customer.request.*;
import com.jsrxjt.mobile.api.customer.response.*;
import com.jsrxjt.mobile.api.customer.types.*;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionCustomerInfoRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCustomerBalanceResponseDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCustomerInfoResponseDTO;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.riskcontrol.types.RiskBusinessTypeEnum;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.sms.SmsSendService;
import com.jsrxjt.mobile.domain.advertisement.entity.AdvertisementEntity;
import com.jsrxjt.mobile.domain.advertisement.service.AdvertisementService;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.contentcenter.service.ContentRegionService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.entity.CustomerPaySortEntity;
import com.jsrxjt.mobile.domain.customer.entity.CustomerPhoneChangeRecordEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerPaySortRepository;
import com.jsrxjt.mobile.domain.customer.repository.CustomerPhoneChangeRecordRepository;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.customer.repository.PaymentMethodRepository;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistVerifySignRequest;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.message.repository.MessageDetailRepository;
import com.jsrxjt.mobile.domain.message.repository.MessageServeRepository;
import com.jsrxjt.mobile.domain.order.entity.TradeCardBalanceChangeEntity;
import com.jsrxjt.mobile.domain.order.query.CouponPackageListQuery;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.repository.TradeCardBalanceChangeRepository;
import com.jsrxjt.mobile.domain.payment.gateway.OnlinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.CardInfoRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.CardRechargeRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.CheckFindCardBalanceRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.FindCardBalanceRequest;
import com.jsrxjt.mobile.domain.payment.gateway.response.CardRechargeResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.CheckFindCardBalanceResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.FindCardBalanceResponse;
import com.jsrxjt.mobile.domain.riskcontrol.entity.ProductRiskControlCompanyEntity;
import com.jsrxjt.mobile.domain.riskcontrol.repository.ProductRiskControlCompanyRepository;
import com.jsrxjt.mobile.domain.rxmember.gateway.RxMemberGateway;
import com.jsrxjt.mobile.domain.rxmember.request.RxMemberRequest;
import com.jsrxjt.mobile.domain.rxmember.request.RxMemberRiskRequest;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberCardDetail;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberCardResponse;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberLoginResponse;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberResponse;
import com.jsrxjt.mobile.domain.rxmember.service.RxMemberRiskService;
import com.jsrxjt.mobile.domain.thirdcard.gateway.ThirdCardGateWay;
import com.jsrxjt.mobile.domain.thirdcard.request.CardTradeQueryRequest;
import com.jsrxjt.mobile.domain.thirdcard.response.CardTradeInfoResponse;
import com.jsrxjt.mobile.domain.thirdcard.response.CardTradeQueryResponse;
import com.jsrxjt.mobile.domain.ticket.repository.TicketDeliveryRepository;
import com.jsrxjt.mobile.domain.wechat.gateway.WechatGateway;
import com.jsrxjt.mobile.domain.wechat.request.WechatLoginRequest;
import com.jsrxjt.mobile.domain.wechat.request.WechatUserInfoRequest;
import com.jsrxjt.mobile.domain.wechat.response.WechatLoginResponse;
import com.jsrxjt.mobile.domain.wechat.response.WechatUserInfoResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerServiceImpl implements CustomerService {

    /**
     * 验证码有效期 300s
     */
    private static final int VERIFICATION_CODE_EXPIRE_TIME = 300;

    /**
     * 卡号密码查询卡余额次数
     */
    private static final int CARD_BALANCE_DAILY_QUERY_MAX_COUNT = 3;

    /**
     * 密码错误最大次数
     */
    private static final int PSW_ERROR_MAX_COUNT = 3;

    /**
     * 密码错误锁定时间
     */
    private static final int PSW_LOCK_TIME = 10 * 60;

    @Value("${flq.user.default.avatar}")
    private String userDefaultAvatar;

    @Value("${flq.user.default.name}")
    private String userDefaultName;

    @Value("${sa-token.timeout}")
    private long tokenTimeout;

    /**
     * 红卡前缀
     */
    @Value("${flq.redCardPrefix}")
    private String redCardPrefix;

    /**
     * 不允许绑定的卡bin
     */
    @Value("${flq.notAllowedBindCardBin}")
    private String notAllowedBindCardBin;

    private final WechatGateway wechatGateway;

    private final RxMemberGateway rxMemberGateway;

    private final BusinessIdGenerator businessIdGenerator;

    private final CustomerRepository customerRepository;

    private final CustomerPaySortRepository customerPaySortRepository;

    private final ThirdCardGateWay thirdCardGateWay;

    private final ProductRiskControlCompanyRepository productRiskControlCompanyRepository;

    private final PaymentMethodRepository paymentMethodRepository;

    private final RedisUtil redisUtil;

    private final SmsSendService smsSendService;

    private final ConfigRepository configRepository;

    private final CustomerPhoneChangeRecordRepository customerPhoneChangeRecordRepository;

    private final MessageServeRepository messageServeRepository;

    private final MessageDetailRepository messageDetailRepository;

    private final OrderRepository orderRepository;

    private final AfterSaleRepository afterSaleRepository;

    private final AdvertisementService advertisementService;

    private final ContentRegionService contentRegionService;

    private final OnlinePaymentGateway onlinePaymentGateway;

    private final OrderDeliveryRepository orderDeliveryRepository;

    private final RxMemberRiskService rxMemberRiskService;

    private final TradeCardBalanceChangeRepository tradeCardBalanceChangeRepository;

    private final AppGoodsRepository appGoodsRepository;

    private final UnifiedDistributionApi unifiedDistributionApi;

    private final TicketDeliveryRepository ticketDeliveryRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerResponse login(CustomerLoginRequest request) {
        switch (request.getLoginType()) {
            case 1:
                return wechatMiniProgramLogin(request);
            case 2:
                return wechatAppLogin(request);
            case 3:
                return phoneLogin(request);
            default:
                throw new BizException("不支持的登录方式");
        }
    }

    @Override
    public void sendVerificationCode(CustomerSendVerificationCodeRequest request) {
        // 根据不同类型获取手机号并验证
        String phone = getPhoneByCodeType(request);
        // 生成并发送验证码
        sendCodeToPhone(phone);
    }

    private String getPhoneByCodeType(CustomerSendVerificationCodeRequest request) {
        VerificationCodeSendTypeEnum codeType;
        try {
            codeType = VerificationCodeSendTypeEnum.fromCode(request.getCodeType());
        } catch (IllegalArgumentException e) {
            throw new BizException("不支持的验证码类型");
        }
        return switch (codeType) {
            case REGISTER -> validateAndGetRegisterPhone(request.getPhone());
            case LOGIN -> validateAndGetLoginPhone(request.getPhone());
            case CHANGE_INFO -> validateAndGetChangeInfoPhone(request.getCustomerId());
            case CHANGE_PHONE -> validateAndGetChangePhone(request.getPhone(), request.getCustomerId());
            case DELETE_ACCOUNT -> validateAndGetDeleteAccountPhone(request.getCustomerId());
        };
    }

    private String validateAndGetRegisterPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            throw new BizException("用户手机号码不能为空");
        }
        if (customerRepository.selectCustomerByMobile(phone) != null) {
            throw new BizException("该用户手机号码已被其他用户绑定");
        }
        return phone;
    }

    private String validateAndGetLoginPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            throw new BizException("手机号码不能为空");
        }
        CustomerEntity customer = customerRepository.selectCustomerByMobile(phone);
        if (customer == null) {
            throw new BizException("该手机号未注册，请使用微信登录");
        }
        checkCustomerStatus(customer);
        return customer.getPhone();
    }

    private String validateAndGetChangeInfoPhone(Long customerId) {
        if (customerId == null) {
            throw new BizException("用户id不能为空");
        }
        CustomerEntity customer = customerRepository.selectCustomerById(customerId);
        if (customer == null) {
            throw new BizException("用户不存在");
        }
        return customer.getPhone();
    }

    private String validateAndGetChangePhone(String phone, Long customerId) {
        if (StringUtils.isBlank(phone)) {
            throw new BizException("用户手机号码不能为空");
        }
        if (customerId == null) {
            throw new BizException("用户id不能为空");
        }
        // 校验用户手机号码更换次数
        String modPhoneMaxCountStr = configRepository.getValueByType(SystemConfigTypeEnum.MOD_PHONE_MAX_COUNT_TYPE);
        int modPhoneMaxCount = Integer.parseInt(modPhoneMaxCountStr);
        if (modPhoneMaxCount > 0) {
            long changePhoneCount = customerPhoneChangeRecordRepository.getPhoneChangeRecordCount(customerId);
            if (changePhoneCount >= modPhoneMaxCount) {
                throw new BizException("用户一年内手机号码更换次数已达上限");
            }
        }
        CustomerEntity customer = customerRepository.selectCustomerByMobile(phone);
        if (customer != null) {
            throw new BizException("手机号码已存在");
        }
        return phone;
    }

    private String validateAndGetDeleteAccountPhone(Long customerId) {
        if (customerId == null) {
            throw new BizException("用户id不能为空");
        }
        CustomerEntity customer = customerRepository.selectCustomerById(customerId);
        if (customer == null) {
            throw new BizException(Status.USER_NOT_EXIST);
        }
        String deleteAccountMaxCountStr = configRepository
                .getValueByType(SystemConfigTypeEnum.DELETE_USER_MAX_COUNT_TYPE);
        int deleteAccountMaxCount = Integer.parseInt(deleteAccountMaxCountStr);
        if (deleteAccountMaxCount > 0) {
            long deleteAccountCount = customerRepository.getDeleteAccountCountInCurrentYear(customer.getUnionid());
            if (deleteAccountCount >= deleteAccountMaxCount) {
                throw new BizException(Status.OUT_MAX_DELETE_ACCOUNT_COUNT);
            }
        }
        return customer.getPhone();
    }

    private void sendCodeToPhone(String phone) {
        String redisKey = RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + phone;
        String verificationCode = generateVerificationCode(6);
        log.info("发送验证码，手机号码：{}, 验证码：{}", phone, verificationCode);

        boolean sendResult = smsSendService.sendVerifyCode(phone, verificationCode);
        if (!sendResult) {
            log.error("验证码发送失败，手机号: {}", phone);
            throw new BizException("验证码发送失败");
        }
        redisUtil.set(redisKey, verificationCode, VERIFICATION_CODE_EXPIRE_TIME);
    }

    /**
     * 生成验证码
     *
     * @param length
     * @return
     */
    private String generateVerificationCode(int length) {
        StringBuilder code = new StringBuilder();
        SecureRandom random = new SecureRandom();
        for (int i = 0; i < length; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    /**
     * 校验用户是否允许注册
     *
     * @param unionid
     * @return
     */
    private void checkIsAllowedRegister(String unionid) {
        Date lastLoginOffTime = customerRepository.getLatestDeleteAccountTime(unionid);
        if (lastLoginOffTime != null) {
            String reRegisterIntervalStr = configRepository
                    .getValueByType(SystemConfigTypeEnum.REREGISTER_TIME_INTERVAL_TYPE);
            int interval = Integer.parseInt(reRegisterIntervalStr);
            if (interval > 0) {
                if (lastLoginOffTime.after(DateUtils.addDays(new Date(), -interval))) {
                    throw new BizException(Status.ERROR_REREGISTER_INTERVAL);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerResponse bindPhone(CustomerBindPhoneRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        if (customerEntity == null) {
            throw new BizException("该手机号未注册，请使用微信登录");
        }
        if (StringUtils.isNotBlank(customerEntity.getPhone())) {
            throw new BizException("该用户已绑定手机号码");
        }
        if (customerRepository.selectCustomerByMobile(request.getPhone()) != null) {
            throw new BizException("该用户手机号码已被其他用户绑定");
        }
        String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhone());
        log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
        if (!request.getVerificationCode().equals(code)) {
            throw new BizException("短信验证码不一致");
        }
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setUnionid(customerEntity.getUnionid());
        rxMemberRequest.setMobile(request.getPhone());
        RxMemberResponse rxMemberResponse = rxMemberGateway.bindMobile(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()) {
            throw new BizException(rxMemberResponse.getErrorMessage());
        }
        customerEntity.setPhone(request.getPhone());
        customerRepository.updateCustomer(customerEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhone());
        redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());

        // 上报风控数据
        try {
            RxMemberRiskRequest riskRequest = new RxMemberRiskRequest();
            riskRequest.setTypeEnum(RiskBusinessTypeEnum.LOGIN_TYPE);
            riskRequest.setCustomerId(customerEntity.getId());
            riskRequest.setIP(Objects.nonNull(request.getLoginIp()) ? request.getLoginIp() : "unknown");
            riskRequest.setToken(request.getYdToken());
            riskRequest.setBusinessId(request.getYdBusinessId());
            RxMemberRiskRequest.RegisterOrLogData registerOrLogData = new RxMemberRiskRequest.RegisterOrLogData();
            registerOrLogData.setOperationType("log");
            registerOrLogData.setOpenId(Objects.nonNull(customerEntity.getOpenid()) ? customerEntity.getOpenid()
                    : customerEntity.getMiniOpenid());
            registerOrLogData.setAppChannel("unknown");
            registerOrLogData.setRegisterOrLogChannel("other");
            registerOrLogData.setRegisterOrLogType("phoneAuth");
            riskRequest.setRegisterOrLogData(registerOrLogData);
            rxMemberRiskService.getUserRiskLevel(riskRequest);
        } catch (Exception e) {
            log.error("绑定手机号-注册登录上报易盾失败");
            e.printStackTrace();
        }

        return buildCustomerResponse(customerEntity);
    }

    @Override
    public CustomerInfoResponse getCustomerInfo(Long customerId) {
        CustomerEntity customerEntity = getCustomerById(customerId);
        CustomerInfoResponse customerInfoResponse = new CustomerInfoResponse();
        BeanUtils.copyProperties(customerEntity, customerInfoResponse);
        // 获取用户卡包数量
        CouponPackageListQuery query = CouponPackageListQuery.builder()
                .customerId(customerId)
                .delFlag(0)
                .build();
        customerInfoResponse.setCouponNum(orderDeliveryRepository.getCustomerCouponPackageNum(query));
        // 获取用户优惠券数量
        customerInfoResponse.setTicketNum(ticketDeliveryRepository.countCustomerTicketNum(customerId));
        // 未读消息数量 (客服消息数量+系统消息数量)
        customerInfoResponse.setUnReadMsgNum(
                messageServeRepository.countServeMsg(customerId) + messageDetailRepository.countSysMsg(customerId));
        // 待付款订单数量
        customerInfoResponse.setPendingPaymentOrderNum(
                orderRepository.countOrderByStatus(customerId, OrderStatusEnum.PENDING_PAYMENT));
        // 进行中订单数量
        customerInfoResponse
                .setInProcessOrderNum(orderRepository.countOrderByStatus(customerId, OrderStatusEnum.IN_PROGRESS));
        // 退款/售后订单数量 1-待审核 20-审核通过
        customerInfoResponse.setAfterSaleOrderNum(afterSaleRepository.countAfterSaleByStatus(customerId));
        return customerInfoResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCustomerInfo(CustomerEditRequest request) {
        CustomerEntity customerEntity = getCustomerById(request.getCustomerId());
        if (customerEntity == null) {
            throw new BizException("用户不存在");
        }
        CustomerEntity updateEntity = new CustomerEntity();
        updateEntity.setId(customerEntity.getId());
        switch (request.getEditType()) {
            case 1 -> {
                if (StringUtils.isBlank(request.getUserUrl())) {
                    throw new BizException("请选择头像");
                }
                updateEntity.setUserUrl(request.getUserUrl());
            }
            case 2 -> {
                if (StringUtils.isBlank(request.getUserName())) {
                    throw new BizException("请输入用户昵称");
                }
                updateEntity.setUserName(request.getUserName());
            }
            case 3 ->
                // 性别可以设置为null
                    updateEntity.setSex(request.getSex());
            case 4 -> {
                if (StringUtils.isBlank(request.getPhone()) || StringUtils.isBlank(request.getVerificationCode())) {
                    throw new BizException("手机号码或验证码不能为空");
                }
                String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhone());
                log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
                if (!request.getVerificationCode().equals(code)) {
                    throw new BizException("短信验证码不一致");
                }
                // 更新大会员用户手机号码
                RxMemberRequest rxMemberRequest = new RxMemberRequest();
                rxMemberRequest.setUnionid(customerEntity.getUnionid());
                rxMemberRequest.setMobile(request.getPhone());
                RxMemberResponse rxMemberResponse = rxMemberGateway.changeUserMobile(rxMemberRequest);
                if (!rxMemberResponse.isSuccess()) {
                    throw new BizException(rxMemberResponse.getErrorMessage());
                }
                updateEntity.setPhone(request.getPhone());
                redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhone());
            }
            case 5 -> {
                if (customerEntity.getBirthday() != null) {
                    throw new BizException("用户生日信息已存在，不支持编辑操作");
                } else {
                    updateEntity.setBirthday(request.getBirthday());
                }
            }
            default -> throw new BizException("不支持的编辑类型");
        }
        updateEntity.setModTime(new Date());
        updateEntity.setModId(customerEntity.getId());
        customerRepository.updateCustomerInfoById(updateEntity);
        if (request.getEditType() == 4) {
            CustomerPhoneChangeRecordEntity customerPhoneChangeRecordEntity = new CustomerPhoneChangeRecordEntity();
            customerPhoneChangeRecordEntity.setCustomerId(customerEntity.getId());
            customerPhoneChangeRecordEntity.setOriginalPhone(customerEntity.getPhone());
            customerPhoneChangeRecordEntity.setNewPhone(request.getPhone());
            customerPhoneChangeRecordEntity.setChangeMethod(CustomerPhoneChangeMethod.USER_ACTIVE.getCode());
            customerPhoneChangeRecordEntity.setCreateTime(new Date());
            customerPhoneChangeRecordEntity.setCreateId(customerEntity.getId());
            customerPhoneChangeRecordRepository.savePhoneChangeRecord(customerPhoneChangeRecordEntity);
        }
        redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrModifyPassword(CustomerPasswordEditRequest request) {
        CustomerEntity customerEntity = getCustomerById(StpUtil.getLoginIdAsLong());
        if (customerEntity == null) {
            throw new BizException("用户不存在");
        }
        if (!CommonUtils.checkPassword(request.getNewPassword())) {
            throw new BizException(Status.ERROR_PASSWORD_FORMAT);
        }
        if (!StringUtils.isEmpty(customerEntity.getPassword())) {
            String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + customerEntity.getPhone());
            log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
            if (!request.getVerificationCode().equals(code)) {
                throw new BizException("短信验证码不一致");
            }
        }
        CustomerEntity updateEntity = new CustomerEntity();
        updateEntity.setId(customerEntity.getId());
        updateEntity.setPassword(new BCryptPasswordEncoder().encode(request.getNewPassword()));
        updateEntity.setModId(customerEntity.getId());
        updateEntity.setModTime(new Date());
        customerRepository.updateCustomer(updateEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + customerEntity.getPhone());
        redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());
    }

    @Override
    public void setFreePassword(CustomerSetFreePasswordRequest request) {
        CustomerEntity customerEntity = getCustomerById(request.getCustomerId());
        if (StringUtils.isEmpty(customerEntity.getPassword())) {
            throw new BizException(Status.PAYMENT_PASSWORD_NOT_SET);
        }
        if (request.getOpenFreePayment() == 1 && StringUtils.isEmpty(request.getPassword())) {
            throw new BizException("请输入支付密码");
        }
        // 校验用户支付密码
        if (request.getOpenFreePayment() == 1
                && !new BCryptPasswordEncoder().matches(request.getPassword(), customerEntity.getPassword())) {
            throw new BizException(Status.PASSWORD_ERROR);
        }
        CustomerEntity updateEntity = new CustomerEntity();
        updateEntity.setId(customerEntity.getId());
        updateEntity.setOpenPasswordFreePayment(request.getOpenFreePayment());
        updateEntity.setModId(customerEntity.getId());
        updateEntity.setModTime(new Date());
        customerRepository.updateCustomer(updateEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());
    }

    private CustomerResponse wechatMiniProgramLogin(CustomerLoginRequest request) {
        if (StringUtils.isBlank(request.getWxCode())) {
            throw new BizException("微信code不能为空");
        }
        WechatLoginRequest wechatLoginRequest = new WechatLoginRequest();
        wechatLoginRequest.setWxCode(request.getWxCode());
        WechatLoginResponse wechatLoginResponse = wechatGateway.miniLogin(wechatLoginRequest);
        if (wechatLoginResponse == null) {
            throw new BizException("微信小程序登录失败");
        }
        CustomerEntity customerEntity = processCustomer(wechatLoginResponse, CustomerLoginTypeEnum.MINI_LOGIN.getType(),
                request.getYdBusinessId(), request.getYdToken(), request.getLoginIp());
        return buildCustomerResponse(customerEntity);
    }

    private CustomerResponse wechatAppLogin(CustomerLoginRequest request) {
        if (StringUtils.isBlank(request.getWxCode())) {
            throw new BizException("微信code不能为空");
        }
        WechatLoginRequest wechatLoginRequest = new WechatLoginRequest();
        wechatLoginRequest.setWxCode(request.getWxCode());
        WechatLoginResponse wechatLoginResponse = wechatGateway.appLogin(wechatLoginRequest);
        if (wechatLoginResponse == null) {
            throw new BizException("微信app授权登录失败");
        }
        CustomerEntity customerEntity = processCustomer(wechatLoginResponse, CustomerLoginTypeEnum.APP_LOGIN.getType(),
                request.getYdBusinessId(), request.getYdToken(), request.getLoginIp());
        return buildCustomerResponse(customerEntity);
    }

    private CustomerResponse phoneLogin(CustomerLoginRequest request) {
        if (StringUtils.isBlank(request.getPhoneCode()) || StringUtils.isBlank(request.getVerificationCode())) {
            throw new BizException("手机号码或验证码不能为空");
        }
        String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhoneCode());
        log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
        if (!request.getVerificationCode().equals(code)) {
            throw new BizException(Status.ERROR_VALIDATE_CODE);
        }
        CustomerEntity customerEntity = customerRepository.selectCustomerByMobile(request.getPhoneCode());
        if (customerEntity == null) {
            throw new BizException("用户未注册，请使用微信登录");
        }
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setUnionid(customerEntity.getUnionid());
        if (StringUtils.isEmpty(customerEntity.getOpenid())) {
            rxMemberRequest.setOpenid(customerEntity.getMiniOpenid());
        } else {
            rxMemberRequest.setOpenid(customerEntity.getOpenid());
        }
        RxMemberResponse rxMemberResponse = rxMemberGateway.login(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()) {
            throw new BizException("登录失败");
        }
        customerEntity.setLoginTime(new Date());
        customerRepository.updateLoginInfo(customerEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhoneCode());

        // 上报风控数据
        try {
            RxMemberRiskRequest riskRequest = new RxMemberRiskRequest();
            riskRequest.setTypeEnum(RiskBusinessTypeEnum.LOGIN_TYPE);
            riskRequest.setCustomerId(customerEntity.getId());
            riskRequest.setIP(Objects.nonNull(request.getLoginIp()) ? request.getLoginIp() : "unknown");
            riskRequest.setToken(request.getYdToken());
            riskRequest.setBusinessId(request.getYdBusinessId());
            RxMemberRiskRequest.RegisterOrLogData registerOrLogData = new RxMemberRiskRequest.RegisterOrLogData();
            registerOrLogData.setOperationType("log");
            registerOrLogData.setOpenId(rxMemberRequest.getOpenid());
            registerOrLogData.setAppChannel("unknown");
            registerOrLogData.setRegisterOrLogChannel("other");
            registerOrLogData.setRegisterOrLogType("phoneAuth");
            riskRequest.setRegisterOrLogData(registerOrLogData);
            rxMemberRiskService.getUserRiskLevel(riskRequest);
        } catch (Exception e) {
            log.error("注册登录上报易盾失败");
            e.printStackTrace();
        }

        return buildCustomerResponse(customerEntity);
    }

    private CustomerEntity processCustomer(WechatLoginResponse wechatLoginResponse, int loginType, String businessId,
                                           String token, String IP) {
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setUnionid(wechatLoginResponse.getUnionId());
        rxMemberRequest.setOpenid(wechatLoginResponse.getOpenId());
        RxMemberResponse rxMemberResponse = rxMemberGateway.login(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()) {
            throw new BizException("登录失败");
        }
        RxMemberLoginResponse rxMemberLoginResponse = (RxMemberLoginResponse) rxMemberResponse.getData();
        CustomerEntity customerEntity = customerRepository.selectCustomerByUnionId(wechatLoginResponse.getUnionId());
        RxMemberRiskRequest riskRequest = new RxMemberRiskRequest();
        RxMemberRiskRequest.RegisterOrLogData registerOrLogData = new RxMemberRiskRequest.RegisterOrLogData();
        if (customerEntity == null) {
            // 校验用户是否能再次注册
            checkIsAllowedRegister(wechatLoginResponse.getUnionId());
            customerEntity = new CustomerEntity();
            customerEntity.setId(businessIdGenerator.generateId());
            customerEntity.setVipId(Long.valueOf(rxMemberLoginResponse.getVipId()));
            customerEntity.setUnionid(rxMemberRequest.getUnionid());
            customerEntity.setStatus(NumberUtils.INTEGER_ONE);
            customerEntity.setLoginNum(NumberUtils.LONG_ONE);
            customerEntity.setLoginTime(new Date());
            customerEntity.setPhone(rxMemberLoginResponse.getMobile());
            if (loginType == CustomerLoginTypeEnum.APP_LOGIN.getType()) {
                // 获取微信用户信息
                WechatUserInfoRequest userInfoRequest = new WechatUserInfoRequest();
                userInfoRequest.setAccessToken(wechatLoginResponse.getAccessToken());
                userInfoRequest.setOpenId(wechatLoginResponse.getOpenId());
                userInfoRequest.setAccessToken(wechatLoginResponse.getAccessToken());
                WechatUserInfoResponse userInfoResponse = wechatGateway.getAppUserInfo(userInfoRequest);
                if (userInfoResponse == null || StringUtils.isBlank(userInfoResponse.getNickName())) {
                    throw new BizException("获取微信用户信息失败");
                }
                customerEntity.setUserName(userInfoResponse.getNickName());
                customerEntity.setUserUrl(userInfoResponse.getHeadImgUrl());
                customerEntity.setSex(userInfoResponse.getSex());
                customerEntity.setOpenid(wechatLoginResponse.getOpenId());
            } else if (loginType == CustomerLoginTypeEnum.MINI_LOGIN.getType()) {
                customerEntity.setMiniOpenid(wechatLoginResponse.getOpenId());
                customerEntity.setUserName(userDefaultName);
                customerEntity.setUserUrl(userDefaultAvatar);
            }
            customerRepository.saveCustomer(customerEntity);

            riskRequest.setTypeEnum(RiskBusinessTypeEnum.REGISTER_TYPE);
            registerOrLogData.setOperationType("register");
        } else {
            checkCustomerStatus(customerEntity);
            boolean needUpdate = false;
            CustomerEntity updateEntity = new CustomerEntity();
            updateEntity.setId(customerEntity.getId());
            if (!Objects.equals(customerEntity.getVipId(), Long.valueOf(rxMemberLoginResponse.getVipId()))) {
                updateEntity.setVipId(Long.valueOf(rxMemberLoginResponse.getVipId()));
                needUpdate = true;
            }
            if (loginType == CustomerLoginTypeEnum.APP_LOGIN.getType() && customerEntity.getOpenid() == null) {
                customerEntity.setOpenid(wechatLoginResponse.getOpenId());
                updateEntity.setOpenid(wechatLoginResponse.getOpenId());
                needUpdate = true;
            } else if (loginType == CustomerLoginTypeEnum.MINI_LOGIN.getType()
                    && customerEntity.getMiniOpenid() == null) {
                customerEntity.setMiniOpenid(wechatLoginResponse.getOpenId());
                updateEntity.setMiniOpenid(wechatLoginResponse.getOpenId());
                needUpdate = true;
            }
            if (needUpdate) {
                customerRepository.updateCustomer(updateEntity);
            }
            customerEntity.setLoginTime(new Date());
            customerRepository.updateLoginInfo(customerEntity);
            redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());
            riskRequest.setTypeEnum(RiskBusinessTypeEnum.LOGIN_TYPE);
            registerOrLogData.setOperationType("log");
        }
        // 上报风控数据
        try {
            riskRequest.setCustomerId(customerEntity.getId());
            riskRequest.setIP(Objects.nonNull(IP) ? IP : "unknown");
            riskRequest.setToken(token);
            riskRequest.setBusinessId(businessId);
            registerOrLogData.setOpenId(wechatLoginResponse.getOpenId());
            registerOrLogData.setAppChannel("unknown");
            registerOrLogData.setRegisterOrLogChannel("weixin");
            registerOrLogData.setRegisterOrLogType("otherPlatformAuth");
            riskRequest.setRegisterOrLogData(registerOrLogData);
            rxMemberRiskService.getUserRiskLevel(riskRequest);
        } catch (Exception e) {
            log.error("注册登录上报易盾失败");
            e.printStackTrace();
        }
        return customerEntity;
    }

    private CustomerEntity getCustomerByUnionId(String unionId) {
        String cacheKey = RedisKeyConstants.CUSTOMER_INFO_UNIONID + unionId;
        String customerJson = redisUtil.get(cacheKey);
        if (StringUtils.isBlank(customerJson)) {
            return customerRepository.selectCustomerByUnionId(unionId);
        } else {
            return JSONObject.parseObject(customerJson, CustomerEntity.class);
        }
    }

    @Override
    public CustomerEntity getCustomerById(Long customerId) {
        String cacheKey = RedisKeyConstants.CUSTOMER_INFO + customerId;
        String customerJson = redisUtil.get(cacheKey);
        if (StringUtils.isBlank(customerJson)) {
            CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
            redisUtil.set(cacheKey, JSONObject.toJSONString(customerEntity), true);
            return customerEntity;
        } else {
            return JSONObject.parseObject(customerJson, CustomerEntity.class);
        }
    }

    /**
     * 计算到次日零点的秒数
     */
    private long calculateExpireSecondsToMidnight() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        return Duration.between(now, midnight).getSeconds();
    }

    @Override
    public CardBalanceResponse getBalanceByCardNoAndPassword(CardBalanceQueryRequest request) {
        long customerId = StpUtil.getLoginIdAsLong();
        String cacheKey = RedisKeyConstants.CARD_BALANCE_DAILY_QUERY_COUNT + customerId;
        Long newCount = redisUtil.incrBy(cacheKey, 1);
        if (newCount != null && newCount == 1) {
            // 如果是第一次查询，设置过期时间到次日零点
            long expireSeconds = calculateExpireSecondsToMidnight();
            redisUtil.expire(cacheKey, (int) expireSeconds);
        }
        // 每日查询次数不得超过3次 只算成功的次数
        if (newCount != null && newCount > CARD_BALANCE_DAILY_QUERY_MAX_COUNT) {
            throw new BizException(Status.OUT_MAX_QUERY_CARD_BALANCE_COUNT);
        }
        try {
            CheckFindCardBalanceRequest checkFindCardBalanceRequest = new CheckFindCardBalanceRequest();
            checkFindCardBalanceRequest.setCardNo(request.getCardNo());
            checkFindCardBalanceRequest.setPassword(request.getPassword());
            CheckFindCardBalanceResponse checkFindCardBalanceResponse = onlinePaymentGateway
                    .findBalanceByCardNoAndPassword(checkFindCardBalanceRequest);
            if (checkFindCardBalanceResponse == null || checkFindCardBalanceResponse.getBalance() == null) {
                // 查询失败，减少计数
                redisUtil.strdecrBy(cacheKey, 1);
                throw new BizException(Status.CARD_BALANCE_QUERY_FAIL);
            }
            CardBalanceResponse cardBalanceResponse = new CardBalanceResponse();
            cardBalanceResponse.setCardNo(request.getCardNo());
            cardBalanceResponse.setValidAmount(new BigDecimal(checkFindCardBalanceResponse.getBalance())
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            return cardBalanceResponse;
        } catch (Exception e) {
            // 查询失败，减少计数
            redisUtil.strdecrBy(cacheKey, 1);
            throw new BizException(Status.CARD_BALANCE_QUERY_FAIL);
        }
    }

    @Override
    public CardBalanceResponse getBalanceByRecharge(CardBalanceQueryRequest request) {
        CheckFindCardBalanceRequest checkFindCardBalanceRequest = new CheckFindCardBalanceRequest();
        checkFindCardBalanceRequest.setCardNo(request.getCardNo());
        checkFindCardBalanceRequest.setPassword(request.getPassword());
        CheckFindCardBalanceResponse checkFindCardBalanceResponse = onlinePaymentGateway.findBalanceByCardNoAndPassword(checkFindCardBalanceRequest);
        CardBalanceResponse cardBalanceResponse = new CardBalanceResponse();
        cardBalanceResponse.setCardNo(request.getCardNo());
        cardBalanceResponse.setValidAmount(new BigDecimal(checkFindCardBalanceResponse.getBalance()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        return cardBalanceResponse;

    }

    @Override
    public DistributionCustomerInfoResponseDTO vipInfo(Long customerId) {
        log.info("开始获取会员信息");
        CustomerEntity customer = getCustomerById(customerId);
        DistributionCustomerInfoResponseDTO responseDTO = new DistributionCustomerInfoResponseDTO();
        responseDTO.setUserId(String.valueOf(customer.getId()));
        responseDTO.setName(customer.getUserName());
        responseDTO.setNickname(customer.getUserName());
        responseDTO.setAvatar(customer.getUserUrl());
        responseDTO.setMobile(customer.getPhone());
        return responseDTO;
    }

    @Override
    public String getOnceToken(long customerId) {
        log.info("开始生成一次性token，customerId：{}", customerId);

        // 参数校验：customerId小于等于0则抛出用户信息异常
        if (customerId <= 0) {
            log.info("用户未登录，返回固定token");
            return "0";
        }

        // 生成不重复的id作为token
        long token = businessIdGenerator.generateId();

        // 构建Redis缓存key：customer:token:once:{token}
        String cacheKey = RedisKeyConstants.CUSTOMER_TOKEN_ONCE + token;
        if (redisUtil.exists(cacheKey)) {
            log.warn("一次性token已存在，customerId：{}，token：{}", customerId, token);
            throw new BizException("生成token异常");
        }

        // 将customerId存储到Redis中，设置过期时间为5分钟（300秒）
        redisUtil.set(cacheKey, String.valueOf(customerId), 300);

        log.info("一次性token生成成功，customerId：{}，token：{}，缓存时间：5分钟", customerId, token);

        return String.valueOf(token);
    }

    @Override
    public CustomerDetailResponse getCustomerInfoByOnceToken(String onceToken) {
        log.info("开始根据一次性token获取用户信息，onceToken：{}", onceToken);

        // 参数校验
        if (StringUtils.isBlank(onceToken)) {
            log.warn("一次性token为空");
            throw new BizException("token无效");
        }

        // 构建Redis缓存key：customer:token:once:{token}
        String cacheKey = RedisKeyConstants.CUSTOMER_TOKEN_ONCE + onceToken;

        // 从Redis中获取customerId
        String customerIdStr = redisUtil.get(cacheKey);
        if (StringUtils.isBlank(customerIdStr)) {
            log.warn("一次性token已过期或不存在，onceToken：{}", onceToken);
            throw new BizException("token已过期或无效");
        }

        // 解析customerId
        Long customerId;
        try {
            customerId = Long.parseLong(customerIdStr);
        } catch (NumberFormatException e) {
            log.error("缓存中的customerId格式错误，customerIdStr：{}", customerIdStr, e);
            throw new BizException("token数据异常");
        }

        // 获取用户信息
        CustomerEntity customerEntity = getCustomerById(customerId);
        if (customerEntity == null) {
            log.warn("用户不存在，customerId：{}", customerId);
            throw new BizException("用户不存在");
        }

        // 转换为CustomerDetailResponse
        CustomerDetailResponse response = new CustomerDetailResponse();
        BeanUtils.copyProperties(customerEntity, response);
        response.setPassword("");

        // 使用完token后删除缓存（一次性使用）
        redisUtil.delete(cacheKey);

        log.info("根据一次性token获取用户信息成功，customerId：{}，userName：{}", customerId, customerEntity.getUserName());

        return response;
    }

    @Override
    public CustomerCardRechargeResponse cardRecharge(CustomerCardRechargeRequest request) {
        checkRechargeCardAndPayType(request.getRechargeType(), request.getPayType(), request.getTradeCard());
        CardRechargeRequest cardRechargeRequest = new CardRechargeRequest();
        cardRechargeRequest.setSource(request.getSource());
        cardRechargeRequest.setRechargeType(request.getRechargeType());
        cardRechargeRequest.setPayType(request.getPayType());
        cardRechargeRequest.setRechargeOutOrderNo(generateOrderNo());
        cardRechargeRequest.setCustomerId(StpUtil.getLoginIdAsLong());
        cardRechargeRequest.setRechargeCard(request.getRechargeCard());

        if (request.getPayType().equals("WX")) {
            if (request.getRechargeAmount() == null) {
                throw new BizException("微信充值金额不能为空");
            }
            String maxRechargeAmount = "";
            if (request.getRechargeType().equals(CustomerCardTypeEnum.WHITE_CARD.getCardTypeCode())){
                maxRechargeAmount = configRepository.getValueByType(SystemConfigTypeEnum.BJ_RECHARGE_LIMIT);
            } else if (request.getRechargeType().equals(CustomerCardTypeEnum.BLACK_CARD.getCardTypeCode())){
                maxRechargeAmount = configRepository.getValueByType(SystemConfigTypeEnum.HJ_RECHARGE_LIMIT);
            }
            if (StringUtils.isNotBlank(maxRechargeAmount) && request.getRechargeAmount().compareTo(new BigDecimal(maxRechargeAmount)) > 0) {
                log.error("微信充值金额不能超过{}", maxRechargeAmount);
                throw new BizException(Status.OUT_MAX_WX_RECHARGE_AMOUNT);
            }
            cardRechargeRequest.setRechargeAmount(request.getRechargeAmount().multiply(new BigDecimal("100")).intValue());
        } else {
            cardRechargeRequest.setTradeCard(request.getTradeCard());
            cardRechargeRequest.setTradeCardPwd(request.getTradeCardPwd());
            cardRechargeRequest.setTradeCardCvv(request.getTradeCardCvv());
            if (request.getPayType().equals(CustomerCardTypeEnum.BUSINESS_CARD.getCardTypeCode())) {
                if (request.getRechargeAmount() == null) {
                    throw new BizException("充值金额不能为空");
                }
                cardRechargeRequest.setRechargeAmount(request.getRechargeAmount().multiply(new BigDecimal("100")).intValue());
            }
        }
        CardRechargeResponse cardBalanceResponse = onlinePaymentGateway.cardRecharge(cardRechargeRequest);
        CustomerCardRechargeResponse customerCardRechargeResponse = new CustomerCardRechargeResponse();
        customerCardRechargeResponse.setWxPayParam(cardBalanceResponse.getWxPayConfig());
        if (cardBalanceResponse.getRechargeAmount() != null) {
            customerCardRechargeResponse.setRechargeAmount(new BigDecimal(cardBalanceResponse.getRechargeAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        }

        //只要收银台的充值接口返回0就可以向卡系统查询该卡属于的企业,并与风控企业匹配更新至用户表
        if (StringUtils.isNotEmpty(request.getPayType()) && !request.getPayType().equals("WX")) {
            try {
                log.info("根据充值卡号获取所属企业--卡号:" + request.getTradeCard());
                String customerNameByCardNo = thirdCardGateWay.getCustomerNameByCardNo(request.getTradeCard(), request.getPayType());
                log.info("根据充值卡号获取所属企业--企业:" + (StringUtils.isNotBlank(customerNameByCardNo) ? customerNameByCardNo : "无"));
                if (StringUtils.isNotBlank(customerNameByCardNo)) {
                    ProductRiskControlCompanyEntity riskControlCompany = productRiskControlCompanyRepository
                            .getRiskControlCompanyByName(customerNameByCardNo);
                    if (ObjectUtil.isNotEmpty(riskControlCompany)) {
                        CustomerEntity updateCustomerEntity = new CustomerEntity();
                        updateCustomerEntity.setId(StpUtil.getLoginIdAsLong());
                        updateCustomerEntity.setCompanyId(riskControlCompany.getId());
                        customerRepository.updateCustomer(updateCustomerEntity);
                    }
                }
            } catch (Exception e) {
                log.error("根据卡号未匹配到卡所属企业");
                e.printStackTrace();
            }
        }

        return customerCardRechargeResponse;
    }

    private String generateOrderNo() {
        StringBuilder sb = new StringBuilder();
        SecureRandom random = new SecureRandom();
        for (int i = 0; i < 6; i++) {
            sb.append(random.nextInt(10));
        }
        return "RECHARGE" + System.currentTimeMillis() + sb.toString();
    }

    @Override
    public PageDTO<CustomerCardTradeResponse> getCardTradeList(CustomerCardTradeRequest request) {
        PageDTO<TradeCardBalanceChangeEntity> page = tradeCardBalanceChangeRepository.getCardTradeList(request);
        // 转换为响应DTO
        List<CustomerCardTradeResponse> responseList = page.getRecords().stream()
                .map(item -> {
                    CustomerCardTradeResponse response = new CustomerCardTradeResponse();
                    response.setCardNo(item.getCardNo());
                    response.setTradeTime(item.getTradeTime());
                    response.setOrderNo(item.getOrderNo());
                    response.setType(item.getType());
                    if (item.getType().equals("RECHARGE") || item.getType().equals("AIR_RECHARGE")) {
                        response.setTradeName("充值");
                        response.setType("RECHARGE"); // 统一类型
                    } else if (item.getType().equals("CANCEL")) {
                        response.setTradeName("冲正");
                    } else {
                        response.setTradeName(orderRepository.getProductNameByOrderNo(item.getOrderNo()));
                    }
                    response.setTradePrice(new BigDecimal(item.getTradePrice()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                    return response;
                }).collect(Collectors.toList());
        return PageDTO.<CustomerCardTradeResponse>builder()
                .records(responseList)
                .total(page.getTotal())
                .current(page.getCurrent())
                .size(page.getSize())
                .pages(page.getPages())
                .build();
    }

    /**
     * 校验充值卡类型和支付方式
     */
    private void checkRechargeCardAndPayType(String rechargeType, String payType, String tradeCard) {
        CardRechargeConfigEnum config = CardRechargeConfigEnum.getByCardType(rechargeType);
        if (config == null) {
            throw new BizException("当前卡类型不支持被充值");
        }
        if (!config.supportsPayType(payType)) {
            log.error("支付方式错误，请重新选择。当前卡类型：{}，支付方式：{}", rechargeType, payType);
            throw new BizException("支付方式错误，请重新选择");
        }
        // 校验红卡号段
        if (rechargeType.equals(CustomerCardTypeEnum.WHITE_CARD.getCardTypeCode())
                && payType.equals(CustomerCardTypeEnum.BUSINESS_CARD.getCardTypeCode())) {
            List<String> redCardPrefixList = Arrays.stream(redCardPrefix.split(",")) .map(String::trim).toList();
            boolean isRedCard = redCardPrefixList.stream().anyMatch(tradeCard::startsWith);
            if (!isRedCard){
                throw new BizException(Status.NOT_RED_CARD);
            }
        }
    }

    private CustomerResponse buildCustomerResponse(CustomerEntity customerEntity) {
        CustomerResponse customerResponse = new CustomerResponse();
        customerResponse.setCustomerId(customerEntity.getId());
        if (customerEntity.getPhone() == null || StringUtils.isBlank(customerEntity.getPhone())) {
            // 用户未绑定手机号码，手机号码绑定成功之后才能登录成功
            return customerResponse;
        }
        StpUtil.login(customerEntity.getId());
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        customerResponse.setUnionid(customerEntity.getUnionid());
        customerResponse.setPhone(customerEntity.getPhone());
        customerResponse.setOpenid(customerEntity.getOpenid());
        customerResponse.setMiniOpenid(customerEntity.getMiniOpenid());
        customerResponse.setToken(tokenInfo.getTokenValue());
        return customerResponse;
    }

    private void cacheCustomer(CustomerEntity customer) {
        String cacheKey = RedisKeyConstants.CUSTOMER_INFO_UNIONID + customer.getUnionid();
        redisUtil.set(cacheKey, JSON.toJSONString(customer), true);
    }

    @Override
    public List<CustomerCardResponse> getCardList(CustomerCardRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setVipId(String.valueOf(customerEntity.getVipId()));
        rxMemberRequest.setUnionid(customerEntity.getUnionid());
        if (request.getCardType() == null) {
            rxMemberRequest.setCardType(CustomerCardTypeEnum.getAllCardTypes());
        } else {
            rxMemberRequest.setCardType(String.valueOf(CustomerCardTypeEnum.getCardType(request.getCardType())));
        }
        RxMemberResponse rxMemberResponse = rxMemberGateway.getCardList(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()) {
            throw new BizException("获取卡列表失败");
        }
        List<RxMemberCardResponse> cardData = (List<RxMemberCardResponse>) rxMemberResponse.getData();
        List<CustomerCardResponse> cardResponseList = processCustomerCardList(cardData);
        findCardBalance(cardResponseList);
        return cardResponseList;
    }

    private List<CustomerCardResponse> processCustomerCardList(List<RxMemberCardResponse> rxMemberCardResponseList) {
        if (CollectionUtils.isEmpty(rxMemberCardResponseList)) {
            return Collections.emptyList();
        }
        Map<String, List<RxMemberCardDetail>> map = rxMemberCardResponseList.stream()
                .collect(Collectors.toMap(RxMemberCardResponse::getCardType, RxMemberCardResponse::getNoList));
        List<CustomerCardResponse> cardResponseList = new ArrayList<>();
        for (String key : map.keySet()) {
            List<RxMemberCardDetail> list = map.getOrDefault(key, Collections.emptyList());
            CustomerCardResponse cardResponse = new CustomerCardResponse();
            cardResponse.setCardType(Integer.valueOf(key));
            cardResponse.setCardNum(list.size());
            cardResponse.setCardInfoList(mapToCardDetailResponses(list));
            cardResponseList.add(cardResponse);
        }
        return cardResponseList;
    }

    private void findCardBalance(List<CustomerCardResponse> cardResponseList) {
        if (CollectionUtils.isEmpty(cardResponseList)) {
            return;
        }
        List<CardInfoRequest> cardInfoRequestList = cardResponseList.stream()
                .flatMap(response -> {
                    String cardTypeCode = CustomerCardTypeEnum.getCardTypeCodeByType(response.getCardType());
                    if (cardTypeCode == null) {
                        return Stream.empty();
                    }
                    return response.getCardInfoList().stream().map(cardDetail -> createCardInfoRequest(cardDetail, cardTypeCode));
                })
                .collect(Collectors.toList());

        FindCardBalanceRequest findCardBalanceRequest = new FindCardBalanceRequest();
        // 个人中心查询卡余额缓存
        findCardBalanceRequest.setIsCache(false);
        findCardBalanceRequest.setCards(cardInfoRequestList);
        List<FindCardBalanceResponse> findCardBalanceResponses;
        try {
            findCardBalanceResponses = onlinePaymentGateway.findBalance(findCardBalanceRequest);
        } catch (Exception e) {
            log.error("查询卡余额接口调用失败，错误信息：{}", e.getMessage(), e);
            // 发生异常时返回空列表，不影响主流程
            findCardBalanceResponses = Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(findCardBalanceResponses)) {
            return;
        }
        Map<String, BigDecimal> balanceMap = findCardBalanceResponses.stream()
                .filter(response -> response.getBalance() != null)
                .collect(Collectors.toMap(
                        FindCardBalanceResponse::getCardNo,
                        response -> new BigDecimal(response.getBalance()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)
                ));
        cardResponseList.forEach(response ->
                response.getCardInfoList().forEach(cardDetail -> {
                    BigDecimal validAmount = balanceMap.get(cardDetail.getCardNo());
                    cardDetail.setValidAmount(validAmount);
                    if (response.getCardType() == CustomerCardTypeEnum.BUSINESS_CARD.getCardType()
                            || response.getCardType() == CustomerCardTypeEnum.TRADE_UNION_VOUCHER.getCardType()){
                        cardDetail.setIsZero(validAmount != null && validAmount.compareTo(BigDecimal.ZERO) == 0 ? 1 : 0);
                    }
                })
        );
    }

    private CardInfoRequest createCardInfoRequest(CustomerCardDetailResponse cardDetailResponse, String cardTypeCode) {
        CardInfoRequest request = new CardInfoRequest();
        request.setCardNo(cardDetailResponse.getCardNo());
        request.setCardType(cardTypeCode);
        return request;
    }

    private List<CustomerCardDetailResponse> mapToCardDetailResponses(List<RxMemberCardDetail> details) {
        return details.stream()
                .map(item -> {
                    CustomerCardDetailResponse response = new CustomerCardDetailResponse();
                    response.setCardNo(item.getCardNo());
                    response.setIsZero(item.getIsZero());
                    response.setValidAmount(BigDecimal.ZERO);
                    response.setCreateTime(item.getCreateTime());
                    return response;
                })
                .toList();
    }

    @Override
    public CustomerCheckLoginOffResponse checkIsAllowedLoginOff(CustomerRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setVipId(String.valueOf(customerEntity.getVipId()));
        RxMemberResponse rxMemberResponse = rxMemberGateway.checkIsAllowedLoginOff(rxMemberRequest);
        CustomerCheckLoginOffResponse customerCheckLoginOffResponse = new CustomerCheckLoginOffResponse();
        if (!rxMemberResponse.isSuccess()) {
            customerCheckLoginOffResponse.setAllowed(false);
            customerCheckLoginOffResponse.setErrMsg(rxMemberResponse.getErrorMessage());
        } else {
            customerCheckLoginOffResponse.setAllowed(true);
        }
        return customerCheckLoginOffResponse;
    }

    @Override
    public void deleteAccount(CustomerDeleteAccountRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        if (customerEntity == null) {
            throw new BizException(Status.USER_NOT_EXIST);
        }
        String deleteAccountMaxCountStr = configRepository.getValueByType(SystemConfigTypeEnum.DELETE_USER_MAX_COUNT_TYPE);
        int deleteAccountMaxCount = Integer.parseInt(deleteAccountMaxCountStr);
        if (deleteAccountMaxCount > 0) {
            long deleteAccountCount = customerRepository.getDeleteAccountCountInCurrentYear(customerEntity.getUnionid());
            if (deleteAccountCount >= deleteAccountMaxCount) {
                throw new BizException(Status.OUT_MAX_DELETE_ACCOUNT_COUNT);
            }
        }
        String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + customerEntity.getPhone());
        log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
        if (!request.getVerificationCode().equals(code)) {
            throw new BizException("短信验证码不一致");
        }
        // 校验账户余额是否都为0 待处理
        CustomerEntity updateEntity = new CustomerEntity();
        updateEntity.setId(customerEntity.getId());
        updateEntity.setStatus(NumberUtils.INTEGER_TWO);
        updateEntity.setDelFlag(NumberUtils.INTEGER_ONE);
        updateEntity.setModTime(new Date());
        updateEntity.setModId(customerEntity.getId());
        customerRepository.deleteCustomer(updateEntity);
        CustomerPhoneChangeRecordEntity customerPhoneChangeRecordEntity = new CustomerPhoneChangeRecordEntity();
        customerPhoneChangeRecordEntity.setCustomerId(customerEntity.getId());
        customerPhoneChangeRecordEntity.setOriginalPhone(customerEntity.getPhone());
        customerPhoneChangeRecordEntity.setChangeMethod(CustomerPhoneChangeMethod.USER_CANCEL.getCode());
        customerPhoneChangeRecordEntity.setCreateTime(new Date());
        customerPhoneChangeRecordEntity.setCreateId(customerEntity.getId());
        customerPhoneChangeRecordRepository.savePhoneChangeRecord(customerPhoneChangeRecordEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + customerEntity.getPhone());
        redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());
        StpUtil.logout(customerEntity.getId());
    }

    @Override
    public void bindCard(CustomerBindCardRequest request) {
        if (!CustomerCardTypeEnum.isBindableCardType(request.getCardTypeCode())){
            throw new BizException("不支持的卡类型");
        }
        List<String> notAllowedBindCardBinList = Arrays.stream(notAllowedBindCardBin.split(",")) .map(String::trim).toList();
        boolean isNotAllowedBindCard = notAllowedBindCardBinList.stream().anyMatch(request.getCardNo()::startsWith);
        if (isNotAllowedBindCard){
            throw new BizException(Status.NOT_ALLOWED_CARD_BIN);
        }
        CustomerEntity customerEntity = getCustomerById(request.getCustomerId());
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setVipId(String.valueOf(customerEntity.getVipId()));
        rxMemberRequest.setNo(request.getCardNo());
        rxMemberRequest.setPassword(request.getPassword());
        rxMemberRequest.setCvv(request.getCvv());
        RxMemberResponse rxMemberResponse = rxMemberGateway.addCard(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()) {
            throw new BizException(rxMemberResponse.getErrorMessage());
        }
        // 到卡系统查询该卡属于的企业,并与风控企业匹配更新至用户表
        try {
            String customerNameByCardNo = thirdCardGateWay.getCustomerNameByCardNo(request.getCardNo(), request.getCardTypeCode());
            if (StringUtils.isNotBlank(customerNameByCardNo)) {
                ProductRiskControlCompanyEntity riskControlCompany = productRiskControlCompanyRepository
                        .getRiskControlCompanyByName(customerNameByCardNo);
                if (ObjectUtil.isNotEmpty(riskControlCompany)) {
                    CustomerEntity updateCustomerEntity = new CustomerEntity();
                    updateCustomerEntity.setId(customerEntity.getId());
                    updateCustomerEntity.setCompanyId(riskControlCompany.getId());
                    customerRepository.updateCustomer(updateCustomerEntity);
                }
            }
        } catch (Exception e) {
            log.error("根据卡号未匹配到卡所属企业");
        }

        // 上报风控数据
        try {
            RxMemberRiskRequest riskRequest = new RxMemberRiskRequest();
            riskRequest.setTypeEnum(RiskBusinessTypeEnum.BIND_CARD_TYPE);
            riskRequest.setCustomerId(customerEntity.getId());
            riskRequest.setIP(Objects.nonNull(request.getLoginIp()) ? request.getLoginIp() : "unknown");
            riskRequest.setToken(request.getYdToken());
            riskRequest.setBusinessId(request.getYdBusinessId());
            RxMemberRiskRequest.BindCardData bindCardData = new RxMemberRiskRequest.BindCardData();
            bindCardData.setCardNo(request.getCardNo());
            riskRequest.setBindCardData(bindCardData);
            rxMemberRiskService.getUserRiskLevel(riskRequest);
        } catch (Exception e) {
            log.error("绑卡上报易盾失败");
            e.printStackTrace();
        }
    }

    @Override
    public void unbindCard(CustomerUnbindCardRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setVipId(String.valueOf(customerEntity.getVipId()));
        rxMemberRequest.setNo(request.getCardNo());
        RxMemberResponse rxMemberResponse = rxMemberGateway.deleteCard(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()) {
            throw new BizException(rxMemberResponse.getErrorMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setPaySort(CustomerSetPaySortRequest request) {
        String cacheKey = RedisKeyConstants.CUSTOMER_PAY_SORT + request.getCustomerId();
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        CustomerPaySortResponse response = new CustomerPaySortResponse();
        response.setCustomerId(request.getCustomerId());
        if (request.getUseDefault()) {
            if (!Objects.equals(customerEntity.getUseDefaultPaySort(), NumberUtils.INTEGER_ONE)) {
                CustomerEntity updateEntity = new CustomerEntity();
                updateEntity.setId(customerEntity.getId());
                updateEntity.setUseDefaultPaySort(NumberUtils.INTEGER_ONE);
                customerRepository.updateCustomer(updateEntity);
            }
            CustomerPaySortEntity paySortEntity = customerPaySortRepository
                    .getCustomerPaySortByCustomerId(request.getCustomerId());
            if (paySortEntity != null) {
                customerPaySortRepository.deleteCustomerPaySort(paySortEntity.getId());
            }
            response.setUseDefault(true);
            response.setPaySortList(getDefaultPaySort());
            redisUtil.set(cacheKey, JSONUtil.toJsonStr(response), true);
        } else {
            if (!Objects.equals(customerEntity.getUseDefaultPaySort(), NumberUtils.INTEGER_ZERO)) {
                CustomerEntity updateEntity = new CustomerEntity();
                updateEntity.setId(customerEntity.getId());
                updateEntity.setUseDefaultPaySort(NumberUtils.INTEGER_ZERO);
                customerRepository.updateCustomer(updateEntity);
            }
            CustomerPaySortEntity paySortEntity = customerPaySortRepository
                    .getCustomerPaySortByCustomerId(request.getCustomerId());
            if (paySortEntity != null) {
                paySortEntity.setPaySort(String.join(",", request.getPaySortList()));
                paySortEntity.setModTime(new Date());
                customerPaySortRepository.updateCustomerPaySort(paySortEntity);
            } else {
                paySortEntity = new CustomerPaySortEntity();
                paySortEntity.setCustomerId(request.getCustomerId());
                paySortEntity.setDelFlag(NumberUtils.INTEGER_ZERO);
                paySortEntity.setPaySort(String.join(",", request.getPaySortList()));
                customerPaySortRepository.saveCustomerPaySort(paySortEntity);
            }
            response.setUseDefault(false);
            response.setPaySortList(request.getPaySortList());
            redisUtil.set(cacheKey, JSONUtil.toJsonStr(response), true);
        }
    }

    /**
     * 获取用户支付顺序
     *
     * @param customerId
     * @return
     */
    @Override
    public CustomerPaySortResponse getPaySort(Long customerId) {
        String cacheKey = RedisKeyConstants.CUSTOMER_PAY_SORT + customerId;
        String cacheValue = redisUtil.get(cacheKey);
        CustomerPaySortResponse response = new CustomerPaySortResponse();
        if (StrUtil.isNotBlank(cacheValue)) {
            response = JSONObject.parseObject(cacheValue, CustomerPaySortResponse.class);
        } else {
            CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
            response.setCustomerId(customerId);
            if (Objects.equals(customerEntity.getUseDefaultPaySort(), NumberUtils.INTEGER_ONE)) {
                response.setUseDefault(true);
                response.setPaySortList(getDefaultPaySort());
            } else {
                CustomerPaySortEntity customerPaySortEntity = customerPaySortRepository
                        .getCustomerPaySortByCustomerId(customerId);
                List<String> paySortList = Arrays.stream(customerPaySortEntity.getPaySort().split(","))
                        .map(String::trim).toList();
                response.setUseDefault(false);
                response.setPaySortList(paySortList);
            }
            redisUtil.set(cacheKey, JSONUtil.toJsonStr(response), true);
        }
        return response;
    }

    @Override
    public List<AdvertisementInfoDTO> getPersonalCenterAd(Integer regionId) {
        // 广告
        List<AdvertisementEntity> adList = advertisementService
                .getAdvertisementList(AdvertisementTypeEnum.ADV_USER_CENTER.getCode(), null);
        if (CollectionUtil.isNotEmpty(adList)) {
            List<AdvertisementInfoDTO> advertisementInfoDTOList = new ArrayList<>();
            adList.forEach(item -> {
                if (item.getIsNationwide() == 1
                        || contentRegionService.isOnlineInRegion(item.getId(),
                        ContentcenterTypeEnum.CONTENT_ADV.getCode(), regionId)) {
                    AdvertisementInfoDTO advertisementInfoDTO = new AdvertisementInfoDTO();
                    BeanUtils.copyProperties(item, advertisementInfoDTO);
                    advertisementInfoDTOList.add(advertisementInfoDTO);
                }
            });
            return advertisementInfoDTOList;
        }
        return null;
    }

    @Override
    public Boolean checkCustomerHasPayPassword(Long customerId) {
        CustomerEntity customerEntity = getCustomerById(customerId);
        return !StringUtils.isEmpty(customerEntity.getPassword());
    }

    @Override
    public LoginOffConfigResponse getLoginOffConfig() {
        String maxDeleteAccountCount = configRepository.getValueByType(SystemConfigTypeEnum.DELETE_USER_MAX_COUNT_TYPE);
        String reRegisterInterval = configRepository.getValueByType(SystemConfigTypeEnum.REREGISTER_TIME_INTERVAL_TYPE);
        LoginOffConfigResponse response = new LoginOffConfigResponse();
        response.setMaxDeleteAccountCount(Integer.parseInt(maxDeleteAccountCount));
        response.setReRegisterInterval(Integer.parseInt(reRegisterInterval));
        return response;
    }

    /**
     * 获取平台默认支付顺序 默认顺序为：提货凭证、黑金、工会凭证、白金、瑞祥红卡、商联卡
     *
     * @return
     */
    private List<String> getDefaultPaySort() {
        return paymentMethodRepository.getDefaultPaySortList();
    }

    @Override
    public boolean checkCustomerPsw(Long customerId, String psw) {
        if (customerId == null) {
            throw new BizException("用户id不能为空");
        }
        //1获取用户信息
        CustomerEntity customer = customerRepository.selectCustomerById(customerId);
        if (customer == null) {
            throw new BizException("用户不存在");
        }
        //2未设置密码，提示用户设置
        if (StringUtils.isEmpty(customer.getPassword())) {
            throw new BizException(Status.PAYMENT_PASSWORD_NOT_SET);
        }
        //3验证是否免密(开启免密无需验证密码)
        if (Objects.equals(customer.getOpenPasswordFreePayment(), NumberUtils.INTEGER_ONE)) {
            return true;
        }
        //4验证是否锁定
        String lockKey = RedisKeyConstants.CUSTOMER_PSW_LOCK + customerId;
        long pswAttempt = redisUtil.incrBy(lockKey, 1);
        if (pswAttempt == 1) {
            redisUtil.expire(lockKey, PSW_LOCK_TIME);
        }
        if (pswAttempt >= PSW_ERROR_MAX_COUNT) {                         // 第3次及以后
            if (pswAttempt == PSW_ERROR_MAX_COUNT) {                     // 刚达到3次，把过期改成锁时间
                redisUtil.expire(lockKey, PSW_LOCK_TIME);
            }
            long remain = redisUtil.getExpireTime(lockKey, TimeUnit.MINUTES);
            throw new BizException("账户支付已锁定，剩余 " + remain + " 分钟");
        }
        //5用户·密码验证（获取支付密码）
        String password = customer.getPassword();
        if (StringUtils.isEmpty(psw)) {
            throw new BizException("未设置免密，请输入密码支付");
        }
        boolean isPass = new BCryptPasswordEncoder().matches(psw, password);
        if (!isPass) {
            log.error("用户:{} 第{}次密码输入错误", customerId, pswAttempt);
            if (pswAttempt == PSW_ERROR_MAX_COUNT - 1) {
                throw new BizException("支付密码输入错误, 下次错误将锁定支付");
            }
            throw new BizException("支付密码输入错误, 请重新输入");
        } else {
            //清除锁定标志
            redisUtil.delete(lockKey);
        }
        return isPass;
    }

    @Override
    public ApiResponse<DistributionCustomerBalanceResponseDTO> getBalance(DistributionCustomerInfoRequestDTO requestDTO, Long appSpuId) {
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(appSpuId);
        if (appGoodsEntity == null) {
            return ApiResponse.fail("当前应用不存在或已下架");
        }
        // 分销接口验签
        if (!distributionVerifySign(requestDTO, appGoodsEntity.getAppFlag())) {
            return ApiResponse.fail("签名验证失败");
        }
        DistributionCustomerBalanceResponseDTO balanceResponseDTO = new DistributionCustomerBalanceResponseDTO();
        if (StringUtils.isEmpty(appGoodsEntity.getPayType())) {
            balanceResponseDTO.setAmount(BigDecimal.ZERO.toString());
            return ApiResponse.success(balanceResponseDTO);
        }
        List<String> cardCodeTypes = Arrays.asList(appGoodsEntity.getPayType().split(";"));
        CustomerEntity customerEntity = getCustomerById(Long.valueOf(requestDTO.getUserId()));
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setVipId(String.valueOf(customerEntity.getVipId()));
        rxMemberRequest.setUnionid(customerEntity.getUnionid());
        rxMemberRequest.setCardType(CustomerCardTypeEnum.getCardTypesByCodes(cardCodeTypes));
        RxMemberResponse rxMemberResponse = rxMemberGateway.getCardList(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()) {
            throw new BizException("获取卡列表失败");
        }
        if (rxMemberResponse.getData() == null) {
            balanceResponseDTO.setAmount(BigDecimal.ZERO.toString());
            return ApiResponse.success(balanceResponseDTO);
        }
        List<RxMemberCardResponse> rxMemberCardResponseList = (List<RxMemberCardResponse>) rxMemberResponse.getData();
        balanceResponseDTO.setAmount(getBalanceSum(rxMemberCardResponseList).toString());
        return ApiResponse.success(balanceResponseDTO);
    }

    @Override
    public PageDTO<CustomerCardTradeResponse> getCardRechargeRecordList(CustomerCardRechargeRecordQueryRequest request) {
        CardTradeQueryRequest cardTradeQueryRequest = new CardTradeQueryRequest();
        cardTradeQueryRequest.setCardNo(request.getCardNo());
        cardTradeQueryRequest.setCardTypeCode(request.getCardTypeCode());
        // 只查询充值记录
        cardTradeQueryRequest.setTradeType("1");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        if (request.getYearMonth() != null) {
            LocalDateTime startTime = request.getYearMonth().atDay(1).atStartOfDay();
            LocalDateTime endTime = request.getYearMonth().atEndOfMonth().atTime(23, 59, 59);
            cardTradeQueryRequest.setStartTime(startTime.format(formatter));
            cardTradeQueryRequest.setEndTime(endTime.format(formatter));
        } else {
            cardTradeQueryRequest.setStartTime(LocalDateTime.now().minusYears(1).format(formatter));
            cardTradeQueryRequest.setEndTime(LocalDateTime.now().format(formatter));
        }
        cardTradeQueryRequest.setPageSize(request.getPageSize());
        cardTradeQueryRequest.setPageNo(request.getPageNum());
        CardTradeQueryResponse response = thirdCardGateWay.getRechargeRecordsByCardNo(cardTradeQueryRequest);
        if (response == null || CollectionUtils.isEmpty(response.getTransData())) {
            return PageDTO.<CustomerCardTradeResponse>builder()
                    .records(Collections.emptyList())
                    .total(0L)
                    .pages(0L)
                    .current(request.getPageNum().longValue())
                    .size(request.getPageSize().longValue())
                    .build();
        } else {
            List<CardTradeInfoResponse> transData = response.getTransData();
            List<CustomerCardTradeResponse> list = new ArrayList<>(transData.size());
            transData.forEach(cardTradeInfoResponse -> {
                CustomerCardTradeResponse tradeResponse = new CustomerCardTradeResponse();
                tradeResponse.setCardNo(cardTradeInfoResponse.getCardId());
                tradeResponse.setTradePrice(new BigDecimal(cardTradeInfoResponse.getTxnAmt()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                tradeResponse.setTradeTime(cardTradeInfoResponse.getTxnTime());
                tradeResponse.setType("RECHARGE");
                list.add(tradeResponse);
            });
            return PageDTO.<CustomerCardTradeResponse>builder()
                    .records(list)
                    .total(response.getTradeCount().longValue())
                    .current(request.getPageNum().longValue())
                    .size(request.getPageSize().longValue())
                    .pages(response.getPageCount().longValue())
                    .build();
        }
    }

    @Override
    public String getConfigByType(CommonConfigQueryRequest request) {
        return configRepository.getValueByType(SystemConfigTypeEnum.getConfigEnumByType(request.getConfigType()));
    }

    @Override
    public void renewTokenTimeout() {
        try {
            long beforeTimeout = StpUtil.getTokenTimeout();
            StpUtil.renewTimeout(tokenTimeout);
            long afterTimeout = StpUtil.getTokenTimeout();
            log.debug("Token续期成功 - 前: {}秒, 后: {}秒", beforeTimeout, afterTimeout);
        } catch (Exception e) {
            log.error("Token续期失败，tokenTimeout: {}", tokenTimeout, e);
            throw new BizException("Token续期失败");
        }
    }

    @Override
    public ScanRechargeResponse getCardInfoByScanResult(ScanRechargeRequest request) {
        try {
            String cardInfo = SM4DecryptUtil.decryptSM4CFB(request.getQRCode());
            if (StringUtils.isNotBlank(cardInfo)){
                String[] parts = cardInfo.split("\\|");
                ScanRechargeResponse response = new ScanRechargeResponse();
                response.setCardNo(parts[0]);
                response.setPassword(parts[1]);
                response.setCvv(parts[2]);
                return response;
            }
            return null;
        } catch (Exception e) {
            throw new BizException("二维码解析失败");
        }
    }

    private BigDecimal getBalanceSum(List<RxMemberCardResponse> list) {
        List<CardInfoRequest> cardInfoRequestList = new ArrayList<>();
        list.forEach(rxMemberCardResponse -> {
            String cardTypeCode = CustomerCardTypeEnum.getCardTypeCodeByType(Integer.parseInt(rxMemberCardResponse.getCardType()));
            List<RxMemberCardDetail> noList = rxMemberCardResponse.getNoList();
            noList.forEach(rxMemberCardDetail -> {
                CardInfoRequest cardInfoRequest = new CardInfoRequest();
                cardInfoRequest.setCardNo(rxMemberCardDetail.getCardNo());
                cardInfoRequest.setCardType(cardTypeCode);
                cardInfoRequestList.add(cardInfoRequest);
            });
        });
        FindCardBalanceRequest findCardBalanceRequest = new FindCardBalanceRequest();
        // 分销查询卡余额不使用缓存
        findCardBalanceRequest.setIsCache(false);
        findCardBalanceRequest.setCards(cardInfoRequestList);
        List<FindCardBalanceResponse> findCardBalanceResponses = onlinePaymentGateway.findBalance(findCardBalanceRequest);

        BigDecimal divisor = new BigDecimal("100");
        int scale = 2;
        return findCardBalanceResponses.stream()
                .filter(Objects::nonNull)
                .map(FindCardBalanceResponse::getBalance)
                .filter(Objects::nonNull)
                .map(balanceStr -> {
                    try {
                        return new BigDecimal(balanceStr);
                    } catch (NumberFormatException e) {
                        return BigDecimal.ZERO;
                    }
                })
                .map(amount -> amount.divide(divisor, scale, RoundingMode.HALF_UP))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private boolean distributionVerifySign(DistributionCustomerInfoRequestDTO requestDTO, String appFlag) {
        DistChannelType distChannelType = DistChannelType.valueOf(appFlag);
        DistVerifySignRequest distVerifySignRequest = DistVerifySignRequest
                .builder()
                .channelType(distChannelType)
                .verifySignDTO(requestDTO)
                .build();
        return unifiedDistributionApi.verifySign(distVerifySignRequest);
    }

    /**
     * 校验用户状态
     */
    private void checkCustomerStatus(CustomerEntity customerEntity) {
        if (customerEntity.getStatus() == CustomerStatusEnum.DISABLE.getType()) {
            throw new BizException(Status.CUSTOMER_DISABLE);
        }
        if (customerEntity.getStatus() == CustomerStatusEnum.DEACTIVATED.getType()) {
            throw new BizException(Status.CUSTOMER_DEACTIVATED);
        }
    }

}
