package com.jsrxjt.mobile.biz.homeScanPay.service;

import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.*;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayRefundRequest;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayResultResponse;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayStatusResultResponse;

import java.util.List;

/**
 * @Description: 扫码提货服务接口
 * @Author: ywt
 * @Date: 2025-05-19 10:23
 * @Version: 1.0
 */
public interface PickScanCaseService {
    PickPaymentCodeResponseDTO getPaymentCode(PickPaymentCodeRequestDTO requestDTO);

    /**
     * 创建订单(外部系统调用)
     *
     * @param requestDTO
     * @return
     */
    PickBaseResponse<PickChannelOrderResponseDTO> createChannelOrder(PickChannelOrderRequestDTO requestDTO);

    /**
     * 订单变更
     */
    BaseResponse updateChannelOrder(PickChannelOrderUpdateRequestDTO requestDTO);

    /**
     * 支付状态查询 (外部系统调用)
     */
    PickPlatformPayStatusResultResponse getChannelPayStatus(PickPaymentRefundRequestDTO requestDTO);

    /**
     * 订单退款(外部系统调用)
     */
    PickBaseResponse<PickChannelOrderRefundResponseDTO> refundChannelOrder(PickChannelOrderRefundRequestDTO requestDTO);

    /**
     * 退款状态查询(外部系统调用)
     */
    PickBaseResponse<PickOrderRefundStatusResponseDTO> getRefundStatus(PickOrderRefundStatusRequestDTO requestDTO);

    /**
     * 订单退款结果回告[幂等性]
     */
    PickOrderRefundResultResponseDTO getPaymentOrderRefund(PickPlatformPayRefundRequest request);

    /**
     * 订单支付结果回告
     */
    PickPlatformPayResultResponse getPaymentOrderResult(PickPlatformPayResultRequestDTO requestDTO);

    /**
     * 卡消费记录
     * @param cardNo
     * @return {@link BaseResponse}<{@link PickCardTradeListResponseDTO}>
     */
    BaseResponse<List<PickCardTradeListResponseDTO>> cardTradeList(String cardNo);

    /**
     * 提货券订单确认
     * @param requestDTO
     * @return {@link Object}
     */
    PickBaseResponse orderConfirm(PickChannelOrderConfirmRequestDTO requestDTO);

    /**
     * 提货券中台交易状态查询
     * @param requestDTO
     * @return {@link PickBaseResponse}<{@link PickChannelOrderResultResponseDTO}>
     */
    PickBaseResponse<PickChannelOrderResultResponseDTO> getChannelOrderPayStatus(PickChannelOrderStatusRequestDTO requestDTO);

    /**
     * 推送消息
     * @param tag
     * @param parseObject
     */
    void sendBackMessage(String tag, JSONObject parseObject);
}
