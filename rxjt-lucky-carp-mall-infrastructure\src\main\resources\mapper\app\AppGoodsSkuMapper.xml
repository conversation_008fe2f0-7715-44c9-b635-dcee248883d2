<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.app.persistent.mapper.AppCouponGoodsSkuMapper">

  <update id="increaseSoldNum">
    UPDATE app_coupon_goods_sku
    SET sold_num = COALESCE(sold_num, 0) + #{quantity},
        mod_time = NOW()
    WHERE app_sku_id = #{appSkuId}
  </update>

</mapper>
