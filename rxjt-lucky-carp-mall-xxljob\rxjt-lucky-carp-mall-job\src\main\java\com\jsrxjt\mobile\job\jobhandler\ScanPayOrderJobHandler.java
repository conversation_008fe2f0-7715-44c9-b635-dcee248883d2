package com.jsrxjt.mobile.job.jobhandler;

import com.jsrxjt.mobile.biz.homeScanPay.service.HomeScanPayService;
import com.jsrxjt.mobile.biz.module.PageInfoCaseService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 扫码付订单状态更新
 * <AUTHOR>
 * @date 2025/11/10
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ScanPayOrderJobHandler {

    private final HomeScanPayService homeScanPayService;

    /**
     * 线下支付订单状态更新
     *
     */
    @XxlJob("scanPayOrderJobHandler")
    public void scanOrderStatusUpdate() {
        try {
            homeScanPayService.scanOrderStatusUpdate();
        } catch (Exception e) {
            String errorMsg = "更新扫码付订单状态异常";
            log.error(errorMsg, e);
            XxlJobHelper.handleFail(errorMsg);
            throw e;
        }
    }

} 