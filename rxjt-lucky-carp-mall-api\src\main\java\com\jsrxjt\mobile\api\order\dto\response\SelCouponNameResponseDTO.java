package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SelCouponNameResponseDTO {

    private Integer flgType;

    @Schema(description = "是否是自发券 1是 0否")
    private Integer isSelfCoupon = 0;

    private String brandName;

    private String couponAmountName;

    private String spuName;

    private String backgroundColor;

    private String remark;

    /**
     * 提货券分销平台的产品id，isSelfCoupon = 1时有效
     */
    private Integer pickProductId;

    private String offsetDialog;

    private BigDecimal amount;

    private String imgUrl;
}
