package com.jsrxjt.mobile.infra.payment.gatewayimpl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.payment.gateway.OnlinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.*;
import com.jsrxjt.mobile.domain.payment.gateway.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 线上支付Gateway实现类
 * <AUTHOR>
 * @since 2025/8/8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OnlinePaymentGatewayImpl implements OnlinePaymentGateway {

    private final HttpClientGateway httpClientGateway;
    
    @Value("${payment.online.host:https://flqcash-test.rxcrs.com}")
    private String paymentHost;
    
    @Value("${payment.online.prepay.url:/online/pay/preview}")
    private String prePayUrl;
    
    @Value("${payment.online.pay.url:/online/pay}")
    private String payUrl;

    @Value("${payment.online.refund.url:/online/refund}")
    private String refundUrl;

    @Value("${payment.online.find.balance.url:/online/find_balance}")
    private String findBalanceUrl;

    @Value("${payment.online.check.find.balance.url:/online/check_find_balance}")
    private String checkFindBalanceUrl;

    @Value("${payment.online.card.recharge.url:/card/recharge}")
    private String cardRechargeUrl;

    @Value("${payment.online.offline.sync.order.url:/offline/sync/order}")
    private String syncOrderUrl;

    @Value("${payment.online.connect.timeout:3000}")
    private int connectTimeout;
    
    @Value("${payment.online.read.timeout:5000}")
    private int readTimeout;


    @Override
    public PrePayResponse prePayment(PrePayRequest request) {
        log.info("开始调用预支付接口，订单号：{}，来源：{}", request.getOrderNo(), request.getSource());
        
        try {
            // 构建请求JSON - 转换为下划线格式
            String requestJson = JSON.toJSONString(request);
            log.info("预支付请求参数：{}", requestJson);
            
            // 调用远程接口
            String url = paymentHost + prePayUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("预支付接口响应：{}", responseStr);
            
            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);
            
            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("预支付接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.ONLINE_PAY_PRE_INVOKE_ERROR.getCode(),errorMsg);
            }
            
            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("预支付接口返回数据为空");
                throw new BizException("预支付接口返回数据为空");
            }


            // 解析 JSON 数据为 PrePayResponse 类
            PrePayResponse response = JSON.parseObject(data, PrePayResponse.class);
            log.info("预支付处理成功，交易流水号：{},订单号：{}", response.getTradeNo(),request.getOrderNo());
            
            return response;
            
        } catch (BizException e) {
            log.error("预支付处理业务异常，订单号：{}，错误码：{} 错误信息：{}", request.getOrderNo(), e.getCode(),e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("预支付接口调用异常，订单号：{}，错误信息：{}", request.getOrderNo(), e.getMessage(), e);
            throw new BizException("预支付接口调用IO异常", e);
        } catch (Exception e) {
            log.error("预支付处理失败，订单号：{}，错误信息：{}", request.getOrderNo(), e.getMessage(), e);
            throw new BizException("预支付处理失败", e);
        }
    }

    @Override
    public PaymentResponse pay(PaymentRequest request) {
        log.info("开始调用发起支付接口，订单号：{}，预支付订单号：{}", 
                request.getOrderNo(), request.getPreOrderNo());
        
        try {
            String requestJson = JSON.toJSONString(request);
            log.info("发起支付请求参数：{}", requestJson);
            
            // 调用远程接口
            String url = paymentHost + payUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("发起支付接口响应：{}", responseStr);
            
            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (Objects.equals(code, 599)) {
                // 支付密码错误 单独处理
                String errorMsg = responseJson.getString("message");
                log.error("支付接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.ONLINE_PAY_PASS_ERROR.getCode(),errorMsg);
            }
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("支付接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.ONLINE_PAY_PAY_INVOKE_ERROR.getCode(),errorMsg);
            }
            
            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("发起支付接口返回数据为空");
                throw new BizException("预支付接口返回数据为空");
            }


            // 解析 JSON 数据为 PaymentResponse 类
            PaymentResponse response = JSON.parseObject(data, PaymentResponse.class);
            log.info("发起支付处理成功，交易流水号：{},订单号：{}", response.getTradeNo(),request.getOrderNo());

            return response;

            
        } catch (BizException e) {
            log.error("发起支付处理业务异常，订单号：{}，错误码：{} 错误信息：{}", request.getOrderNo(), e.getCode(),e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("发起支付接口调用异常，订单号：{}，错误信息：{}", request.getOrderNo(), e.getMessage(), e);
            throw new BizException("发起支付接口调用IO异常", e);
        } catch (Exception e) {
            log.error("支付处理失败，订单号：{}，错误信息：{}", request.getOrderNo(), e.getMessage(), e);
            throw new BizException("发起支付处理失败", e);
        }
    }

    @Override
    public List<FindCardBalanceResponse> findBalance(FindCardBalanceRequest request) {
        log.info("开始调用查询卡余额接口");
        try {
            String requestJson = JSON.toJSONString(request);
            log.info("查询卡余额请求参数：{}", requestJson);
            // 调用远程接口
            String url = paymentHost + findBalanceUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("查询卡余额接口响应：{}", responseStr);

            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("查询卡余额接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(),code + ":" +errorMsg);
            }

            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("查询卡余额接口返回数据为空");
                throw new BizException("查询卡余额接口返回数据为空");
            }
            JSONArray findResultJSONArray = JSON.parseObject(data).getJSONArray("find_result");
            if (findResultJSONArray == null) {
                log.error("查询卡余额接口返回数据格式错误");
                throw new BizException("查询卡余额接口返回数据格式错误");
            }

            List<FindCardBalanceResponse> responseList = JSON.parseArray(findResultJSONArray.toJSONString(), FindCardBalanceResponse.class);            log.info("查询卡余额接口查询结束");
            return responseList;

        } catch (BizException e) {
            log.error("查询卡余额接口调用异常，错误码：{} 错误信息：{}", e.getCode(), e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("查询卡余额接口调用异常，错误信息：{}", e.getMessage(), e);
            throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(), "查询卡余额接口调用IO异常", e);
        } catch (Exception e) {
            log.error("查询卡余额接口调用失败，错误信息：{}", e.getMessage(), e);
            throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(), "查询卡余额接口调用失败", e);
        }
    }

    @Override
    public CheckFindCardBalanceResponse findBalanceByCardNoAndPassword(CheckFindCardBalanceRequest request) {
        log.info("开始调用根据卡号密码查询卡余额接口");
        try {
            String requestJson = JSON.toJSONString(request);
            log.info("根据卡号密码查询卡余额请求参数：{}", requestJson);
            // 调用远程接口
            String url = paymentHost + checkFindBalanceUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("根据卡号密码查询卡余额接口响应：{}", responseStr);

            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("根据卡号密码查询卡余额接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(),errorMsg);
            }

            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("根据卡号密码查询卡余额接口返回数据为空");
                throw new BizException("根据卡号密码查询卡余额接口返回数据为空");
            }
            CheckFindCardBalanceResponse response = JSON.parseObject(data, CheckFindCardBalanceResponse.class);
            log.info("根据卡号密码查询卡余额成功，卡号：{},余额：{}", request.getCardNo(),request.getPassword());
            return response;
        } catch (BizException e) {
            log.error("根据卡号密码查询卡余额接口调用异常，错误码：{} 错误信息：{}", e.getCode(), e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("根据卡号密码查询卡余额接口调用异常，错误信息：{}", e.getMessage(), e);
            throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(), "根据卡号密码查询卡余额接口调用IO异常", e);
        } catch (Exception e) {
            log.error("根据卡号密码查询卡余额接口调用失败，错误信息：{}", e.getMessage(), e);
            throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(), "根据卡号密码查询卡余额接口调用失败", e);
        }
    }

    @Override
    public RefundResponse refund(AfterSaleEntity afterSale) {
        JSONObject params = getRefundParam(afterSale);
        String url = paymentHost + refundUrl;
        String refundResultStr;
        try {
            log.info("调用退款接口参数：{}", params);
            refundResultStr = httpClientGateway.doPostJson(url, params.toJSONString(), connectTimeout, readTimeout);
            log.info("调用退款接口返回结果：{}", refundResultStr);
        } catch (IOException e) {
            log.error("调用退款接口IO异常", e);
            throw new BizException("调用退款接口IO异常");
        }
        // 解析响应结果
        JSONObject refundResult = JSON.parseObject(refundResultStr);

        if (refundResult.getInteger("code") != 0) {
            String errMsg = refundResult.getString("message");
            throw new BizException(errMsg);
        }
        JSONObject data = refundResult.getJSONObject("data");
        return data.to(RefundResponse.class);
    }

    @Override
    public CardRechargeResponse cardRecharge(CardRechargeRequest request) {
        log.info("开始调用会员卡充值接口，充值单号：{}，来源：{}", request.getRechargeOutOrderNo(), request.getSource());

        try {
            // 构建请求JSON - 转换为下划线格式
            String requestJson = JSON.toJSONString(request);
            log.info("会员卡充值请求参数：{}", requestJson);
            // 调用远程接口
            String url = paymentHost + cardRechargeUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("会员卡充值接口响应：{}", responseStr);
            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);
            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("会员卡充值接口调用失败，错误code：{} 错误信息：{}", code, errorMsg);
                throw new BizException(Status.CARD_RECHARGE_FAIL.getCode(), errorMsg);
            }
            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("会员卡充值接口返回数据为空");
                throw new BizException("会员卡充值接口返回数据为空");
            }
            CardRechargeResponse response = JSON.parseObject(data, CardRechargeResponse.class);
            log.info("会员卡充值接口请求成功，充值状态：{}", response.getRechargeStatus());
            return response;
        } catch (BizException e) {
            log.error("会员卡充值接口调用异常，错误码：{} 错误信息：{}", e.getCode(), e.getMsg());
            throw e;
        } catch (Exception e) {
            log.error("会员卡充值接口调用失败，错误信息：{}", e.getMessage(), e);
            throw new BizException("充值失败");
        }
    }

    @Override
    public ResponseEntity<String> syncOrder(String tradeNo){
        JSONObject params = new JSONObject().fluentPut("trade_no", tradeNo);
        String url = paymentHost + syncOrderUrl;
        String syncOrderResultStr;
        try {
            log.info("调用线下同步订单接口参数：{}", params);
            syncOrderResultStr = httpClientGateway.doPostJson(url, params.toJSONString(), connectTimeout, readTimeout);
            log.info("调用线下同步订单返回结果：{}", syncOrderResultStr);
        } catch (IOException e) {
            log.error("调用线下同步订单接口IO异常", e);
            return ResponseEntity.status(HttpStatus.GATEWAY_TIMEOUT).body(null);
        }
        return ResponseEntity.ok(syncOrderResultStr);
    }

    @NotNull
    private  JSONObject getRefundParam(AfterSaleEntity afterSale) {
        Integer refundCent = afterSale.getApplyRefundAmount().multiply(new BigDecimal("100")).intValue();
        JSONObject params = new JSONObject();
        params.put("order_no", afterSale.getOrderNo());
        params.put("trade_no", afterSale.getTradeNo());
        params.put("out_refund_no", afterSale.getAfterSaleNo());
        params.put("user_id", afterSale.getCustomerId());
        params.put("refund_amount", refundCent);
        if (afterSale.getCustomerRemark() != null) {
            params.put("remark", afterSale.getCustomerRemark());
        }
        return params;
    }

}
