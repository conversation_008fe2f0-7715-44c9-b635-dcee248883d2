package com.jsrxjt.common.adapter.annotation;

import java.lang.annotation.*;

/**
 *  SignParam 接口暴露签名
 * 
 **/
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
public @interface SignParam {
    /**
     * 是否需要签名 如不需要 将不会验证签名和key
     */
    boolean isSign() default true;

   /**
    *   非0必须验签
     * 0默认本地验签规则  1提货分销接口验签
     */
    int signType() default 0;
}
