package com.jsrxjt.mobile.domain.product.service.impl;

import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.promotion.dto.PromotionLabelInfo;
import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.domain.product.service.ProductSpuBaseInfoService;
import com.jsrxjt.mobile.domain.promotion.service.PromotionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品SPU基础信息领域服务实现
 * 
 * <AUTHOR>
 * @since 2025/8/4
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductSpuBaseInfoServiceImpl implements ProductSpuBaseInfoService {

    private final ProductSkuSellRegionService productSkuSellRegionService;
    private final PromotionService promotionService;

    @Override
    public List<ProductSpuBaseInfo> filterByRegion(Integer regionId, List<ProductSpuBaseInfo> productSpuBaseInfos) {
        if (regionId == null) {
            log.warn("区域ID为空，返回所有搜索结果");
            return productSpuBaseInfos;
        }

        // 按产品类型分组处理可售性判断
        Map<Integer, List<ProductSpuBaseInfo>> productsByType = productSpuBaseInfos.stream()
                .collect(Collectors.groupingBy(ProductSpuBaseInfo::getProductType));

        List<ProductSpuBaseInfo> sellableProducts = new ArrayList<>();

        // 处理COUPON和PACKAGE类型 - 批量查询优化
        List<ProductSpuBaseInfo> couponAndPackageProducts = new ArrayList<>();
        couponAndPackageProducts.addAll(productsByType.getOrDefault(ProductTypeEnum.COUPON.getType(), Collections.emptyList()));
        couponAndPackageProducts.addAll(productsByType.getOrDefault(ProductTypeEnum.PACKAGE.getType(), Collections.emptyList()));

        if (!couponAndPackageProducts.isEmpty()) {
            sellableProducts.addAll(filterCouponAndPackageProducts(couponAndPackageProducts, regionId));
        }

        // 处理APP和COUPON_APP类型 - 逐个判断
        List<ProductSpuBaseInfo> appProducts = new ArrayList<>();
        appProducts.addAll(productsByType.getOrDefault(ProductTypeEnum.APP.getType(), Collections.emptyList()));
        appProducts.addAll(productsByType.getOrDefault(ProductTypeEnum.COUPON_APP.getType(), Collections.emptyList()));

        if (!appProducts.isEmpty()) {
            sellableProducts.addAll(filterAppProducts(appProducts, regionId));
        }
        
        if (sellableProducts.isEmpty()) {
            log.warn("在该区域的指定商品均不可售 regionId={}", regionId);
            return sellableProducts;
        }

        // 过滤掉区域不可售的商品,保持原有的搜索顺序
        return productSpuBaseInfos.stream()
                .filter(sellableProducts::contains)
                .collect(Collectors.toList());
    }

    @Override
    public void setPromotionLabels(List<ProductSpuBaseInfo> products) {
        if (products == null || products.isEmpty()) {
            return;
        }
        // 设置活动标签
        products.forEach(product -> {
            PromotionLabelInfo promotionLabelInfo = promotionService
                    .getPromotionLabelInfo(Long.valueOf(product.getSpuId()), product.getProductType());
            if (promotionLabelInfo != null) {
                product.setLabelCopy(promotionLabelInfo.getLabelCopy());
                product.setLabelCopyBackgroundImg(promotionLabelInfo.getLabelCopyBackgroundImg());
                product.setSubscriptUrl(promotionLabelInfo.getSubscriptImgUrl());
            }
        });
    }

    /**
     * 过滤COUPON和PACKAGE类型产品的可售性
     */
    private List<ProductSpuBaseInfo> filterCouponAndPackageProducts(List<ProductSpuBaseInfo> products, Integer regionId) {
        return productSkuSellRegionService.filterSellableSpuListByRegion(products, regionId);
    }

    /**
     * 过滤APP和COUPON_APP类型产品的可售性
     */
    private List<ProductSpuBaseInfo> filterAppProducts(List<ProductSpuBaseInfo> products, Integer regionId) {
        List<ProductSpuBaseInfo> sellableProducts = new ArrayList<>();
        
        for (ProductSpuBaseInfo product : products) {
            ProductTypeEnum productType = ProductTypeEnum.getByType(product.getProductType());
            boolean sellable = productSkuSellRegionService.isSellableInRegion(
                    Long.valueOf(product.getSpuId()), null, productType, regionId);
            
            if (sellable) {
                sellableProducts.add(product);
            }
        }
        
        return sellableProducts;
    }
}