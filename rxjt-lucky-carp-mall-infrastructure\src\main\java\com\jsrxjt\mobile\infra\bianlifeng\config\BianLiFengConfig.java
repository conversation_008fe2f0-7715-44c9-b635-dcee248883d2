package com.jsrxjt.mobile.infra.bianlifeng.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2025-10-22
 */
@Data
@Configuration
@RefreshScope
public class BianLiFengConfig {

    @Value("${bianlifeng.host:}")
    private String host;

    @Value("${bianlifeng.merchant:}")
    private String merchant;

    @Value("${bianlifeng.secretKey:}")
    private String secretKey;

    @Value("${bianlifeng.connectTimeout:}")
    private Integer connectTimeout;

    @Value("${bianlifeng.readTimeout:}")
    private Integer readTimeout;
}
