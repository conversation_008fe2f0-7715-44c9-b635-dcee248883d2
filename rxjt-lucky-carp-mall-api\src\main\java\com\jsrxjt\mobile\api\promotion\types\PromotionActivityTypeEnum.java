package com.jsrxjt.mobile.api.promotion.types;


/**
 * 活动类型枚举
 */
public enum PromotionActivityTypeEnum {

    DISCOUNT(1,"打折"),

    PRICE_REDUCTION(2,"直降");

    private final Integer type;

    private final String typeName;

    PromotionActivityTypeEnum(Integer type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public Integer getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }

}
