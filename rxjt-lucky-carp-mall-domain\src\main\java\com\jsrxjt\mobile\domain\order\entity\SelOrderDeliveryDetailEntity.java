package com.jsrxjt.mobile.domain.order.entity;

import lombok.Data;

import java.util.Date;


/**
 * 自发券url列表
 *
 */
@Data
public class SelOrderDeliveryDetailEntity {

    /**
     * 卡券兑换链接
     */
    private String couponUrl;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 订单时间
     */
    private Date orderTime;
    
    /**
     * 核销类型
     */
    private Integer flqType;

    //核销吗 couponUrl的最后一个字段
    private String checkCode;



}