package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.bailian.dto.request.BaiLianQRCodeRequestDTO;
import com.jsrxjt.mobile.api.bailian.dto.request.BaiLianShopRequestDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianBrandResponseDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianCategoryResponseDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianQRCodeResponseDTO;
import com.jsrxjt.mobile.api.bailian.dto.response.BaiLianShopResponseDTO;

import java.util.List;

public interface BaiLianCaseService {

    /**
     * 获取付款二维码
     * @param requestDTO 请求参数
     * @return 二维码信息
     */
    BaseResponse<BaiLianQRCodeResponseDTO> getPaymentQRCode(BaiLianQRCodeRequestDTO requestDTO);

    /**
     * 获取分类列表
     ** @return 分类列表
     */
    BaseResponse<List<BaiLianCategoryResponseDTO>> getCategoryList();

    /**
     * 根据分类id获取品牌列表
     *
     * @param categoryId 分类id
     * @return 品牌列表
     */
    BaseResponse<List<BaiLianBrandResponseDTO>> getBrandList(Long categoryId);

    /**
     * 根据品牌id获取门店列表
     *
     * @param requestDTO 请求参数
     * @return 门店列表
     */
    BaseResponse<List<BaiLianShopResponseDTO>> getShopList(BaiLianShopRequestDTO requestDTO);

}
