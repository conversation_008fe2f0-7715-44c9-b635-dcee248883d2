package com.jsrxjt.mobile.api.xcy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Created by jeffery.yang on 2023/12/21 17:02
 *
 * @description: 查询可应平台用户返参
 * @author: jeffery.yang
 * @date: 2023/12/21 17:02
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@Data
public class XcyMallQueryUserResponseDTO extends XcyMallBaseResponse {

	/**
	 * 登录用户手机号
	 * <p>required</p>
	 */
	private String phone;

	/**
	 * 用户唯一标识
	 * <p>required</p>
	 */
	private String uid;

	/**
	 * 渠道标识
	 * <p>required</p>
	 */
	private String channel;

	private String channel_name;

	/**
	 * 是否食堂 0-否 1-是
	 */
	private Integer is_canteen;

	/**
	 * 企业简称
	 */
	private String company;

	/**
	 * 分组id
	 */
	private String group_id;

	/**
	 * 分组名称
	 */
	private String group_name;
}
