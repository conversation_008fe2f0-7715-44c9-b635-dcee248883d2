package com.jsrxjt.mobile.biz.inventory.service;

import com.jsrxjt.mobile.domain.inventory.entity.SkuRegionReleasePlanEntity;
import com.jsrxjt.mobile.domain.inventory.entity.SkuReleaseInventoryEntity;
import com.jsrxjt.mobile.domain.inventory.entity.SkuReleasePlanEntity;
import com.jsrxjt.mobile.domain.inventory.types.SkuReleaseMessage;

import java.time.LocalDate;
import java.util.List;

/**
 * 定时放量库存业务接口
 * 
 * <AUTHOR> Fengping
 * @since 2025/3/10
 **/
public interface ReleaseInventoryCaseService {
    /**
     * 获取所有需要放量的SKU列表（基础放量肯定包含了特殊的区域放量的sku）
     * 
     * @param productType 产品类型 1:卡券 2:套餐
     * @return 需要放量的SKU ID列表
     */
    List<Long> getSkusWithReleasePlan(Integer productType);

    /**
     * 获取指定SKU的基础放量计划,指定区域的额外放量计划 包含在SkuReleasePlanEntity的属性下面
     * 
     * @param skuId       SKU ID
     * @param productType 产品类型 1:卡券 2:套餐
     * @return 基础放量计划列表
     */
    List<SkuReleasePlanEntity> getSkuReleasePlan(Long skuId, Integer productType);

    /**
     * 获取指定SKU的区域额外放量计划
     *
     * @param skuId       SKU ID
     * @param productType 产品类型 1:卡券 2:套餐
     * @return 区域额外放量计划列表
     */
    List<SkuRegionReleasePlanEntity> getSkuRegionReleasePlan(Long skuId, Integer productType);

    /**
     * 生成SKU放量库存记录
     * 一个放量计划记录对应一个放量库存记录，放量开始时间是放量日期+放量时间，放量结束时间是放量日期下一天+放量时间前一秒
     * 
     * @param skuReleasePlanList SKU放量计划列表
     * @param releaseDate        放量日期
     * @return 生成的放量库存记录列表
     */
    List<SkuReleaseInventoryEntity> generateReleaseInventoryRecords(List<SkuReleasePlanEntity> skuReleasePlanList,
            LocalDate releaseDate);

    /**
     * 批量保存SKU放量库存记录
     * 
     * @param releaseInventoryRecords 放量库存记录列表
     * @return 保存成功地记录数
     */
    int batchSaveReleaseInventoryRecords(List<SkuReleaseInventoryEntity> releaseInventoryRecords);

    /**
     * 执行次日放量任务
     * 该方法会执行完整的放量流程：
     * 1. 获取所有需要放量的SKU
     * 2. 遍历每个SKU：
     * a. 获取基础放量计划
     * b. 获取区域额外放量计划
     * 3. 一个放量计划记录对应一个放量库存记录，放量开始时间是次日+放量时间，放量结束时间是放量日期下一天+放量时间的前一秒
     * 4. 生成次日放量库存记录
     * 5. 写入数据库
     * 
     * @param productType 产品类型 1:卡券 2:套餐
     * @return 处理成功的SKU数量
     */
    int executeNextDayReleaseTask(Integer productType);

    /**
     * 处理单个SKU的放量计划
     * 
     * @param skuId       SKU ID
     * @param productType 产品类型 1:卡券 2:套餐
     * @param releaseDate 放量日期
     * @return 处理是否成功
     */
    boolean processSkuReleasePlan(Long skuId, Integer productType, LocalDate releaseDate);

    /**
     * 自动更新放量库存状态
     * 根据当前时间自动更新放量库存状态：
     * 1. 当前时间 >= 放量开始时间 且 < 放量结束时间，状态更新为"已生效(1)"
     * 2. 当前时间 >= 放量结束时间，状态更新为"已结束(2)"
     * 
     * @param productType 产品类型 1:卡券 2:套餐
     * @return 更新成功地记录数
     */
    int autoUpdateReleaseInventoryStatus(Integer productType);

    /**
     * 处理放量计划变更
     * 根据操作类型处理放量计划的变更：
     * 1. 新增：立即生成当天的放量库存记录
     * 2. 编辑：更新未结束的放量库存记录
     * 3. 删除：标记未结束的放量库存记录为删除状态
     *
     * @param releaseMessage 放量计划变更消息，包含操作类型和计划ID列表
     * @return 处理结果，true表示处理成功
     */
    boolean handleReleasePlanChange(SkuReleaseMessage releaseMessage);
}
