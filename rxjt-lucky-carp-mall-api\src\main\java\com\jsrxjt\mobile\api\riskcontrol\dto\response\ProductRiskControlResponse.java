package com.jsrxjt.mobile.api.riskcontrol.dto.response;

import com.jsrxjt.mobile.api.riskcontrol.types.RiskControlStrategyStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/25 17:59
 */
@Data
public class ProductRiskControlResponse {

    /**
     * 风控策略id
     */
    private Long id;

    /**
     * 风险账户id
     */
    private Long riskAccountId;

    private Long customerId;

    /**
     * 1、当前商品未在风控策略中
     * 2、当前商品在风控策略中且用户命中策略
     * 3、当前商品在策略中，用户未命中策略
     */
    private Integer hitStrategyStatus;

    /**
     * 风控策略类型 1白名单策略 2黑名单策略
     */
    private Integer strategyType;
    /**
     * 风控产品类型
     */
    private Integer riskProductType;
    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 产品spuId
     */
    private Long productSpuId;

    /**
     * 产品skuId 应用为0
     */
    private Long productSkuId;

    /**
     * 手续费百分比
     */
    private BigDecimal commissionFee;

    /**
     * 每人每日限购张数
     */
    private Integer limitNumPerDay;

    /**
     * 每人每月限购张数
     */
    private Integer limitNumPerMonth;

    /**
     * spu每月限售总额
     */
    private BigDecimal spuAmountPerMonth;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 是否命中风控策略
     */
    public boolean isHitStrategy() {
        return Objects.equals(RiskControlStrategyStatus.HIT_STRATEGY.getStatus(), hitStrategyStatus);
    }

}
