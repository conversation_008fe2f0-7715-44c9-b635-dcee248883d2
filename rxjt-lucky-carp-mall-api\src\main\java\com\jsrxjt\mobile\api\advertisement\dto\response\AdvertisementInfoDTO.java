package com.jsrxjt.mobile.api.advertisement.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 广告信息
 * @Author: ywt
 * @Date: 2025-05-09 13:52
 * @Version: 1.0
 */
@Data
@Schema(description = "广告信息响应")
public class AdvertisementInfoDTO {
    @Schema(description = "广告id")
    private Long id;
    @Schema(description = "广告标题名称")
    private String advName;//广告标题名称
    @Schema(description = "广告图")
    private String imlUrl;//广告图
    @Schema(description = "ios跳转路径")
    private String iosUrl;
    @Schema(description = "android跳转路径")
    private String androidUrl;
    @Schema(description = "小程序appid")
    private String wechatMiniAppid;
    @Schema(description = "小程序跳转链接")
    private String wechatMiniUrl;
}
