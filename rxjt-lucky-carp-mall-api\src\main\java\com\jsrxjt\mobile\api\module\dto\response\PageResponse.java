package com.jsrxjt.mobile.api.module.dto.response;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.core.util.Json;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Schema(description = "页面详情")
public class PageResponse {

    @Schema(description = "组件id")
    private Integer moduleId;

    /**
     * 组件code
     */
    @Schema(description = "组件code")
    private String moduleCode;

    /**
     * 组件名称
     */
    @Schema(description = "组件名称")
    private String moduleTitle;

    /**
     * 装修样式属性json
     */
    @Schema(description = "修样式属性json")
    private Map<String, Object> content;

    @Schema(description = "组件详情列表")
    private List<ModuleDetailResponse> moduleDetails;
}
