package com.jsrxjt.mobile.api.coupon.dto.response;

import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.product.dto.ProductExplainResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * @Description: 卡券信息响应
 * @Author: ywt
 * @Date: 2025-04-16 13:42
 * @Version: 1.0
 */
@Data
@Schema(description = "卡券信息响应")
public class CouponInfoResponseDTO {
    @Schema(description = "卡券spuId")
    private Long couponSpuId;

    @Schema(description = "品牌名称")
    private String  brandName;

    @Schema(description = "卡券类型，1:普通卡券 2:账号直冲,3:品诺")
    private Integer couponType;

    @Schema( description = "卡券名称")
    private String couponSpuName;

    @Schema( description = "卡券副标题")
    private String subTitle;

    @Schema( description = "状态 0:下架 1:出售中")
    private Integer status;

    @Schema(description = "是否自发券，0:否  1:是）")
    private Integer isSelfCoupon;

    @Schema(description = "提货券分销平台的产品id，isSelfCoupon = 1时有效")
    private Integer pickProductId;

    @Schema(description = "兑换流程富文本")
    private String exchangeProcess;

    @Schema( description = "卡券销量")
    private Integer virtualStock;

    @Schema( description = "卡券转发量")
    private Integer forwardNum;

    @Schema(description = "角标url")
    private String subscriptUrl;

    @Schema(description = "卡券服务标签列表")
    private List<String> labelList;

    @Schema( description = "logo图片url")
    private String logoUrl;

    @Schema( description = "卡券图片url")
    private String imgUrl;

    @Schema( description = "用于前端判断详情页使用哪种模版样式显示，暂用于直充类卡券")
    private Integer showTemplate;

    @Schema( description = "兑换须知")
    private List<ProductExplainResponseDTO> exchangeList;

    @Schema( description = "核销须知")
    private List<ProductExplainResponseDTO> offsetList;
    @Schema( description = "广告列表")
    private List<AdvertisementInfoDTO> advertiseList;

    @Schema( description = "选中/第一个可售的sku信息")
    CouponSkuInfoResponseDTO skuInfoResponseDTO;
}
