package com.jsrxjt.mobile.api.scanPay.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 卡号dto
 * @Author: ZY
 * @Date: 20250530
 */
@Data
@Schema(description = "951卡号请求dto")
public class HomeScanNumberRequestDTO {
    @Schema(description = "951卡号")
    @NotBlank(message = "卡号不能为空")
    private String code;
}
