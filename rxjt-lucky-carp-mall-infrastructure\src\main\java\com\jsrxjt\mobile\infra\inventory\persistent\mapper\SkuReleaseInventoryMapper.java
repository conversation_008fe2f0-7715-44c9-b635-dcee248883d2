package com.jsrxjt.mobile.infra.inventory.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.inventory.persistent.po.SkuReleaseInventoryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * SKU放量库存记录 Mapper
 */
@Mapper
public interface SkuReleaseInventoryMapper extends CommonBaseMapper<SkuReleaseInventoryPO> {

        /**
         * 批量插入SKU放量库存记录
         */
        int batchInsert(List<SkuReleaseInventoryPO> records);

        /**
         * 更新已开始但未结束的放量库存状态为已生效(1)
         * 
         * @param productType 产品类型
         * @param currentTime 当前时间
         * @return 更新成功的记录数
         */
        int updateStartedInventoryStatus(@Param("productType") Integer productType,
                        @Param("currentTime") LocalDateTime currentTime);

        /**
         * 更新已结束的放量库存状态为已结束(2)
         * 
         * @param productType 产品类型
         * @param currentTime 当前时间
         * @return 更新成功的记录数
         */
        int updateEndedInventoryStatus(@Param("productType") Integer productType,
                        @Param("currentTime") LocalDateTime currentTime);

        /**
         * 根据计划ID和政策ID查询未结束的放量库存记录
         * 
         * @param planId   放量计划ID
         * @param policyId 政策ID
         * @return 未结束的放量库存记录列表
         */
        List<SkuReleaseInventoryPO> selectListUnfinishedByPlanId(@Param("planId") Long planId,
                        @Param("policyId") Long policyId);

        /**
         * 批量更新放量库存记录
         * 
         * @param records 需要更新的放量库存记录列表
         * @return 更新成功的记录数
         */
        int batchUpdateReleaseInventories(@Param("list") List<SkuReleaseInventoryPO> records);

        /**
         * 更新未结束的放量库存记录为删除状态
         * 
         * @param planId  放量计划ID
         * @param modTime 修改时间
         * @return 更新成功的记录数
         */
        int updateUnfinishedAsDeleted(@Param("planId") Long planId, @Param("modTime") LocalDateTime modTime);

        /**
         * 检查指定计划ID和放量日期的放量库存记录是否存在
         * 
         * @param planId    放量计划ID
         * @param startDate 放量起始日期
         * @param endDate   放量结束日期
         * @return 记录数
         */
        int countByPlanIdAndDateRange(@Param("planId") Long planId, @Param("startDate") LocalDate startDate,
                        @Param("endDate") LocalDate endDate);

        /**
         * 检查指定计划ID、政策ID和放量日期的放量库存记录是否存在
         * 
         * @param planId    放量计划ID
         * @param policyId  政策ID
         * @param startDate 放量起始日期
         * @param endDate   放量结束日期
         * @return 记录数
         */
        int countByPlanIdAndPolicyIdAndDateRange(@Param("planId") Long planId,
                        @Param("policyId") Long policyId,
                        @Param("startDate") LocalDate startDate,
                        @Param("endDate") LocalDate endDate);

        List<SkuReleaseInventoryPO> selectValidByProductTypeAndSkuIdAndPolicyId(
                        @Param("productType") Integer productType,
                        @Param("skuId") Long skuId,
                        @Param("policyId") long policyId);

        /**
         * 根据ID扣减库存数量
         * 
         * @param id       库存记录ID
         * @param quantity 扣减数量
         * @return 更新成功的记录数
         */
        int reduceInventoryById(@Param("id") Long id, @Param("quantity") Integer quantity);

        /**
         * 批量根据ID扣减库存数量
         * 
         * @param reductionList 扣减列表，包含ID和扣减数量
         * @return 更新成功的记录数
         */
        int batchReduceInventoryById(@Param("list") List<Map<String, Object>> reductionList);

        /**
         * 根据ID恢复库存数量
         * 
         * @param id       库存记录ID
         * @param quantity 恢复数量
         * @return 更新成功的记录数
         */
        int recoverInventoryById(@Param("id") Long id, @Param("quantity") Integer quantity);

        /**
         * 批量根据ID恢复库存数量
         *
         * @param reductionList 恢复列表，包含ID和恢复数量
         * @return 更新成功的记录数
         */
        int batchRecoverInventoryById(@Param("list") List<Map<String, Object>> reductionList);

        /**
         * 根据产品SKU ID、产品类型、政策ID和日期查询当天的放量库存记录
         *
         * @param productSkuId 产品SKU ID
         * @param productType  产品类型
         * @param policyId     政策ID
         * @param releaseDate  放量日期
         * @return 当天的放量库存记录列表
         */
        List<SkuReleaseInventoryPO> selectTodayReleaseInventoryBySkuAndPolicy(@Param("productSkuId") Long productSkuId,
                        @Param("productType") Integer productType, @Param("policyId") Long policyId,
                        @Param("releaseDate") LocalDate releaseDate);
}