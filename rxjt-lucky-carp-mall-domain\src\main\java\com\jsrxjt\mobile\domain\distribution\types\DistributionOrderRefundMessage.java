package com.jsrxjt.mobile.domain.distribution.types;

import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import lombok.AllArgsConstructor;
import lombok.Value;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Value
@AllArgsConstructor
public class DistributionOrderRefundMessage {

    /**
     * 应用类型
     */
    String appFlag;
    /**
     * 分销中心业务交易号
     */
    String disTradeNo;

    /**
     * 福鲤圈支付交易号
     */
    String flqTradeNo;
    /**
     * 分销中心订单号
     */
    String orderNo;
    /**
     * 福鲤圈订单号
     */
    String flqOrderNo;
    /**
     * 分销中心退款订单号
     */
    String refundNo;
    /**
     * 福鲤圈退款单号
     */
    String flqRefundNo;

    /**
     * 退款状态
     */
    Integer refundStatus;

    /**
     * 退款成功时间
     */
    LocalDateTime refundTime;

    /**
     * 退款金额
     */
    BigDecimal refundAmount;


    public DistributionOrderRefundMessage(OrderInfoEntity orderInfo, AfterSaleEntity afterSaleEntity) {
        this.appFlag = orderInfo.getAppFlag();
        this.disTradeNo = orderInfo.getDistTradeNo();
        this.flqTradeNo = orderInfo.getTradeNo();
        this.flqOrderNo = orderInfo.getOrderNo();
        this.orderNo = orderInfo.getExternalOrderNo();
        this.refundNo = afterSaleEntity.getExternalRefundNo();
        this.flqRefundNo = afterSaleEntity.getRefundNo();
        this.refundStatus = afterSaleEntity.getRefundStatus();
        this.refundTime = afterSaleEntity.getRefundSuccessTime();
        this.refundAmount = afterSaleEntity.getExternalRefundAmount();
    }
}
