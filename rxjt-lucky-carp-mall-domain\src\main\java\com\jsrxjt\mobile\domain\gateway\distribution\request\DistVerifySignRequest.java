package com.jsrxjt.mobile.domain.gateway.distribution.request;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025-10-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistVerifySignRequest {
    /**
     * 分销渠道类型
     */
    private DistChannelType channelType;

    /**
     * 待验签的对象
     */
    private Object verifySignDTO;
}
