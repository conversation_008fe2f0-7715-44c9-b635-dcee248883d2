package com.jsrxjt.mobile.biz.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformFactory;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.payment.types.PaymentSuccessMessage;
import com.jsrxjt.mobile.domain.ticket.config.TicketPlatformConfig;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketInfo;
import com.jsrxjt.mobile.domain.ticket.gateway.TicketPlatformGateway;
import com.jsrxjt.mobile.domain.ticket.gateway.cmd.GiveOutTicketCmd;
import com.jsrxjt.mobile.domain.ticket.repository.GiftTicketOrderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PaymentSuccessCaseServiceImpl测试类
 * 专注于测试不需要静态Mock的核心逻辑
 *
 * <AUTHOR>
 * @date 2025/09/23
 */
@ExtendWith(MockitoExtension.class)
class PaymentSuccessCaseServiceImplTest {

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private DistributedLock distributedLock;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private GiftTicketOrderRepository giftTicketOrderRepository;

    @Mock
    private CustomerRepository customerRepository;

    @Mock
    private TicketPlatformConfig ticketPlatformConfig;

    @Mock
    private TicketPlatformGateway ticketPlatformGateway;

    @InjectMocks
    private PaymentSuccessCaseServiceImpl paymentSuccessCaseService;

    private PaymentSuccessMessage paymentSuccessMessage;
    private OrderInfoEntity orderInfo;
    private CustomerEntity customer;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        paymentSuccessMessage = new PaymentSuccessMessage(1L, "ORDER123", 100L, "APP", 1001);

        orderInfo = new OrderInfoEntity();
        orderInfo.setId(1L);
        orderInfo.setOrderNo("ORDER123");
        orderInfo.setCustomerId(100L);

        // 客户信息
        customer = new CustomerEntity();
        customer.setId(100L);
        customer.setUnionid("test_union_id");
    }

    @Test
    void testHandleGiftTicketsSend_Success_OnlyGlobalTickets() throws InterruptedException {
        // Given - 只测试全球购券，避免静态Mock
        List<GiftTicketInfo> onlyGlobalTickets = new ArrayList<>();
        GiftTicketInfo globalTicket = new GiftTicketInfo();
        globalTicket.setTicketId(1L);
        globalTicket.setTicketName("全球购券");
        globalTicket.setTicketType(1); // 全球购券
        globalTicket.setCenterCouponId("123");
        globalTicket.setTicketNum(2);
        globalTicket.setBrandId(10L);
        globalTicket.setSpecPicUrl("http://example.com/pic1.jpg");
        onlyGlobalTickets.add(globalTicket);

        String giftTicketJson = JSON.toJSONString(onlyGlobalTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);
        when(customerRepository.selectCustomerById(100L)).thenReturn(customer);

        // Mock营销中台发券成功（全球购券也通过营销中台发放）
        List<String> ticketNumbers = Arrays.asList("GLOBAL_TICKET_001");
        when(ticketPlatformGateway.giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class)))
                .thenReturn(ticketNumbers);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(customerRepository).selectCustomerById(100L);
        verify(ticketPlatformGateway).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
    }

    @Test
    void testHandleGiftTicketsSend_NoCacheData() {
        // Given
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        when(redisUtil.get(cacheKey)).thenReturn(null);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock, never()).tryLock(anyString());
        verify(orderRepository, never()).findByOrderNo(anyString());
    }

    @Test
    void testHandleGiftTicketsSend_LockFailed() throws InterruptedException {
        // Given
        List<GiftTicketInfo> giftTicketInfos = createGlobalTicketInfos();
        String giftTicketJson = JSON.toJSONString(giftTicketInfos);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(false);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository, never()).findByOrderNo(anyString());
        verify(distributedLock, never()).unLock(anyString());
    }

    @Test
    void testHandleGiftTicketsSend_OrderNotFound() throws InterruptedException {
        // Given
        List<GiftTicketInfo> giftTicketInfos = createGlobalTicketInfos();
        String giftTicketJson = JSON.toJSONString(giftTicketInfos);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(null);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(distributedLock).unLock(lockKey);
        verify(giftTicketOrderRepository, never()).batchSave(anyList());
    }

    @Test
    void testHandleGiftTicketsSend_EmptyGiftTickets() throws InterruptedException {
        // Given
        String giftTicketJson = JSON.toJSONString(new ArrayList<>());
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(giftTicketOrderRepository, never()).batchSave(anyList());
        verify(distributedLock).unLock(lockKey);
    }

    /**
     * 创建全球购券测试数据
     */
    private List<GiftTicketInfo> createGlobalTicketInfos() {
        List<GiftTicketInfo> giftTicketInfos = new ArrayList<>();

        GiftTicketInfo globalTicket = new GiftTicketInfo();
        globalTicket.setTicketId(1L);
        globalTicket.setTicketName("全球购券");
        globalTicket.setTicketType(1); // 全球购券，使用unionId作为userCode
        globalTicket.setCenterTicketId("GLOBAL_TICKET_001"); // 全球购券使用centerTicketId
        globalTicket.setTicketNum(2);
        globalTicket.setBrandId(10L);
        globalTicket.setSpecPicUrl("http://example.com/pic1.jpg");
        giftTicketInfos.add(globalTicket);

        return giftTicketInfos;
    }

    @Test
    void testHandleGiftTicketsSend_OnlyCouponTickets_Success() throws InterruptedException {
        // Given - 只有卡管券，测试成功场景
        List<GiftTicketInfo> couponTickets = createCouponTicketInfos();
        String giftTicketJson = JSON.toJSONString(couponTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
        // 验证没有调用营销中台发券接口
        verify(ticketPlatformGateway, never()).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
    }

    @Test
    void testHandleGiftTicketsSend_MixedTickets_Success() throws InterruptedException {
        // Given - 混合券类型，测试成功场景
        List<GiftTicketInfo> mixedTickets = createMixedTicketInfos();
        String giftTicketJson = JSON.toJSONString(mixedTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);
        when(customerRepository.selectCustomerById(100L)).thenReturn(customer);

        // Mock营销中台发券成功（所有券类型都通过营销中台发放）
        List<String> ticketNumbers = Arrays.asList("TICKET_001", "TICKET_002");
        when(ticketPlatformGateway.giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class)))
                .thenReturn(ticketNumbers);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(customerRepository).selectCustomerById(100L);
        verify(ticketPlatformGateway, atLeastOnce()).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
    }

    @Test
    void testHandleGiftTicketsSend_CouponPlatformFactoryTest() {
        // Given - 测试CouponPlatformFactory的使用
        try {
            // 直接使用CouponPlatformFactory获取策略，不使用静态Mock
            CouponPlatformStrategy couponPlatform = CouponPlatformFactory.getCouponPlatform(CouponTypeEnum.REGULAR);

            // 验证策略不为空（如果工厂正确配置的话）
            // 注意：在单元测试环境中，由于没有Spring容器，这个调用可能会抛出异常
            // 这个测试主要是为了演示如何使用CouponPlatformFactory
            assertNotNull(couponPlatform, "CouponPlatformStrategy should not be null");

        } catch (IllegalArgumentException e) {
            // 在测试环境中，由于没有Spring容器初始化，可能会抛出异常
            // 这是预期的行为
            assertTrue(e.getMessage().contains("coupon type not supported") ||
                    e.getMessage().contains("coupon type is empty"),
                    "Expected IllegalArgumentException with appropriate message");
        }
    }

    /**
     * 创建卡管券测试数据
     */
    private List<GiftTicketInfo> createCouponTicketInfos() {
        List<GiftTicketInfo> giftTicketInfos = new ArrayList<>();

        // 卡管券 - 类型0
        // GiftTicketInfo couponTicket = new GiftTicketInfo();
        // couponTicket.setTicketId(2L);
        // couponTicket.setTicketName("卡管券");
        // couponTicket.setTicketType(0); // 卡管券
        // couponTicket.setCenterCouponId("456");
        // couponTicket.setTicketNum(1);
        // couponTicket.setBrandId(20L);
        // couponTicket.setSpecPicUrl("http://example.com/pic2.jpg");
        // giftTicketInfos.add(couponTicket);

        // 瑞祥代发券 - 类型2
        GiftTicketInfo rxTicket = new GiftTicketInfo();
        rxTicket.setTicketId(20L);
        rxTicket.setTicketName("久久洗衣优惠券");
        rxTicket.setTicketType(2); // 瑞祥代发券
        rxTicket.setCenterCouponId("1642");
        rxTicket.setTicketNum(1);
        rxTicket.setBrandId(11L);
        rxTicket.setSpecPicUrl("https://lucky-pic.rxlpzj.com/flq/backend/202509240946232791.jpeg");
        giftTicketInfos.add(rxTicket);

        return giftTicketInfos;
    }

    /**
     * 创建混合券类型测试数据
     */
    private List<GiftTicketInfo> createMixedTicketInfos() {
        List<GiftTicketInfo> giftTicketInfos = new ArrayList<>();

        // 全球购券
        GiftTicketInfo globalTicket = new GiftTicketInfo();
        globalTicket.setTicketId(1L);
        globalTicket.setTicketName("全球购券");
        globalTicket.setTicketType(1); // 全球购券
        globalTicket.setCenterCouponId("123");
        globalTicket.setTicketNum(1);
        globalTicket.setBrandId(10L);
        globalTicket.setSpecPicUrl("http://example.com/pic1.jpg");
        giftTicketInfos.add(globalTicket);

        // 卡管券
        GiftTicketInfo couponTicket = new GiftTicketInfo();
        couponTicket.setTicketId(20L);
        couponTicket.setTicketName("卡管券");
        couponTicket.setTicketType(2); // 卡管券
        couponTicket.setCenterCouponId("456");
        couponTicket.setTicketNum(1);
        couponTicket.setBrandId(20L);
        couponTicket.setSpecPicUrl("http://example.com/pic2.jpg");
        giftTicketInfos.add(couponTicket);

        return giftTicketInfos;
    }

    @Test
    void testHandleGiftTicketsSend_MarketingTickets_Success() throws InterruptedException {
        // Given - 测试营销中台发券成功场景
        List<GiftTicketInfo> marketingTickets = createMarketingTicketInfos();
        String giftTicketJson = JSON.toJSONString(marketingTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);
        when(customerRepository.selectCustomerById(100L)).thenReturn(customer);

        // Mock营销中台发券成功
        List<String> ticketNumbers = Arrays.asList("TICKET001", "TICKET002");
        when(ticketPlatformGateway.giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class)))
                .thenReturn(ticketNumbers);

        // Mock配置
        when(ticketPlatformConfig.getGiveNotifyUrl()).thenReturn("http://test.com/notify");

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(customerRepository).selectCustomerById(100L);
        verify(ticketPlatformGateway).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
    }

    @Test
    void testHandleGiftTicketsSend_MarketingTickets_Failed() throws InterruptedException {
        // Given - 测试营销中台发券失败场景
        List<GiftTicketInfo> marketingTickets = createMarketingTicketInfos();
        String giftTicketJson = JSON.toJSONString(marketingTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);
        when(customerRepository.selectCustomerById(100L)).thenReturn(customer);

        // Mock营销中台发券失败 - 返回空列表
        when(ticketPlatformGateway.giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class)))
                .thenReturn(new ArrayList<>());

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(customerRepository).selectCustomerById(100L);
        verify(ticketPlatformGateway).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
    }

    @Test
    void testHandleGiftTicketsSend_MarketingTickets_Exception() throws InterruptedException {
        // Given - 测试营销中台发券异常场景
        List<GiftTicketInfo> marketingTickets = createMarketingTicketInfos();
        String giftTicketJson = JSON.toJSONString(marketingTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);
        when(customerRepository.selectCustomerById(100L)).thenReturn(customer);

        // Mock营销中台发券抛出异常
        when(ticketPlatformGateway.giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class)))
                .thenThrow(new RuntimeException("网络异常"));

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(customerRepository).selectCustomerById(100L);
        verify(ticketPlatformGateway).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
    }

    @Test
    void testHandleGiftTicketsSend_CustomerNotFound() throws InterruptedException {
        // Given - 测试客户不存在的场景
        List<GiftTicketInfo> globalTickets = createGlobalTicketInfos();
        String giftTicketJson = JSON.toJSONString(globalTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);
        when(customerRepository.selectCustomerById(100L)).thenReturn(null); // 客户不存在

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(customerRepository).selectCustomerById(100L);
        // 由于客户不存在，不应该调用营销中台发券接口
        verify(ticketPlatformGateway, never()).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
    }

    @Test
    void testHandleGiftTicketsSend_TicketPlatform_Failed() throws InterruptedException {
        // Given - 测试营销中台发券失败场景
        List<GiftTicketInfo> globalTickets = createGlobalTicketInfos();
        String giftTicketJson = JSON.toJSONString(globalTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);
        when(customerRepository.selectCustomerById(100L)).thenReturn(customer);

        // Mock营销中台发券失败（返回空列表表示失败）
        when(ticketPlatformGateway.giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class)))
                .thenReturn(new ArrayList<>());

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(customerRepository).selectCustomerById(100L);
        verify(ticketPlatformGateway).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
    }

    /**
     * 创建营销中台券测试数据
     */
    private List<GiftTicketInfo> createMarketingTicketInfos() {
        List<GiftTicketInfo> giftTicketInfos = new ArrayList<>();

        // 营销中台券 - 类型4
        GiftTicketInfo marketingTicket = new GiftTicketInfo();
        marketingTicket.setTicketId(4L);
        marketingTicket.setTicketName("营销中台券");
        marketingTicket.setTicketType(4); // 营销中台券
        marketingTicket.setCenterTicketId("MARKETING_001");
        marketingTicket.setTicketNum(2);
        marketingTicket.setBrandId(40L);
        marketingTicket.setSpecPicUrl("http://example.com/pic4.jpg");
        giftTicketInfos.add(marketingTicket);

        return giftTicketInfos;
    }

    @Test
    void testHandleGiftTicketsSend_InvalidGiftTicketJson() throws InterruptedException {
        // Given - 测试无效的JSON格式
        String invalidJson = "invalid json format";
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(invalidJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);

        // When & Then - 应该抛出异常或优雅处理
        assertDoesNotThrow(() -> {
            paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);
        });

        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(distributedLock).unLock(lockKey);
    }

    @Test
    void testHandleGiftTicketsSend_NullGiftTicketJson() throws InterruptedException {
        // Given - 测试JSON解析为null的情况
        String nullJson = "null";
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(nullJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(giftTicketOrderRepository, never()).batchSave(anyList());
        verify(distributedLock).unLock(lockKey);
    }

    @Test
    void testHandleGiftTicketsSend_CustomerWithoutUnionId() throws InterruptedException {
        // Given - 测试客户没有unionId的场景（影响全球购券的userCode）
        List<GiftTicketInfo> globalTickets = createGlobalTicketInfos();
        String giftTicketJson = JSON.toJSONString(globalTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        // 创建没有unionId的客户
        CustomerEntity customerWithoutUnionId = new CustomerEntity();
        customerWithoutUnionId.setId(100L);
        customerWithoutUnionId.setUnionid(null); // 没有unionId

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);
        when(customerRepository.selectCustomerById(100L)).thenReturn(customerWithoutUnionId);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(customerRepository).selectCustomerById(100L);
        // 由于客户没有unionId，不应该调用营销中台发券接口
        verify(ticketPlatformGateway, never()).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
    }

    @Test
    void testHandleGiftTicketsSend_AllTicketTypes_Mixed() throws InterruptedException {
        // Given - 测试包含所有券类型的混合场景
        List<GiftTicketInfo> allTypesTickets = createAllTypesTicketInfos();
        String giftTicketJson = JSON.toJSONString(allTypesTickets);
        String cacheKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + "ORDER123";
        String lockKey = "gift_tickets_send_lock:ORDER123";

        when(redisUtil.get(cacheKey)).thenReturn(giftTicketJson);
        when(distributedLock.tryLock(lockKey)).thenReturn(true);
        when(orderRepository.findByOrderNo("ORDER123")).thenReturn(orderInfo);
        when(customerRepository.selectCustomerById(100L)).thenReturn(customer);

        // Mock营销中台发券成功（所有券类型都通过营销中台发放）
        List<String> ticketNumbers = Arrays.asList("TICKET001", "TICKET002");
        when(ticketPlatformGateway.giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class)))
                .thenReturn(ticketNumbers);

        // When
        paymentSuccessCaseService.handleGiftTicketsSend(paymentSuccessMessage);

        // Then
        verify(redisUtil).get(cacheKey);
        verify(distributedLock).tryLock(lockKey);
        verify(orderRepository).findByOrderNo("ORDER123");
        verify(customerRepository).selectCustomerById(100L);
        verify(ticketPlatformGateway, atLeastOnce()).giveOutTicket(any(GiveOutTicketCmd.class), any(BiConsumer.class));
        verify(giftTicketOrderRepository).batchSave(anyList());
        verify(redisUtil).delete(cacheKey);
        verify(distributedLock).unLock(lockKey);
    }

    /**
     * 创建包含所有券类型的测试数据
     */
    private List<GiftTicketInfo> createAllTypesTicketInfos() {
        List<GiftTicketInfo> giftTicketInfos = new ArrayList<>();

        // 全球购券 - 类型1
        GiftTicketInfo globalTicket = new GiftTicketInfo();
        globalTicket.setTicketId(1L);
        globalTicket.setTicketName("全球购券");
        globalTicket.setTicketType(1);
        globalTicket.setCenterCouponId("GLOBAL_001");
        globalTicket.setTicketNum(1);
        globalTicket.setBrandId(10L);
        globalTicket.setSpecPicUrl("http://example.com/global.jpg");
        giftTicketInfos.add(globalTicket);

        // 瑞祥代发券 - 类型2
        GiftTicketInfo rxTicket = new GiftTicketInfo();
        rxTicket.setTicketId(2L);
        rxTicket.setTicketName("瑞祥代发券");
        rxTicket.setTicketType(2);
        rxTicket.setCenterTicketId("RX_001");
        rxTicket.setTicketNum(1);
        rxTicket.setBrandId(20L);
        rxTicket.setSpecPicUrl("http://example.com/rx.jpg");
        giftTicketInfos.add(rxTicket);

        // 商家自发券 - 类型3
        GiftTicketInfo merchantTicket = new GiftTicketInfo();
        merchantTicket.setTicketId(3L);
        merchantTicket.setTicketName("商家自发券");
        merchantTicket.setTicketType(3);
        merchantTicket.setCenterTicketId("MERCHANT_001");
        merchantTicket.setTicketNum(1);
        merchantTicket.setBrandId(30L);
        merchantTicket.setSpecPicUrl("http://example.com/merchant.jpg");
        giftTicketInfos.add(merchantTicket);

        // 营销中台券 - 类型4
        GiftTicketInfo marketingTicket = new GiftTicketInfo();
        marketingTicket.setTicketId(4L);
        marketingTicket.setTicketName("营销中台券");
        marketingTicket.setTicketType(4);
        marketingTicket.setCenterTicketId("MARKETING_001");
        marketingTicket.setTicketNum(1);
        marketingTicket.setBrandId(40L);
        marketingTicket.setSpecPicUrl("http://example.com/marketing.jpg");
        giftTicketInfos.add(marketingTicket);

        return giftTicketInfos;
    }
}
