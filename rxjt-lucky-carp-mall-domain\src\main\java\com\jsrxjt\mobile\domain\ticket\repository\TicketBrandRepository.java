package com.jsrxjt.mobile.domain.ticket.repository;

import com.jsrxjt.mobile.domain.ticket.entity.TicketBrandEntity;

import java.util.List;

/**
 * @Description: 优惠券品牌领域接口
 * @Author: ywt
 * @Date: 2025-08-20 17:50
 * @Version: 1.0
 */
public interface TicketBrandRepository {
    List<TicketBrandEntity> getTicketBrandsByIds(List<Long> brandIds);

    /**
     * 根据brandId查询单个优惠券品牌
     * 
     * @param brandId 品牌ID
     * @return 优惠券品牌实体
     */
    TicketBrandEntity getTicketBrandById(Long brandId);
}
