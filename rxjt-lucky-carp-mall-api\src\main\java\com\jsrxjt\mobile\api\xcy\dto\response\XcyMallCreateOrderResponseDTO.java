package com.jsrxjt.mobile.api.xcy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Created by jeffery.yang on 2023/12/21 17:02
 *
 * @description: 查询可应平台预下单返参
 * @author: jeffery.yang
 * @date: 2023/12/21 17:02
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@Data
public class XcyMallCreateOrderResponseDTO extends XcyMallBaseResponse {

	/**
	 * 福鲤圈订单号，用于支付、退款请求
	 * <p>required</p>
	 */
	private String outTradeNo;

	/**
	 * （用户）跳转收银台地址
	 * <p>required</p>
	 */
	private String payUrl;

}
