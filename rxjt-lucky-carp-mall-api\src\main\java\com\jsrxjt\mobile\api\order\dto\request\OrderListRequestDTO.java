package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 订单列表查询请求参数
 * 
 * <AUTHOR>
 * @since 2025/7/17
 */
@Getter
@Setter
public class OrderListRequestDTO extends BaseParam {
    
    @Schema(description = "订单状态：0-待付款 10-进行中 20-交易成功 30-交易关闭 40-超时取消 41-手动取消，-1查全部")
    @NotNull(message = "订单状态不能为空")
    private Integer orderStatus;

    @Schema(description = "创建时间开始（格式：yyyy-MM-dd）")
    private String createTimeStart;

    @Schema(description = "创建时间结束（格式：yyyy-MM-dd）")
    private String createTimeEnd;

    @Schema(description = "商品一级分类ID")
    private Long firstCategoryId;

    @Schema(description = "搜索关键字 商品名称/订单号")
    private String keyword;
}
