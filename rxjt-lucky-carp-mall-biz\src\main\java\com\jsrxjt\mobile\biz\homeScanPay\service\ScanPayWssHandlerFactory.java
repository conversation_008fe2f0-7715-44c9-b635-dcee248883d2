package com.jsrxjt.mobile.biz.homeScanPay.service;

import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.response.ScanPayReceiverResponseDTO;
import com.jsrxjt.mobile.api.scanPay.types.WssPushTypeEnum;
import com.jsrxjt.mobile.api.scanPay.types.ScanWsReceiveTypeEnum;
import com.jsrxjt.mobile.biz.homeScanPay.service.handler.ScanPushHandler;
import com.jsrxjt.mobile.biz.homeScanPay.service.handler.ScanReceiveHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

@Component
public class ScanPayWssHandlerFactory {
    private final List<ScanPushHandler> pushHandlers;

    private final List<ScanReceiveHandler> receivehandlers;

    @Autowired
    public ScanPayWssHandlerFactory(
            List<ScanPushHandler> pushHandlers,
            List<ScanReceiveHandler> receiveHandlers) {
        this.pushHandlers = pushHandlers;
        this.receivehandlers = receiveHandlers;
    }

    public ScanPushHandler getPushHandler(WssPushTypeEnum messageType) {
        return pushHandlers.stream()
                .filter(handler -> handler.supports(messageType))
                .findFirst()
                .orElse(null);
    }

    public ScanReceiveHandler getReceiveHandler(ScanWsReceiveTypeEnum messageType) {
        return receivehandlers.stream()
                .filter(handler -> handler.supports(messageType))
                .findFirst()
                .orElse(null);
    }

    public BaseResponse handlePushMessage(WssPushTypeEnum messageType, JSONObject jsonObject) {
        ScanPushHandler handler = getPushHandler(messageType);
        if (handler != null) {
            // 根据handler类型进行转换
            Object convertedData = convertJsonToHandlerType(handler, jsonObject);
            return handler.handle(convertedData);
        }else {
            //未处理的类型，直接返回
            return BaseResponse.succeed(jsonObject);
        }
    }

    public ScanPayReceiverResponseDTO handleReceiveMessage(ScanWsReceiveTypeEnum messageType, JSONObject jsonObject) {
        ScanReceiveHandler handler = getReceiveHandler(messageType);
        if (handler != null) {
            // 根据handler类型进行转换
            Object convertedData = convertJsonToHandlerType(handler, jsonObject);
            return handler.handle(convertedData);
        }
        ScanPayReceiverResponseDTO response = new ScanPayReceiverResponseDTO();
        response.setCode(Status.LFailed.getCode());
        response.setType("WSS_ERROR");
        response.setMsg("不支持的消息类型");
        return response;
    }

    @SuppressWarnings("unchecked")
    private Object convertJsonToHandlerType(Object handler, JSONObject jsonObject) {
        // 通过反射获取handler的泛型类型
        Type genericInterface = handler.getClass().getGenericInterfaces()[0];
        if (genericInterface instanceof ParameterizedType) {
            Type actualType = ((ParameterizedType) genericInterface).getActualTypeArguments()[0];
            Class<?> targetClass = (Class<?>) actualType;
            return jsonObject.toJavaObject(targetClass);
        }
        throw new IllegalArgumentException("无法确定handler的参数类型");
    }

}
