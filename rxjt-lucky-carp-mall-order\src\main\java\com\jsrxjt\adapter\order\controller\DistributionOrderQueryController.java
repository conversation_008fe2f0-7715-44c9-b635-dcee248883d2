package com.jsrxjt.adapter.order.controller;

import cn.hutool.json.JSONUtil;
import com.jsrxjt.common.core.util.ServletUtils;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.*;
import com.jsrxjt.mobile.biz.distribution.service.DistributionOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/v1/distribution-order-query")
@RequiredArgsConstructor
public class DistributionOrderQueryController {

    private final DistributionOrderService distributionOrderService;

    /**
     * 订单支付状态查询包含支付金额(叮咚买菜,大润发,食行,卫岗,物美,永辉,中免日上,清美)
     *
     * @param request
     * @return
     */
    @PostMapping("/order/payInfoWithPayAmount")
    public ApiResponse<DistributionOrderPayInfoWithAmountResponseDTO> distributionOrderPayInfoWithPayAmount(DistributionOrderPayInfoQueryDTO request) {
        log.info("distributionOrderPayInfoWithPayAmount({})", JSONUtil.toJsonStr(request));
        return distributionOrderService.distributionOrderPayInfoWithPayAmount(request);
    }


    /**
     * 大润发扫码付 订单支付状态查询包含支付金额 JSON头+表单数据
     *
     * @param httpServletRequest 请求参数
     * @return 返回结果
     */
    @PostMapping("/rtMartScanPay/order/payInfoWithPayAmount")
    public ApiResponse<DistributionOrderPayInfoWithAmountResponseDTO> rtMartScanPayDistributionOrderPayInfoWithPayAmount(HttpServletRequest httpServletRequest) {
        Map<String, String> params = null;
        try {
            params = ServletUtils.extractParamsFromRequest(httpServletRequest);
        } catch (IOException e) {
            throw new RuntimeException("提取参数失败", e);
        }
        DistributionOrderPayInfoQueryDTO request = new DistributionOrderPayInfoQueryDTO();
        convertMapToDTO(params, request);
        log.info("rtMartScanPayDistributionOrderPayInfoWithPayAmount(request = {})", JSONUtil.toJsonStr(request));
        return distributionOrderService.distributionOrderPayInfoWithPayAmount(request);
    }

    private void convertMapToDTO(Map<String, String> params,  Object request) {
        BeanWrapper beanWrapper = new BeanWrapperImpl(request);
        params.forEach((key, value) -> {
            try {
                if (beanWrapper.isWritableProperty(key)) {
                    beanWrapper.setPropertyValue(key, value);
                } else {
                    log.debug("DTO中没有字段: {}", key);
                }
            } catch (Exception e) {
                log.warn("设置字段失败: key={}, value={}, error={}", key, value, e.getMessage());
            }
        });
    }

    /**
     * 订单支付状态查询(美团,视听,水韵,同程,团油,西橙电影)
     *
     * @param request
     * @return
     */
    @PostMapping("/order/payInfo")
    public ApiResponse<DistributionOrderPayInfoResponseDTO> distributionOrderPayInfo(DistributionOrderPayInfoQueryDTO request) {
        log.info("distributionOrderPayInfo({})", JSONUtil.toJsonStr(request));
        return distributionOrderService.distributionOrderPayInfo(request);
    }

    /**
     * 订单退款状态查询(美团,视听,水韵,同程,团油,西橙电影)
     *
     * @param request
     * @return
     */
    @PostMapping("/order/refundInfo")
    public ApiResponse<DistributionOrderRefundInfoResponseDTO> distributionOrderRefundInfo(DistributionOrderRefundQueryDTO request) {
        log.info("distributionOrderRefundInfo(request = {})", JSONUtil.toJsonStr(request));
        return distributionOrderService.distributionOrderRefundInfo(request);
    }

    /**
     * 大润发扫码付订单退款状态查询 JSON头+表单数据
     *
     * @param httpServletRequest 请求参数
     * @return 退款状态
     */
    @PostMapping("/rtMartScanPay/order/refundInfo")
    public ApiResponse<DistributionOrderRefundInfoResponseDTO> rtMartScanPayDistributionOrderRefundInfo(HttpServletRequest httpServletRequest) {
        Map<String, String> params = null;
        try {
            params = ServletUtils.extractParamsFromRequest(httpServletRequest);
        } catch (IOException e) {
            throw new RuntimeException("提取参数失败", e);
        }
        DistributionOrderRefundQueryDTO request = new DistributionOrderRefundQueryDTO();
        convertMapToDTO(params, request);
        log.info("rtMartScanPayDistributionOrderRefundInfo(request = {})", JSONUtil.toJsonStr(request));
        return distributionOrderService.distributionOrderRefundInfo(request);
    }

    /**
     * 沃尔玛订单状态查询
     *
     * @param request
     * @return
     */
    @PostMapping("/walMart/order/payInfo")
    public ApiResponse<WalMartOrderQueryResponseDTO> walMartDistributionOrderPayInfo(@RequestBody WalMartOrderQueryRequestDTO request) {
        log.info("walMartDistributionOrderPayInfo({})", JSONUtil.toJsonStr(request));
        return distributionOrderService.walMartDistributionOrderPayInfo(request);
    }

    /**
     * 饿了么订单支付状态查询
     *
     * @param request
     * @return
     */
    @PostMapping("/eLeMe/order/payInfo")
    public ApiResponse<ELeMeOrderQueryResponseDTO> eLeMeDistributionOrderPayInfo(@RequestBody ELeMeOrderQueryRequestDTO request) {
        log.info("eLeMeDistributionOrderPayInfo({})", JSONUtil.toJsonStr(request));
        return distributionOrderService.eLeMeDistributionOrderPayInfo(request);
    }

    /**
     * 饿了么订单退款状态查询
     *
     * @param request
     * @return
     */
    @PostMapping("/eLeMe/order/refundInfo")
    public ApiResponse<ELeMeOrderRefundResponseDTO> eLeMeDistributionOrderRefundInfo(@RequestBody ELeMeOrderRefundNotifyDTO request) {
        log.info("eLeMeDistributionOrderRefundInfo({})", JSONUtil.toJsonStr(request));
        return distributionOrderService.eLeMeDistributionOrderRefundInfo(request);
    }

}
