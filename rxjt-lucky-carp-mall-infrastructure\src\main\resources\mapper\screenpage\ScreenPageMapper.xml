<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.screenpage.persistent.mapper.ScreenPageMapper">

    <sql id="Base_Column_List">
        s.screen_id, s.name, s.img_url, s.begin_time, s.end_time, s.click_num, s.is_nationwide, s.countdown, s.ios_url, s.android_url, s.wechat_mini_appid, s.wechat_mini_url,
            s.sort
    </sql>

    <select id="getScreenListByRegionId" resultType="com.jsrxjt.mobile.infra.screenpage.persistent.po.ScreenPagePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM screen_detail s left join content_region_relation c on s.screen_id = c.content_id and c.content_type = 3 and c.del_flag = 0
        where s.del_flag = 0
        and s.begin_time &lt; now()
        and s.end_time &gt; now()
        and (s.is_nationwide = 1 or (s.is_nationwide = 0 and c.region_id = #{regionId}))
        GROUP BY s.screen_id
        ORDER BY s.sort DESC
    </select>

    <update id="incScreenPageClickNum">
        update screen_detail
        set click_num = click_num + 1
        where screen_id = #{screenId}
    </update>
</mapper>
