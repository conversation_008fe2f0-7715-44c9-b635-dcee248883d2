package com.jsrxjt.mobile.api.app.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 便利蜂直充详情请求参数
 * @Author: ywt
 * @Date: 2025-10-21 11:49
 * @Version: 1.0
 */
@Data
@Schema(description = "便利蜂直充详情请求参数")
public class BianlfAppRequest {
    @Schema(description = "便利蜂的spuid")
    @NotNull(message = "便利蜂的spuid为空错误")
    private Long appSpuId;
    @Schema(description = "便利蜂的skuid，本字段用于标记选中的sku，若不传默认标记第一个sku")
    private Long appSkuId;
    @Schema(description = "三级地址id")
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
}
