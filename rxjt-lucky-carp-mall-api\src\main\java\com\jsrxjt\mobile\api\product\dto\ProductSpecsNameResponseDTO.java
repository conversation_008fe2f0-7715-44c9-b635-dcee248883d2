package com.jsrxjt.mobile.api.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 产品规格信息响应
 * @Author: ywt
 * @Date: 2025-04-30 09:53
 * @Version: 1.0
 */
@Data
@Schema(description = "产品规格信息响应")
public class ProductSpecsNameResponseDTO {
    @Schema(description = "规格id")
    private Integer id;
    @Schema(description = "产品spuid")
    private Long productSpuId;//产品id
    @Schema(description = "规格名称")
    private String specsName;//规格名称
    @Schema(description = "排序（数值越大排序越靠前）")
    private Integer sort;
    @Schema(description = "规格值列表")
    private List<ProductSpecsValueResponseDTO> specsValueList;
}
