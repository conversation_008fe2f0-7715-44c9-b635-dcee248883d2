package com.jsrxjt.mobile.biz.order.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.enums.OrderTypeEnum;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleOperationTypeEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.order.AutoRefundCaseService;
import com.jsrxjt.mobile.domain.distribution.messaging.DistributionOrderRefundMessageProducer;
import com.jsrxjt.mobile.domain.distribution.types.DistributionOrderRefundMessage;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleApplyRequest;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleLogEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleLogRepository;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.AfterSaleLogService;
import com.jsrxjt.mobile.domain.order.service.AfterSaleService;
import com.jsrxjt.mobile.domain.payment.gateway.OnlinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.response.RefundResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 自动售后退款业务服务实现
 * 
 * <AUTHOR>
 * @since 2025-09-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AutoRefundCaseServiceImpl implements AutoRefundCaseService {
    private final AfterSaleService afterSaleService;
    private final AfterSaleRepository afterSaleRepository;
    private final AfterSaleLogService afterSaleLogService;
    private final AfterSaleLogRepository afterSaleLogRepository;
    private final OrderRepository orderRepository;
    private final OnlinePaymentGateway onlinePaymentGateway;
    private final DistributedLock distributedLock;
    private final DistributionOrderRefundMessageProducer distributionOrderRefundMessageProducer;

    @Override
    public AfterSaleEntity autoRefund(AutoRefundRequestDTO request) {
        // 1. 构建审核通过的售后单
        AfterSaleEntity afterSale = buildAuditPassedAfterSale(request);
        // 2. 退款
        return refund(afterSale);
    }

    @Override
    public AfterSaleEntity onlyBuildSuccessAfterSale(AutoRefundRequestDTO request) {
        AfterSaleEntity afterSale = buildAuditPassedAfterSale(request);
        int refundStatus = RefundStatusEnum.REFUND_SUCCESS.getCode();
        afterSale.setRefundStatus(refundStatus);
        LocalDateTime now = LocalDateTime.now();
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setRefundTime(now);
        // 更新售后单信息
        updateAfterSaleRefundInfo(afterSale, refundStatus, refundResponse,now);

        // 记录售后日志
        recordRefundLog(afterSale, refundStatus);
        return afterSale;
    }

    /**
     * 构建审核通过的售后单
     *
     * @param request 自动退款请求
     * @return 售后单实体
     */
    private AfterSaleEntity buildAuditPassedAfterSale(AutoRefundRequestDTO request) {
        log.info("开始构建审核通过的售后单，订单号：{}，外部退款单号：{}", request.getOrderNo(), request.getExternalRefundNo());

        AfterSaleEntity afterSale = null;

        // 1. 如果externalRefundNo存在，根据orderNo和externalRefundNo联合查询t_after_sale
        if (StringUtils.hasText(request.getExternalRefundNo())) {
            afterSale = afterSaleRepository.findByOrderNoAndExternalRefundNo(request.getOrderNo(),
                    request.getExternalRefundNo());

            // 2. 如果查到了，校验售后状态和退款状态
            if (afterSale != null) {
                return validateAndReturnExistingAfterSale(afterSale);
            }
        }

        // 3. 如果没查到或者externalRefundNo为空，创建新的售后单
        return createNewAfterSale(request);
    }

    /**
     * 校验并返回已存在的售后单
     *
     * @param afterSale 已存在的售后单
     * @return 售后单实体
     */
    private AfterSaleEntity validateAndReturnExistingAfterSale(AfterSaleEntity afterSale) {
        // 售后状态是审核通过，退款状态不是拒绝退款，则返回售后单实体
        if (AfterSaleStatusEnum.AUDIT_PASSED.getCode().equals(afterSale.getAfterSaleStatus()) &&
                !RefundStatusEnum.REFUND_REJECTED.getCode().equals(afterSale.getRefundStatus())) {
            log.info("找到符合条件的售后单，售后单号：{}", afterSale.getAfterSaleNo());
            return afterSale;
        }

        // 如果售后状态不对，抛出BizException
        AfterSaleStatusEnum afterSaleStatusEnum = AfterSaleStatusEnum.getByCode(afterSale.getAfterSaleStatus());
        RefundStatusEnum refundStatusEnum = RefundStatusEnum.getByCode(afterSale.getRefundStatus());
        String statusDesc = afterSaleStatusEnum != null
                ? afterSaleStatusEnum.getDesc()
                : "未知状态";
        String refundStatusDesc = refundStatusEnum != null
                ? refundStatusEnum.getDesc()
                : "未知状态";

        log.warn("售后单状态不符合要求，售后单号：{}，售后状态：{}，退款状态：{}",
                afterSale.getAfterSaleNo(), statusDesc, refundStatusDesc);
        throw new BizException(String.format("售后状态问题：当前售后状态为%s，退款状态为%s，无法进行自动退款",
                statusDesc, refundStatusDesc));
    }

    /**
     * 创建新的售后单
     *
     * @param request 自动退款请求
     * @return 售后单实体
     */
    private AfterSaleEntity createNewAfterSale(AutoRefundRequestDTO request) {
        log.info("开始创建新的售后单，订单号：{}", request.getOrderNo());

        // 1. 根据订单号查询订单
        OrderInfoEntity order = orderRepository.findByOrderNo(request.getOrderNo());
        if (order == null) {
            log.error("订单不存在，订单号：{}", request.getOrderNo());
            throw new BizException("订单不存在：" + request.getOrderNo());
        }

        // 2. 构建售后申请请求
        AfterSaleApplyRequest applyRequest = buildAfterSaleApplyRequest(request, order);

        // 3. 调用AfterSaleService的applyAfterSale自动申请售后单
        AfterSaleEntity afterSale = afterSaleService.applyAfterSale(order, applyRequest);

        // 4. 变更aftersale对象的售后单状态为审核通过，并记录审核时间
        afterSale.setAfterSaleStatus(AfterSaleStatusEnum.AUDIT_PASSED.getCode());
        afterSale.setAuditTime(LocalDateTime.now());

        afterSale.setAuditRemark("自动退款，系统自动审核通过");

        // 5. 保存售后单信息
        afterSaleRepository.save(afterSale);

        // 6. 创建售后日志
        AfterSaleLogEntity afterSaleLog = afterSaleLogService.createAfterSaleLog(
                afterSale,
                AfterSaleOperationTypeEnum.AUDIT_PASSED.getCode(),
                "系统自动审核通过，准备退款",
                "系统",
                1L);
        afterSaleLogRepository.save(afterSaleLog);

        // 7. 同步修改订单的售后状态
        afterSaleService.syncOrderAfterSaleStatus(afterSale, order);

        log.info("新售后单创建成功，售后单号：{}", afterSale.getAfterSaleNo());
        return afterSale;
    }

    /**
     * 构建售后申请请求
     *
     * @param request 自动退款请求
     * @param order   订单信息
     * @return 售后申请请求
     */
    private AfterSaleApplyRequest buildAfterSaleApplyRequest(AutoRefundRequestDTO request, OrderInfoEntity order) {
        AfterSaleApplyRequest applyRequest = new AfterSaleApplyRequest();

        applyRequest.setOrderNo(request.getOrderNo());
        applyRequest.setCustomerId(order.getCustomerId());
        applyRequest.setAfterSaleType(request.getAfterSaleType());
        applyRequest.setAfterSaleQuantity(
                request.getAfterSaleQuantity() == null ? order.getOrderItems().get(0).getQuantity()
                        : request.getAfterSaleQuantity()); // 默认数量为订单项数量
        applyRequest.setApplyRefundAmount(request.getApplyRefundAmount());
        applyRequest.setAfterSaleReason("系统自动申请售后退款");
        applyRequest.setRefundDescription("系统检测到需要退款，自动申请售后");
        applyRequest.setCustomerRemark("系统自动申请");
        applyRequest.setExternalRefundNo(request.getExternalRefundNo());
        applyRequest.setExternalRefundAmount(request.getExternalRefundAmount());
        return applyRequest;
    }

    /**
     * 执行退款操作
     *
     * @param afterSale 售后单实体
     * @return 退款结果
     */
    private AfterSaleEntity refund(AfterSaleEntity afterSale) {
        log.info("开始执行退款操作，售后单号：{}", afterSale.getAfterSaleNo());

        String lockKey = "lock:refund:" + afterSale.getAfterSaleNo();
        boolean lock = distributedLock.tryLock(lockKey);
        if (!lock) {
            throw new BizException("售后单正在处理中，请稍后再试");
        }

        try {
            // 查询订单信息
            OrderInfoEntity order = orderRepository.findByOrderNo(afterSale.getOrderNo());
            if (order == null) {
                throw new BizException("订单不存在：" + afterSale.getOrderNo());
            }

            LocalDateTime now = LocalDateTime.now();
            if (Objects.equals(afterSale.getRefundStatus(), RefundStatusEnum.REFUND_SUCCESS.getCode())) {
                // 如果售后单状态为退款成功，则直接返回
                return afterSale;
            }

            // 调用退款接口
            if (isOnlinePayment(order)) {
                // 线上支付退款
                RefundResponse refundResponse = sendOnlineRefundRequest(afterSale);
                int refundStatus = RefundStatusEnum.REFUNDING.getCode();

                if (refundResponse.getId() != null) {
                    // 退款成功
                    refundStatus = RefundStatusEnum.REFUND_SUCCESS.getCode();
                }

                // 更新售后单信息
                updateAfterSaleRefundInfo(afterSale, refundStatus, refundResponse,now);

                // 记录售后日志
                recordRefundLog(afterSale, refundStatus);

                // 同步订单售后状态
                updateOrderAfterSaleStatus(afterSale, order);

                // 普通应用退款需通知分销中台
                if (Objects.equals(OrderTypeEnum.ORDINARY_APP.getType(),order.getOrderType())) {
                    DistributionOrderRefundMessage refundMessage = new DistributionOrderRefundMessage(order,afterSale);
                    distributionOrderRefundMessageProducer.sendOrderRefundDelayCallBack(refundMessage);
                }
            }

            return  afterSale;

        } finally {
            distributedLock.unLock(lockKey);
        }
    }

    /**
     * 判断是否为线上支付
     *
     * @param order 订单信息
     * @return 是否为线上支付
     */
    private boolean isOnlinePayment(OrderInfoEntity order) {
        return !Objects.equals(order.getOrderChannel(), OrderChannelEnum.OFFLINE_SCAN.getCode());
    }

    /**
     * 发送线上退款请求
     *
     * @param afterSale 售后单
     * @return 退款响应
     */
    private RefundResponse sendOnlineRefundRequest(AfterSaleEntity afterSale) {
        log.info("发送线上退款请求，售后单号：{}", afterSale.getAfterSaleNo());
        return onlinePaymentGateway.refund(afterSale);
    }

    /**
     * 更新售后单退款信息
     *
     * @param afterSale      售后单
     * @param refundStatus   退款状态
     * @param refundResponse 退款响应
     * @param now            当前时间
     */
    private void updateAfterSaleRefundInfo(AfterSaleEntity afterSale, int refundStatus,
            RefundResponse refundResponse, LocalDateTime now) {
        afterSale.setRefundRequestTime(now);
        if (Objects.equals(refundStatus, RefundStatusEnum.REFUND_SUCCESS.getCode())) {
            afterSale.setRefundStatus(RefundStatusEnum.REFUND_SUCCESS.getCode());
            afterSale.setAfterSaleStatus(AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode());
            LocalDateTime refundTime = refundResponse.getRefundTime();
            afterSale.setRefundTime(refundTime);
            afterSale.setRefundSuccessTime(now);
            afterSale.setAfterSaleCompleteTime(now);
        } else {
            afterSale.setRefundStatus(refundStatus);
        }
        afterSale.setRefundTradeNo(refundResponse.getRefundNo());

        // 保存售后单更新
        afterSaleRepository.update(afterSale);
    }

    /**
     * 记录退款日志
     *
     * @param afterSale    售后单
     * @param refundStatus 退款状态
     */
    private void recordRefundLog(AfterSaleEntity afterSale, int refundStatus) {
        String logMessage;
        Integer operationType;

        if (Objects.equals(refundStatus, RefundStatusEnum.REFUND_SUCCESS.getCode())) {
            logMessage = "退款成功";
            operationType = AfterSaleOperationTypeEnum.REFUND_SUCCESS.getCode();
        } else if (Objects.equals(refundStatus, RefundStatusEnum.REFUND_FAILED.getCode())) {
            logMessage = "退款失败";
            operationType = AfterSaleOperationTypeEnum.REFUND_FAILED.getCode();
        } else {
            logMessage = "退款处理中";
            operationType = AfterSaleOperationTypeEnum.REFUND_SUCCESS.getCode();
        }

        AfterSaleLogEntity afterSaleLog = afterSaleLogService.createAfterSaleLog(
                afterSale,
                operationType,
                logMessage,
                "系统",
                1L);
        afterSaleLogRepository.save(afterSaleLog);
    }

    /**
     * 同步订单售后状态
     *
     * @param afterSale 售后单
     * @param order     订单信息
     */
    private void updateOrderAfterSaleStatus(AfterSaleEntity afterSale, OrderInfoEntity order) {
        log.info("同步订单售后状态，订单号：{}，售后状态：{}", order.getOrderNo(), afterSale.getAfterSaleStatus());
        afterSaleService.syncOrderAfterSaleStatus(afterSale, order);
    }

}
