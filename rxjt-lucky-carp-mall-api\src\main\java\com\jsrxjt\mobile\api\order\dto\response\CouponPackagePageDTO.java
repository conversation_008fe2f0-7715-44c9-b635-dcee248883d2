package com.jsrxjt.mobile.api.order.dto.response;

import com.jsrxjt.mobile.api.common.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class CouponPackagePageDTO<T>{

    private PageDTO<T> pageInfo;

    @Schema(description = "用户总卡券数")
    private Integer totalCouponNum;

    public CouponPackagePageDTO(PageDTO<T> pageDTO) {
        this.pageInfo = pageDTO;
    }

    public Integer getTotalCouponNum() {
        return totalCouponNum;
    }

    public void setTotalCouponNum(Integer totalCouponNum) {
        this.totalCouponNum = totalCouponNum;
    }

    public List<T> getRecords() { return pageInfo.getRecords(); }
    public Long getTotal() { return pageInfo.getTotal(); }
    public Long getSize() { return pageInfo.getSize(); }
    public Long getPages() { return pageInfo.getPages(); }
    public Long getCurrent() { return pageInfo.getCurrent(); }
}
