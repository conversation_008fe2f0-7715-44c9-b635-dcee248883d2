package com.jsrxjt.adapter.coupon.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.product.dto.request.ProductDialogDetailRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.ProductDialogDetailResponseDTO;
import com.jsrxjt.mobile.biz.product.service.ProductDialogCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description: 产品弹框控制器
 * @Author: ywt
 * @Date: 2025-11-06 09:17
 * @Version: 1.0
 */
@RestController
@RequestMapping("/v1/product-dialog")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "产品弹框接口", description = "产品弹框相关接口")
public class ProductDialogController {
    private final ProductDialogCaseService productDialogCaseService;

    @Operation(summary = "获取产品弹框信息")
    @PostMapping("/dialogdetail")
    @VerifySign
    public BaseResponse<ProductDialogDetailResponseDTO> getDialogDetail(@RequestBody @Valid ProductDialogDetailRequestDTO requestDTO) {
        return BaseResponse.succeed(productDialogCaseService.getDialogDetail(requestDTO));
    }
}
