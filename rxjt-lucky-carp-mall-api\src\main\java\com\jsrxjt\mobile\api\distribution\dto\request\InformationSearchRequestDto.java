package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 资讯搜索请求参数
 * @Author: ywt
 * @Date: 2025-07-03 16:08
 * @Version: 1.0
 */
@Data
@Schema(description = "资讯搜索请求参数")
public class InformationSearchRequestDto {
    @Schema(description = "搜索关键字", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请输入搜索关键字")
    private  String keywrod;

    @Schema(description = "三级地址id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
}
