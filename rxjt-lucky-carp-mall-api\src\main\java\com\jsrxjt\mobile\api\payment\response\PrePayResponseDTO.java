package com.jsrxjt.mobile.api.payment.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 预支付响应DTO
 * <AUTHOR>
 * @since 2025/8/8
 */
@Data
@Schema(description = "预支付响应体")
public class PrePayResponseDTO {
    
    /**
     * 业务订单号
     */
    @Schema(description = "业务订单号")
    private String orderNo;
    
    /**
     * 交易流水号
     */
    @Schema(description = "交易流水号")
    private String tradeNo;
    
    /**
     * 预支付订单号
     */
    @Schema(description = "预支付订单号")
    private String preOrderNo;
    
    /**
     * 是否存在微信支付
     */
    @Schema(description = "是否存在微信支付")
    private Boolean isExistWx;

    /**
     * 是否设置了支付密码
     */
    @Schema(description = "是否设置了支付密码")
    private Boolean isSetPayPass;

    /**
     * 是否免密支付
     */
    @Schema(description = "是否免密支付")
    private Boolean isPasswordFreePayment;
    
    /**
     * 微信支付金额(分)
     */
    @Schema(description = "微信支付金额(分)")
    private Integer wxPayAmount;
    
    /**
     * 支付金额(分)
     */
    @Schema(description = "支付金额(分)")
    private Integer payAmount;

    /**
     * 卡支付金额(分)
     */
    @Schema(description = "卡支付金额(分)")
    private Integer cardPayAmount;

    /**
     * 来源
     */
    @Schema(description = "来源标识 APP_ANDROID,APP_IOS,WX_MINI")
    private String source;
    
    /**
     * 卡类型信息
     */
    @Schema(description = "卡类型信息")
    private List<TradeCategoryInfo> cardCategoryInfo;
    
    @Data
    public static class TradeCategoryInfo {
        /**
         * 卡类型
         */
        @Schema(description = "卡类型")
        private String cardType;
        
        /**
         * 金额
         */
        @Schema(description = "金额 分")
        private Integer amount;
    }
}
