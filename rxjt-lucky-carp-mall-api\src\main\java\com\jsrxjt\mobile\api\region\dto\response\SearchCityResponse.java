package com.jsrxjt.mobile.api.region.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "城市详细信息")
public class SearchCityResponse {

    @Schema(description = "城市id")
    private Integer id;

    @Schema(description = "父级id(中国0)")
    private Integer parentId;

    @Schema(description = "城市名称")
    private String regionName;

    @Schema(description = "1 省  2  市  3 区 4.镇")
    private Integer regionType;

    @Schema(description = "父级区域名称")
    private String parentName;

    @Schema(description = "经度")
    private String lat;

    @Schema(description = "纬度")
    private String lng;

    @Schema(description = "子区域列表")
    private List<SearchCityResponse> children;

    @Schema(description = "当前区域")
    private SearchCityResponse currentLocation;
}
