package com.jsrxjt.mobile.domain.order.repository;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.customer.request.CustomerCardTradeRequest;
import com.jsrxjt.mobile.domain.order.entity.TradeCardBalanceChangeEntity;

import java.util.List;

/**
 * 卡交易流水接口
 *
 * <AUTHOR>
 * @date 2025/10/11
 */
public interface TradeCardBalanceChangeRepository {
    List<TradeCardBalanceChangeEntity> findAllByCardNo(String cardNo);

    /**
     * 分页获取卡交易记录
     * @param request
     * @return
     */
    PageDTO<TradeCardBalanceChangeEntity> getCardTradeList(CustomerCardTradeRequest request);

    /**
     * 保存空中充值的卡充值记录
     * @param entity
     * @return
     */
    Integer saveAirRecharge(TradeCardBalanceChangeEntity entity);

}
