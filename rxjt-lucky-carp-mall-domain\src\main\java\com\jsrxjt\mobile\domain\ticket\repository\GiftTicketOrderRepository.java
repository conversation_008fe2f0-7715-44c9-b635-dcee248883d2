package com.jsrxjt.mobile.domain.ticket.repository;

import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketOrderEntity;

import java.util.List;

/**
 * 下单赠送券订单仓储接口
 * 
 * <AUTHOR>
 * @date 2025/09/22
 */
public interface GiftTicketOrderRepository {

    /**
     * 批量保存赠券订单
     * 
     * @param giftTicketOrders 赠券订单列表
     */
    void batchSave(List<GiftTicketOrderEntity> giftTicketOrders);

    /**
     * 根据订单号查询赠券订单列表
     *
     * @param ticketOrderNo 券订单号
     * @return 赠券订单
     */
    GiftTicketOrderEntity findByTicketOrderNo(String ticketOrderNo);

    /**
     * 根据营销中台优惠券发放编号查询赠券订单
     *
     * @param centerTicketCouponNumber 营销中台优惠券发放编号
     * @return 赠券订单
     */
    GiftTicketOrderEntity findByCenterTicketCouponNumber(String centerTicketCouponNumber);

    /**
     * 根据外部订单号查询赠券订单
     *
     * @param externalOrderNo 外部订单号
     * @return 赠券订单
     */
    GiftTicketOrderEntity findByExternalOrderNo(String externalOrderNo);

    /**
     * 保存赠券订单
     *
     * @param giftTicketOrder 赠券订单
     */
    void save(GiftTicketOrderEntity giftTicketOrder);
}
