package com.jsrxjt.mobile.biz.product.service;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.product.dto.request.DefaultSearchKeywordRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.ProductSearchRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.ProductSuggestionRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.SearchKeywordResponseDTO;
import com.jsrxjt.mobile.api.product.dto.response.ProductSuggestionResponseDTO;
import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;

import java.util.List;

/**
 * 产品搜索服务接口
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/5/8
 **/
public interface ProductSearchCaseService {

    /**
     * 分页搜索产品
     * 
     * @param request 搜索请求
     * @return 搜索结果
     */
    PageDTO<ProductSpuBaseInfo> pageSearchProducts(ProductSearchRequestDTO request);

    /**
     * 搜索产品
     *
     * @param request 搜索请求
     * @return 搜索结果
     */
    List<ProductSpuBaseInfo>  searchProducts(ProductSearchRequestDTO request);



    /**
     * 获取产品联想词
     * 
     * @param request 联想词请求
     * @return 联想词列表
     */
    List<ProductSuggestionResponseDTO> getSuggestions(ProductSuggestionRequestDTO request);

    /**
     * 获取默认搜索词
     *
     * @param request 请求参数
     * @return 默认搜索词列表
     */
    List<SearchKeywordResponseDTO> getDefaultSearchKeywords(DefaultSearchKeywordRequestDTO request);

    /**
     * 增加搜索关键词点击量
     *
     * @param id 搜索关键词ID
     */
    void incrementSearchKeywordClickNum(Long id);
}