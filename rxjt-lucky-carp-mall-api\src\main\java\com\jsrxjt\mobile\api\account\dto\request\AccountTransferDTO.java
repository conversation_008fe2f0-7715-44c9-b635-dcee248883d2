package com.jsrxjt.mobile.api.account.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 示例代码，转账请求DTO
 * @since 2024/4/19
 **/
@Data
@Schema(title = "转账demo用例请求参数")
public class AccountTransferDTO  {
    @Schema(title = "源用户ID")
    @NotNull
    private Long sourceUserId;
    @Schema(title = "目标账户号码")
    private String targetAccountNumber;
    @Schema(title = "转出金额")
    private BigDecimal targetAmount;

    @Schema(title = "目标货币")
    @NotBlank
    private String targetCurrency;

}
