package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 商户大全的门店响应
 * @Author: ywt
 * @Date: 2025-05-28 09:48
 * @Version: 1.0
 */
@Data
@Schema(description = "商户大全的门店响应")
public class MerchantShopResponseDTO {
    @Schema(description = "门店id")
    private Integer id;
    @Schema(description = "商户Id")
    private Integer third_id;
    @Schema(description = "品牌Id")
    private Integer brand_id;
    @Schema(description = "商户分组id")
    private Integer group_id;//商户分组id
    @Schema(description = "名称")
    private String name;//名称
    @Schema(description = "纬度")
    private String lat;//纬度
    @Schema(description = "经度")
    private String lng;//经度
    @Schema(description = "地址")
    private String address;//地址
    @Schema(description = "特约商户LOGO")
    private String special_logo;//特约商户LOGO
    @Schema(description = "是否提货凭证商户,N或Y")
    private String is_th_shop;//是否提货凭证商户
    @Schema(description = "是否开启提货凭证支付,N或Y")
    private String is_th_open;//是否开启提货凭证支付
    @Schema(description = "商户标签，用英文逗号分隔，如‘CERT_PICK,CODE_PICK’，其中CERT_PICK提货凭证，CODE_PICK展码提货")
    private String tag;//商户标签
    @Schema(description = "福鲤圈动态码支付开关,N或Y")
    private String pay_code;//福鲤圈动态码支付开关,"N"or"Y"
    @Schema(description = "福鲤圈动态码提货凭证开关,N或Y")
    private String dynamic_union_switch;//福鲤圈动态码提货凭证开关
    @Schema(description = "福鲤圈动态码提货券开关,N或Y")
    private String dynamic_voucher_switch;//福鲤圈动态码提货券开关
    @Schema(description = "福鲤圈电子支付入账开关")
    private String order_type;//福鲤圈电子支付入账开关
    @Schema(description = "距离, 单位千米")
    private Double distance;//距离
}
