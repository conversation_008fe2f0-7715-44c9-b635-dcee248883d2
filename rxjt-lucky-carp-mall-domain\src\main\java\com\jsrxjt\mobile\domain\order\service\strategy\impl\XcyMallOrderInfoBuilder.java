package com.jsrxjt.mobile.domain.order.service.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Created by jeffery.yang on 2025/10/22 14:10
 *
 * @description:
 * @author: jeffery.yang
 * @date: 2025/10/22 14:10
 * @version: 1.0
 */
@Component
@Slf4j
public class XcyMallOrderInfoBuilder extends DefaultOrderInfoBuilder{
    public XcyMallOrderInfoBuilder(BusinessIdGenerator businessIdGenerator, ConfigRepository configRepository) {
        super(businessIdGenerator, configRepository);
    }

    @Override
    public void supplementOrderInfo(OrderInfoEntity orderInfo, CreateOrderDTO request) {
        log.info("补充祥采云推单订单信息，订单号：{}", orderInfo.getOrderNo());
        // 补充第三方特有信息
        if (StrUtil.isNotBlank(request.getExternalOrderNo())) {
            orderInfo.setExternalOrderNo(request.getExternalOrderNo());
        }

        if (StrUtil.isNotBlank(request.getThirdId())) {
            orderInfo.setThirdId(request.getThirdId());
        }

        if (StrUtil.isNotBlank(request.getExternalShopId())) {
            orderInfo.setExternalShopId(request.getExternalShopId());
        }

        if (StrUtil.isNotBlank(request.getExternalShopUserId())) {
            orderInfo.setExternalShopUserId(request.getExternalShopUserId());
        }

        if (StrUtil.isNotBlank(request.getTradeNo())) {
            orderInfo.setTradeNo(request.getTradeNo());
        }

        if(request.getExternalOrderExpireTime() != null) {
            orderInfo.setPayExpireTimestamp(Long.valueOf(request.getExternalOrderExpireTime()));
        }
        if(StrUtil.isNotBlank(request.getRemark())) {
            orderInfo.setRemark(request.getRemark());
        }
        if(StrUtil.isNotBlank(request.getOrderDetailUrl())) {
            orderInfo.setOrderDetailUrl(request.getOrderDetailUrl());
        }
        if(StrUtil.isNotBlank(request.getExternalPayResultUrl())) {
            orderInfo.setExternalPayResultUrl(request.getExternalPayResultUrl());
        }
        // 设置特定的订单渠道
        orderInfo.setOrderChannel(OrderChannelEnum.XCY_MALL.getCode());
        log.info("本地祥采云订单信息补充完成，外部订单号：{}", orderInfo.getExternalOrderNo());
    }
}
