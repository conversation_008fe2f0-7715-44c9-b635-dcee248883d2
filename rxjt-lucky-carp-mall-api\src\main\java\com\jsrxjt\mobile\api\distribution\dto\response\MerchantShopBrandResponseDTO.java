package com.jsrxjt.mobile.api.distribution.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 商户大全的门店详情品牌响应
 */
@Data
@Schema(description = "商户大全门店详情响应")
public class MerchantShopBrandResponseDTO {

    @Schema(description = "ID")
    private String id;

    @Schema(description = "品牌名称")
    private String name;

    @Schema(description = "Logo")
    private String logo;

    @Schema(description = "品牌类型")
    private String type;

}
