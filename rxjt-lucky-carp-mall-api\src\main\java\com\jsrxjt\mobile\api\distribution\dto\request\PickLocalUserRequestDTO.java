package com.jsrxjt.mobile.api.distribution.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 当前用户信息
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "当前用户信息")
public class PickLocalUserRequestDTO {

    private Long id;

    /**
     * 瑞祥会员中心会员id
     */
    private Long vipId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 手机
     */
    private String phone;

    /**
     * 纳客宝的绑定id，类似会员Id
     */
    private String nkbBindId;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 当前区域id
     */
    private String regionId;

    /**
     * 当前编码
     */
    private String adcode;
}
