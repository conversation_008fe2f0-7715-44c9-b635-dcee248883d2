package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 订单详情查询请求参数
 * 
 * <AUTHOR>
 * @since 2025/7/21
 */
@Getter
@Setter
public class OrderDetailRequestDTO extends BaseParam {
    
    @Schema(description = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
}