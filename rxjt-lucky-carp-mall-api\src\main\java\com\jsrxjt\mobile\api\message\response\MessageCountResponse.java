package com.jsrxjt.mobile.api.message.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="MessageCountResponse", description = "消息数量")
public class MessageCountResponse {
    @Schema(description = "所有消息数量")
    private Integer allMsgCount;
    @Schema(description = "瑞祥消息数量")
    private Integer sysMsgCount;
//    @Schema(description = "账户消息数量")
//    private Integer accountMsgCount;
    @Schema(description = "客服消息数量")
    private Integer serveMsgCount;
}
