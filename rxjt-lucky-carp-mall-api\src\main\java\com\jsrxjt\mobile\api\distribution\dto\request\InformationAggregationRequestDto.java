package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 资讯聚合页请求参数
 * @Author: ywt
 * @Date: 2025-06-16 08:49
 * @Version: 1.0
 */
@Data
public class InformationAggregationRequestDto {
    @Schema(description = "三级地址id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "三级地址id为空错误")
    private Integer regionId;
}
