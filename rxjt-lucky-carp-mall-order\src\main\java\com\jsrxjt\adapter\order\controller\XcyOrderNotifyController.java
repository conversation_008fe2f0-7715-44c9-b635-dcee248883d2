package com.jsrxjt.adapter.order.controller;

import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallCancelOrderRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallCreateOrderRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallQueryUserRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallRefundRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCancelResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallQueryUserResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallRefundResponseDTO;
import com.jsrxjt.mobile.biz.payment.service.PaymentSuccessCaseService;
import com.jsrxjt.mobile.biz.xcyMall.IXcyMallService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by jeffery.yang on 2025/10/21 11:09
 *
 * @description: 祥采云商城对接
 * @author: jeffery.yang
 * @date: 2025/10/21 11:09
 * @version: 1.0
 */
@RestController
@RequestMapping("/v1/xcy-order")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "祥采云商城订单接口", description = "祥采云商城订单接口相关接口")
public class XcyOrderNotifyController {

	private final IXcyMallService xcyMallService;


	@PostMapping("/createOrder/{appFlag}")
	@Operation(summary = "祥采云下单")
	public XcyMallCreateOrderResponseDTO createOrder(@PathVariable("appFlag") String appFlag,
													 @RequestBody XcyMallCreateOrderRequestDTO requestDTO) {
		return xcyMallService.createOrder(requestDTO,appFlag);
	}

	@PostMapping("/refundOrder/{appFlag}")
	@Operation(summary = "祥采云退款")
	public XcyMallRefundResponseDTO refundOrder(@PathVariable("appFlag") String appFlag,
												@RequestBody XcyMallRefundRequestDTO requestDTO) {
		return xcyMallService.refundOrder(requestDTO,appFlag);
	}

	@PostMapping("/cancelOrder/{appFlag}")
	@Operation(summary = "祥采云取消订单")
	public XcyMallCancelResponseDTO cancelOrder(@PathVariable("appFlag") String appFlag,
												@RequestBody XcyMallCancelOrderRequestDTO requestDTO) {
		return xcyMallService.cancelOrder(requestDTO,appFlag);
	}

	@PostMapping("/getVipInfo/{appFlag}")
	@Operation(summary = "获取用户信息")
	public XcyMallQueryUserResponseDTO getVipInfo(@PathVariable("appFlag") String appFlag,
												  @RequestBody XcyMallQueryUserRequestDTO requestDTO) {
		return xcyMallService.getVipInfo(requestDTO,appFlag);
	}


}
