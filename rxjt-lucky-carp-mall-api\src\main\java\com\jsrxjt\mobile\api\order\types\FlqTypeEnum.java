package com.jsrxjt.mobile.api.order.types;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "自定义核销类型")
public enum FlqTypeEnum {
    
    @Schema(description = "面值+兑换码")
    CODE_ONLY(1, "面值+兑换码"),
    
    @Schema(description = "面值+兑换码+一维二维码")
    CODE_AND_QR_CODE(2, "面值+兑换码+一维二维码"),
    
    @Schema(description = "面值+卡号+一维二维码")
    CARD_NUMBER_AND_QR_CODE(3, "面值+卡号+一维二维码"),
    
    @Schema(description = "面值+卡号卡密")
    CARD_NUMBER_AND_PASSWORD(4, "面值+卡号卡密"),
    
    @Schema(description = "面值+卡号卡密+兑换码")
    CARD_NUMBER_PASSWORD_AND_CODE(5, "面值+卡号卡密+兑换码"),
    
    @Schema(description = "面值+卡号卡密+一维二维码")
    CARD_NUMBER_PASSWORD_AND_QR_CODE(6, "面值+卡号卡密+一维二维码"),
    
    @Schema(description = "面值+卡密+一维二维码")
    PASSWORD_AND_QR_CODE(7, "面值+卡密+一维二维码"),
    
    @Schema(description = "面值+链接")
    LINK_ONLY(8, "面值+链接"),
    
    @Schema(description = "自发券(面值+提货券形式)")
    SELF_ISSUED_VOUCHER(9, "自发券(面值+提货券形式)");
    
    private final Integer code;
    private final String description;
    
    FlqTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static FlqTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (FlqTypeEnum type : FlqTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        
        throw new IllegalArgumentException("未找到匹配的自定义核销类型: " + code);
    }
    
    @Override
    public String toString() {
        return "FlqType{" +
                "code=" + code +
                ", description='" + description + '\'' +
                '}';
    }
}
