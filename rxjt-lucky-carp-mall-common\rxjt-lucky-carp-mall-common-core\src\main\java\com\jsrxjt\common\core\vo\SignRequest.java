package com.jsrxjt.common.core.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
public class SignRequest {
    @Getter
    @Setter
    @Schema(description = "时间戳")
    private Long timestamp;

    @Getter
    @Setter
    @Schema(description = "随机数")
    private String nonce;

    @Getter
    @Setter
    @Schema(description = "签名")
    private String sign;

}
