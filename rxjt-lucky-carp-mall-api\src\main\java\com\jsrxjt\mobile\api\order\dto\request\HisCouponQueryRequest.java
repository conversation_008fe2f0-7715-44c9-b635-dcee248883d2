package com.jsrxjt.mobile.api.order.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@Schema(name="HisCouponQueryRequest", description="用户历史券包查询条件")
public class HisCouponQueryRequest {

    @Schema(description = "券类型 1电子卡 2提货券 3套餐 不传默认查全部")
    private Integer label;

    @Schema(description = "页码 每页数量固定，只需要传页码")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;
}