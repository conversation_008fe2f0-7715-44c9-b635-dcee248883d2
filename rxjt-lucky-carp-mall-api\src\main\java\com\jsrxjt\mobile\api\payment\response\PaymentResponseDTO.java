package com.jsrxjt.mobile.api.payment.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 发起支付响应DTO
 * <AUTHOR>
 * @since 2025/8/15
 */
@Data
@Schema(description = "支付结果响应")
public class PaymentResponseDTO {
    
    /**
     * 业务订单号
     */
    @Schema(description = "业务订单号")
    private String orderNo;
    
    /**
     * 交易单号
     */
    @Schema(description = "交易单号")
    private String tradeNo;
    
    /**
     * 是否存在其他支付方式
     */
    @Schema(description = "是否存在其他支付方式")
    private Boolean isOther;
    
    /**
     * 其他支付通道
     */
    @Schema(description = "其他支付渠道 不存在其他支付渠道的时候为空")
    private String otherChannel;
    
    /**
     * app支付参数
     */
    @Schema(description = "app支付参数")
    private String appPayParams;
    
    /**
     * 小程序支付参数
     */
    @Schema(description = "小程序支付参数")
    private String miniPayParams;
    
    /**
     * 支付状态 WAIT 待交易 IN 交易中 FAIL 交易失败 SUCCESS 交易成功 CLOSE 交易关闭
     */
    @Schema(description = "支付状态 WAIT 待交易 IN 交易中 FAIL 交易失败 SUCCESS 交易成功 CLOSE 交易关闭" )
    private String payStatus;
}