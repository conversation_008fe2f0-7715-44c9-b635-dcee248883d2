package com.jsrxjt.mobile.api.enums;

//支付状态: 0-未支付 1-已支付 2-部分支付 3-部分退款中 4-部分退款完成 5-全额退款中 6-全额退款已完成
public enum PaymentStatusEnum {
    UNPAID(0, "未支付"),
    PAID(1, "已支付"),
    PARTIAL_PAID(2, "部分支付"),
    PARTIAL_REFUNDING(3, "部分退款中"),
    PARTIAL_REFUNDED(4, "部分退款完成"),
    FULL_REFUNDING(5, "全额退款中"),
    FULL_REFUNDED(6, "全额退款已完成");

    private final Integer code;
    private final String description;

    PaymentStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescriptionByCode(Integer code) {
        for (PaymentStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDescription();
            }
        }
        return null;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static PaymentStatusEnum getByCode(Integer code) {
        for (PaymentStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断是否为退款状态
     *
     * @return true-退款状态，false-非退款状态
     */
    public boolean isRefundStatus() {
        return this == PARTIAL_REFUNDING || this == PARTIAL_REFUNDED || 
               this == FULL_REFUNDING || this == FULL_REFUNDED;
    }

    /**
     * 判断是否为退款中状态
     *
     * @return true-退款中，false-非退款中
     */
    public boolean isRefunding() {
        return this == PARTIAL_REFUNDING || this == FULL_REFUNDING;
    }

    /**
     * 判断是否为退款完成状态
     *
     * @return true-退款完成，false-非退款完成
     */
    public boolean isRefunded() {
        return this == PARTIAL_REFUNDED || this == FULL_REFUNDED;
    }
}
