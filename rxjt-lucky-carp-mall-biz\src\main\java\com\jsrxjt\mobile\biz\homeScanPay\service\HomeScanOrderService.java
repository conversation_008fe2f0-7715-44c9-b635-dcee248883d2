package com.jsrxjt.mobile.biz.homeScanPay.service;

import com.jsrxjt.mobile.api.scanPay.types.PosV2OrderStatusTypeEnum;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;

/**
 * 首页扫码付订单服务
 * <AUTHOR>
 * @date 2025/11/07
 */
public interface HomeScanOrderService {

    /**
     * 扫码付订单状态更新
     * @param tradeNo
     */
    OrderInfoEntity scanOrderStatusUpdate(String tradeNo, PosV2OrderStatusTypeEnum  status);
}
