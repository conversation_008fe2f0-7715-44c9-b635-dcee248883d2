package com.jsrxjt.mobile.biz.oss;

import com.jsrxjt.common.core.vo.BaseResponse;

/**
 *  uploadAO
 *  文件上传服务
 * <AUTHOR> Fengping
 * 2023/3/20 15:11
 * 
 **/
public interface UploadImageService {
    /**
     * 上传图片( 兼容老项目接口)
     * @param imageUrl 图片路径
     * @param data 图片文件内容
     * @return 响应结果
     */
    BaseResponse<Object> uploadImage(String imageUrl, byte[] data);

    /**
     *  上传base64编码的图片
     * @param imageUrl 图片路径
     * @param base64Data 图片的base64数据
     * @return 响应结果
     */
    BaseResponse<Object> uploadImage(String imageUrl, String base64Data);

}
