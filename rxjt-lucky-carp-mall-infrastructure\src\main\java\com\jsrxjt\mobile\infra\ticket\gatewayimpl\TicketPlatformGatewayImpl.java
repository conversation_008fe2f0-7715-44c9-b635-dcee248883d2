package com.jsrxjt.mobile.infra.ticket.gatewayimpl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.ticket.config.TicketPlatformConfig;
import com.jsrxjt.mobile.domain.ticket.gateway.TicketPlatformGateway;
import com.jsrxjt.mobile.domain.ticket.gateway.cmd.GiveOutTicketCmd;
import com.jsrxjt.mobile.infra.ticket.po.TicketPlateformPO;
import com.jsrxjt.mobile.infra.ticket.util.TicketPlateformUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BiConsumer;

/**
 * @Description: 优惠券中台网关
 * @Author: ywt
 * @Date: 2025-10-16 14:48
 * @Version: 1.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TicketPlatformGatewayImpl implements TicketPlatformGateway {
    protected static final String CODE = "code";
    protected static final String DATA = "data";
    protected static final Integer SUCCESS_CODE = 200;

    protected final HttpClientGateway httpClientGateway;
    private final TicketPlatformConfig ticketPlateformConfig;

    @Override
    public List<String> giveOutTicket(GiveOutTicketCmd cmd, BiConsumer<Integer, String> onError) {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("appid", ticketPlateformConfig.getAppId());
        requestMap.put("nounce", cmd.getNonce());
        requestMap.put("timestamp", cmd.getTimestamp());
        requestMap.put("userCode", cmd.getUserCode());
        requestMap.put("couponId", cmd.getTicketId());
        requestMap.put("number", cmd.getNumber());
        requestMap.put("giveTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        if (StringUtils.isNotBlank(cmd.getNotifyUrl())) {
            requestMap.put("notifyUrl", cmd.getNotifyUrl());
        }

        SortedMap<Object, Object> parameters = JSON.parseObject(JSONObject.toJSONString(requestMap), SortedMap.class);
        String signature = TicketPlateformUtil.getSignature(ticketPlateformConfig.getAppSecret(), parameters);
        requestMap.put("signature", signature);

        List<String> responseList = Collections.emptyList();
        try {
            log.info("营销中台-发放优惠券接口请求参数: {}", JSON.toJSONString(requestMap));
            String ticketStr = httpClientGateway.doPostJson(
                    ticketPlateformConfig.getHost() + ticketPlateformConfig.getGiveOut(),
                    JSON.toJSONString(requestMap), ticketPlateformConfig.getConnectTimeout(),
                    ticketPlateformConfig.getReadTimeout());
            TicketPlateformPO result = JSON.parseObject(ticketStr, TicketPlateformPO.class);
            if (result.getCode().equals(SUCCESS_CODE)
                    && Objects.nonNull(result.getData())
                    && CollectionUtil.isNotEmpty(result.getData().getNumbers())) {
                responseList = result.getData().getNumbers();
            } else {
                // 200回调错误给调用者
                if (onError != null) {
                    onError.accept(result.getCode(), result.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("营销中台-发放优惠券 异常: {}", e.getMessage(), e);
            if (onError != null) {
                onError.accept(-1, "服务异常: " + e.getMessage());
            }

        }
        return responseList;
    }

    @Override
    public boolean verifySign(Map<String, Object> requestMap) {
        if (requestMap == null || requestMap.isEmpty()) {
            log.warn("验签失败：请求参数为空");
            return false;
        }

        String requestSignature = (String) requestMap.get("signature");
        if (StringUtils.isBlank(requestSignature)) {
            log.warn("验签失败：签名字段为空");
            return false;
        }

        try {
            // 移除签名字段，避免参与签名计算
            Map<String, Object> signParams = new HashMap<>(requestMap);
            signParams.remove("signature");

            // 使用TicketPlateformUtil计算签名
            SortedMap<Object, Object> parameters = JSON.parseObject(JSONObject.toJSONString(signParams),
                    SortedMap.class);
            String calculatedSignature = TicketPlateformUtil.getSignature(ticketPlateformConfig.getAppSecret(),
                    parameters);

            boolean isValid = requestSignature.equals(calculatedSignature);
            if (!isValid) {
                log.warn("验签失败：请求签名[{}]，计算签名[{}]", requestSignature, calculatedSignature);
            } else {
                log.info("验签成功");
            }
            return isValid;
        } catch (Exception e) {
            log.error("验签异常", e);
            return false;
        }
    }
}