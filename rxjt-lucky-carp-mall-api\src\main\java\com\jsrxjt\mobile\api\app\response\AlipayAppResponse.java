package com.jsrxjt.mobile.api.app.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Schema(description = "支付宝红包应用信息响应")
public class AlipayAppResponse {

    @Schema(description = "spu列表（类目属性为1时按银行页样式展示 类目属性为2时list数量等于1不展示spu名称大于1展示spu名称）")
    List<AlipayAppSpuResponse> alipayAppSpuResponseList = new ArrayList<>();

    @Schema(description = "应用Sku信息 如无选中 默认排序第一个spu的sku ")
    List<AlipayAppSkuResponse> alipayAppSkuResponseList = new ArrayList<>();

    @Schema(description = "指定城市 多元适用场景 若无-取spu全国适用场景")
    private String diverseUsageScene;

    @Schema(description = "支付宝tab分类ID 用于选中分类")
    private Long alipayTabCatId;

    @Schema(description = "类目属性 1银行 2其他")
    private Integer attribute;

}
