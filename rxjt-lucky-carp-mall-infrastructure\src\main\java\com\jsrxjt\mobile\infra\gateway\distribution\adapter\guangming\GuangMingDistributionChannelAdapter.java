package com.jsrxjt.mobile.infra.gateway.distribution.adapter.guangming;

import com.alibaba.fastjson2.JSON;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.adapter.AbstractDistributionChannelAdapter;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 光明随心订渠道适配器
 * @Author: ywt
 * @Date: 2025-12-17 14:05
 * @Version: 1.0
 */
@Component
@Slf4j
public class GuangMingDistributionChannelAdapter extends AbstractDistributionChannelAdapter {
    public GuangMingDistributionChannelAdapter(HttpClientGateway httpClientGateway,
                                           DistributionConfig distributionConfig) {
        super(httpClientGateway, distributionConfig);
    }

    @Override
    protected DistributionConfig.ChannelConfig getDistributionChanelConfig(DistributionConfig distributionConfig) {
        return distributionConfig.getGuangming();
    }

    @Override
    public DistChannelType getChannelType() {
        return DistChannelType.GUANGMING;
    }

    @Override
    public DistAccessResponse access(DistAccessRequest request) {
        try {
            if (StringUtils.isBlank(request.getUserId())) {
                return DistAccessResponse.builder()
                        .success(false)
                        .errorMessage("用户ID不能为空")
                        .build();
            }
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("user_id", request.getUserId());

            // 添加公共参数和签名
            otherAddCommonParams(params);

            String baseUrl = config.getBaseUrl() + config.getApiPath() + config.getAccessPath();
            String url = baseUrl + "&" + httpClientGateway.buildUrlParams(params);

            return DistAccessResponse.builder()
                    .success(true)
                    .redirectUrl(url)
                    .build();
        } catch (Exception e) {
            log.error("光明随心订免登接入异常: {}", e.getMessage(), e);
            return DistAccessResponse.builder()
                    .success(false)
                    .errorMessage("光明随心订免登接入异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public DistPaidNotifyResponse paidNotify(DistPaidNotifyRequest request) {
        log.info("光明随心订支付结果通知: {}", JSON.toJSONString(request));
        // 调用父类的通用实现，使用紧凑的日期时间格式（yyyyMMddHHmmss）
        return doPaidNotify(request, COMPACT_DAY_TIME_FORMAT);
    }

    @Override
    public DistRefundResultNotifyResponse refundResultNotify(DistRefundResultNotifyRequest request) {
        // 光明随心订需要额外参数 outTradeNo 和 outRefundNo
        log.info("光明随心订退款结果通知: {}", JSON.toJSONString(request));
        return doRefundResultNotify(request, COMPACT_DAY_TIME_FORMAT, (params, req) -> {
            params.put("tradeNo", req.getDistTradeNo());
            params.put("outTradeNo", req.getOutTradeNo());
            params.put("refundNo", req.getDistRefundNo());
            params.put("outRefundNo", req.getOutRefundNo());
            params.put("refundAmount", req.getRefundAmount().toString());
            params.put("status", req.getStatus());

            // 转换时间格式
            if (req.getRefundTime() != null) {
                params.put("refundTime", req.getRefundTime().format(COMPACT_DAY_TIME_FORMAT));
            } else {
                params.put("refundTime", "");
            }
        });
    }

    @Override
    public DistOrderDetailQueryResponse queryOrderDetail(DistOrderDetailQueryRequest request) {
        return null;
    }
}
