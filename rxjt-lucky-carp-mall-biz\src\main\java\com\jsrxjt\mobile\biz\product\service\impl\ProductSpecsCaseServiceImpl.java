package com.jsrxjt.mobile.biz.product.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.mobile.api.product.dto.ProductSpecsNameResponseDTO;
import com.jsrxjt.mobile.api.product.dto.ProductSpecsValueResponseDTO;
import com.jsrxjt.mobile.api.product.dto.request.ProductSpecRequestDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.product.service.ProductSpecsCaseService;
import com.jsrxjt.mobile.domain.product.entity.ProductSpecsEntity;
import com.jsrxjt.mobile.domain.product.repository.ProductSpecsRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 产品规格服务
 * @Author: ywt
 * @Date: 2025-05-12 15:02
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductSpecsCaseServiceImpl implements ProductSpecsCaseService {
    private final ProductSpecsRepository productSpecsRepository;

    @Override
    public List<ProductSpecsNameResponseDTO> specInfo(ProductSpecRequestDTO requestDTO) {
        List<ProductSpecsNameResponseDTO> responseList = new ArrayList<>();
        List<ProductSpecsEntity> specsEntityList = productSpecsRepository.getSpecInfoBySpuId(requestDTO.getProductSpuId(), requestDTO.getProductType());
        if (CollectionUtil.isEmpty(specsEntityList)) {
            return responseList;
        }
        for (ProductSpecsEntity entity : specsEntityList) {
            ProductSpecsNameResponseDTO specsNameResponseDTO = new ProductSpecsNameResponseDTO();
            BeanUtils.copyProperties(entity, specsNameResponseDTO);
            specsNameResponseDTO.setSpecsValueList(new ArrayList<>());
            if (CollectionUtil.isNotEmpty(entity.getSpecsValueList())) {
                entity.getSpecsValueList().forEach(value -> {
                    ProductSpecsValueResponseDTO valueResponseDTO = new ProductSpecsValueResponseDTO();
                    BeanUtils.copyProperties(value, valueResponseDTO);
                    specsNameResponseDTO.getSpecsValueList().add(valueResponseDTO);
                });
            }
            responseList.add(specsNameResponseDTO);
        }
        return responseList;
    }
}
