package com.jsrxjt.mobile.domain.customer.gateway.request;

import lombok.Data;

/**
 * @Description: 优惠券列表查询请求DTO
 * @Author: 
 * @Date: 2025-09-03
 * @Version: 1.0
 */
@Data
public class ShopTicketListRequestDTO {
    /**
     * vip用户id
     */
    private Long vipid;
    
    /**
     * 页码
     */
    private Long page;
    private Long size;
    
    /**
     * 标签类型 1-未使用 2-已使用 3-过期
     */
    private String tab;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 随机字符串
     */
    private String nonce;
    
    /**
     * 签名
     */
    private String sign;
}
