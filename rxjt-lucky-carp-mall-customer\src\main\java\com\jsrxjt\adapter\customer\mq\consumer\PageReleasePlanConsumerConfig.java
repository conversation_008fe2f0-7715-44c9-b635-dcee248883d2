package com.jsrxjt.adapter.customer.mq.consumer;

import lombok.SneakyThrows;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;


/**
 * 装修页面消息消费者配置
 * <AUTHOR>
 * @date 2025/08/26
 */
@Configuration
@RefreshScope
public class PageReleasePlanConsumerConfig {
  @SneakyThrows
  @Bean(destroyMethod = "close")
  public PushConsumer pageReleasePlanPushConsumer(ClientConfiguration clientConfiguration,
    PageReleasePlanMessageListener pageReleasePlanMessageListener) {
    FilterExpression filterExpression = new FilterExpression("*", FilterExpressionType.TAG);
    String consumerGroup = "page_release_consumer";
    String topic = "page_release_topic";
    return ClientServiceProvider.loadService().newPushConsumerBuilder()
        .setClientConfiguration(clientConfiguration)
        .setConsumerGroup(consumerGroup)
        .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
        .setMessageListener(pageReleasePlanMessageListener)
        .build();
  }
}