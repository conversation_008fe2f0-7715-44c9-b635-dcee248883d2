package com.jsrxjt.mobile.domain.merchant.service;

import com.jsrxjt.mobile.domain.merchant.entity.MerchantShopDataEntity;
import com.jsrxjt.mobile.domain.merchant.request.MerchantShopRequest;

import java.util.List;

/**
 * @Description: 商户服务
 * @Author: ywt
 * @Date: 2025-08-25 10:34
 * @Version: 1.0
 */
public interface MerchantService {
    List<MerchantShopDataEntity.MerchantShopEntity> getRxShopList(MerchantShopRequest request);
}
