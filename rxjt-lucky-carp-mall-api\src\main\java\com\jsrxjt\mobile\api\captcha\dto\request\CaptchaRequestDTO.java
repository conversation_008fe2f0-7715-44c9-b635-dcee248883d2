package com.jsrxjt.mobile.api.captcha.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 前端获取验证码请求DTO
 * 
 * <AUTHOR>
 * @since 2025-12-04
 */
@Data
@Schema(description = "获取验证码请求")
public class CaptchaRequestDTO extends BaseParam  {

    @Schema(description = "验证码类型", example = "blockPuzzle", allowableValues = {"blockPuzzle", "clickWord"})
    @NotBlank(message = "验证码类型不能为空")
    private String captchaType;

}
