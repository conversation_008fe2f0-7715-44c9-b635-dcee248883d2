package com.jsrxjt.mobile.api.payment.dto.response;

import com.jsrxjt.mobile.api.enums.PaymentChannelEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 支付回调处理结果DTO
 * <AUTHOR>
 * @since 2025/6/19
 **/
@Data
public class PaymentCallbackHandlerDTO {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;
    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;
    /**
     * 支付渠道交易号
     */
    private String tradeNo;
    /**
     * 解析的map参数
     */

    private Map<String, String> mapParams;

    /**
     * 支付渠道
     */
    private PaymentChannelEnum paymentChannel;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 退款单号 （即售后单号）
     */
    private String refundNo;

    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    private LocalDateTime refundTime;

}
