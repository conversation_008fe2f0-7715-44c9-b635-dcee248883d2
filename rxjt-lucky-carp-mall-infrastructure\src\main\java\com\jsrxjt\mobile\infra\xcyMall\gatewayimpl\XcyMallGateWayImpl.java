package com.jsrxjt.mobile.infra.xcyMall.gatewayimpl;

import com.alibaba.fastjson.JSONObject;
import com.jsrxjt.mobile.api.customer.response.CustomerDetailResponse;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCancelResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallQueryUserResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallRefundResponseDTO;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.xcyMall.gateway.IXcyMallGateWay;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import com.jsrxjt.mobile.infra.xcyMall.util.XcyMallUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.SortedMap;
import java.util.UUID;
import java.util.function.Function;

/**
 * Created by jeffery.yang on 2025/10/21 17:11
 *
 * @description:
 * @author: jeffery.yang
 * @date: 2025/10/21 17:11
 * @version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XcyMallGateWayImpl implements IXcyMallGateWay {

	private final DistributionConfig distributionConfig;

	@Value("${flq.cashier.url}")
	private String cashierUrl;

	private static final String UTF_8 = "UTF-8";
	private static final int SUCCESS_CODE = 0;
	private static final String SUCCESS_DESC = "SUCCESS";

	@Override
	public Boolean verifySign(Map<String, Object> requestMap, String appFlag) {
		return XcyMallUtils.verifySignature(requestMap, getChannelConfig(appFlag).getAppSecret());
	}

	@Override
	public XcyMallQueryUserResponseDTO buildGetVipInfoResponse(CustomerDetailResponse customerDetail, AppGoodsEntity appGoods) {
		return buildResponse(appGoods.getAppFlag(), config ->
								 XcyMallQueryUserResponseDTO.builder()
									 .phone(customerDetail.getPhone())
									 .uid(customerDetail.getId().toString())
									 .channel(appGoods.getAppFlag())
									 .channel_name(appGoods.getAppName())
									 .is_canteen(0)
									 .company("默认")
									 .group_id("0")
									 .group_name("默认")
									 .appid(config.getAppId())
									 .timestamp(getCurrentTimestamp())
									 .nounce(generateNonce())
									 .resCode(SUCCESS_CODE)
									 .resDesc(SUCCESS_DESC)
							);
	}

	@Override
	public XcyMallCreateOrderResponseDTO buildCreateOrderResponse(OrderInfoEntity orderInfoEntity, String appFlag) {
		return buildResponse(appFlag, config ->
								 XcyMallCreateOrderResponseDTO.builder()
									 .outTradeNo(orderInfoEntity.getOrderNo())
									 .payUrl(cashierUrl + orderInfoEntity.getOrderNo())
									 .appid(config.getAppId())
									 .timestamp(getCurrentTimestamp())
									 .nounce(generateNonce())
									 .resCode(SUCCESS_CODE)
									 .resDesc(SUCCESS_DESC)
							);
	}

	@Override
	public XcyMallRefundResponseDTO buildRefundOrderResponse(AfterSaleEntity afterSaleEntity, String appFlag) {
		return buildResponse(appFlag, config ->
								 XcyMallRefundResponseDTO.builder()
									 .appid(config.getAppId())
									 .timestamp(getCurrentTimestamp())
									 .nounce(generateNonce())
									 .resCode(SUCCESS_CODE)
									 .resDesc(SUCCESS_DESC)
							);
	}

	@Override
	public XcyMallCancelResponseDTO buildCancelOrderResponse(AppGoodsEntity appGoods, String appFlag) {
		return buildResponse(appFlag, config ->
								 XcyMallCancelResponseDTO.builder()
									 .appid(appGoods.getAppId().toString())
									 .timestamp(getCurrentTimestamp())
									 .nounce(generateNonce())
									 .resCode(SUCCESS_CODE)
									 .resDesc(SUCCESS_DESC)
							);
	}

	/**
	 * 通用响应构建方法
	 */
	private <T, B> T buildResponse(String appFlag, Function<DistributionConfig.ChannelConfig, B> builderSupplier) {
		DistributionConfig.ChannelConfig config = getChannelConfig(appFlag);
		B builder = builderSupplier.apply(config);

		try {
			Method buildMethod = builder.getClass().getMethod("build");
			buildMethod.setAccessible(true);
			T response = (T) buildMethod.invoke(builder);
			return signResponse(response, config.getAppSecret());
		} catch (Exception e) {
			throw new RuntimeException("构建响应失败", e);
		}
	}

	private DistributionConfig.ChannelConfig getChannelConfig(String appFlag) {
		return distributionConfig.getChannelConfigByType(DistChannelType.getByCode(appFlag));
	}

	private <T> T signResponse(T response, String appSecret) {
		SortedMap<Object, Object> parameters = JSONObject.parseObject(JSONObject.toJSONString(response), SortedMap.class);
		String sign = XcyMallUtils.createSign(UTF_8, parameters, appSecret);

		try {
			response.getClass().getMethod("setSign", String.class).invoke(response, sign);
			return response;
		} catch (Exception e) {
			throw new RuntimeException("设置签名失败", e);
		}
	}

	private String getCurrentTimestamp() {
		return String.valueOf(System.currentTimeMillis() / 1000);
	}

	private String generateNonce() {
		return UUID.randomUUID().toString().replace("-", "");
	}

}
