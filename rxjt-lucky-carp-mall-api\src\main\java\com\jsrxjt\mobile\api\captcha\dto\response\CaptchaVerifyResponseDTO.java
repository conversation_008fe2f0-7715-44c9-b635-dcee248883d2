package com.jsrxjt.mobile.api.captcha.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 验证码校验响应DTO
 * 
 * <AUTHOR>
 * @since 2025-12-04
 */
@Data
@Schema(description = "验证码校验响应")
public class CaptchaVerifyResponseDTO {

    @Schema(description = "校验是否成功", example = "true")
    private boolean success;

    @Schema(description = "响应消息", example = "验证成功")
    private String message;

    @Schema(description = "响应码", example = "0000")
    private String code;

}
