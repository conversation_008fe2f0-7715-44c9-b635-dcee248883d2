---
type: "always_apply"
---

[编程规矩]
1.代码必须遵循SOLID原则
2.每个函数只干一件事
3.代码参考示例com.jsrxjt.adapter.demo.controller.TestController的transferAccount方法的实现
Controller方法的request和response参数在rxjt-lucky-carp-mall-api模块下面
rxjt-lucky-carp-mall-api模块负责外部接口定义，api接口的相关元素，简单的pojo，领域值对象等
rxjt-lucky-carp-mall-biz模块负责业务编排
rxjt-lucky-carp-mall-domain 领域层，包括领域建模,领域服务，领域对象
rxjt-lucky-carp-mall-infrastructure模块负责基础设施,包括数据库,缓存,消息队列的具体实现，防腐接口的实现等
rxjt-lucky-carp-mall-xxljob 定时任务模块


[辅助模式]
1.看到能复用的代码就提醒我
2.检查代码边界情况

[文档]
1. 每次修改的记录和总结说明，自动加入到change.md文档中

[语言]
使用中文回答问题