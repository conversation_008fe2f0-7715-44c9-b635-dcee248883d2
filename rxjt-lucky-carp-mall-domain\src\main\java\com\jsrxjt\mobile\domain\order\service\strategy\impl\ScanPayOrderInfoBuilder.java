package com.jsrxjt.mobile.domain.order.service.strategy.impl;

import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 扫码付推单订单信息构建
 * <AUTHOR>
 * @date 2025/10/31
 */
@Component
@Slf4j
public class ScanPayOrderInfoBuilder extends DefaultOrderInfoBuilder {

    public ScanPayOrderInfoBuilder(BusinessIdGenerator businessIdGenerator, ConfigRepository configRepository) {
        super(businessIdGenerator, configRepository);
    }

    @Override
    public void supplementOrderInfo(OrderInfoEntity orderInfo, CreateOrderDTO request) {
        log.info("补充扫码付订单信息，订单号：{}", orderInfo.getOrderNo());
        // 补充第三方特有信息
        if (request.getExternalShopId() != null) {
            orderInfo.setExternalShopId(request.getExternalShopId());
        }
        if (request.getExternalShopUserId() != null) {
            orderInfo.setExternalShopUserId(request.getExternalShopUserId());
        }
        if (request.getTradeNo() != null) {
            orderInfo.setTradeNo(request.getTradeNo());
        }
        // 设置特定的订单渠道
        orderInfo.setOrderChannel(OrderChannelEnum.OFFLINE_SCAN.getCode());
        log.info("扫码付单信息补充完成，外部订单号：{}", orderInfo.getTradeNo());
    }

}
