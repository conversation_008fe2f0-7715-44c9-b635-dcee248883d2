package com.jsrxjt.mobile.api.module.annotation;

import com.jsrxjt.mobile.api.module.types.ModuleDetailType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 组件详情类型注解
 * <AUTHOR>
 * @date 2025/04/22
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModuleDetailTypeHandler {
    ModuleDetailType[] value();
}
