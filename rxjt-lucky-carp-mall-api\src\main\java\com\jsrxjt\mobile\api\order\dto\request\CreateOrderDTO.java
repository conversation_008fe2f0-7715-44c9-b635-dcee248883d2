package com.jsrxjt.mobile.api.order.dto.request;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 创建订单DTO
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/6/16
 **/
@Getter
@Setter
public class CreateOrderDTO  {
    /**
     * 客户Id
     */
    private Long customerId;

    /**
     * 客户手机号
     */
    private String customerMobile;

    /**
     * 充值账号:类型为直充/红包时不为空
     */
    private String rechargeAccount;

    /**
     * 产品类型
     */
    private Integer productType;
    
    /**
     * 产品spuid
     */
    private Long productSpuId;

    /**
     * 产品skuid,不存在时传0
     */
    private Long productSkuId;

    /**
     * 数量，默认是1
     */
    private Integer quantity = 1;

    /**
     * 充值账号类型 0 其他 1 手机号 2 qq号 支付宝充值传0 视频直充传1或2
     */
    private Integer rechargeAccountType;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    // 第三方订单相关字段
    
    /**
     * 外部订单号
     */
    private String externalOrderNo;
    /**
     * 分销中心业务交易号
     */
    private String distTradeNo;

    /**
     * 第三方ID
     */
    private String thirdId;

    /**
     * 外部店铺ID
     */
    private String externalShopId;

    /**
     * 外部门店员ID
     */
    private String externalShopUserId;

    /**
     * 支付交易号
     */
    private String tradeNo;

    /**
     * 第三方应用产品价格，一般是推送过来的交易金额
     */
    private BigDecimal externalAppProductPrice;

    private Integer regionId;

    /**
     * 支付失效时间戳(秒)
     */
    private Long payExpireTimestamp;

    /**
     * 第三方支付成功页面
     */
    private String externalPayResultUrl;

    /**
     * 第三方订单到期时间
     */
    private String externalOrderExpireTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 赠券请求参数
     */
    private List<GiftTicketRequestDTO> giftTickets;

    /**
     * 分销应用订单详情链接
     */
    private String orderDetailUrl;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     *商品额外信息
     */
    private JSONObject extraInfo;

    /**
     * 验证码类型
     */
    private String captchaType;

    /**
     * 验证码token
     */
    private String captchaToken;

    /**
     * 验证码坐标信息
     */
    private String captchaCoordinates;

    /**
     * 浏览器信息--验证码校验使用
     */
    private String browserInfo;
}
