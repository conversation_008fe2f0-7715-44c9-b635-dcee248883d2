package com.jsrxjt.mobile.api.customer.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "用户解绑卡请求参数")
public class CustomerUnbindCardRequest extends BaseParam {

    @Schema(description = "用户Id")
    @NotNull(message = "用户Id不能为空")
    private Long customerId;

    @Schema(description = "卡号")
    @NotBlank(message = "卡号不能为空")
    private String cardNo;

}
