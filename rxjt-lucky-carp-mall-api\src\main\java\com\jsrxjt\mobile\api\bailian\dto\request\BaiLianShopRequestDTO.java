package com.jsrxjt.mobile.api.bailian.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "获取百联门店列表查询条件")
public class BaiLianShopRequestDTO {

    @Schema(description = "百联品牌id")
    @NotNull(message = "百联品牌id不能为空")
    private Integer brandId;

    @Schema(description = "门店名称")
    private String search;
}
