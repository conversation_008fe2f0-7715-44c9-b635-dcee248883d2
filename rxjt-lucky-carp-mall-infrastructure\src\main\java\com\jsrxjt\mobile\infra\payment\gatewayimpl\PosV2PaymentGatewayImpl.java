package com.jsrxjt.mobile.infra.payment.gatewayimpl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.response.ScanPayOrderStatusDTO;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.payment.gateway.PosV2PaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.OfflinePayRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * pos-v2支付gateway接口实现
 * <AUTHOR>
 * @date 2025/10/27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PosV2PaymentGatewayImpl implements PosV2PaymentGateway {

    private final HttpClientGateway httpClientGateway;

    @Value("${pos-v2.host:https://devapi.rxcrs.com}")
    private String posV2Host;

    @Value("${pos-v2.offlinePayUrl:/flqv2/flq/v2/newflq/doPay}")
    private String offlinePayUrl;

    @Value("${pos-v2.offlineOrderStatusUrl:/flqv2/flq/v2/flq/findOrderDetail}")
    private String orderStatusUrl;

    @Value("${payment.online.connect.timeout:3000}")
    private int connectTimeout;

    @Value("${payment.online.read.timeout:5000}")
    private int readTimeout;

    @Override
    public BaseResponse pay(OfflinePayRequest request) {
        log.info("开始调用线下支付接口，订单号：{}，来源：{}", request.getTradeNo(), request.getSource());
        try {
            // 构建请求JSON - 转换为下划线格式
            String requestJson = JSON.toJSONString(request);
            log.info("线下支付请求参数：{}", requestJson);

            // 调用远程接口
            String url = posV2Host + offlinePayUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("线下支付接口响应：{}", responseStr);

            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("线下支付接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.ONLINE_PAY_PAY_INVOKE_ERROR.getCode(),errorMsg);
            }
            return BaseResponse.succeed();
        } catch (BizException e) {
            log.error("线下支付处理业务异常，tradeNo：{}，错误码：{} 错误信息：{}", request.getTradeNo(), e.getCode(),e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("线下支付接口调用异常，tradeNo：{}，错误信息：{}", request.getTradeNo(), e.getMessage(), e);
            throw new BizException("线下支付接口调用IO异常", e);
        } catch (Exception e) {
            log.error("线下支付处理失败，tradeNo：{}，错误信息：{}", request.getTradeNo(), e.getMessage(), e);
            throw new BizException("线下支付处理失败", e);
        }
    }

    @Override
    public BaseResponse<ScanPayOrderStatusDTO> orderStatus(Long vipId, String tradeNo) {
        log.info("开始调用线下订单支付状态接口，订单号：{}，vip：{}", tradeNo, vipId);
        try {
            // 构建请求JSON - 转换为下划线格式
            JSONObject request = new JSONObject().fluentPut("vipId", vipId)
                    .fluentPut("tradeNo", tradeNo);
            log.info("线下订单支付状态请求参数：{}", request.toJSONString());

            // 调用远程接口
            String url = posV2Host + orderStatusUrl;
            String responseStr = httpClientGateway.doPostJson(url, request.toJSONString(), connectTimeout, readTimeout);
            log.info("线下订单支付状态接口响应：{}", responseStr);

            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("线下订单支付状态接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.LFailed.getCode(),errorMsg);
            }else {
                String tradeStatus = responseJson.getJSONObject("data").getString("trade_status");
                ScanPayOrderStatusDTO scanPayOrderStatusDTO = new ScanPayOrderStatusDTO();
                scanPayOrderStatusDTO.setTradeStatus(tradeStatus);
                return BaseResponse.succeed(scanPayOrderStatusDTO);
            }
        } catch (BizException e) {
            log.error("线下订单支付状态处理业务异常，tradeNo：{}，错误码：{} 错误信息：{}", tradeNo, e.getCode(),e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("线下订单支付状态接口调用异常，tradeNo：{}，错误信息：{}", tradeNo, e.getMessage(), e);
            throw new BizException("线下订单支付状态接口调用IO异常", e);
        } catch (Exception e) {
            log.error("线下订单支付状态处理失败，tradeNo：{}，错误信息：{}", tradeNo, e.getMessage(), e);
            throw new BizException("线下订单支付状态处理失败", e);
        }
    }
}
