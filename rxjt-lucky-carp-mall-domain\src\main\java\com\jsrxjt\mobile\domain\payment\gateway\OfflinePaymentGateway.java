package com.jsrxjt.mobile.domain.payment.gateway;

import com.jsrxjt.mobile.domain.payment.gateway.request.OfflineCardSortRequest;
import com.jsrxjt.mobile.domain.payment.gateway.response.OfflineCardSortResponse;

/**
 * 线下支付gateway接口

 */
public interface OfflinePaymentGateway {

    /**
     * 线下查询卡支付顺序
     * @param request
     * @return
     */
    OfflineCardSortResponse findOfflineCardSort(OfflineCardSortRequest request);

}
