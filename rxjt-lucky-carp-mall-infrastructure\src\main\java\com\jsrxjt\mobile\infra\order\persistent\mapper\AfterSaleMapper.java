package com.jsrxjt.mobile.infra.order.persistent.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.AfterSalePO;
import com.jsrxjt.mobile.infra.order.persistent.po.AfterSaleWithItemPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 售后数据访问层
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface AfterSaleMapper extends CommonBaseMapper<AfterSalePO> {

    /**
     * 分页查询售后单及订单项信息
     */
    IPage<AfterSaleWithItemPO> selectAfterSaleListWithItemsPage(IPage<AfterSaleWithItemPO> page,
                                                               @Param(Constants.WRAPPER) Wrapper<AfterSalePO> queryWrapper);
}
