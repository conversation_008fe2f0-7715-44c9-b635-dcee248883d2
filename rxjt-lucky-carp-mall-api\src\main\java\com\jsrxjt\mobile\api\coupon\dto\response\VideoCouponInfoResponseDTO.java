package com.jsrxjt.mobile.api.coupon.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 视听直充卡券信息响应
 * @Author: ywt
 * @Date: 2025-07-22 10:34
 * @Version: 1.0
 */
@Data
public class VideoCouponInfoResponseDTO {
    @Schema(description = "直充聚合页是否展示 1显示 0不显示")
    private Integer isShowPolymerize;

    @Schema( description = "所有试听会员充值卡券的聚合信息，仅isShowPolymerize为1有效")
    private List<VideoCouponPolymerizeDetailResponseDTO> goodsVideoResponseList;

    @Schema( description = "单SPU的所有信息，仅isShowPolymerize为0有效")
    private VideoCouponSingleDetailResponseDTO singleDetail;
}
