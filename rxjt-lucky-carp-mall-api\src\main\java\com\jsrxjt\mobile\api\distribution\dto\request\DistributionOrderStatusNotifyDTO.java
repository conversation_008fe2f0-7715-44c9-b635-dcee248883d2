package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DistributionOrderStatusNotifyDTO extends DistributionNotifyCommonDTO{

    @Schema(description = "商城订单号")
    private String orderNo;

    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "订单状态")
    private String changeStatus;

    @Schema(description = "订单类型(美团,小象)")
    private Integer changeType;

    @Schema(description = "百胜取餐号 ⾃取时上传")
    private String mealNumber;
}
