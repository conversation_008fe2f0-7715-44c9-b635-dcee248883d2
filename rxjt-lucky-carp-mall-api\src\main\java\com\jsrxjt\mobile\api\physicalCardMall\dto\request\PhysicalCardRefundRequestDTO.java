package com.jsrxjt.mobile.api.physicalCardMall.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 实体卡退款请求
 * @Author: ywt
 * @Date: 2025-10-27 11:08
 * @Version: 1.0
 */
@Data
public class PhysicalCardRefundRequestDTO extends PhysicalCardBaseDTO {
    @Schema(description = "实体卡侧订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    @Schema(description = "退款订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "退款订单号不能为空")
    private String refundOrderNo;
    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "退款金额不能为空")
    private String refundAmount;
}
