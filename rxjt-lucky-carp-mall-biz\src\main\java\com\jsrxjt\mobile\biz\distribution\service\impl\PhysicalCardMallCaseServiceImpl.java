package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.api.locallife.dto.response.LocalLifeCreateOrderResponse;
import com.jsrxjt.mobile.api.locallife.dto.response.LocalLifeOrderRefundResponse;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.api.physicalCardMall.dto.request.PhysicalCardCreateOrderRequestDTO;
import com.jsrxjt.mobile.api.physicalCardMall.dto.request.PhysicalCardRefundRequestDTO;
import com.jsrxjt.mobile.api.physicalCardMall.dto.response.PhysicalCardCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.physicalCardMall.dto.response.PhysicalCardRefundResponseDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.distribution.service.PhysicalCardMallCaseService;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.order.AfterSaleCaseService;
import com.jsrxjt.mobile.biz.order.AutoRefundCaseService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.biz.order.impl.AfterSaleCaseServiceImpl;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.locallife.types.LocalLifeGoodsEnum;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.PhysicalCardMallOrderInfoBuilder;
import com.jsrxjt.mobile.domain.physicalCardMall.PhysicalCardMallGateWay;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-10-23 10:05
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PhysicalCardMallCaseServiceImpl implements PhysicalCardMallCaseService {
    private final PhysicalCardMallGateWay physicalCardMallGateWay;
    private final DistributedLock distributedLock;
    private final OrderRepository orderRepository;
    private final CustomerService customerService;
    private final RegionRepository regionRepository;
    private final OrderCaseService orderCaseService;
    private final UnifiedDistributionApi unifiedDistributionApi;
    private final PhysicalCardMallOrderInfoBuilder physicalCardMallOrderInfoBuilder;
    private final AutoRefundCaseService autoRefundCaseService;
    private final AfterSaleCaseService afterSalecaseService;

    private final String PHYSICAL_CARD_MALL_ORDER_LOCK_PREFIX = "physical_card_mall_order_lock:";
    private final String PHYSICAL_CARD_MALL_REFUND_ORDER_LOCK_PREFIX = "physical_card_mall_refund_order_lock:";

    @Value("${flq.cashier.url}")
    private String cashierUrl;

    @Override
    public ApiResponse<PhysicalCardCreateOrderResponseDTO> prePay(PhysicalCardCreateOrderRequestDTO requestDTO) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(requestDTO.getUserId())
                || StringUtils.isEmpty(requestDTO.getOrderNo())
                || StringUtils.isEmpty(requestDTO.getTotalPrice())
//                || StringUtils.isEmpty(requestDTO.getFee())
                || StringUtils.isEmpty(requestDTO.getSignature())
                || StringUtils.isEmpty(requestDTO.getTimestamp())
                || StringUtils.isEmpty(requestDTO.getNounce())
                || StringUtils.isEmpty(requestDTO.getTradeAmount())
        ) {
            return ApiResponse.fail("参数不全");
        }
        // 校验金额是否异常
        BigDecimal tradeAmount = new BigDecimal(requestDTO.getTradeAmount());
        if (tradeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return ApiResponse.fail("订单金额非法");
        }
        // 验签
        Map<String, Object> map = JSONObject.parseObject(JSONUtil.toJsonStr(requestDTO), Map.class);
        if (!physicalCardMallGateWay.checkSign(map)) {
            log.error("实体卡商城-创建订单-签名校验失败: {}", JSONUtil.toJsonStr(map));
            return ApiResponse.fail("签名验证失败");
        }
        String outOrderSn = requestDTO.getOrderNo();
        String lockKey = PHYSICAL_CARD_MALL_ORDER_LOCK_PREFIX + outOrderSn;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("实体卡商城-创建订单-处理锁失败，外部订单号：{}", outOrderSn);
                return ApiResponse.fail("系统繁忙，请稍后重试");
            }

            OrderInfoEntity existOrderEntity = orderRepository.findByExternalOrderNoAndAppFlag(outOrderSn, DistChannelType.PHYSICALCARDMALL.name());
            // 幂等校验
            if (existOrderEntity != null) {
                log.info("实体卡商城-创建订单-此订单已存在，三方（实体卡商城）订单号：{}", outOrderSn);
                if (existOrderEntity.getOrderStatus() != OrderStatusEnum.PENDING_PAYMENT.getCode()) {
                    log.error("实体卡商城-创建订单-订单不是待付款状态，无法继续支付：{}", outOrderSn);
                    return ApiResponse.fail("订单不是待付款状态，无法继续支付。");
                }
                PhysicalCardCreateOrderResponseDTO physicalCardCreateOrderResponseDTO = PhysicalCardCreateOrderResponseDTO
                        .builder()
                        .flqOrderNo(existOrderEntity.getOrderNo())
                        .cashierUrl(cashierUrl + existOrderEntity.getOrderNo())
                        .build();
                Map<String, Object> prePayResponse = physicalCardMallGateWay.addCommonParams(physicalCardCreateOrderResponseDTO);
                physicalCardCreateOrderResponseDTO = JSON.parseObject(JSON.toJSONString(prePayResponse), PhysicalCardCreateOrderResponseDTO.class);
                return ApiResponse.success(physicalCardCreateOrderResponseDTO);
            }

            CustomerEntity customer = customerService.getCustomerById(Long.valueOf(requestDTO.getUserId()));
            if (Objects.isNull(customer)) {
                log.error("实体卡商城-创建订单-未找到会员ID：{}", requestDTO.getUserId());
                return ApiResponse.fail(700, "用户不存在");
            }

            //获取实体卡商城订单url
            DistAccessRequest localLifeRedirectUrlDTO = new DistAccessRequest();
            BeanUtils.copyProperties(requestDTO, localLifeRedirectUrlDTO);
            localLifeRedirectUrlDTO.setUserId(requestDTO.getUserId());
            localLifeRedirectUrlDTO.setSceneValue(requestDTO.getOrderNo());
            localLifeRedirectUrlDTO.setSceneId(LocalLifeGoodsEnum.INDEX.getCode());
            String indexUrl = null;
            try {
                RegionEntity region = regionRepository.getCurrentRegion(customer.getId());
                localLifeRedirectUrlDTO.setLatitude(region.getLat());
                localLifeRedirectUrlDTO.setLongitude(region.getLng());
                localLifeRedirectUrlDTO.setChannelType(DistChannelType.PHYSICALCARDMALL);
                DistAccessResponse distAccessResponse = unifiedDistributionApi.access(localLifeRedirectUrlDTO);
                indexUrl = distAccessResponse.getRedirectUrl();
            } catch (Exception e) {
                log.error("未获取到用户定位信息 customer={}", customer.getId());
            }

            CreateOrderDTO createOrderDTO = new CreateOrderDTO();
            createOrderDTO.setCustomerId(customer.getId());
            createOrderDTO.setCustomerMobile(customer.getPhone());
            createOrderDTO.setExternalOrderNo(outOrderSn);
            createOrderDTO.setProductSpuId(37L);
            createOrderDTO.setProductType(ProductTypeEnum.APP.getType());
            createOrderDTO.setExternalAppProductPrice(tradeAmount);
//            createOrderDTO.setExternalPayResultUrl(externalPayResultUrl);
            createOrderDTO.setOrderDetailUrl(indexUrl);//首页或订单页地址
            OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO, physicalCardMallOrderInfoBuilder);
            PhysicalCardCreateOrderResponseDTO physicalCardCreateOrderResponseDTO = PhysicalCardCreateOrderResponseDTO
                    .builder()
                    .flqOrderNo(orderInfoEntity.getOrderNo())
                    .cashierUrl(cashierUrl + orderInfoEntity.getOrderNo())
                    .build();
            Map<String, Object> prePayResponse = physicalCardMallGateWay.addCommonParams(physicalCardCreateOrderResponseDTO);
            physicalCardCreateOrderResponseDTO = JSON.parseObject(JSON.toJSONString(prePayResponse), PhysicalCardCreateOrderResponseDTO.class);
            log.info("实体卡商城下单返回数据{}", JSONUtil.toJsonStr(physicalCardCreateOrderResponseDTO));
            return ApiResponse.success(physicalCardCreateOrderResponseDTO, "OK");
        } catch (Exception e) {
            log.error("处理实体卡商城下创建通知异常，订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
            return ApiResponse.fail("系统异常：" + e.getMessage());
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public ApiResponse<PhysicalCardRefundResponseDTO> refund(PhysicalCardRefundRequestDTO requestDTO) {
        // 校验入参是否有效
        if (StringUtils.isEmpty(requestDTO.getRefundOrderNo())
                || StringUtils.isEmpty(requestDTO.getOrderNo())
                || StringUtils.isEmpty(requestDTO.getRefundAmount())
                || StringUtils.isEmpty(requestDTO.getSignature())
                || StringUtils.isEmpty(requestDTO.getTimestamp())) {
            return ApiResponse.fail("请求参数不全");
        }
        // 校验金额是否异常
        BigDecimal refundAmount = new BigDecimal(requestDTO.getRefundAmount());
        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return ApiResponse.fail("退款订单金额非法");
        }
        // 验签
        Map<String, Object> map = JSONObject.parseObject(JSONUtil.toJsonStr(requestDTO), Map.class);
        if (!physicalCardMallGateWay.checkSign(map)) {
            log.error("实体卡商城-退款订单-签名校验失败: {}", JSONUtil.toJsonStr(map));
            return ApiResponse.fail("签名验证失败");
        }
        String outOrderSn = requestDTO.getOrderNo();
        String lockKey = PHYSICAL_CARD_MALL_REFUND_ORDER_LOCK_PREFIX + outOrderSn;
        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.error("获取实体卡商城订单退款通知处理锁失败，订单号：{}", outOrderSn);
                return ApiResponse.fail("系统繁忙，请稍后重试");
            }

            OrderInfoEntity order = orderRepository.findByExternalOrderNoAndDistTradeNo(requestDTO.getOrderNo(), null);
            if (order == null) {
                return ApiResponse.fail(605, "订单不存在");
            }

            AfterSaleEntity afterSaleEntity = new AfterSaleEntity();
            Integer paymentStatus = order.getPaymentStatus();
            if (Objects.equals(paymentStatus, PaymentStatusEnum.UNPAID.getCode())) {
                return ApiResponse.fail(609, "订单状态不可退");
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.PAID.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDING.getCode())
                    || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDED.getCode())) {
                AutoRefundRequestDTO autoRefundRequestDTO = new AutoRefundRequestDTO();
                autoRefundRequestDTO.setOrderNo(order.getOrderNo());
                autoRefundRequestDTO.setExternalRefundNo(requestDTO.getRefundOrderNo());
                autoRefundRequestDTO.setExternalRefundAmount(refundAmount);
                BigDecimal orderAmount = order.getTotalSellAmount();
                if (orderAmount.compareTo(refundAmount) == 0) {
                    // 整单退
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode());
                    autoRefundRequestDTO.setApplyRefundAmount(order.getOrderAmount());
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else if (orderAmount.compareTo(refundAmount) > 0) {
                    //部分退
                    autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.PARTIAL_REFUND.getCode());
                    autoRefundRequestDTO = afterSalecaseService.calDistributionOrderRefundAmount(autoRefundRequestDTO);
                    afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
                } else {
                    return ApiResponse.fail(607, "退款金额大于订单金额");
                }
            } else if (Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDED.getCode())) {
                return ApiResponse.fail(608, "已整单退款,不可再退");
            }
            PhysicalCardRefundResponseDTO physicalCardRefundResponseDTO = PhysicalCardRefundResponseDTO
                    .builder()
                    .flqRefundNo(afterSaleEntity.getRefundNo())
                    .build();

            Map<String, Object> prePayResponse = physicalCardMallGateWay.addCommonParams(physicalCardRefundResponseDTO);
            physicalCardRefundResponseDTO = JSON.parseObject(JSON.toJSONString(prePayResponse), PhysicalCardRefundResponseDTO.class);
            log.info("实体卡商城退款返回数据{}", JSONUtil.toJsonStr(physicalCardRefundResponseDTO));
            return ApiResponse.success(physicalCardRefundResponseDTO, "OK");
        } catch (Exception e) {
            log.error("处理实体卡商城订单退款通知异常，订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
            return ApiResponse.fail("系统异常：" + e.getMessage());
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }
}
