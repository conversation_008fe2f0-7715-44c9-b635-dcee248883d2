package com.jsrxjt.mobile.infra.bianlifeng.gatewayimpl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.domain.bianlifeng.gateway.BianLiFengGateway;
import com.jsrxjt.mobile.domain.bianlifeng.request.BianLiFengRechargeRequest;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.bianlifeng.config.BianLiFengConfig;
import com.jsrxjt.mobile.infra.bianlifeng.util.BianLiFengSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025-10-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BianLiFengGatewayImpl implements BianLiFengGateway {

    private final BianLiFengConfig bianLiFengConfig;

    private final HttpClientGateway httpClientGateway;


    @Override
    public ApiResponse<String> recharge(BianLiFengRechargeRequest request) {
        try {
            request.setMerchant(bianLiFengConfig.getMerchant());
            log.info("便利蜂充值请求参数为:{}", JSONUtil.toJsonStr(request));
            Map<String, Object> params = JSONObject.parseObject(JSONUtil.toJsonStr(request), Map.class);
            String signature = BianLiFengSignUtil.getSignature(params, bianLiFengConfig.getSecretKey());
            params.put("sign", signature);
            log.info("增加验签后的便利蜂充值请求参数为:{}", JSONUtil.toJsonStr(params));
            String response = httpClientGateway.doPostJson(bianLiFengConfig.getHost(), JSON.toJSONString(params), bianLiFengConfig.getConnectTimeout(),
                    bianLiFengConfig.getReadTimeout());
            log.info("便利蜂充值结果为:{}", response);
            JSONObject jsonObject = JSONObject.parseObject(response);
            String returnCode = jsonObject.getString("returnCode");
            if (Objects.equals("success", returnCode) && Objects.nonNull(jsonObject.get("orderNo"))) {
                return ApiResponse.success(jsonObject.getString("orderNo"));
            }
            return ApiResponse.fail(jsonObject.getString("errMessage"));
        } catch (Exception e) {
            return ApiResponse.fail(e.getMessage());
        }
    }
}
