package com.jsrxjt.mobile.api.physicalCardMall.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-10-23 09:31
 * @Version: 1.0
 */
@Data
public class PhysicalCardBaseDTO {
    private String nounce;

    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

    @NotBlank(message = "签名不能为空")
    private String signature;
}
